package com.mrcar.gov.user.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.izu.framework.response.PageDTO;
import com.mrcar.gov.common.dto.BaseDTO;
import com.mrcar.gov.common.dto.msg.GovMessageTrackingSenderDTO;
import com.mrcar.gov.common.dto.user.req.*;
import com.mrcar.gov.common.dto.user.resp.GovUserDropDownDTO;
import com.mrcar.gov.common.dto.workflow.common.CustomerQueryReqDTO;
import com.mrcar.gov.common.dto.workflow.common.CustomerStructInfoDTO;
import com.mrcar.gov.common.dto.user.resp.GovUserDetailForModifyRespDTO;
import com.mrcar.gov.common.dto.user.resp.GovUserDetailGetRespDTO;
import com.mrcar.gov.common.dto.user.resp.GovUserListRespDTO;
import com.mrcar.gov.common.dto.user.resp.UserForRoleResponseDTO;
import com.mrcar.gov.common.dto.user.resp.UserRoleAndPermRespDTO;
import com.mrcar.gov.common.dto.user.resp.UserSimpleResponseDTO;
import com.mrcar.gov.user.domain.GovUser;
import org.apache.commons.lang3.tuple.Pair;

import java.util.List;

/**
* <AUTHOR>
*/
public interface GovUserService extends IService<GovUser> {

    /**
     * 添加用户
     */
    String addUser(GovUserAddReqDTO requestDTO);

    /**
     * 修改用户信息
     */
    Boolean modifyUser( GovUserModifyReqDTO reqDTO);

    /**
     * 修改用户页面，获取用户信息
     */
    GovUserDetailForModifyRespDTO getGovUserDetailForModify(GovUserDetailGetForModifyReqDTO reqDTO);

    /**
     * 获取用户详情信息
     */
    GovUserDetailGetRespDTO getGovUserDetail(GovUserDetailGetReqDTO reqDTO);

    /**
     * 停用用户
     */
    Boolean stopGovUser(GovUserStatusChangeReqDTO reqDTO);

    /**
     * 启用用户
     */
    Boolean enableGovUser(GovUserStatusChangeReqDTO reqDTO);

    /**
     * 按条件查询用户
     * <AUTHOR>
     * @date 2024/11/12 17:03
     */
    List<UserForRoleResponseDTO> getMemberForList(GovUserQueryRequestDTO govUserQueryRequestDTO);
    /**
     * 按条件查询用户分页
     */
    PageDTO<UserForRoleResponseDTO> getMemberPageList(GovUserQueryRequestDTO govUserQueryRequestDTO);

    /**
     * 查询用户下拉列表
     */
    List<UserSimpleResponseDTO> getUserSelect(UserQueryRequestDTO userQueryRequestDTO);

    /**
     *  获取角色和数据权限-给用户创建页面使用
     */
    UserRoleAndPermRespDTO getRoleAndPermForCreate(GetRoleAndPermForCreateReqDTO  reqDTO);

    /**
     * 通过用户编码批量查询用户信息
     */
    List<GovUser> getUserByUserCodeList(List<String> userCodeList);

    /**
     * 查询企业下的客户
     * @param customerQueryReqDTO
     * @return
     */
    PageDTO<CustomerStructInfoDTO> queryCustomerByPage(CustomerQueryReqDTO customerQueryReqDTO);
    /**
     * 查询用户列表
     */
    PageDTO<GovUserListRespDTO> listGovUser(GovUserListReqDTO reqDTO);

    /**
     * 查询用户下拉
     */
    PageDTO<GovUserDropDownDTO> dropdown(GovUserListReqDTO reqDTO);


    List<GovOrderUserSearchRespDTO> searchUser(GovOrderUserSearchReqDTO reqDTO);

    GovOrderUserSearchRespDTO getUserInfo(BaseDTO reqDTO);

    /**
     * 用户修改密码
     */
    boolean modifyPassword(Integer userId, String password);


    Pair<List<String>, List<String>> getUserDataCode(BaseDTO reqDTO, String selectStructCode);

    List<GovOrderUserSearchRespDTO> searchUserV2(GovOrderUserV2SearchReqDTO reqDTO);

    /**
     * 消费埋点消息，补充用户信息
     */
    void fillInInfo(List<GovMessageTrackingSenderDTO> govMessageTrackingSenderDTO);


    // 填充角色数据
    void fillRoleName(List<GovUserListRespDTO> govUserListRespList);
    // 填充数据权限
    void fillUserDataName(List<GovUserListRespDTO> govUserListRespList);

    List<GovOrderUserSearchRespDTO> searchPassengers(GovOrderPassengersSearchReqDTO reqDTO);
}
