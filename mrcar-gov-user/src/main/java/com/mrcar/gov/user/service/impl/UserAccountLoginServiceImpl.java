package com.mrcar.gov.user.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.izu.cache.core.redis.RedisCache;
import com.izu.framework.exception.ApiException;
import com.izu.framework.web.util.BeanUtil;
import com.mrcar.gov.common.config.GovCityDicDTO;
import com.mrcar.gov.common.config.GovDistrictDicDTO;
import com.mrcar.gov.common.config.GovProvinceDicDTO;
import com.mrcar.gov.common.constant.RestErrorCode;
import com.mrcar.gov.common.constant.company.GovCompanyStatusEnum;
import com.mrcar.gov.common.constant.user.*;
import com.mrcar.gov.common.dto.AccountMenu;
import com.mrcar.gov.common.dto.AccountMenuDTO;
import com.mrcar.gov.common.dto.session.*;
import com.mrcar.gov.common.dto.user.GovStructImportDTO;
import com.mrcar.gov.common.dto.user.req.GovStructDropDownReqDTO;
import com.mrcar.gov.common.dto.user.req.GovStructRequestDTO;
import com.mrcar.gov.common.dto.user.resp.GovStructRespondDTO;
import com.mrcar.gov.common.enums.AdministrativeLevelEnum;
import com.mrcar.gov.common.service.config.GovCityDicCommonService;
import com.mrcar.gov.common.service.config.GovDistrictDicCommonService;
import com.mrcar.gov.common.service.config.GovProvinceDicCommonService;
import com.mrcar.gov.common.util.CheckConditionUtil;
import com.mrcar.gov.user.common.OrgStatusEnum;
import com.mrcar.gov.user.domain.*;
import com.mrcar.gov.user.service.*;
import com.mrcar.thirdparty.sms.AliyunSmsSender;
import com.mrcar.thirdparty.sms.SmsSendResult;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.mrcar.gov.common.constant.RedisKeyConstants.*;
import static com.mrcar.gov.common.constant.RestErrorCode.*;
import static com.mrcar.thirdparty.sms.SmsTemplateConstant.DEFAULT_TEMPLATE_CODE;
import static com.mrcar.thirdparty.sms.SmsTemplateConstant.DEFAULT_TEMPLATE_PARAMS;

@Service
public class UserAccountLoginServiceImpl implements UserAccountLoginService {

    private final DateTimeFormatter formatter =
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").withZone(ZoneId.systemDefault());

    @Resource
    private RedisCache cache;
    @Autowired
    private AliyunSmsSender sender;
    @Autowired
    private GovUserService govUserService;
    @Autowired
    private GovPushSmsService pushSmsService;
    @Autowired
    private GovRoleService roleService;
    @Autowired
    private GovUserRoleRelationService userRoleRelationService;
    @Autowired
    private GovPermissionService permissionService;
    @Autowired
    private GovCompanyService companyService;
    @Autowired
    private GovStructService structService;
    @Autowired
    private GovDataRelationService dataRelationService;
    @Resource
    private GovOrgInfoService govOrgInfoService;


    // 锁定时间
    @NacosValue(value = "${login.account.locked.time:180}", autoRefreshed = true)
    private int lockedTime;
    // 验证码
    @NacosValue(value = "${login.verify.code.send.switch:false}", autoRefreshed = true)
    private Boolean verifyCodeSwitch;
    @NacosValue(value = "${login.verify.code.content:''}", autoRefreshed = true)
    private String verifyCodeContent;
    @NacosValue(value = "${login.verify.code.modifyPassword.content:''}", autoRefreshed = true)
    private String modifyPasswordCodeContent;

    // 登录失败次数判断
    private static final int LOGIN_FAILURE_MAX_COUNT = 5;
    private static final int LOGIN_FAILURE_TIPS_COUNT = 3;

    // 每天最多发送的短信个数
    private final static int VERIFY_CODE_MAX_SEND_TIMES_PER_DAY = 1000;
    // 每60秒发送一个，限制发送频率
    private final static int VERIFY_CODE_MAX_SEND_INTERVAL = 60;
    // 手机号验证码有效时间180秒
    private final static int VERIFY_CODE_VALID_TIME = 180;
    // 默认验证码
    private final static String VERIFY_CODE_DEFAULT_CONTENT = "111111";
    // 验证码位数
    private final static int VERIFY_CODE_DIGITS = 6;

    private static final String ACCOUNT_LOGIN_FAILURE_MESSAGE_TYPE1 = "您输入密码错误次数超过5次，请%s分钟后再试！";
    private static final String ACCOUNT_LOGIN_FAILURE_MESSAGE_TYPE2 = "您输入密码错误次数超过%s次，还有%s次机会！";
    private static final String ACCOUNT_LOGIN_FAILURE_MESSAGE_TYPE3 = "密码输入错误！";
    @Autowired
    private GovStructServiceImpl govStructServiceImpl;
    @Resource
    private GovProvinceDicCommonService govProvinceDicCommonService;
    @Autowired
    private GovDataPermServiceImpl govDataPermServiceImpl;
    @Resource
    private GovDistrictDicCommonService govDistrictDicCommonService;
    @Resource
    private GovCityDicCommonService govCityDicCommonService;

    @Override
    public boolean isAccountLock(String mobile) {
        String key = this.formatAccountLockKey(mobile);
        Boolean exists = cache.hasKey(key);
        if (exists) {
            // 错误状态下每次尝试，锁定时间重新计时
            cache.expire(key, lockedTime);
        }
        return exists;
    }

    @Override
    public int getAccountLockTime(String mobile) {
        return lockedTime / 60;
    }

    @Override
    public boolean unLockAccount(String mobile) {
        String key1 = this.formatAccountLockKey(mobile);
        String key2 = this.formatFailureCountKey(mobile);
        return this.cache.deleteObject(Lists.newArrayList(key1, key2));
    }

    @Override
    public String triggerAccountLoginFailure(String mobile) {
        // 查询失败次数
        Long count = cache.getRedisTemplate()
                .opsForValue()
                .increment(this.formatFailureCountKey(mobile), 1);
        cache.expire(this.formatFailureCountKey(mobile), lockedTime);
        String message = null;
        // 返回错误提示信息
        if (count >= LOGIN_FAILURE_MAX_COUNT) {
            // 当前时间
            String value = LocalDateTime.now().format(formatter);
            // 锁定账号
            cache.setCacheObject(this.formatAccountLockKey(mobile), value, lockedTime, TimeUnit.SECONDS);
            message = String.format(ACCOUNT_LOGIN_FAILURE_MESSAGE_TYPE1, this.getAccountLockTime(mobile));
        } else if (count == LOGIN_FAILURE_TIPS_COUNT) {
            int left = Math.max(0, LOGIN_FAILURE_MAX_COUNT - LOGIN_FAILURE_TIPS_COUNT);
            message = String.format(ACCOUNT_LOGIN_FAILURE_MESSAGE_TYPE2, LOGIN_FAILURE_TIPS_COUNT, left);
        } else {
            message = ACCOUNT_LOGIN_FAILURE_MESSAGE_TYPE3;
        }
        return message;
    }

    @Override
    public void sendVerifyCode(String mobile) {
        if (checkVerifyCodeExceedDaily(mobile)) {
            throw new ApiException(LOGIN_VERIFY_CODE_SEND_TO_MANY);
        }
        if (checkVerifyCodeExceedFrequency(mobile)) {
            throw new ApiException(LOGIN_UNABLE_OBTAIN_VERIFY_CODE_AGAIN);
        }
        // 生成验证码
        String verifyCode = this.generateVerifyCode();
        // 短信内容
        String content = String.format(this.verifyCodeContent, verifyCode);
        // 发送短信
        if (this.verifyCodeSwitch) {
            this.doSendSms(mobile, content);
        }
        // 设置缓存
        String key = this.formatVerifyCodeKey(mobile);
        cache.setCacheObject(key, verifyCode, VERIFY_CODE_VALID_TIME, TimeUnit.SECONDS);
        // 验证码统计
        this.cache.getRedisTemplate()
                .opsForValue().increment(this.formatVerifyCodeCountKey(mobile));
        this.cache.expire(this.formatVerifyCodeCountKey(mobile), 1, TimeUnit.DAYS);
    }

    @Override
    public boolean checkVerifyCode(String mobile, String verifyCode) {
        String value = this.cache.getCacheObject(this.formatVerifyCodeKey(mobile));
        if (Objects.isNull(value)) {
            throw new ApiException(LOGIN_VERIFY_CODE_INVALID);
        }
        return Objects.equals(verifyCode, value);
    }

    @Override
    public void sendModifyPasswordVerifyCode(String mobile) {
        if (checkVerifyCodeExceedDaily(mobile)) {
            throw new ApiException(LOGIN_VERIFY_CODE_SEND_TO_MANY);
        }
        if (checkVerifyCodeExceedFrequency(mobile)) {
            throw new ApiException(LOGIN_UNABLE_OBTAIN_VERIFY_CODE_AGAIN);
        }
        // 生成验证码
        String verifyCode = this.generateVerifyCode();
        // 短信内容
        String content = String.format(this.modifyPasswordCodeContent, verifyCode);
        // 发送短信
        if (this.verifyCodeSwitch) {
            this.doSendSms(mobile, content);
        }
        // 设置缓存
        String key = this.formatModifyPasswordVerifyCodeKey(mobile);
        cache.setCacheObject(key, verifyCode, VERIFY_CODE_VALID_TIME, TimeUnit.SECONDS);
        // 验证码统计
        this.cache.getRedisTemplate()
                .opsForValue().increment(this.formatVerifyCodeCountKey(mobile));
        this.cache.expire(this.formatVerifyCodeCountKey(mobile), 1, TimeUnit.DAYS);
    }

    @Override
    public boolean checkModifyPasswordVerifyCode(String mobile, String verifyCode) {
        String value = this.cache.getCacheObject(this.formatModifyPasswordVerifyCodeKey(mobile));
        if (Objects.isNull(value)) {
            throw new ApiException(LOGIN_VERIFY_CODE_INVALID);
        }
        return Objects.equals(verifyCode, value);
    }

    @Override
    public GovUser getActivateUserByMobile(String mobile) {
        return this.govUserService.getOne(
                new LambdaQueryWrapper<GovUser>()
                        .eq(GovUser::getMobile, mobile)
                        .eq(GovUser::getUserStatus, GovUserStatusEnum.NORMAL.getCode()));
    }

    @Override
    public GovUser getUserByMobile(String mobile) {
        return this.govUserService.getOne(
                new LambdaQueryWrapper<GovUser>()
                        .eq(GovUser::getMobile, mobile).eq(GovUser::getUserStatus, GovUserStatusEnum.NORMAL.getCode()).last("limit 1"));
    }

    @Override
    public GovUserSessionInfoDTO getLoginUserFromCache(String mobile) {
        this.isCompanyExpired(mobile);
        String value = cache.getCacheObject(this.formatAccountInfoKey(mobile));
        GovUserSessionInfoDTO session = null;
        if (Objects.nonNull(value)) {
            session = JSON.parseObject(value, GovUserSessionInfoDTO.class);
        }
        if (Objects.isNull(session)) {
            session = new GovUserSessionInfoDTO();
            // 查询用户信息
            GovUser user = this.getActivateUserByMobile(mobile);
            // 基本信息
            session.setBaseInfo(this.buildAccountBaseInfo(user));

            // 公司信息
            GovCompany company = companyService.getById(user.getCompanyId());
            session.setCompanyInfo(this.buildCompanyInfo(company));

            // 用户角色
            List<String> roleCodes = userRoleRelationService.getUserRoleList(user.getUserCode());
            List<GovRole> roles = this.roleService.getRoleByCodeList(roleCodes)
                    .stream()
                    .filter(s -> Objects.equals(s.getRoleStatus(), RoleStatusEnum.VALID.getCode()))
                    .collect(Collectors.toList());
            session.setRoleList(roles.stream().map(this::buildAccountRole).collect(Collectors.toList()));

            // 菜单信息
            AccountMenuDTO menu = permissionService.getPermissionTreeByRoleCodes(roleCodes);
            // session.setMenuTree(menu.getMenuTree());
            session.setAppMenuTree(menu.getAppletMenuTree());
            session.setPcMenuTree(menu.getPcMenuTree());
            // 首汽支持菜单处理
            technicalSupportMenuHandle(session, user.getUserType());

            // 所有按钮权限
            session.setAppButtonPermCodes(menu.getAppletButtonPermCodes());
            session.setPcButtonPerCodes(menu.getPcButtonPerCodes());
            session.setPcContentPageCodes(menu.getPcContentPageCodes());

            // 权限信息
            AccountDataPerm dataPerm = this.buildDataPerm(user);
            session.setDataPerm(dataPerm);

            // 填充机构信息
            fillOrgInfo(user, session);
            // 填充行政区信息
            fillAdministrativeInfo(user, session);
            fillCenterPoint(session);

            session.setPcMenuUrlList(getPcMenuUrlList(menu.getPcMenuTree()));
            // 更新缓存
            this.cache.setCacheObject(this.formatAccountInfoKey(mobile),
                    JSON.toJSONString(session, SerializerFeature.DisableCircularReferenceDetect), 5, TimeUnit.MINUTES);
        }
        return session;
    }


    private void fillCenterPoint(GovUserSessionInfoDTO session){
        // 默认定位北京
        BigDecimal centerPointLongitude = new BigDecimal("116.3683244");
        BigDecimal centerPointLatitude = new BigDecimal("39.915085");
        session.setCenterPointLatitude(centerPointLatitude);
        session.setCenterPointLongitude(centerPointLongitude);
        if(StringUtils.isBlank(session.getAdministrativeRegionCode())){
            return;
        }

        if(Objects.equals(session.getAdministrativeLevel(), AdministrativeLevelEnum.PROVINCE.getCode())){
            GovProvinceDicDTO dic = govProvinceDicCommonService.getByAdCode(Integer.parseInt(session.getAdministrativeRegionCode().substring(0, 2)));
            if(Objects.isNull(dic)){
                return;
            }
            if(StringUtils.isNotBlank(dic.getCoordinatePointAmap())){
                String[] split = dic.getCoordinatePointAmap().split(",");
                if(split.length < 2){
                    return;
                }
                session.setCenterPointLongitude(new BigDecimal(split[0]));
                session.setCenterPointLatitude(new BigDecimal(split[1]));
            }

        }else if(Objects.equals(session.getAdministrativeLevel(), AdministrativeLevelEnum.CITY.getCode())){
            GovCityDicDTO dic = govCityDicCommonService.getByAdCode(Integer.parseInt(session.getAdministrativeRegionCode().substring(0, 4)));
            if(Objects.isNull(dic)){
                return;
            }
            if(StringUtils.isNotBlank(dic.getCoordinatePointAmap())){
                String[] split = dic.getCoordinatePointAmap().split(",");
                if(split.length < 2){
                    return;
                }
                session.setCenterPointLongitude(new BigDecimal(split[0]));
                session.setCenterPointLatitude(new BigDecimal(split[1]));
            }

        }else if(Objects.equals(session.getAdministrativeLevel(), AdministrativeLevelEnum.DISTRICT.getCode())){
            GovDistrictDicDTO dic = govDistrictDicCommonService.getByAdCode(Integer.parseInt(session.getAdministrativeRegionCode()));
            if(Objects.isNull(dic)){
                return;
            }
            if(StringUtils.isNotBlank(dic.getCoordinatePointAmap())){
                String[] split = dic.getCoordinatePointAmap().split(",");
                if(split.length < 2){
                    return;
                }
                session.setCenterPointLongitude(new BigDecimal(split[0]));
                session.setCenterPointLatitude(new BigDecimal(split[1]));
            }
        }


    }

    /**
     * 首汽支持菜单处理
     */
    private void technicalSupportMenuHandle(GovUserSessionInfoDTO sessionInfoDTO, Integer userType){
        List<AccountMenu> pcMenuTree = sessionInfoDTO.getPcMenuTree();
        if(!Objects.equals(GovUserTypeEnum.TECHNICAL_SUPPORT.getCode(), userType)){
            return;
        }
        if(CollectionUtils.isEmpty(pcMenuTree)){
            pcMenuTree = Lists.newArrayList();
            sessionInfoDTO.setPcMenuTree(pcMenuTree);
        }
        // 三级菜单
        AccountMenu technicalSupport = new AccountMenu();
        technicalSupport.setMenuUrl("govPrimaryData");
        technicalSupport.setPermCode("govPrimaryData");
        technicalSupport.setPermName("运营后台系统");
        technicalSupport.setSortSeq(9999);

        AccountMenu firstCarManager = new AccountMenu();
        firstCarManager.setMenuUrl("firstCarManager");
        firstCarManager.setPermCode("firstCarManager");
        firstCarManager.setPermName("首汽管理");
        firstCarManager.setSortSeq(1);
        technicalSupport.setSubmenuList(Lists.newArrayList(firstCarManager));

        AccountMenu menus = new AccountMenu();
        menus.setMenuUrl("menus");
        menus.setPermCode("menus");
        menus.setPermName("功能菜单");
        menus.setSortSeq(1);
        firstCarManager.setSubmenuList(Lists.newArrayList(menus));


        AccountMenu appletMenus = new AccountMenu();
        appletMenus.setMenuUrl("smallProgramMenus");
        appletMenus.setPermCode("smallProgramMenus");
        appletMenus.setPermName("小程序功能菜单");
        appletMenus.setSortSeq(2);
        firstCarManager.getSubmenuList().add(appletMenus);

        AccountMenu companyAccountList = new AccountMenu();
        companyAccountList.setMenuUrl("companyAccountList");
        companyAccountList.setPermCode("companyAccountList");
        companyAccountList.setPermName("客户列表");
        companyAccountList.setOriginalRoute("DepartMentUser");
        companyAccountList.setSortSeq(3);
        firstCarManager.getSubmenuList().add(companyAccountList);


        AccountMenu govCommandList = new AccountMenu();
        govCommandList.setMenuUrl("vehicleCommandList");
        govCommandList.setPermCode("vehicleCommandList");
        govCommandList.setPermName("车机指令列表");
        govCommandList.setOriginalRoute("vehicleCommandList");
        govCommandList.setSortSeq(4);
        firstCarManager.getSubmenuList().add(govCommandList);
        
        pcMenuTree.add(technicalSupport);
    }

    private void fillAdministrativeInfo(GovUser user, GovUserSessionInfoDTO session){
        session.setAdministrativeStructCode("");
        session.setAdministrativeRegionName("");
        session.setAdministrativeRegionCode("100000");
        // 供应商 或 首汽支持不展示 首页
        if(GovUserTypeEnum.isOrgUserType(user.getUserType())
                || Objects.equals(user.getDataPermType(), GovDataPermTypeEnum.SELF.getCode())
                || Objects.equals(user.getUserType(), GovUserTypeEnum.TECHNICAL_SUPPORT.getCode())){
            return;
        }
        if(Objects.equals(user.getDataPermType(), GovDataPermTypeEnum.COMPANY.getCode())){
            fillAdministrativeInfo4Company(user, session);
        }else if(Objects.equals(user.getDataPermType(), GovDataPermTypeEnum.ASSIGN_STRUCT.getCode())){
            fillAdministrativeInfo4AssignStruct(user, session);
        }
    }

    private void fillAdministrativeInfo4Company(GovUser user, GovUserSessionInfoDTO session){
        GovStruct govStruct = govStructServiceImpl.getOne(new LambdaQueryWrapper<GovStruct>()
                .eq(GovStruct::getCompanyId, user.getCompanyId())
                .eq(GovStruct::getStructLevel, 1));
        if(Objects.isNull(govStruct)){
            return;
        }
        doFillAdministrativeInfo(govStruct, session);
    }

    @Resource
    private GovDataRelationService govDataRelationService;

    private void fillAdministrativeInfo4AssignStruct(GovUser user, GovUserSessionInfoDTO session){
        List<GovDataRelation> dataRelationList = govDataRelationService.list(new LambdaQueryWrapper<GovDataRelation>()
                .eq(GovDataRelation::getCompanyId, user.getCompanyId())
                .eq(GovDataRelation::getBussCode, user.getUserCode()));
        if(CollectionUtils.isEmpty(dataRelationList)){
            return;
        }
        List<String> structCodeList = dataRelationList.stream().map(GovDataRelation::getRelatedCode).collect(Collectors.toList());
        List<GovStruct> govStructList = govStructServiceImpl.list(new LambdaQueryWrapper<GovStruct>()
                .eq(GovStruct::getCompanyId, user.getCompanyId())
                .in(GovStruct::getStructCode, structCodeList));
        if(CollectionUtils.isEmpty(govStructList)){
            return;
        }
        govStructList.sort((g1, g2) -> {
            int c = g1.getStructLevel() - g2.getStructLevel();
            if(c == 0){
                c = g2.getStatus() - g1.getStatus();
            }
            if(c == 0){
                c = g2.getIsArea() - g1.getIsArea();
            }
            if(c == 0){
                c = g1.getId() - g2.getId();
            }
            return c;
        });
        List<GovStruct> structList = govStructServiceImpl.list(
                new LambdaQueryWrapper<GovStruct>().eq(GovStruct::getCompanyId, user.getCompanyId()));
        Map<Integer, GovStruct> structRespondMap = structList.stream().collect(Collectors.toMap(GovStruct::getId, Function.identity()));
        // 取第一个
        GovStruct firstStruct = govStructList.get(0);
        if(StringUtils.isNotBlank(firstStruct.getProvinceCode())
        || StringUtils.isNotBlank(firstStruct.getCityCode())
        || StringUtils.isNotBlank(firstStruct.getRegionCode())){
            doFillAdministrativeInfo(firstStruct, session);
            return;
        }
//        if(firstStruct.getIsArea() == 1){
//            // 找到数据
//            doFillAdministrativeInfo(firstStruct, session);
//            return;
//        }
        GovStruct parentStruct = null;
        Integer parentStructId = firstStruct.getParentId();
        while(true){
            if(Objects.isNull(parentStructId)){
                break;
            }
            parentStruct = structRespondMap.get(parentStructId);
            if(Objects.isNull(parentStruct)){
                break;
            }
            if(StringUtils.isNotBlank(parentStruct.getProvinceCode())
                    || StringUtils.isNotBlank(parentStruct.getCityCode())
                    || StringUtils.isNotBlank(parentStruct.getRegionCode())){
                doFillAdministrativeInfo(parentStruct, session);
                return;
            }
//            if(parentStruct.getIsArea() == 1){
//                // 找到数据
//                doFillAdministrativeInfo(parentStruct, session);
//                break;
//            }
            parentStructId = parentStruct.getParentId();
        }
    }

    private void doFillAdministrativeInfo(GovStruct govStruct, GovUserSessionInfoDTO session){
        String administrativeRegionCode = "100000";
        // 跟前端约定如果是全国则父行政区编码传1
        String parentAdministrativeRegionCode = "1";
        String administrativeRegionName = "全国";
        Integer administrativeLevel = AdministrativeLevelEnum.NATIONAL.getCode();
        if(StringUtils.isNotBlank(StringUtils.trim(govStruct.getProvinceCode()))){
            administrativeRegionCode = StringUtils.rightPad(govStruct.getProvinceCode(), 6, "0");
            administrativeRegionName = govStruct.getProvinceName();
            administrativeLevel = AdministrativeLevelEnum.PROVINCE.getCode();
        }
        if(StringUtils.isNotBlank(StringUtils.trim(govStruct.getCityCode()))){
            parentAdministrativeRegionCode = administrativeRegionCode;
            administrativeRegionCode = StringUtils.rightPad(govStruct.getCityCode(), 6, "0");
            administrativeRegionName = govStruct.getCityName();
            administrativeLevel = AdministrativeLevelEnum.CITY.getCode();
        }
        if(StringUtils.isNotBlank(StringUtils.trim(govStruct.getRegionCode()))){
            parentAdministrativeRegionCode = administrativeRegionCode;
            administrativeRegionCode = StringUtils.rightPad(govStruct.getRegionCode(), 6, "0");
            administrativeRegionName = govStruct.getRegionName();
            administrativeLevel = AdministrativeLevelEnum.DISTRICT.getCode();
        }
        session.setAdministrativeRegionCode(administrativeRegionCode);
        session.setAdministrativeRegionName(administrativeRegionName);
        session.setAdministrativeLevel(administrativeLevel);
        session.setParentAdministrativeRegionCode(parentAdministrativeRegionCode);
        session.setAdministrativeStructCode(govStruct.getStructCode());

    }



    private List<String> getPcMenuUrlList(List<AccountMenu> pcMenuTree){
        if(CollectionUtils.isEmpty(pcMenuTree)){
            return Lists.newArrayList();
        }
        List<String> menuUrlList = new ArrayList<>(500);
        for (AccountMenu pcMenu : pcMenuTree) {
            Stack<AccountMenu> tempStack = new Stack<>();
            tempStack.push(pcMenu);
            while (!tempStack.isEmpty()) {
                final AccountMenu currMenu = tempStack.pop();
                if (CollectionUtils.isEmpty(currMenu.getSubmenuList())) {
                    if(!currMenu.getMenuUrl().startsWith("/")){
                        menuUrlList.add(getMenuOriginalRouteWithSuffix(currMenu) + currMenu.getMenuUrl());
                    }else{
                        menuUrlList.add(getMenuOriginalRouteWithPrefix(currMenu) + currMenu.getMenuUrl());
                    }
                }
                if (CollectionUtils.isNotEmpty(currMenu.getSubmenuList())) {
                    for (AccountMenu sub : currMenu.getSubmenuList()) {
                        // 记录父url
                        sub.setTmpMenuUrl(currMenu.getMenuUrl());
                        tempStack.push(sub);
                    }
                }
            }
        }
        return menuUrlList;
    }


    private String getMenuOriginalRouteWithSuffix(AccountMenu menu){
        if(StringUtils.isNotBlank(menu.getOriginalRoute()) && CollectionUtils.isEmpty(menu.getSubmenuList())){
            return "/" + menu.getOriginalRoute() + "/";
        }
        if(StringUtils.isNotBlank(menu.getTmpMenuUrl())){
            return "/" + menu.getTmpMenuUrl() + "/";
        }
        return "/";
    }
    private String getMenuOriginalRouteWithPrefix(AccountMenu menu){
        if(StringUtils.isNotBlank(menu.getOriginalRoute()) && CollectionUtils.isEmpty(menu.getSubmenuList())){
            return "/" + menu.getOriginalRoute();
        }
        if(StringUtils.isNotBlank(menu.getTmpMenuUrl())){
            return "/" + menu.getTmpMenuUrl();
        }
        return "";
    }

    private void fillOrgInfo(GovUser user, GovUserSessionInfoDTO session){
        // 机构员工
        if(GovUserTypeEnum.isOrgUserType(user.getUserType())){
            GovOrgInfo govOrgInfo = govOrgInfoService.getOne(new LambdaQueryWrapper<GovOrgInfo>()
                    .eq(GovOrgInfo::getOrgNo, user.getSupplierServiceCode())
//                    .eq(GovOrgInfo::getOrgStatus, OrgStatusEnum.ENABLE.getState())
                    .eq(GovOrgInfo::getCompanyId, user.getCompanyId()));
            if(Objects.nonNull(govOrgInfo)){
                if(Objects.equals(govOrgInfo.getOrgStatus(), OrgStatusEnum.DISABLE.getState())){
                    throw new ApiException(USER_SUPPLIER_LOCKED);
                }
                OrgInfo orgInfo = new OrgInfo();
                orgInfo.setOrgNo(govOrgInfo.getOrgNo());
                orgInfo.setOrgName(govOrgInfo.getOrgName());
                orgInfo.setOrgType(govOrgInfo.getOrgType());
                session.setOrgInfo(orgInfo);
            }
        }
    }

    @Override
    public boolean clearLoginUserCache(String mobile) {
        String key = this.formatAccountInfoKey(mobile);
        return this.cache.deleteObject(key);
    }


    private String formatAccountLockKey(String mobile) {
        return String.format(LOGIN_LOCKED_ACCOUNTS_KEY, mobile);
    }

    private String formatFailureCountKey(String mobile) {
        return String.format(LOGIN_FAILURE_COUNT_KEY, mobile);
    }

    private String formatAccountInfoKey(String mobile) {
        return String.format(LOGIN_USER_INFO_KEY, mobile);
    }

    private String formatVerifyCodeCountKey(String mobile) {
        return String.format(LOGIN_VERIFY_CODE_TIMES_KEY, mobile);
    }

    private String formatVerifyCodeKey(String mobile) {
        return String.format(LOGIN_VERIFY_CODE_KEY, mobile);
    }

    private String formatModifyPasswordVerifyCodeKey(String mobile) {
        return String.format(LOGIN_MODIFY_PASSWORD_VERIFY_CODE_KEY, mobile);
    }

    private AccountBaseInfo buildAccountBaseInfo(GovUser user) {
        AccountBaseInfo info = new AccountBaseInfo();
        info.setUserId(user.getUserId()); // 用户ID
        info.setUserCode(user.getUserCode()); // 用户编码
        info.setUserName(user.getUserName()); // 用户姓名
        info.setUserType(user.getUserType()); // 员工类型
        info.setUserStatus(user.getUserStatus()); // 人员状态
        info.setPinyinName(user.getPinyinName()); // 拼音名
        info.setMobile(user.getMobile()); // 用户手机号
        info.setPosition(user.getUserPosition()); // 员工岗位名称
        info.setGender(user.getGender()); // 用户性别
        info.setHeadIcon(user.getHeadIcon()); // 头像
        info.setEmail(user.getEmail()); // 用户邮箱
        info.setCreateCode(user.getCreateCode());
        info.setCreateName(user.getCreateName());
        info.setUpdateCode(user.getUpdateCode());
        info.setUpdateName(user.getUpdateName());
        info.setBelongDeptCode(user.getBelongDeptCode());
        info.setDataPermType(user.getDataPermType());
        if (Objects.nonNull(user.getBelongDeptCode())) {
            GovStruct struct = structService.getOne(
                    new LambdaQueryWrapper<GovStruct>()
                            .eq(GovStruct::getStructCode, user.getBelongDeptCode()));
            if (Objects.nonNull(struct)) {
                info.setBelongDeptName(struct.getStructName());
                info.setBelongDeptId(struct.getId());
            }
        }
        info.setBelongStructCode(user.getBelongStructCode());
        if (Objects.nonNull(user.getBelongStructCode())) {
            GovStruct struct = structService.getOne(
                    new LambdaQueryWrapper<GovStruct>()
                            .eq(GovStruct::getStructCode, user.getBelongStructCode()));
            if (Objects.nonNull(struct)) {
                info.setBelongStructName(struct.getStructName());
                info.setBelongStructId(struct.getId());
            }
        }
        return info;
    }

    private AccountRole buildAccountRole(GovRole role) {
        AccountRole result = new AccountRole();
        result.setRoleCode(role.getRoleCode());
        result.setRoleName(role.getRoleName());
        result.setRoleDesc(role.getRoleDesc());
        result.setRoleStatus(role.getRoleStatus());
        result.setRoleType(role.getRoleType());
        result.setRoleMark(role.getRoleMark());
        return result;
    }

    private CompanyInfo buildCompanyInfo(GovCompany company) {
        CompanyInfo result = new CompanyInfo();
        result.setCompanyId(company.getCompanyId());
        result.setCompanyName(company.getCompanyName());
        result.setCompanyStatus(company.getCompanyStatus());
        result.setExpireTime(company.getExpireTime());
        return result;
    }

    // 数据权限信息
    private AccountDataPerm buildDataPerm(GovUser user) {
        AccountDataPerm dataPerm = new AccountDataPerm();
        dataPerm.setDataPermType(user.getDataPermType());
        // 根据类型设置范围编码
        GovDataPermTypeEnum type = GovDataPermTypeEnum.getByCode(user.getDataPermType());
        switch (type) {
            case ASSIGN_STRUCT:
            {
                // 指定部门
                List<GovDataRelation> relations =
                        dataRelationService.list(
                                new LambdaQueryWrapper<GovDataRelation>()
                                        .eq(GovDataRelation::getBussCode, user.getUserCode())
                                        .eq(GovDataRelation::getRelationType, GovDataPermTypeEnum.ASSIGN_STRUCT.getCode()));
                // 部门编码
                Set<String> dataSetCode = relations.stream().map(GovDataRelation::getRelatedCode).collect(Collectors.toSet());
                dataPerm.setDataSetCode(dataSetCode);
                break;
            }
            case COMPANY:
            {
                // 所属公司
                dataPerm.setDataSetCode(Collections.singleton(String.valueOf(user.getCompanyId())));
                break;
            }
            case SELF:
            {
                // 个人
                dataPerm.setDataSetCode(Collections.singleton(String.valueOf(user.getUserCode())));
                break;
            }
            default:
            {
                dataPerm.setDataSetCode(Collections.emptySet());
                break;
            }
        }
        return dataPerm;
    }

    /**
     * 是否超过每天限额
     */
    private boolean checkVerifyCodeExceedDaily(String mobile) {
        Integer count = this.cache.getCacheObject(this.formatVerifyCodeCountKey(mobile));
        return count != null && count > VERIFY_CODE_MAX_SEND_TIMES_PER_DAY;
    }

    /**
     * 是否超过发送频率
     */
    private boolean checkVerifyCodeExceedFrequency(String mobile) {
        String key = this.formatVerifyCodeKey(mobile);
        // 验证码剩余时间
        long ttl = this.cache.hasKey(key) ? this.cache.getExpire(key) : 0;
        // 频率限制
        int limit = VERIFY_CODE_VALID_TIME - VERIFY_CODE_MAX_SEND_INTERVAL;
        return ttl != 0 && ttl >= limit;
    }

    /**
     * 生成验证码
     */
    private String generateVerifyCode() {
        return this.verifyCodeSwitch ?
                RandomStringUtils.randomNumeric(VERIFY_CODE_DIGITS) : VERIFY_CODE_DEFAULT_CONTENT;
    }

    /**
     * 发送短信
     */
    private void doSendSms(String mobile, String content) {
        SmsSendResult result = this.sender.send(mobile, DEFAULT_TEMPLATE_CODE,
                String.format(DEFAULT_TEMPLATE_PARAMS, content));
        // 保存发送结果
        GovPushSms entity = new GovPushSms();
        entity.setPushType(PushSmsTypeEnum.LOGIN.getCode());
        entity.setReceiveMobile(mobile);
        entity.setSendContent(content);
        entity.setSendTime(formatter.format(LocalDateTime.now()));
        entity.setSendStatus(result.isSuccess() ? 1 : 4);
        entity.setResultContent(result.getMessage());
        pushSmsService.save(entity);
    }

    /**
     * 判断公司是否到期
     */
    private void isCompanyExpired(String mobile) {
        GovUser user = this.getActivateUserByMobile(mobile);
        CheckConditionUtil.check(Objects.nonNull(user),RestErrorCode.LOGIN_PHONE_UNAVAILABLE, RestErrorCode.LOGIN_PHONE_UNAVAILABLE.getMsg());
        // 公司信息
        GovCompany company = companyService.getById(user.getCompanyId());
        //判断公司是否到期
        if(Objects.isNull(company)||Objects.isNull(company.getExpireTime())||new Date().after(company.getExpireTime()) || Objects.equals(company.getCompanyStatus(), GovCompanyStatusEnum.STOP.getCode())){
            //清除缓存
            clearLoginUserCache(mobile);
            throw new ApiException(RestErrorCode.ACCOUNT_EXPIRED);
        }
        // 机构员工
        if(GovUserTypeEnum.isOrgUserType(user.getUserType())){
            GovOrgInfo govOrgInfo = govOrgInfoService.getOne(new LambdaQueryWrapper<GovOrgInfo>()
                    .eq(GovOrgInfo::getOrgNo, user.getSupplierServiceCode())
                    .eq(GovOrgInfo::getCompanyId, user.getCompanyId()));
            if(Objects.nonNull(govOrgInfo) && Objects.equals(govOrgInfo.getOrgStatus(), OrgStatusEnum.DISABLE.getState())){
                    //清除缓存
                    clearLoginUserCache(mobile);
                    throw new ApiException(USER_SUPPLIER_LOCKED);
            }
        }
    }


}
