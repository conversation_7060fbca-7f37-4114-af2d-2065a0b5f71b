package com.mrcar.gov.user.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 服务机构信息
 * @TableName gov_org_info
 */
@TableName(value ="gov_org_info")
@Data
public class GovOrgInfo implements Serializable {
    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 创建人编码
     */
    private String createCode;

    /**
     * 创建人名称
     */
    private String createName;

    /**
     * 创建人单位code
     */
    private String createDeptCode;

    /**
     * 创建人单位名称
     */
    private String createDeptName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人编码
     */
    private String updateCode;

    /**
     * 更新人名称
     */
    private String updateName;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 所属企业名称
     */
    private String companyName;

    /**
     * 企业id
     */
    private Integer companyId;

    /**
     * 服务机构编号
     */
    private String orgNo;

    /**
     * 服务机构名称
     */
    private String orgName;

    /**
     * logo地址
     */
    private String logoUrl;

    /**
     * 服务机构状态（1启用 2禁用）
     */
    private Integer orgStatus;

    /**
     * 服务机构类型（1维保 2保险 3加油 4租赁）
     */
    private Integer orgType;

    /**
     * 所在省份编码
     */
    private String orgProvinceCode;

    /**
     * 所在省份名称
     */
    private String orgProvinceName;

    /**
     * 所在城市code
     */
    private String orgCityCode;

    /**
     * 所在城市name
     */
    private String orgCityName;

    /**
     * 详细地址 手动输入
     */
    private String orgAddressDetail;

    /**
     * 坐标经度
     */
    private BigDecimal longitude;

    /**
     * 坐标纬度
     */
    private BigDecimal latitude;

    /**
     * 地址名称 地图
     */
    private String addressName;

    /**
     * 服务机构负责人（联系人）姓名
     */
    private String orgContactorName;

    /**
     * 服务机构负责人（联系人）手机号
     */
    private String orgContactorPhone;

    /**
     * 服务机构负责人（联系人）编码
     */
    private String orgContactorCode;

    /**
     * 合同开始日期
     */
    private Date contractBeginDate;

    /**
     * 合同结束日期
     */
    private Date contractEndDate;

    /**
     * 发票类型（0:无发票 1:增值税专用发票 2:增值税普通发票）
     */
    private Integer invoiceType;

    /**
     * 发票开具类型（0:纸质发票 1:电子发票）
     */
    private Integer invoiceOpenType;

    /**
     * 发票开具税率
     */
    private BigDecimal invoiceRate;

    /**
     * 统一社会信用代码
     */
    private String creditCode;

    /**
     * 开户银行
     */
    private String openBank;

    /**
     * 开户银行账号
     */
    private String bankAccount;

    /**
     * 服务电话（多个英文逗号分隔）
     */
    private String serviceTelephone;

    /**
     * 中标区域（多个英文逗号分隔）
     */
    private String awardedAreaCode;

    /**
     * 中标区域（多个英文逗号分隔）
     */
    private String awardedAreaName;

    /**
     * 综合评分
     */
    private BigDecimal evaluationScore;

    /**
     * 评价总次数
     */
    private Integer evaluationNums;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}