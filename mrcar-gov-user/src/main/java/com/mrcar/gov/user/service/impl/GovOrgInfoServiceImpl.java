package com.mrcar.gov.user.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.izu.framework.exception.ApiException;
import com.izu.framework.resp.InfoCode;
import com.izu.framework.response.PageDTO;
import com.izu.framework.web.util.BeanUtil;
import com.mrcar.gov.base.service.SequenceGenerator;
import com.mrcar.gov.common.InvoiceTypeEnum;
import com.mrcar.gov.common.constant.user.ConstructionInvoiceRate;
import com.mrcar.gov.common.constant.user.GovDataPermTypeEnum;
import com.mrcar.gov.common.constant.user.GovStructStatusEnum;
import com.mrcar.gov.common.constant.user.GovUserAddSourceEnum;
import com.mrcar.gov.common.constant.user.GovUserStatusEnum;
import com.mrcar.gov.common.constant.user.GovUserTypeEnum;
import com.mrcar.gov.common.dto.DictionaryEnumDTO;
import com.mrcar.gov.common.dto.user.req.*;
import com.mrcar.gov.common.dto.user.resp.*;
import com.mrcar.gov.user.common.*;
import com.mrcar.gov.user.domain.*;
import com.mrcar.gov.user.mapper.GovOrgRelationMapper;
import com.mrcar.gov.user.mapper.GovUserMapper;
import com.mrcar.gov.user.service.*;
import com.mrcar.gov.user.mapper.GovOrgInfoMapper;
import com.mrcar.gov.user.utils.DateUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.mrcar.gov.common.constant.user.OrgTypeEnum;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【gov_org_info(服务机构信息)】的数据库操作Service实现
 * @createDate 2024-11-09 15:06:30
 */
@Service
public class GovOrgInfoServiceImpl extends ServiceImpl<GovOrgInfoMapper, GovOrgInfo>
        implements GovOrgInfoService {

    @Resource
    private GovUserService userService;

    @Resource
    private GovUserMapper userMapper;

    @Resource
    private GovOrgInfoFileService fileService;

    @Resource
    private GovMaintainGarageExtendService extendService;

    @Resource
    private GovOrgInfoMapper orgInfoMapper;

    @Resource
    private SequenceGenerator sequenceGenerator;

    @Resource
    private GovStructService govStructService;

    @Resource
    private GovOrgRelationService govOrgRelationService;

    @Resource
    private GovOrgRelationMapper govOrgRelationMapper;
    @Resource
    private GovOrgInfoMapper govOrgInfoMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrgInfo(GovOrgInfoEditDTO req) {
        //校验参数
        Date contractEndDate = req.getContractEndDate();
        if (DateUtil.compareDate(new Date(), DateUtil.getEndOfDay(contractEndDate)) > 0 && Objects.equals(req.getOrgStatus(), GovStructStatusEnum.NORMAL.getCode())) {
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "服务机构合作已过期,无法启用");

        }
        Long count = this.lambdaQuery().eq(GovOrgInfo::getOrgType, req.getOrgType()).eq(GovOrgInfo::getOrgName, req.getOrgName()).ne(Objects.nonNull(req.getId()), GovOrgInfo::getId, req.getId()).count();
        if (count > 0L) {
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "服务机构名称与已有其他服务机构重复，请勿重复创建");
        }
        GovOrgInfo orgInfo = null;
        if (StringUtils.isNotEmpty(req.getOrgNo())) {
            orgInfo = this.lambdaQuery().eq(GovOrgInfo::getOrgNo, req.getOrgNo()).one();
            if (Objects.isNull(orgInfo)) {
                throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "服务机构不存在");
            }
        }

        List<GovUser> govUserList = Lists.newArrayList();
        // 新增操作
        if (Objects.isNull(req.getId())) {
            govUserList = userService.lambdaQuery().eq(GovUser::getMobile, req.getOrgContactorPhone()).eq(GovUser::getUserStatus, GovUserStatusEnum.NORMAL.getCode()).list();
            if (StringUtils.isEmpty(req.getOrgNo())) {
                if (CollectionUtil.isNotEmpty(govUserList)) {
                    throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "负责人手机号已存在,无法创建");
                }
            } else {
                //负责人手机号存在修改且之前已存在
                if (CollectionUtil.isNotEmpty(govUserList) && !Objects.equals(orgInfo.getOrgContactorPhone(), req.getOrgContactorPhone())) {
                    throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "负责人手机号已存在,无法创建");
                }
            }
        } else {// 编辑操作
            govUserList = userService.lambdaQuery().eq(GovUser::getMobile, req.getOrgContactorPhone()).eq(GovUser::getUserStatus, GovUserStatusEnum.NORMAL.getCode()).list();

            checkContactorEditInfo(orgInfo, govUserList);
        }


        //组装基础信息
        GovOrgInfo govOrgInfo = buildOrgInfo(req);
        //组装拓展信息
        GovMaintainGarageExtend extend = buildExtend(req);
        if (StringUtils.isEmpty(req.getOrgNo())) {
            String prefix = getPrefixByOrgType(req.getOrgType());
            govOrgInfo.setOrgNo(sequenceGenerator.generate(new Date(), prefix));
            govOrgInfo.setCreateCode(req.getLoginUserCode());
            govOrgInfo.setCreateName(req.getLoginUserName());
            govOrgInfo.setCreateTime(new Date());
            this.save(govOrgInfo);
            //维保拓展信息
            if (Objects.equals(req.getOrgType(), OrgTypeEnum.WB.getCode())) {
                extend.setCreateCode(req.getLoginUserCode());
                extend.setCreateName(req.getLoginUserName());
                extend.setCreateTime(new Date());
                extend.setOrgNo(govOrgInfo.getOrgNo());
                extendService.save(extend);
            }
        } else {
            govOrgInfo.setId(orgInfo.getId());
            LambdaUpdateWrapper<GovOrgInfo> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(GovOrgInfo::getId, orgInfo.getId());
            updateWrapper.set(GovOrgInfo::getUpdateCode, req.getLoginUserCode());
            updateWrapper.set(GovOrgInfo::getUpdateName, req.getLoginUserName());
            updateWrapper.set(GovOrgInfo::getUpdateTime, new Date());
            updateWrapper.set(GovOrgInfo::getOrgName, req.getOrgName());
            updateWrapper.set(GovOrgInfo::getOrgStatus, req.getOrgStatus());
            updateWrapper.set(GovOrgInfo::getOrgProvinceCode, req.getOrgProvinceCode());
            updateWrapper.set(GovOrgInfo::getOrgProvinceName, req.getOrgProvinceName());
            updateWrapper.set(GovOrgInfo::getOrgCityCode, req.getOrgCityCode());
            updateWrapper.set(GovOrgInfo::getOrgCityName, req.getOrgCityName());
            updateWrapper.set(GovOrgInfo::getOrgAddressDetail, req.getOrgAddressDetail());
            updateWrapper.set(GovOrgInfo::getOrgContactorName, req.getOrgContactorName());
            updateWrapper.set(GovOrgInfo::getOrgContactorPhone, req.getOrgContactorPhone());
            if (CollectionUtils.isNotEmpty(govUserList)) {
                updateWrapper.set(GovOrgInfo::getOrgContactorCode, govUserList.get(0).getUserCode());
            }
            updateWrapper.set(GovOrgInfo::getContractBeginDate, req.getContractBeginDate());
            updateWrapper.set(GovOrgInfo::getContractEndDate, req.getContractEndDate());
            updateWrapper.set(GovOrgInfo::getInvoiceType, req.getInvoiceType());
            updateWrapper.set(GovOrgInfo::getInvoiceOpenType, req.getInvoiceOpenType());
            if (Objects.equals(req.getInvoiceType(), InvoiceTypeEnum.SPECIAL_USE.getType())) {
                updateWrapper.set(GovOrgInfo::getInvoiceRate, req.getInvoiceRate());
            } else {
                updateWrapper.set(GovOrgInfo::getInvoiceRate, BigDecimal.ZERO);
            }
            updateWrapper.set(GovOrgInfo::getOpenBank, req.getOpenBank());
            updateWrapper.set(GovOrgInfo::getBankAccount, req.getBankAccount());
            updateWrapper.set(GovOrgInfo::getCreditCode, req.getCreditCode());
            updateWrapper.set(GovOrgInfo::getServiceTelephone, req.getServiceTelephone());
            updateWrapper.set(GovOrgInfo::getLatitude, req.getLatitude());
            updateWrapper.set(GovOrgInfo::getLongitude, req.getLongitude());
            updateWrapper.set(GovOrgInfo::getAddressName, req.getAddressName());
            updateWrapper.set(GovOrgInfo::getAwardedAreaCode, req.getAwardedAreaCode());
            //中标区域
            if (StringUtils.isNotEmpty(req.getAwardedAreaCode())) {
                List<String> awardedAreaCode = Arrays.asList(req.getAwardedAreaCode().split(","));
                List<GovStruct> list = govStructService.lambdaQuery().in(GovStruct::getStructCode, awardedAreaCode).list();
                if (CollectionUtil.isNotEmpty(list)) {
                    String awardedAreaName = list.stream().map(GovStruct::getStructName).collect(Collectors.joining(","));
                    updateWrapper.set(GovOrgInfo::getAwardedAreaName, awardedAreaName);
                }
            }
            this.baseMapper.update(updateWrapper);
            //维保拓展信息
            if (Objects.equals(req.getOrgType(), OrgTypeEnum.WB.getCode())) {
                GovMaintainGarageExtend govOrgInfoExtend = extendService.lambdaQuery().eq(GovMaintainGarageExtend::getOrgNo, req.getOrgNo()).one();
                extend.setId(govOrgInfoExtend.getId());
                extendService.updateById(extend);
            }
        }

        if (Objects.equals(req.getOrgType(), OrgTypeEnum.WB.getCode())) {
            req.setOrgNo(govOrgInfo.getOrgNo());
            //查询所有图片
            List<Integer> fileTypes = Lists.newArrayList(OrgFileTypeEnum.ORG_HEAD_PHOTO.getType(), OrgFileTypeEnum.ORG_LICENSE_PHOTO.getType(),
                    OrgFileTypeEnum.ORG_WORK_PHOTO.getType(), OrgFileTypeEnum.ORG_REST_PHOTO.getType());
            List<GovOrgInfoFile> orgFileList = fileService.lambdaQuery().eq(GovOrgInfoFile::getOrgNo, req.getOrgNo()).in(GovOrgInfoFile::getFileType, fileTypes).list();
            if (CollectionUtil.isNotEmpty(orgFileList)) {
                List<Integer> collect = orgFileList.stream().map(GovOrgInfoFile::getId).collect(Collectors.toList());
                fileService.removeBatchByIds(collect);
            }
            List<GovOrgInfoFile> fileInfos = this.buildPhoto(req);
            if (CollectionUtil.isNotEmpty(fileInfos)) {
                fileService.saveBatch(fileInfos);
            }
        }
        //新建用户
        if (CollectionUtil.isEmpty(govUserList)) {
            GovUserAddReqDTO userAddReqDTO = buildUserInfo(req.getOrgContactorPhone(), req.getCompanyId(), req.getOrgContactorName(), req.getOrgType());
            userAddReqDTO.setLoginUserCode(req.getLoginUserCode());
            userAddReqDTO.setLoginUserName(req.getLoginUserName());
            userAddReqDTO.setRoleCodeList(Collections.singletonList(getRoleCodeByOrgType(req.getOrgType())));
            userAddReqDTO.setAddSource(GovUserAddSourceEnum.ORG_ADD);
            userAddReqDTO.setSupplierServiceCode(govOrgInfo.getOrgNo());
            String userCode = userService.addUser(userAddReqDTO);
            // 更新负责人编码
            update(new LambdaUpdateWrapper<GovOrgInfo>().eq(GovOrgInfo::getOrgNo, govOrgInfo.getOrgNo()).set(GovOrgInfo::getOrgContactorCode, userCode));
        }

        //新增且选择了中标区域同步服务机构关系
        if (Objects.isNull(req.getId())) {
            if (StringUtils.isNotEmpty(req.getAwardedAreaCode())) {
                GovOrgRelationSaveDTO govOrgRelationSaveDTO = new GovOrgRelationSaveDTO();
                govOrgRelationSaveDTO.setCompanyId(req.getCompanyId());
                govOrgRelationSaveDTO.setCompanyName(req.getCompanyName());
                govOrgRelationSaveDTO.setLoginUserCode(req.getLoginUserCode());
                govOrgRelationSaveDTO.setLoginUserName(req.getLoginUserName());
                govOrgRelationSaveDTO.setCreateDeptForDb(req.getCreateDeptName());
                govOrgRelationSaveDTO.setCreateDeptCodeForDb(req.getCreateDeptCode());
                govOrgRelationSaveDTO.setOrgNoList(Collections.singletonList(govOrgInfo.getOrgNo()));
                govOrgRelationSaveDTO.setSelectType(0);
                //传父节点
                govOrgRelationSaveDTO.setDeptCodeList(Arrays.asList(req.getAwardedAreaCode().split(",")));
                govOrgRelationService.saveOrgRelation(govOrgRelationSaveDTO);
            }
        } else {
            //更新下关系表的数据
            LambdaUpdateWrapper<GovOrgRelation> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(GovOrgRelation::getOrgNo, govOrgInfo.getOrgNo());
            updateWrapper.set(GovOrgRelation::getOrgName, req.getOrgName());
            updateWrapper.set(GovOrgRelation::getOrgStatus, req.getOrgStatus());
            govOrgRelationMapper.update(updateWrapper);
            if (CollectionUtils.isEmpty(govUserList)) {
                return;
            }
            GovUser user = govUserList.get(0);
            //负责人手机号不更新，名称更新时 同步用户表
            if (Objects.equals(req.getOrgContactorPhone(), user.getMobile())
                    && !Objects.equals(user.getUserName(), req.getOrgContactorName())) {
//                GovUser user = userService.lambdaQuery().eq(GovUser::getMobile, orgInfo.getOrgContactorPhone()).eq(GovUser::getUserStatus, 1).orderByDesc(GovUser::getUserId).last("limit 1").one();
                LambdaUpdateWrapper<GovUser> govUserUpdateWrapper = new LambdaUpdateWrapper<>();
                govUserUpdateWrapper.eq(GovUser::getUserId, user.getUserId());
                govUserUpdateWrapper.set(GovUser::getUserName, req.getOrgContactorName());
                govUserUpdateWrapper.set(GovUser::getSupplierServiceCode, govOrgInfo.getOrgNo());
                userMapper.update(govUserUpdateWrapper);
            }
            //负责人变更了
            if (!Objects.equals(req.getOrgContactorPhone(), user.getMobile())) {
                LambdaUpdateWrapper<GovOrgInfo> updateContactor = new LambdaUpdateWrapper<>();
                updateContactor.eq(GovOrgInfo::getOrgNo, govOrgInfo.getOrgNo());
                updateContactor.set(GovOrgInfo::getOrgContactorCode, user.getUserCode());
                updateContactor.set(GovOrgInfo::getOrgContactorPhone, user.getMobile());
                updateContactor.set(GovOrgInfo::getOrgContactorName, user.getUserName());
                this.baseMapper.update(updateContactor);
            }
        }
    }

    private void checkContactorEditInfo(GovOrgInfo orgInfo, List<GovUser> govUserList) {
        if (Objects.isNull(orgInfo) || CollectionUtils.isEmpty(govUserList)) {
            return;
        }
        GovUser govUser = govUserList.get(0);
        // 如果用户停用，从新给用户生成一条数据
//        if(Objects.equals(govUser.getUserStatus(), GovUserStatusEnum.STOP.getCode())){
//            govUserList = Lists.newArrayList();
//            return;
//        }
        if (Objects.equals(orgInfo.getOrgContactorCode(), govUser.getUserCode())) {
            return;
        }
        LambdaQueryWrapper<GovOrgInfo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(GovOrgInfo::getCompanyId, orgInfo.getCompanyId());
        queryWrapper.ne(GovOrgInfo::getOrgNo, orgInfo.getOrgNo());
        queryWrapper.eq(GovOrgInfo::getOrgContactorCode, govUserList.get(0).getUserCode());
        queryWrapper.eq(GovOrgInfo::getOrgStatus, OrgStatusEnum.ENABLE.getState());
        long existRst = count(queryWrapper);
        if (existRst > 0) {
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "负责人已负责其它机构,请从新编辑负责人信息");
        }
        if (!Objects.equals(govUserList.get(0).getUserType(), getUserTypeByOrgType(orgInfo.getOrgType()))) {
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "负责人非当前机构人员,请从新编辑负责人信息");
        }
        if (!Objects.equals(govUserList.get(0).getSupplierServiceCode(), orgInfo.getOrgNo())) {
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "负责人非当前机构人员,请从新编辑负责人信息");
        }
    }

    @Override
    public void openOrClose(GovOrgInfoOpenOrCloseDTO req) {
        GovOrgInfo govOrgInfo = this.lambdaQuery().eq(GovOrgInfo::getId, req.getId()).one();
        if (Objects.isNull(govOrgInfo)) {
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "服务机构不存在");
        }
        if (Objects.equals(govOrgInfo.getOrgStatus(), OrgStatusEnum.DISABLE.getState())
                && Objects.equals(req.getOrgStatus(), OrgStatusEnum.ENABLE.getState())) {
            LambdaQueryWrapper<GovOrgInfo> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.eq(GovOrgInfo::getCompanyId, govOrgInfo.getCompanyId());
            queryWrapper.ne(GovOrgInfo::getOrgNo, govOrgInfo.getOrgNo());
            queryWrapper.eq(GovOrgInfo::getOrgContactorCode, govOrgInfo.getOrgContactorCode());
            queryWrapper.eq(GovOrgInfo::getOrgStatus, OrgStatusEnum.ENABLE.getState());
            long existRst = count(queryWrapper);
            if (existRst > 0) {
                throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "负责人已负责其它机构,请从新编辑负责人信息后再操作启用");
            }
        }
        govOrgInfo.setOrgStatus(req.getOrgStatus());
        this.updateById(govOrgInfo);
    }


    private String getPrefixByOrgType(Integer orgType) {
        if (Objects.equals(orgType, OrgTypeEnum.WB.getCode())) {
            return SeqPrefix.GOV_REP_CODE;
        }
        if (Objects.equals(orgType, OrgTypeEnum.BX.getCode())) {
            return SeqPrefix.GOV_BX_CODE;
        }
        if (Objects.equals(orgType, OrgTypeEnum.JY.getCode())) {
            return SeqPrefix.GOV_JY_CODE;
        }
        if (Objects.equals(orgType, OrgTypeEnum.ZL.getCode())) {
            return SeqPrefix.GOV_ZL_CODE;
        }
        return SeqPrefix.GOV_REP_CODE;
    }


    private Integer getUserTypeByOrgType(Integer orgType) {
        if (Objects.equals(orgType, OrgTypeEnum.WB.getCode())) {
            return GovUserTypeEnum.REPAIR_STATION_EMPLOYEE.getCode();
        }
        if (Objects.equals(orgType, OrgTypeEnum.BX.getCode())) {
            return GovUserTypeEnum.INSURANCE_COMPANY_EMPLOYEE.getCode();
        }
        if (Objects.equals(orgType, OrgTypeEnum.JY.getCode())) {
            return GovUserTypeEnum.OIL_COMPANY_EMPLOYEE.getCode();
        }
        if (Objects.equals(orgType, OrgTypeEnum.ZL.getCode())) {
            return GovUserTypeEnum.RENT_COMPANY_EMPLOYEE.getCode();
        }
        return GovUserTypeEnum.REPAIR_STATION_EMPLOYEE.getCode();
    }


    private String getRoleCodeByOrgType(Integer orgType) {
        if (Objects.equals(orgType, OrgTypeEnum.WB.getCode())) {
            return SpecialRoleEnum.MAINTENANCE_STAFF.getRoleCode();
        }
        if (Objects.equals(orgType, OrgTypeEnum.BX.getCode())) {
            return SpecialRoleEnum.INSURANCE_STAFF.getRoleCode();
        }
        if (Objects.equals(orgType, OrgTypeEnum.JY.getCode())) {
            return SpecialRoleEnum.OIL_STAFF.getRoleCode();
        }
        if (Objects.equals(orgType, OrgTypeEnum.ZL.getCode())) {
            return SpecialRoleEnum.RENT_DRIVER.getRoleCode();
        }
        return SpecialRoleEnum.MAINTENANCE_STAFF.getRoleCode();
    }


    @Override
    public PageDTO<GovOrgInfoListRespDTO> getOrgInfoList(GovOrgInfoListReq req) {
        Integer pageNum = req.getPage();
        Integer pageSize = req.getPageSize();
        if (Objects.equals(req.getAccidentType(), AccidentTypeEnum.MAINTENANCE.getType())) {
            req.setSupportRepair(OrgSupportEnum.SUPPORT.getState());
        }
        if (Objects.equals(req.getAccidentType(), AccidentTypeEnum.ACCIDENT.getType())) {
            req.setSupportAccidentRepair(OrgSupportEnum.SUPPORT.getState());
        }
        if (Objects.equals(req.getAccidentType(), AccidentTypeEnum.ALL.getType())) {
            req.setSupportRepair(OrgSupportEnum.SUPPORT.getState());
            req.setSupportAccidentRepair(OrgSupportEnum.SUPPORT.getState());
        }
        Page<GovOrgInfo> page = new Page<>(pageNum, pageSize);
        IPage<GovOrgInfoListRespDTO> govOrgInfoIPage = orgInfoMapper.getListByConditionForPC(page, req);

        if (CollectionUtil.isEmpty(govOrgInfoIPage.getRecords())) {
            return new PageDTO<>(pageNum, pageSize, 0, Lists.newArrayList());
        }

        govOrgInfoIPage.getRecords().forEach(record -> {
            record.setSupportAccidentRepairStr(OrgSupportEnum.getEnum(record.getSupportAccidentRepair()).getName());
            record.setSupportRepairStr(OrgSupportEnum.getEnum(record.getSupportRepair()).getName());
            record.setOrgStatusStr(OrgStatusEnum.getEnum(record.getOrgStatus()).getName());
            record.setOrgTypeStr(OrgTypeEnum.getEnum(record.getOrgType()).getName());
        });
        fillContactorInfo(govOrgInfoIPage.getRecords(), req.getLoginCompanyId());
        return new PageDTO<>(pageNum, pageSize, Integer.parseInt(page.getTotal() + ""), govOrgInfoIPage.getRecords());
    }

    private void fillContactorInfo(List<GovOrgInfoListRespDTO> records, Integer companyId) {
        if (CollectionUtils.isEmpty(records)) {
            return;
        }
        List<String> orgContactorCodeList = records.stream().map(GovOrgInfoListRespDTO::getOrgContactorCode).distinct().collect(Collectors.toList());
        List<GovUser> govUserList = userMapper.selectList(new LambdaQueryWrapper<GovUser>().in(GovUser::getUserCode, orgContactorCodeList).eq(GovUser::getCompanyId, companyId));
        Map<String, GovUser> userMap = govUserList.stream().collect(Collectors.toMap(GovUser::getUserCode, Function.identity(), (k1, k2) -> k1));
        records.forEach(record -> {
            GovUser user = userMap.get(record.getOrgContactorCode());
            if (Objects.nonNull(user)) {
                record.setOrgContactorName(user.getUserName());
                record.setOrgContactorPhone(user.getMobile());
            }
        });
    }

    @Override
    public GovOrgInfoDetailDTO getOrgInfoDetail(String orgNo) {
        GovOrgInfo govOrgInfo = this.lambdaQuery().eq(GovOrgInfo::getOrgNo, orgNo).one();
        if (Objects.isNull(govOrgInfo)) {
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "服务机构不存在");
        }
        GovOrgInfoDetailDTO govOrgInfoDetailDTO = new GovOrgInfoDetailDTO();
        BeanUtils.copyProperties(govOrgInfo, govOrgInfoDetailDTO);
        if (Objects.nonNull(govOrgInfo.getInvoiceRate())) {
            govOrgInfoDetailDTO.setInvoiceRateStr(govOrgInfo.getInvoiceRate().setScale(6, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)).stripTrailingZeros().toPlainString() + "%");
        }
        OrgStatusEnum orgStatusEnum = OrgStatusEnum.getEnum(govOrgInfoDetailDTO.getOrgStatus());
        govOrgInfoDetailDTO.setOrgStatusName(Objects.isNull(orgStatusEnum) ? "" : orgStatusEnum.getName());
        InvoiceOpenTypeEnum invoiceOpenTypeEnum = InvoiceOpenTypeEnum.getEnum(govOrgInfoDetailDTO.getInvoiceOpenType());
        govOrgInfoDetailDTO.setInvoiceOpenTypeName(invoiceOpenTypeEnum.getName());
        InvoiceTypeEnum invoiceTypeEnum = InvoiceTypeEnum.getContructionInvoiceName(govOrgInfoDetailDTO.getInvoiceType());
        govOrgInfoDetailDTO.setInvoiceTypeName(invoiceTypeEnum.getMessage());

        if (!Objects.equals(govOrgInfo.getOrgType(), OrgTypeEnum.WB.getCode())) {
            return govOrgInfoDetailDTO;
        }

        GovMaintainGarageExtend extend = extendService.lambdaQuery().eq(GovMaintainGarageExtend::getOrgNo, orgNo).one();
        BeanUtils.copyProperties(extend, govOrgInfoDetailDTO);
        govOrgInfoDetailDTO.setId(govOrgInfo.getId());

        //填充维修场相关文件
        List<GovOrgInfoFile> fileList = fileService.lambdaQuery().eq(GovOrgInfoFile::getOrgNo, orgNo).list();
        if (CollectionUtil.isNotEmpty(fileList)) {
            List<GovOrgInfoFile> workCollect = fileList.stream().filter(e -> Objects.equals(e.getFileType(), OrgFileTypeEnum.ORG_WORK_PHOTO.getType())).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(workCollect)) {
                govOrgInfoDetailDTO.setWorkAreaPhoto(BeanUtil.copyList(workCollect, OrgFileInfoDTO.class));
            } else {
                govOrgInfoDetailDTO.setWorkAreaPhoto(new ArrayList<>());
            }
            List<GovOrgInfoFile> headCollect = fileList.stream().filter(e -> Objects.equals(e.getFileType(), OrgFileTypeEnum.ORG_HEAD_PHOTO.getType())).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(headCollect)) {
                govOrgInfoDetailDTO.setHeadPhoto(BeanUtil.copyList(headCollect, OrgFileInfoDTO.class));
            } else {
                govOrgInfoDetailDTO.setHeadPhoto(new ArrayList<>());
            }
            List<GovOrgInfoFile> licenseCollect = fileList.stream().filter(e -> Objects.equals(e.getFileType(), OrgFileTypeEnum.ORG_LICENSE_PHOTO.getType())).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(licenseCollect)) {
                govOrgInfoDetailDTO.setBusinessLicensePhoto(BeanUtil.copyList(licenseCollect, OrgFileInfoDTO.class));
            } else {
                govOrgInfoDetailDTO.setBusinessLicensePhoto(new ArrayList<>());
            }
            List<GovOrgInfoFile> restCollect = fileList.stream().filter(e -> Objects.equals(e.getFileType(), OrgFileTypeEnum.ORG_REST_PHOTO.getType())).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(restCollect)) {
                govOrgInfoDetailDTO.setCustomerLoungePhoto(BeanUtil.copyList(restCollect, OrgFileInfoDTO.class));
            } else {
                govOrgInfoDetailDTO.setCustomerLoungePhoto(new ArrayList<>());
            }
        }
        if (StringUtils.isNotBlank(govOrgInfo.getOrgContactorCode())) {
            GovUser govUser = userService.getOne(new LambdaQueryWrapper<GovUser>().eq(GovUser::getUserCode, govOrgInfo.getOrgContactorCode()).eq(GovUser::getCompanyId, govOrgInfo.getCompanyId()));
            if (Objects.nonNull(govUser)) {
                govOrgInfoDetailDTO.setOrgContactorName(govUser.getUserName());
                govOrgInfoDetailDTO.setOrgContactorPhone(govUser.getMobile());
            }
        }
        return govOrgInfoDetailDTO;
    }

    @Override
    public List<GovOrgContactDTO> getContactorEnum(GovOrgEnumReq req) {
        List<GovOrgInfo> govOrgInfoList = this.lambdaQuery()
                .eq(Objects.nonNull(req.getOrgType()), GovOrgInfo::getOrgType, req.getOrgType())
                .eq(GovOrgInfo::getCompanyId, req.getLoginCompanyId())
                .select(GovOrgInfo::getOrgContactorCode)
                .list();
        if (CollectionUtil.isEmpty(govOrgInfoList)) {
            return Lists.newArrayList();
        }
        List<String> userCodeList =
                govOrgInfoList.stream().map(GovOrgInfo::getOrgContactorCode).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(userCodeList)) {
            return Lists.newArrayList();
        }
        List<GovUser> govUsers = userService.list(
                new LambdaQueryWrapper<GovUser>().eq(GovUser::getCompanyId, req.getLoginCompanyId())
                        .in(GovUser::getUserCode, userCodeList)
                        .like(StringUtils.isNotBlank(req.getName()), GovUser::getUserName, "%" + req.getName() + "%"));
        if (CollectionUtil.isEmpty(govUsers)) {
            return Lists.newArrayList();
        }
        return govUsers.stream().map(govUser -> {
            GovOrgContactDTO govOrgContactDTO = new GovOrgContactDTO();
            govOrgContactDTO.setName(govUser.getUserName());
            govOrgContactDTO.setPhone(govUser.getMobile());
            govOrgContactDTO.setNameConcatPhone(govUser.getUserName() + " (" + govUser.getMobile() + ")");
            govOrgContactDTO.setOrgContactorCode(govUser.getUserCode());
            return govOrgContactDTO;
        }).collect(Collectors.toList());
    }

    @Override
    public List<GovOrgNameDTO> getOrgNameEnum(GovOrgEnumReq req) {
        List<GovOrgInfo> govOrgInfoList = this.lambdaQuery()
                .eq(Objects.nonNull(req.getOrgType()), GovOrgInfo::getOrgType, req.getOrgType())
                .like(StringUtils.isNotBlank(req.getName()), GovOrgInfo::getOrgName, "%" + req.getName() + "%")
                .list();
        if (CollectionUtil.isEmpty(govOrgInfoList)) {
            return Lists.newArrayList();
        }

        return govOrgInfoList.stream().map(orgInfo -> {
            GovOrgNameDTO govOrgNameDTO = new GovOrgNameDTO();
            govOrgNameDTO.setOrgNo(orgInfo.getOrgNo());
            govOrgNameDTO.setOrgName(orgInfo.getOrgName());
            return govOrgNameDTO;
        }).collect(Collectors.toList());
    }


    @Override
    public List<DictionaryEnumDTO> getOrgRateEnum() {
        return Arrays.stream(ConstructionInvoiceRate.values())
                .map(e -> new DictionaryEnumDTO(e.getName(), e.getValue()))
                .collect(Collectors.toList());
    }

    private GovOrgInfo buildOrgInfo(GovOrgInfoEditDTO req) {
        GovOrgInfo orgInfo = new GovOrgInfo();
        orgInfo.setUpdateCode(req.getLoginUserCode());
        orgInfo.setUpdateName(req.getLoginUserName());
        orgInfo.setUpdateTime(new Date());
        orgInfo.setOrgName(req.getOrgName());
        orgInfo.setOrgNo(req.getOrgNo());
        orgInfo.setOrgType(req.getOrgType());
        orgInfo.setCompanyId(req.getCompanyId());
        orgInfo.setCompanyName(req.getCompanyName());
        orgInfo.setOrgProvinceCode(req.getOrgProvinceCode());
        orgInfo.setOrgProvinceName(req.getOrgProvinceName());
        orgInfo.setOrgCityCode(req.getOrgCityCode());
        orgInfo.setOrgCityName(req.getOrgCityName());
        orgInfo.setOrgAddressDetail(req.getOrgAddressDetail());
        orgInfo.setOrgContactorName(req.getOrgContactorName());
        orgInfo.setOrgContactorPhone(req.getOrgContactorPhone());
        orgInfo.setContractBeginDate(req.getContractBeginDate());
        orgInfo.setContractEndDate(req.getContractEndDate());
        orgInfo.setInvoiceType(req.getInvoiceType());
        orgInfo.setInvoiceOpenType(req.getInvoiceOpenType());
        orgInfo.setInvoiceRate(req.getInvoiceRate());
        orgInfo.setOpenBank(req.getOpenBank());
        orgInfo.setBankAccount(req.getBankAccount());
        orgInfo.setCreditCode(req.getCreditCode());
        orgInfo.setOrgStatus(req.getOrgStatus());
        orgInfo.setServiceTelephone(req.getServiceTelephone());
        orgInfo.setLongitude(req.getLongitude());
        orgInfo.setLatitude(req.getLatitude());
        orgInfo.setAddressName(req.getAddressName());
        orgInfo.setAwardedAreaCode(req.getAwardedAreaCode());
        orgInfo.setCreateDeptCode(req.getCreateDeptCode());
        orgInfo.setCreateDeptName(req.getCreateDeptName());
        //中标区域
        if (StringUtils.isNotEmpty(req.getAwardedAreaCode())) {
            List<String> awardedAreaCode = Arrays.asList(req.getAwardedAreaCode().split(","));
            List<GovStruct> list = govStructService.lambdaQuery().in(GovStruct::getStructCode, awardedAreaCode).list();
            if (CollectionUtil.isNotEmpty(list)) {
                String awardedAreaName = list.stream().map(GovStruct::getStructName).collect(Collectors.joining(","));
                orgInfo.setAwardedAreaName(awardedAreaName);
            }
        }
        return orgInfo;
    }


    private GovMaintainGarageExtend buildExtend(GovOrgInfoEditDTO req) {
        GovMaintainGarageExtend extend = new GovMaintainGarageExtend();
        extend.setUpdateCode(req.getLoginUserCode());
        extend.setUpdateName(req.getLoginUserName());
        extend.setUpdateTime(new Date());
        extend.setOrgNo(req.getOrgNo());
        extend.setSupportRepair(req.getSupportRepair());
        extend.setSupportAccidentRepair(req.getSupportAccidentRepair());
        extend.setSupportCarType(req.getSupportCarType());
        return extend;
    }


    private List<GovOrgInfoFile> buildPhoto(GovOrgInfoEditDTO orgInfo) {
        List<GovOrgInfoFile> fileInfos = new ArrayList<>();
        List<OrgFileInfoDTO> businessLicensePhoto = orgInfo.getBusinessLicensePhoto();
        if (CollectionUtil.isNotEmpty(businessLicensePhoto)) {
            businessLicensePhoto.forEach(e -> {
                GovOrgInfoFile fileInfo = new GovOrgInfoFile();
                fileInfo.setFileType(OrgFileTypeEnum.ORG_LICENSE_PHOTO.getType());
                fileInfo.setOrgNo(orgInfo.getOrgNo());
                fileInfo.setFileName(e.getFileName());
                fileInfo.setFileUrl(e.getFileUrl());
                fileInfo.setCreateTime(new Date());
                fileInfo.setUpdateTime(new Date());
                fileInfos.add(fileInfo);
            });
        }
        List<OrgFileInfoDTO> headPhoto = orgInfo.getHeadPhoto();
        if (CollectionUtil.isNotEmpty(headPhoto)) {
            headPhoto.forEach(e -> {
                GovOrgInfoFile fileInfo = new GovOrgInfoFile();
                fileInfo.setFileType(OrgFileTypeEnum.ORG_HEAD_PHOTO.getType());
                fileInfo.setOrgNo(orgInfo.getOrgNo());
                fileInfo.setFileName(e.getFileName());
                fileInfo.setFileUrl(e.getFileUrl());
                fileInfo.setCreateTime(new Date());
                fileInfo.setUpdateTime(new Date());
                fileInfos.add(fileInfo);
            });
        }
        List<OrgFileInfoDTO> customerLoungePhoto = orgInfo.getCustomerLoungePhoto();
        if (CollectionUtil.isNotEmpty(customerLoungePhoto)) {
            customerLoungePhoto.forEach(e -> {
                GovOrgInfoFile fileInfo = new GovOrgInfoFile();
                fileInfo.setFileType(OrgFileTypeEnum.ORG_REST_PHOTO.getType());
                fileInfo.setOrgNo(orgInfo.getOrgNo());
                fileInfo.setFileName(e.getFileName());
                fileInfo.setFileUrl(e.getFileUrl());
                fileInfo.setCreateTime(new Date());
                fileInfo.setUpdateTime(new Date());
                fileInfos.add(fileInfo);
            });
        }
        List<OrgFileInfoDTO> workAreaPhoto = orgInfo.getWorkAreaPhoto();
        if (CollectionUtil.isNotEmpty(workAreaPhoto)) {
            workAreaPhoto.forEach(e -> {
                GovOrgInfoFile fileInfo = new GovOrgInfoFile();
                fileInfo.setOrgNo(orgInfo.getOrgNo());
                fileInfo.setFileType(OrgFileTypeEnum.ORG_WORK_PHOTO.getType());
                fileInfo.setFileName(e.getFileName());
                fileInfo.setFileUrl(e.getFileUrl());
                fileInfo.setCreateTime(new Date());
                fileInfo.setUpdateTime(new Date());
                fileInfos.add(fileInfo);
            });
        }
        return fileInfos;
    }


    @Override
    public void insertGarage(GovOrgInfo govOrgInfo) {
        //新建用户
        if (Objects.nonNull(govOrgInfo)) {
            GovUserAddReqDTO userAddReqDTO = buildUserInfo(govOrgInfo.getOrgContactorPhone(), govOrgInfo.getCompanyId(), govOrgInfo.getOrgContactorName(), govOrgInfo.getOrgType());
            userAddReqDTO.setLoginUserCode(govOrgInfo.getCreateCode());
            userAddReqDTO.setLoginUserName(govOrgInfo.getCreateName());
            userAddReqDTO.setRoleCodeList(Collections.singletonList(getRoleCodeByOrgType(govOrgInfo.getOrgType())));
            userAddReqDTO.setAddSource(GovUserAddSourceEnum.ORG_ADD);
            userAddReqDTO.setSupplierServiceCode(govOrgInfo.getOrgNo());
            String userCode = userService.addUser(userAddReqDTO);
            update(new LambdaUpdateWrapper<GovOrgInfo>().eq(GovOrgInfo::getOrgNo, govOrgInfo.getOrgNo()).set(GovOrgInfo::getOrgContactorCode, userCode));
        }
    }

    @Override
    public void insertOrgInfoRelation(GovOrgImportDTO req, String orgNo) {
        if (StringUtils.isNotEmpty(req.getAwardedAreaCode())) {
            GovOrgRelationSaveDTO govOrgRelationSaveDTO = new GovOrgRelationSaveDTO();
            govOrgRelationSaveDTO.setCompanyId(req.getLoginCompanyId());
            govOrgRelationSaveDTO.setCompanyName(req.getLoginCompanyName());
            govOrgRelationSaveDTO.setLoginUserCode(req.getLoginUserCode());
            govOrgRelationSaveDTO.setLoginUserName(req.getLoginUserName());
            govOrgRelationSaveDTO.setCreateDeptForDb(req.getCreateDeptName());
            govOrgRelationSaveDTO.setCreateDeptCodeForDb(req.getCreateDeptCode());
            govOrgRelationSaveDTO.setOrgNoList(Collections.singletonList(orgNo));
            govOrgRelationSaveDTO.setSelectType(0);
            //传父节点
            govOrgRelationSaveDTO.setDeptCodeList(Arrays.asList(req.getAwardedAreaCode().split(",")));
            govOrgRelationService.saveOrgRelation(govOrgRelationSaveDTO);
        }
    }

    @Override
    public String getAwardedAreaName(String awardedCode) {
        //中标区域
        if (StringUtils.isNotEmpty(awardedCode)) {
            List<String> awardedAreaCode = Arrays.asList(awardedCode.split(","));
            List<GovStruct> structList = govStructService.lambdaQuery().in(GovStruct::getStructCode, awardedAreaCode).list();
            if (CollectionUtil.isNotEmpty(structList)) {
                return structList.stream().map(GovStruct::getStructName).collect(Collectors.joining(","));
            }
        }
        return null;
    }

    @Override
    public List<GovOrgInfoDropdownRespDTO> getOrgDropDown(GovOrgInfoDropdownReq reqParam) {
        LambdaQueryWrapper<GovOrgInfo> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(GovOrgInfo::getCompanyId, reqParam.getLoginCompanyId());
        wrapper.eq(Objects.nonNull(reqParam.getOrgType()), GovOrgInfo::getOrgType, reqParam.getOrgType());
        wrapper.like(StringUtils.isNotBlank(reqParam.getOrgName()), GovOrgInfo::getOrgName, reqParam.getOrgName());
        wrapper.eq(StringUtils.isNotBlank(reqParam.getLoginOrgNo()), GovOrgInfo::getOrgNo, reqParam.getLoginOrgNo());
        wrapper.gt(GovOrgInfo::getContractEndDate, LocalDateTime.now().minusDays(1));
        wrapper.orderByDesc(GovOrgInfo::getUpdateTime);
        wrapper.in(CollectionUtils.isNotEmpty(reqParam.getOrgCodeList()), GovOrgInfo::getOrgNo, reqParam.getOrgCodeList());
        if (Objects.nonNull(reqParam.getOrgStatus()) && 3 != reqParam.getOrgStatus()) {
            wrapper.eq(GovOrgInfo::getOrgStatus, reqParam.getOrgStatus());
        }
        List<GovOrgInfo> list = this.list(wrapper);
        if (CollectionUtil.isEmpty(list)) {
            return Lists.newArrayList();
        }
        List<GovOrgInfoDropdownRespDTO> dropdownRespDTOList = BeanUtil.copyList(list, GovOrgInfoDropdownRespDTO.class);
        dropdownRespDTOList.forEach(r ->
                r.setInvoiceRateStr(r.getInvoiceRate().setScale(6, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)).stripTrailingZeros().toPlainString() + "%")
        );
        return dropdownRespDTOList;
    }

    private GovUserAddReqDTO buildUserInfo(String orgContactorPhone, Integer companyId, String orgContactorName, Integer orgType) {
        GovUserAddReqDTO userAddReqDTO = new GovUserAddReqDTO();
        userAddReqDTO.setGender(1);
        userAddReqDTO.setMobile(orgContactorPhone);
        userAddReqDTO.setLoginCompanyId(companyId);
        userAddReqDTO.setUserName(orgContactorName);
        userAddReqDTO.setUserType(getUserTypeByOrgType(orgType));
        userAddReqDTO.setUserDataPermType(GovDataPermTypeEnum.COMPANY.getCode());

        //获取顶级部门
        GovStruct struct = govStructService.lambdaQuery().eq(GovStruct::getStructLevel, 1).eq(GovStruct::getCompanyId, companyId).one();
        if (Objects.nonNull(struct)) {
            userAddReqDTO.setBelongDeptCode(struct.getStructCode());
            userAddReqDTO.setBelongStructCode(struct.getStructCode());
        }
        return userAddReqDTO;
    }


    @Override
    public List<GovOrgInfo> traverse(Long startId, Integer rows) {

        return govOrgInfoMapper.traverse(startId, rows);
    }
}