package com.mrcar.gov.user.mapper;

import com.mrcar.gov.common.dto.asset.maintenance.req.GovMaintainAppletListReq;
import com.mrcar.gov.common.dto.asset.maintenance.resp.GovMaintainGarageAppletRespDTO;
import com.mrcar.gov.common.dto.user.req.GovOrgInfoListReq;
import com.mrcar.gov.common.dto.user.resp.GovOrgInfoListRespDTO;
import com.mrcar.gov.user.domain.GovOrgInfo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【gov_org_info(服务机构信息)】的数据库操作Mapper
 * @createDate 2024-11-13 18:03:54
 * @Entity com.mrcar.gov.user.domain.GovOrgInfo
 */
public interface GovOrgInfoMapper extends BaseMapper<GovOrgInfo> {
    IPage<GovOrgInfoListRespDTO> getListByConditionForPC(Page<GovOrgInfo> page, @Param("req") GovOrgInfoListReq govOrgInfoListReq);

    IPage<GovMaintainGarageAppletRespDTO> selectPageListForApplet(Page page, @Param("req") GovMaintainAppletListReq govOrgInfoListReq);


    List<GovOrgInfo> traverse(@Param("startId")Long startId, @Param("rows")Integer rows);
}




