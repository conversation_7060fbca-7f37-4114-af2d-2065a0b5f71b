package com.mrcar.gov.user.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.izu.cache.core.DistributedLock;
import com.izu.framework.exception.ApiException;
import com.izu.framework.resp.InfoCode;
import com.izu.framework.response.PageDTO;
import com.izu.framework.web.util.BeanUtil;
import com.mrcar.gov.base.service.SequenceGenerator;
import com.mrcar.gov.common.constant.RedisKeyConstants;
import com.mrcar.gov.common.constant.RestErrorCode;
import com.mrcar.gov.common.constant.user.*;
import com.mrcar.gov.common.dto.BaseDTO;
import com.mrcar.gov.common.dto.msg.GovMessageTrackingSenderDTO;
import com.mrcar.gov.common.dto.msg.req.GovMessageTrackingSenderReqDTO;
import com.mrcar.gov.common.dto.user.req.*;
import com.mrcar.gov.common.dto.user.resp.*;
import com.mrcar.gov.common.dto.workflow.common.CustomerQueryReqDTO;
import com.mrcar.gov.common.dto.workflow.common.CustomerStructInfoDTO;
import com.mrcar.gov.common.security.SecurityUtil;
import com.mrcar.gov.common.service.api.MessageTrackingSender;
import com.mrcar.gov.common.util.PinYinUtil;
import com.mrcar.gov.common.util.StrUtils;
import com.mrcar.gov.common.constant.user.DriverEnum;
import com.mrcar.gov.user.common.RoleTypeEnum;
import com.mrcar.gov.user.common.SeqPrefix;
import com.mrcar.gov.user.common.SpecialRoleEnum;
import com.mrcar.gov.user.domain.*;
import com.mrcar.gov.user.mapper.*;
import com.mrcar.gov.user.service.GovCompanyService;
import com.mrcar.gov.user.service.GovPermissionService;
import com.mrcar.gov.user.service.GovStructService;
import com.mrcar.gov.user.service.GovUserRoleRelationService;
import com.mrcar.gov.user.service.GovUserService;
import com.mrcar.gov.user.service.UserAccountLoginService;
import com.mrcar.gov.user.utils.PasswordUtils;
import com.mrcar.thirdparty.sms.AliyunSmsSender;
import com.mrcar.thirdparty.sms.SmsTemplateConstant;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.mrcar.thirdparty.sms.SmsTemplateConstant.DEFAULT_TEMPLATE_PARAMS;


/**
 * <AUTHOR>
 * @description 针对表【gov_user(企业人员信息)】的数据库操作Service实现
 * @createDate 2024-11-07 12:38:10
 */
@Service
public class GovUserServiceImpl extends ServiceImpl<GovUserMapper, GovUser>
        implements GovUserService {

    private static final long LOCK_WAIT_TIME = 1500L;

    @Resource
    private GovUserRoleRelationMapper govUserRoleRelationMapper;
    @Resource
    private GovDataRelationMapper govDataRelationMapper;
    @Resource
    @Lazy
    private GovStructService govStructService;
    @Resource
    private GovRolePermRelationMapper govRolePermRelationMapper;
    @Resource
    private GovPermissionService govPermissionService;
    @Resource
    private GovRoleMapper govRoleMapper;
    @Resource
    private GovStructMapper govStructMapper;
    @Resource
    private GovCompanyMapper companyMapper;
    @Resource
    private GovUserRoleRelationService govUserRoleRelationService;
    @Resource
    @Lazy
    private UserAccountLoginService userAccountLoginService;
    @Resource
    private SequenceGenerator sequenceGenerator;
    @Resource
    private GovUserMapper govUserMapper;
    @Resource
    private DistributedLock distributedLock;
    @Resource
    private MessageTrackingSender messageTrackingSender;
    @Resource
    @Lazy
    private GovCompanyService govCompanyService;
    @Resource
    private AliyunSmsSender aliyunSmsSender;

    private String formatLockKey(String key, String str) {
        return String.format(key, str);
    }


    @Transactional(rollbackFor = Exception.class)
    public String addUser(GovUserAddReqDTO requestDTO) {
        String lockKey = formatLockKey(RedisKeyConstants.GOV_USER_ADD_KEY, requestDTO.getMobile());
        try {
            boolean addUserLocked = distributedLock.lock(lockKey, LOCK_WAIT_TIME);
            if (!addUserLocked) {
                throw new ApiException(RestErrorCode.OPERATE_USER_INFO_OBTAIN_LOCK_FAIL);
            }
            // 加分布式锁 TODO
            checkMobilAndEmailUserInfo(requestDTO);
            GovUser govUser = buildUserFromGovUserAddReqDTO(requestDTO);
            List<GovDataRelation> govDataList = buildDataRelationList(requestDTO.getUserDataList(),
                    govUser.getUserCode(), requestDTO.getLoginCompanyId(), requestDTO.getUserDataPermType());
            List<GovUserRoleRelation> roleRelations = buildRoleRelationList(requestDTO.getRoleCodeList(), govUser.getUserCode(), requestDTO.getLoginCompanyId());
            govUser.setSalt(govUser.getUserCode());
//        govUser.setLoginPassword(PasswordGenerator.generatePassword(govUser.getMobile()));
            // 生成默认密码
            String password = PasswordUtils.generateUnencryptedPassword(govUser.getMobile());
            govUser.setLoginPassword(PasswordUtils.encryptedPassword(password, govUser.getSalt(), 1));
            baseMapper.insert(govUser);
            if (CollectionUtils.isNotEmpty(govDataList)) {
                govDataRelationMapper.batchInsert(govDataList);
            }
            if (CollectionUtils.isNotEmpty(roleRelations)) {
                govUserRoleRelationMapper.batchInsert(roleRelations);
            }
            // 发送短信
            aliyunSmsSender.send(govUser.getMobile(),
                    SmsTemplateConstant.DEFAULT_TEMPLATE_CODE,
                    String.format(SmsTemplateConstant.DEFAULT_TEMPLATE_PARAMS,
                            "尊敬的用户，您好，首汽租车已经为您开通企业账号，登录账号为当前手机号，初始密码：【" + password + "】，请妥善保管。 移动端可在微信小程序搜索【Mr.Car公务用车】。"));
            return govUser.getUserCode();
        } catch (Exception e) {
            throw e;
        } finally {
            distributedLock.unlock(lockKey);
        }
    }


    @Transactional(rollbackFor = Exception.class)
    public Boolean modifyUser(GovUserModifyReqDTO reqDto) {
        LambdaQueryWrapper<GovUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(GovUser::getUserCode, reqDto.getUserCode()).eq(GovUser::getCompanyId, reqDto.getLoginCompanyId());
        GovUser govUser = baseMapper.selectOne(queryWrapper);
        if (Objects.isNull(govUser)) {
            throw new ApiException(RestErrorCode.GOV_USER_NOT_EXIST);
        }
        String lockKey = formatLockKey(RedisKeyConstants.GOV_USER_MODIFY_KEY, reqDto.getMobile());
        try {
            boolean modifyUserLocked = distributedLock.lock(lockKey, LOCK_WAIT_TIME);
            if (!modifyUserLocked) {
                throw new ApiException(RestErrorCode.OPERATE_USER_INFO_OBTAIN_LOCK_FAIL);
            }
            checkModifyUserInfo(govUser, reqDto, govUser.getUserId());
            // 角色变化, 如果是管理员角色，且企业只剩下这一个管理员， 则不允许修改为普通员工
            LambdaUpdateWrapper<GovUser> updateUser = buildUserFromGovUserModifyReqDTO(reqDto, govUser);
            List<GovDataRelation> govDataList = buildDataRelationList(reqDto.getUserDataList(), govUser.getUserCode(), reqDto.getLoginCompanyId(), reqDto.getUserDataPermType());
            List<GovUserRoleRelation> roleRelations = buildRoleRelationList(reqDto.getRoleCodeList(), govUser.getUserCode(), reqDto.getLoginCompanyId());
            LambdaQueryWrapper<GovUser> updateWrapper = new LambdaQueryWrapper<>();
            updateWrapper.eq(GovUser::getUserCode, reqDto.getUserCode()).eq(GovUser::getCompanyId, reqDto.getLoginCompanyId());
            baseMapper.update(updateUser);

            if (CollectionUtils.isNotEmpty(govDataList)) {
                LambdaQueryWrapper<GovDataRelation> delWrapper = new LambdaQueryWrapper<>();
                delWrapper.eq(GovDataRelation::getBussCode, reqDto.getUserCode()).eq(GovDataRelation::getCompanyId, reqDto.getLoginCompanyId());
                govDataRelationMapper.delete(delWrapper);
                govDataRelationMapper.batchInsert(govDataList);
            }
            if (CollectionUtils.isNotEmpty(roleRelations)) {
                LambdaQueryWrapper<GovUserRoleRelation> delWrapper = new LambdaQueryWrapper<>();
                delWrapper.eq(GovUserRoleRelation::getUserCode, reqDto.getUserCode()).eq(GovUserRoleRelation::getCompanyId, reqDto.getLoginCompanyId());
                govUserRoleRelationMapper.delete(delWrapper);
                govUserRoleRelationMapper.batchInsert(roleRelations);
            }
            // 清理用户信息缓存
            userAccountLoginService.clearLoginUserCache(govUser.getMobile());
            // 如果修改的是企业管理员，需要更新企业管理员的数据
            updateCompanySystemUserInfo(reqDto, govUser.getUserCode());
            //修改了手机号之后要退出登录
            if (!Objects.equals(govUser.getMobile(), reqDto.getMobile())) {
                SecurityUtil.logout(govUser.getUserId().longValue());
            }
            return true;
        } catch (Exception e) {
            throw e;
        } finally {
            distributedLock.unlock(lockKey);
        }
    }

    private void updateCompanySystemUserInfo(GovUserModifyReqDTO reqDto, String userCode) {
        GovCompany govCompany = govCompanyService.getOne(new LambdaQueryWrapper<GovCompany>()
                .eq(GovCompany::getCompanyId, reqDto.getLoginCompanyId())
                .eq(GovCompany::getSystemUserCode, userCode).last("limit 1"));
        if (Objects.nonNull(govCompany)) {
            if (!Objects.equals(govCompany.getSystemUserName(), reqDto.getUserName())
                    || !Objects.equals(govCompany.getSystemUserMobile(), reqDto.getMobile())) {
                govCompanyService.update(new LambdaUpdateWrapper<GovCompany>()
                        .eq(GovCompany::getCompanyId, reqDto.getLoginCompanyId())
                        .set(GovCompany::getSystemUserName, reqDto.getUserName())
                        .set(GovCompany::getSystemUserMobile, reqDto.getMobile())
                        .set(GovCompany::getUpdateCode, reqDto.getLoginUserCode())
                        .set(GovCompany::getUpdateTime, new Date())
                        .set(GovCompany::getUpdateName, reqDto.getLoginUserName()));
            }
        }
    }


    public GovUserDetailForModifyRespDTO getGovUserDetailForModify(GovUserDetailGetForModifyReqDTO reqDto) {
        LambdaQueryWrapper<GovUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(GovUser::getUserCode, reqDto.getUserCode()).eq(GovUser::getCompanyId, reqDto.getLoginCompanyId());
        GovUser govUser = baseMapper.selectOne(queryWrapper);
        if (Objects.isNull(govUser)) {
            return new GovUserDetailForModifyRespDTO();
        }
        GovUserDetailForModifyRespDTO respDTO = BeanUtil.copyObject(govUser, GovUserDetailForModifyRespDTO.class);
        fillUserInfo(respDTO, govUser);
        if (Objects.nonNull(reqDto.getBuildDataPerm()) && reqDto.getBuildDataPerm()) {
            List<UserDataPermRespDTO> userDataPermList = buildUserDataPermList(reqDto.getUserCode(), reqDto.getLoginCompanyId());
            respDTO.setUserDataPermList(userDataPermList);
        }
        List<UserRoleInfoRespDTO> userRoleInfoList = buildUserRoleInfoList(govUser.getUserType(), reqDto.getUserCode(), reqDto.getLoginCompanyId());
        respDTO.setUserRoleInfoList(userRoleInfoList);
        return respDTO;
    }

    private void fillUserInfo(GovUserDetailForModifyRespDTO respDTO, GovUser govUser) {
        List<String> structCodeList = Lists.newArrayList();
        if (StringUtils.isNotBlank(govUser.getBelongStructCode())) {
            respDTO.setBelongStructCode(govUser.getBelongStructCode());
            structCodeList.add(govUser.getBelongStructCode());
        }
        if (StringUtils.isNotBlank(govUser.getBelongDeptCode())) {
            respDTO.setBelongDeptCode(govUser.getBelongDeptCode());
            structCodeList.add(govUser.getBelongDeptCode());
        }

        if (CollectionUtils.isEmpty(structCodeList)) {
            return;
        }
        List<GovStruct> structList = govStructMapper.selectList(new LambdaQueryWrapper<GovStruct>()
                .in(GovStruct::getStructCode, structCodeList)
                .eq(GovStruct::getCompanyId, govUser.getCompanyId()));
        if (CollectionUtils.isEmpty(structList)) {
            return;
        }
        Map<String, GovStruct> govStructMap =
                structList.stream().collect(Collectors.toMap(GovStruct::getStructCode, Function.identity(), (s1, s2) -> s1));
        if (StringUtils.isNotBlank(govUser.getBelongStructCode())
                && Objects.nonNull(govStructMap.get(govUser.getBelongStructCode()))) {
            respDTO.setBelongStructName(govStructMap.get(govUser.getBelongStructCode()).getStructName());
        }
        if (StringUtils.isNotBlank(govUser.getBelongDeptCode())
                && Objects.nonNull(govStructMap.get(govUser.getBelongDeptCode()))) {
            respDTO.setBelongDeptName(govStructMap.get(govUser.getBelongDeptCode()).getStructName());
        }

    }

    /**
     * 获取不允许手动赋予的角色编码
     */
    private Set<String> getNotAllowRoleCodeSet() {
        HashSet<String> roleCodeList = new HashSet<>();
        roleCodeList.add(SpecialRoleEnum.COMPANY_DRIVER.getRoleCode());
        roleCodeList.add(SpecialRoleEnum.INSURANCE_STAFF.getRoleCode());
        roleCodeList.add(SpecialRoleEnum.MAINTENANCE_STAFF.getRoleCode());
        roleCodeList.add(SpecialRoleEnum.OIL_STAFF.getRoleCode());
        roleCodeList.add(SpecialRoleEnum.RENT_DRIVER.getRoleCode());
        return roleCodeList;
    }

    /**
     * 获取所有有效的角色
     */
    private List<GovRole> getAllValidRoleList(Integer companyId) {
        LambdaQueryWrapper<GovRole> roleWrapper = Wrappers.lambdaQuery(GovRole.class)
                .and(wrapper -> wrapper.eq(GovRole::getRoleType, RoleTypeEnum.SYSTEM.getCode()).or().eq(GovRole::getCompanyId, companyId))
                .eq(GovRole::getRoleStatus, RoleStatusEnum.VALID.getCode())
                .orderByAsc(GovRole::getRoleType);
        return govRoleMapper.selectList(roleWrapper);
    }

    private List<UserRoleInfoRespDTO> buildUserRoleInfoList(Integer userType, String userCode, Integer companyId) {
        LambdaQueryWrapper<GovUserRoleRelation> roleRelationWrapper = new LambdaQueryWrapper<>();
        roleRelationWrapper.eq(GovUserRoleRelation::getUserCode, userCode).eq(GovUserRoleRelation::getCompanyId, companyId);
        List<GovUserRoleRelation> roleRelations = govUserRoleRelationMapper.selectList(roleRelationWrapper);
        Set<String> roleCodeSet = Sets.newHashSet();
        if (CollectionUtils.isNotEmpty(roleRelations)) {
            roleCodeSet.addAll(roleRelations.stream().map(GovUserRoleRelation::getRoleCode).collect(Collectors.toSet()));
        }
        // 查询所有有效角色
        List<GovRole> govRoleList = this.getAllValidRoleList(companyId);
        if (CollectionUtils.isEmpty(govRoleList)) {
            return Lists.newArrayList();
        }
        Set<String> notAllowRoleCodeSet = getNotAllowRoleCodeSet();
        List<UserRoleInfoRespDTO> userRoleInfoList = new ArrayList<>(govRoleList.size());
        govRoleList.forEach(govRole -> {
            UserRoleInfoRespDTO userRoleInfoRespDTO = new UserRoleInfoRespDTO();
            userRoleInfoRespDTO.setRoleCode(govRole.getRoleCode());
            userRoleInfoRespDTO.setRoleName(govRole.getRoleName());
            userRoleInfoRespDTO.setRoleType(govRole.getRoleType());
            userRoleInfoRespDTO.setRoleTypeDesc(RoleTypeEnum.getDescByCode(govRole.getRoleType()));
            userRoleInfoRespDTO.setChecked(roleCodeSet.contains(govRole.getRoleCode()));
            // 普通员工和司机角色不允许修改
            userRoleInfoRespDTO.setDisabled(notAllowRoleCodeSet.contains(govRole.getRoleCode()));
            userRoleInfoList.add(userRoleInfoRespDTO);
        });
        return userRoleInfoList;
    }


    private List<UserDataPermRespDTO> buildUserDataPermList(String userCode, Integer companyId) {
        // 初始化数据
        List<UserDataPermRespDTO> userDataPermList = initUserDataPermList();
        LambdaQueryWrapper<GovDataRelation> dataRelationWrapper = new LambdaQueryWrapper<>();
        dataRelationWrapper.eq(GovDataRelation::getBussCode, userCode).eq(GovDataRelation::getCompanyId, companyId);
        List<GovDataRelation> dataRelations = govDataRelationMapper.selectList(dataRelationWrapper);
        List<GovDataRelation> assignStructDataRelation = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(dataRelations)) {
            Map<Integer, List<GovDataRelation>> dataRelationMap =
                    dataRelations.stream().collect(Collectors.groupingBy(GovDataRelation::getRelationType));
            Map<Integer, UserDataPermRespDTO> userDataPermMap =
                    userDataPermList.stream().collect(Collectors.toMap(UserDataPermRespDTO::getDataPermType, Function.identity()));
            dataRelationMap.forEach((relationType, dataRelationList) -> {
                if (Objects.equals(relationType, GovDataPermTypeEnum.ASSIGN_STRUCT.getCode())) {
                    UserDataPermRespDTO userDataPerm = userDataPermMap.get(relationType);
                    userDataPerm.setChecked(true);
                    assignStructDataRelation.addAll(dataRelationList);
                } else if (Objects.equals(relationType, GovDataPermTypeEnum.COMPANY.getCode())) {
                    userDataPermMap.get(relationType).setChecked(true);
                } else if (Objects.equals(relationType, GovDataPermTypeEnum.SELF.getCode())) {
                    userDataPermMap.get(relationType).setChecked(true);
                }
            });
        }
        UserDataPermRespDTO userDataPermRespDTO = userDataPermList.stream().filter(
                userDataPerm -> Objects.equals(userDataPerm.getDataPermType(), GovDataPermTypeEnum.ASSIGN_STRUCT.getCode())).collect(Collectors.toList()).get(0);
        assignStructPermProcess(assignStructDataRelation, userDataPermRespDTO, companyId);
        return userDataPermList;
    }

//    private void fillStructTreeInfo(List<UserDataPermRespDTO> userDataPermList, Integer companyId) {
//        UserDataPermRespDTO userDataPermRespDTO = userDataPermList.stream().filter(
//                userDataPerm -> Objects.equals(userDataPerm.getDataPermType(), GovDataPermTypeEnum.ASSIGN_STRUCT.getCode())).collect(Collectors.toList()).get(0);
//        assignStructPermProcess(Lists.newArrayList(), userDataPermRespDTO, companyId);
//    }

    private List<UserDataPermRespDTO> initUserDataPermList() {
        List<UserDataPermRespDTO> userDataPermList = new ArrayList<>();
        UserDataPermRespDTO companyPermRespDTO = new UserDataPermRespDTO();
        companyPermRespDTO.setDataPermType(GovDataPermTypeEnum.COMPANY.getCode());
        companyPermRespDTO.setDataPermTypeName(GovDataPermTypeEnum.COMPANY.getDesc());
        companyPermRespDTO.setChecked(false);

        UserDataPermRespDTO assignStructPermRespDTO = new UserDataPermRespDTO();
        assignStructPermRespDTO.setDataPermType(GovDataPermTypeEnum.ASSIGN_STRUCT.getCode());
        assignStructPermRespDTO.setDataPermTypeName(GovDataPermTypeEnum.ASSIGN_STRUCT.getDesc());
        assignStructPermRespDTO.setChecked(false);

        UserDataPermRespDTO selfPermRespDTO = new UserDataPermRespDTO();
        selfPermRespDTO.setDataPermType(GovDataPermTypeEnum.SELF.getCode());
        selfPermRespDTO.setDataPermTypeName(GovDataPermTypeEnum.SELF.getDesc());
        selfPermRespDTO.setChecked(false);
        userDataPermList.add(companyPermRespDTO);
        userDataPermList.add(assignStructPermRespDTO);
        userDataPermList.add(selfPermRespDTO);
        return userDataPermList;
    }

    private void assignStructPermProcess(List<GovDataRelation> dataRelationList,
                                         UserDataPermRespDTO userDataPermRespDTO,
                                         Integer companyId) {
        Set<String> relationCodeSet =
                dataRelationList.stream().map(GovDataRelation::getRelatedCode).collect(Collectors.toSet());
        // 查询所有部门信息
        GovStructRequestDTO govStructRequestDTO = new GovStructRequestDTO();
        govStructRequestDTO.setLoginCompanyId(companyId);
        List<GovStructRespondDTO> govStructRespondDTOS = govStructService.getGovStructTree(govStructRequestDTO);
        List<UserDataPermInfoRespDTO> dataPermList = buildAssignStructPermPermList(govStructRespondDTOS, relationCodeSet);
        userDataPermRespDTO.setDataPermList(dataPermList);
    }

    private List<UserDataPermInfoRespDTO> buildAssignStructPermPermList(List<GovStructRespondDTO> govStructRespondDTOS, Set<String> relationCodeSet) {
        if (CollectionUtils.isEmpty(govStructRespondDTOS)) {
            return Lists.newArrayList();
        }
        List<UserDataPermInfoRespDTO> dataPermList = new ArrayList<>(govStructRespondDTOS.size());
        for (GovStructRespondDTO govStructRespondDTO : govStructRespondDTOS) {
            UserDataPermInfoRespDTO dataPermInfoRespDTO = new UserDataPermInfoRespDTO();
            dataPermInfoRespDTO.setDataPermCode(govStructRespondDTO.getStructCode());
            dataPermInfoRespDTO.setDataPermName(govStructRespondDTO.getStructName());
            dataPermInfoRespDTO.setStructStatus(govStructRespondDTO.getStatus());
            dataPermInfoRespDTO.setStructStatusDesc(govStructRespondDTO.getStatusStr());
            dataPermInfoRespDTO.setChecked(relationCodeSet.contains(govStructRespondDTO.getStructCode()));
            List<UserDataPermInfoRespDTO> subDataPermList = buildAssignStructPermPermList(govStructRespondDTO.getSubGovStructList(), relationCodeSet);
            dataPermInfoRespDTO.setChildDataPermInfo(subDataPermList);
            dataPermList.add(dataPermInfoRespDTO);
        }
        return dataPermList;
    }

    public List<GovUserRoleRelation> buildRoleRelationList(List<String> roleCodeList, String userCode, Integer companyId) {
        if (CollectionUtils.isEmpty(roleCodeList)) {
            return Lists.newArrayList();
        }
        List<GovUserRoleRelation> relationList = new ArrayList<>(roleCodeList.size());
        roleCodeList.forEach(roleCode -> {
            GovUserRoleRelation relation = new GovUserRoleRelation();
            relation.setCompanyId(companyId);
            relation.setUserCode(userCode);
            relation.setRoleCode(roleCode);
            relation.setCreateTime(new Date());
            relationList.add(relation);
        });
        return relationList;
    }

    public List<GovDataRelation> buildDataRelationList(List<String> userDataList, String userCode, Integer companyId, Integer userDatePermType) {
        if (Objects.equals(userDatePermType, GovDataPermTypeEnum.COMPANY.getCode())) {
            GovDataRelation relation = new GovDataRelation();
            relation.setCompanyId(companyId);
            relation.setBussCode(userCode);
            relation.setRelationType(GovDataPermTypeEnum.COMPANY.getCode());
            relation.setRelatedCode(companyId + "");
            relation.setCreateTime(new Date());
            return Lists.newArrayList(relation);
        } else if (Objects.equals(userDatePermType, GovDataPermTypeEnum.SELF.getCode())) {
            GovDataRelation relation = new GovDataRelation();
            relation.setCompanyId(companyId);
            relation.setBussCode(userCode);
            relation.setRelationType(GovDataPermTypeEnum.SELF.getCode());
            relation.setRelatedCode(userCode);
            relation.setCreateTime(new Date());
            return Lists.newArrayList(relation);
        }
        if (CollectionUtils.isEmpty(userDataList)) {
            return Lists.newArrayList();
        }
        List<GovDataRelation> relationList = new ArrayList<>(userDataList.size());
        userDataList.forEach(data -> {
            GovDataRelation relation = new GovDataRelation();
            relation.setCompanyId(companyId);
            relation.setBussCode(userCode);
            relation.setRelationType(GovDataPermTypeEnum.ASSIGN_STRUCT.getCode());
            relation.setRelatedCode(data);
            relation.setCreateTime(new Date());
            relationList.add(relation);
        });
        return relationList;
    }


    private LambdaUpdateWrapper<GovUser> buildUserFromGovUserModifyReqDTO(GovUserModifyReqDTO reqDto, GovUser govUser) {
        LambdaUpdateWrapper<GovUser> updateWrapper = new LambdaUpdateWrapper<GovUser>();
        updateWrapper.eq(GovUser::getUserCode, govUser.getUserCode());
        updateWrapper.eq(GovUser::getCompanyId, govUser.getCompanyId());
        updateWrapper.set(GovUser::getUserName, reqDto.getUserName());
        // 如果是自己修改自己，这里需要重置登录态里的用户名
        String loginUserName = reqDto.getLoginUserName();
        if (Objects.equals(reqDto.getLoginUserCode(), reqDto.getUserCode())) {
            loginUserName = reqDto.getUserName();
        }
        updateWrapper.set(GovUser::getPinyinName, PinYinUtil.getPinYinHeadChar(reqDto.getUserName()));
        if (Objects.nonNull(reqDto.getUserType())) {
            updateWrapper.set(GovUser::getUserType, reqDto.getUserType());
        }
        updateWrapper.set(GovUser::getMobile, reqDto.getMobile());
        updateWrapper.set(GovUser::getUserPosition, StrUtils.getOrDefaultEmpty(reqDto.getUserPosition()));
        updateWrapper.set(GovUser::getEmail, StrUtils.getOrDefaultEmpty(reqDto.getEmail()));
        updateWrapper.set(GovUser::getHeadIcon, StrUtils.getOrDefaultEmpty(reqDto.getHeadIcon()));
        updateWrapper.set(GovUser::getBelongStructCode, StrUtils.getOrDefaultEmpty(reqDto.getBelongStructCode()));
        updateWrapper.set(GovUser::getBelongDeptCode, StrUtils.getOrDefaultEmpty(reqDto.getBelongDeptCode()));
        updateWrapper.set(GovUser::getDataPermType, reqDto.getUserDataPermType());
        updateWrapper.set(GovUser::getUpdateName, loginUserName);
        updateWrapper.set(GovUser::getUpdateCode, reqDto.getLoginUserCode());
        updateWrapper.set(GovUser::getUpdateDate, new Date());
        updateWrapper.set(GovUser::getSupplierServiceCode, StrUtils.getOrDefaultEmpty(reqDto.getSupplierServiceCode()));
        updateWrapper.set(Objects.nonNull(reqDto.getGender()), GovUser::getGender, reqDto.getGender());
        return updateWrapper;
    }

    private GovUser buildUserFromGovUserAddReqDTO(GovUserAddReqDTO dto) {
        GovUser user = buildCommonUserInfoFromGovUserAddReqDTO(dto);
        Date addDate = new Date();
        String userCode = sequenceGenerator.generate(addDate, SeqPrefix.GOV_USR_CODE);
        user.setUserCode(userCode);
        user.setCreateCode(dto.getLoginUserCode());
        user.setCreateName(dto.getLoginUserName());
        user.setCreateTime(addDate);
        user.setUpdateDate(addDate);
        user.setUpdateCode(dto.getLoginUserCode());
        user.setUpdateName(dto.getLoginUserName());
        user.setSupplierServiceCode(StrUtils.getOrDefaultEmpty(dto.getSupplierServiceCode()));
        user.setSupplierOutletsCode(StrUtils.getOrDefaultEmpty(dto.getSupplierOutletsCode()));
        user.setSupplierOutletsName(StrUtils.getOrDefaultEmpty(dto.getSupplierOutletsName()));
        user.setSupplierOutletsPoint(StrUtils.getOrDefaultEmpty(dto.getSupplierOutletsPoint()));
        return user;
    }

    private GovUser buildCommonUserInfoFromGovUserAddReqDTO(GovUserAddReqDTO dto) {
        GovUser user = new GovUser();
        user.setUserName(dto.getUserName());
        user.setPinyinName(PinYinUtil.getPinYinHeadChar(dto.getUserName()));
        user.setUserType(dto.getUserType());
        user.setMobile(dto.getMobile());
        user.setUserStatus(GovUserStatusEnum.NORMAL.getCode());

        user.setUserPosition(StrUtils.getOrDefaultEmpty(dto.getUserPosition()));
        user.setEmail(StrUtils.getOrDefaultEmpty(dto.getEmail()));
        if (StringUtils.isNotBlank(dto.getHeadIcon())) {
            user.setHeadIcon(dto.getHeadIcon());
        }
        user.setDataPermType(dto.getUserDataPermType());
        user.setCompanyId(dto.getLoginCompanyId());
        if (StringUtils.isNotBlank(dto.getBelongDeptCode())) {
            user.setBelongDeptCode(dto.getBelongDeptCode());
        }
        if (StringUtils.isNotBlank(dto.getBelongStructCode())) {
            user.setBelongStructCode(dto.getBelongStructCode());
        }
        if (Objects.nonNull(dto.getGender())) {
            user.setGender(dto.getGender());
        }
        return user;
    }


    private void checkModifyUserInfo(GovUser govUser, GovUserModifyReqDTO requestDTO, Integer excludeId) {
        if (!GovUserTypeEnum.isOrgUserType(govUser.getUserType())) {
            if (StringUtils.isBlank(requestDTO.getBelongDeptCode())) {
                throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "员工所属单位不能为空");
            }
        }
        //校验手机号
        checkMobile(requestDTO.getMobile(), excludeId);

        checkEmail(requestDTO.getEmail(), excludeId);
        // 检查管理员角色
        checkAdminRole(requestDTO.getLoginCompanyId(), requestDTO.getUserCode(), requestDTO.getRoleCodeList());
    }


    /**
     * 校验手机号是否跟启用的账号重复
     *
     * @param mobile    手机号
     * @param excludeId 需要排除的id，待更新的ID
     */
    private void checkMobile(String mobile, Integer excludeId) {
        if (StringUtils.isNotBlank(mobile)) {
            LambdaQueryWrapper<GovUser> mobileWrapper = Wrappers.lambdaQuery(GovUser.class)
                    .eq(GovUser::getMobile, mobile)
                    //不要跟启用的重复即可
                    .eq(GovUser::getUserStatus, GovUserStatusEnum.NORMAL.getCode())
                    .ne(excludeId != null, GovUser::getUserId, excludeId);
            long count = count(mobileWrapper);
            if (count > 0) {
                throw new ApiException(RestErrorCode.GOV_USER_PHONE_USED);
            }
        }
    }


    /**
     * 校验邮箱是否跟启用的账号重复
     *
     * @param email     邮箱
     * @param excludeId 需要排除的id，待更新的ID
     */
    private void checkEmail(String email, Integer excludeId) {
        email = StringUtils.trim(email);
        if (StringUtils.isNotBlank(email)) {
            LambdaQueryWrapper<GovUser> emailWrapper = Wrappers.lambdaQuery(GovUser.class)
                    .eq(GovUser::getEmail, email)
                    //不要跟启用的重复即可
                    .eq(GovUser::getUserStatus, GovUserStatusEnum.NORMAL.getCode())
                    .ne(excludeId != null, GovUser::getUserId, excludeId);
            long count = count(emailWrapper);
            if (count > 0) {
                throw new ApiException(RestErrorCode.GOV_USER_EMAIL_USED);
            }
        }
    }

    private void checkAdminRole(Integer companyId, String userCode, List<String> rocdeList) {
        List<GovUserRoleRelation> relationList = govUserRoleRelationMapper.selectList(new LambdaQueryWrapper<GovUserRoleRelation>()
                .eq(GovUserRoleRelation::getCompanyId, companyId)
//                .notIn(GovUserRoleRelation::getUserCode, userCode)
                .eq(GovUserRoleRelation::getRoleCode, SpecialRoleEnum.COMPANY_SYSTEM.getRoleCode()));
        List<String> adminUserCodeList =
                relationList.stream().map(GovUserRoleRelation::getUserCode).distinct().collect(Collectors.toList());
        // 如果修改成员是管理员，被修改后没有管理员了，则不允许修改
        if (adminUserCodeList.contains(userCode)
                && !rocdeList.contains(SpecialRoleEnum.COMPANY_SYSTEM.getRoleCode())
                && adminUserCodeList.size() == 1) {
            throw new ApiException(RestErrorCode.GOV_USER_COMPANY_ADMIN_LESS_THAN_ONE);
        }
    }

    public void checkMobilAndEmailUserInfo(GovUserAddReqDTO requestDTO) {
        checkMobile(requestDTO.getMobile(), null);
        checkEmail(requestDTO.getEmail(), null);
    }


    public GovUserDetailGetRespDTO getGovUserDetail(GovUserDetailGetReqDTO reqDTO) {
        LambdaQueryWrapper<GovUser> userQryWrapper = new LambdaQueryWrapper<>();
        userQryWrapper.eq(GovUser::getUserCode, reqDTO.getUserCode()).eq(GovUser::getCompanyId, reqDTO.getLoginCompanyId());
        GovUser govUser = baseMapper.selectOne(userQryWrapper);
        if (Objects.isNull(govUser)) {
            return null;
        }
        LambdaQueryWrapper<GovCompany> companyQryWrapper = new LambdaQueryWrapper<>();
        companyQryWrapper.eq(GovCompany::getCompanyId, govUser.getCompanyId());
        GovCompany govCompany = companyMapper.selectOne(companyQryWrapper);

        GovUserDetailGetRespDTO respDTO = new GovUserDetailGetRespDTO();
        if (Objects.nonNull(reqDTO.getBuildDataPerm()) && reqDTO.getBuildDataPerm()) {
            GovUserDatePermDTO userDataPerm = buildGovUserDataPermList(reqDTO.getUserCode(), govUser.getCompanyId());
            if (Objects.nonNull(userDataPerm)) {
                respDTO.setDataPermInfo(userDataPerm);
            }
        }
        List<GovUserRoleDTO> userRoleInfoList = buildGovUserRoleList(govUser.getUserCode(), govUser.getCompanyId());
        RoleInfoPermScopeRespDTO roleMenuInfo = buildGovUserMenuData(govUser.getCompanyId(), userRoleInfoList);
        respDTO.setUserInfo(BeanUtil.copyObject(govUser, GovUserDTO.class));
        // 填充部门名称
        fillStructName4UserDetailGetRespDTO(respDTO.getUserInfo());
        if (Objects.nonNull(govCompany)) {
            respDTO.setCompanyInfo(BeanUtil.copyObject(govCompany, GovCompanyDTO.class));
        }
        respDTO.setRoleInfoList(userRoleInfoList);
        if (Objects.nonNull(roleMenuInfo)) {
            if (Objects.nonNull(roleMenuInfo.getPermScopeAppletList())) {
                List<GovPermissionDTO> permissionList = roleMenuInfo.getPermScopeAppletList();
                filterNotCheckedMenu(permissionList);
                respDTO.setAppMenuList(permissionList);
            }
            if (Objects.nonNull(roleMenuInfo.getPermScopeClientList())) {
                List<GovPermissionDTO> permissionList = roleMenuInfo.getPermScopeClientList();
                filterNotCheckedMenu(permissionList);
                respDTO.setPcMenuList(permissionList);
            }
        }
        return respDTO;
    }

    private void filterNotCheckedMenu(List<GovPermissionDTO> menuList) {
        if (CollectionUtils.isEmpty(menuList)) {
            return;
        }
        Iterator<GovPermissionDTO> iterator = menuList.iterator();
        while (iterator.hasNext()) {
            GovPermissionDTO permissionDTO = iterator.next();
            if (!permissionDTO.getChecked()) {
                iterator.remove();
                continue;
            }
            filterNotCheckedMenu(permissionDTO.getChildPermissions());
        }
    }

    private void fillStructName4UserDetailGetRespDTO(GovUserDTO userDTO) {
        List<String> structCodeList = Lists.newArrayList();
        if (StringUtils.isNotBlank(userDTO.getBelongStructCode())) {
            structCodeList.add(userDTO.getBelongStructCode());
        }
        if (StringUtils.isNotBlank(userDTO.getBelongDeptCode())) {
            structCodeList.add(userDTO.getBelongDeptCode());
        }
        if (CollectionUtils.isEmpty(structCodeList)) {
            return;
        }
        List<GovStruct> structList = govStructMapper.selectList(new LambdaQueryWrapper<GovStruct>()
                .in(GovStruct::getStructCode, structCodeList)
                .eq(GovStruct::getCompanyId, userDTO.getCompanyId()));
        if (CollectionUtils.isEmpty(structList)) {
            return;
        }
        Map<String, GovStruct> govStructMap =
                structList.stream().collect(Collectors.toMap(GovStruct::getStructCode, Function.identity(), (s1, s2) -> s1));
        if (StringUtils.isNotBlank(userDTO.getBelongStructCode())
                && Objects.nonNull(govStructMap.get(userDTO.getBelongStructCode()))) {
            userDTO.setBelongStructName(govStructMap.get(userDTO.getBelongStructCode()).getStructName());
        }
        if (StringUtils.isNotBlank(userDTO.getBelongDeptCode())
                && Objects.nonNull(govStructMap.get(userDTO.getBelongDeptCode()))) {
            userDTO.setBelongDeptName(govStructMap.get(userDTO.getBelongDeptCode()).getStructName());
        }
    }

    private RoleInfoPermScopeRespDTO buildGovUserMenuData(Integer companyId, List<GovUserRoleDTO> userRoleInfoList) {
        if (CollectionUtils.isEmpty(userRoleInfoList)) {
            return null;
        }
        List<String> roleCodeList = userRoleInfoList.stream().map(GovUserRoleDTO::getRoleCode).collect(Collectors.toList());
        LambdaQueryWrapper<GovRolePermRelation> permRelationQryWrapper = new LambdaQueryWrapper<>();
        permRelationQryWrapper.eq(GovRolePermRelation::getCompanyId, companyId)
                .in(GovRolePermRelation::getRoleCode, roleCodeList);

        List<GovRolePermRelation> relationList = govRolePermRelationMapper.selectList(permRelationQryWrapper);
        if (CollectionUtils.isEmpty(relationList)) {
            return null;
        }
        List<String> permissionCodes = relationList.stream().map(GovRolePermRelation::getPermissionCode).collect(Collectors.toList());
        return govPermissionService.getPermScopeList(permissionCodes);
    }

    private List<GovUserRoleDTO> buildGovUserRoleList(String userCode, Integer companyId) {
        LambdaQueryWrapper<GovUserRoleRelation> roleRelationWrapper = new LambdaQueryWrapper<>();
        roleRelationWrapper.eq(GovUserRoleRelation::getUserCode, userCode).eq(GovUserRoleRelation::getCompanyId, companyId);
        List<GovUserRoleRelation> roleRelations = govUserRoleRelationMapper.selectList(roleRelationWrapper);
        if (CollectionUtils.isEmpty(roleRelations)) {
            return Lists.newArrayList();
        }
        Set<String> roleCodeSet = roleRelations.stream().map(GovUserRoleRelation::getRoleCode).collect(Collectors.toSet());
        // 查询所有角色
        LambdaQueryWrapper<GovRole> roleWrapper = new LambdaQueryWrapper<>();
        roleWrapper.and(wrapper -> wrapper.eq(GovRole::getRoleType, RoleTypeEnum.SYSTEM.getCode()).or().eq(GovRole::getCompanyId, companyId))
                .eq(GovRole::getRoleStatus, RoleStatusEnum.VALID.getCode())
                .in(GovRole::getRoleCode, roleCodeSet);
        List<GovRole> govRoleList = govRoleMapper.selectList(roleWrapper);
        if (CollectionUtils.isEmpty(govRoleList)) {
            return Lists.newArrayList();
        }
        List<GovUserRoleDTO> userRoleInfoList = new ArrayList<>(govRoleList.size());
        govRoleList.forEach(govRole -> {
            GovUserRoleDTO roleDTO = BeanUtil.copyObject(govRole, GovUserRoleDTO.class);
            userRoleInfoList.add(roleDTO);
        });
        return userRoleInfoList;
    }

    private GovUserDatePermDTO buildGovUserDataPermList(String userCode, Integer companyId) {
        // 初始化数据
        LambdaQueryWrapper<GovDataRelation> dataRelationWrapper = new LambdaQueryWrapper<>();
        dataRelationWrapper.eq(GovDataRelation::getBussCode, userCode).eq(GovDataRelation::getCompanyId, companyId);
        List<GovDataRelation> dataRelations = govDataRelationMapper.selectList(dataRelationWrapper);
        if (CollectionUtils.isEmpty(dataRelations)) {
            return null;
        }
        Map<Integer, List<GovDataRelation>> dataRelationMap =
                dataRelations.stream().collect(Collectors.groupingBy(GovDataRelation::getRelationType));
        for (Map.Entry<Integer, List<GovDataRelation>> entry : dataRelationMap.entrySet()) {
            Integer relationType = entry.getKey();
            List<GovDataRelation> dataRelationList = entry.getValue();
            if (Objects.equals(relationType, GovDataPermTypeEnum.ASSIGN_STRUCT.getCode())) {
                return govUserAssignStructPermProcess(dataRelationList, companyId);
            } else if (Objects.equals(relationType, GovDataPermTypeEnum.COMPANY.getCode())) {
                return govUserCompanyPermProcess();
            } else if (Objects.equals(relationType, GovDataPermTypeEnum.SELF.getCode())) {
                return govUserSelfPermProcess();
            }
        }
        return null;
    }


    private GovUserDatePermDTO govUserSelfPermProcess() {
        GovUserDatePermDTO govUserDatePermDTO = new GovUserDatePermDTO();
        govUserDatePermDTO.setDataPermType(GovDataPermTypeEnum.SELF.getCode());
        govUserDatePermDTO.setDataPermTypeName(GovDataPermTypeEnum.SELF.getDesc());
        return govUserDatePermDTO;
    }


    private GovUserDatePermDTO govUserCompanyPermProcess() {
        GovUserDatePermDTO govUserDatePermDTO = new GovUserDatePermDTO();
        govUserDatePermDTO.setDataPermType(GovDataPermTypeEnum.COMPANY.getCode());
        govUserDatePermDTO.setDataPermTypeName(GovDataPermTypeEnum.COMPANY.getDesc());
        return govUserDatePermDTO;
    }

    private GovUserDatePermDTO govUserAssignStructPermProcess(List<GovDataRelation> dataRelationList, Integer companyId) {
        GovUserDatePermDTO govUserDatePermDTO = new GovUserDatePermDTO();
        govUserDatePermDTO.setDataPermType(GovDataPermTypeEnum.ASSIGN_STRUCT.getCode());
        govUserDatePermDTO.setDataPermTypeName(GovDataPermTypeEnum.ASSIGN_STRUCT.getDesc());

        Set<String> relationCodeSet =
                dataRelationList.stream().map(GovDataRelation::getRelatedCode).collect(Collectors.toSet());
        LambdaQueryWrapper<GovStruct> structQryWrapper = new LambdaQueryWrapper<>();
        structQryWrapper.eq(GovStruct::getCompanyId, companyId).in(GovStruct::getStructCode, relationCodeSet);
        List<GovStruct> structList = govStructMapper.selectList(structQryWrapper);
        if (CollectionUtils.isEmpty(structList)) {
            return govUserDatePermDTO;
        }
        List<GovUserDatePermInfoDTO> dataPermInfoList = new ArrayList<>(structList.size());
        structList.forEach(struct -> {
            GovUserDatePermInfoDTO permInfoDTO = new GovUserDatePermInfoDTO();
            permInfoDTO.setDataPermCode(struct.getStructCode());
            permInfoDTO.setDataPermName(struct.getStructName());
            dataPermInfoList.add(permInfoDTO);
        });
        govUserDatePermDTO.setDataPermInfoList(dataPermInfoList);
        return govUserDatePermDTO;
    }

    @Override
    public List<UserForRoleResponseDTO> getMemberForList(GovUserQueryRequestDTO userQueryRequestDTO) {
        LambdaQueryWrapper<GovUser> wrapper = new LambdaQueryWrapper<GovUser>()
                .eq(GovUser::getCompanyId, userQueryRequestDTO.getCompanyId())
                .like(StringUtils.isNotBlank(userQueryRequestDTO.getUserName()), GovUser::getUserName, userQueryRequestDTO.getUserName())
                .eq(StringUtils.isNotBlank(userQueryRequestDTO.getMobile()), GovUser::getMobile, userQueryRequestDTO.getMobile())
                .like(StringUtils.isNotBlank(userQueryRequestDTO.getUserPosition()), GovUser::getUserPosition, userQueryRequestDTO.getUserPosition())
                .eq(StringUtils.isNotBlank(userQueryRequestDTO.getEmail()), GovUser::getEmail, userQueryRequestDTO.getEmail())
                .eq(StringUtils.isNotBlank(userQueryRequestDTO.getBelongStructCode()), GovUser::getBelongStructCode, userQueryRequestDTO.getBelongStructCode())
                .eq(StringUtils.isNotBlank(userQueryRequestDTO.getBelongDeptCode()), GovUser::getBelongDeptCode, userQueryRequestDTO.getBelongDeptCode());
        List<GovUser> list = list(wrapper);

        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        List<UserForRoleResponseDTO> userForRoleResponseDTOS = BeanUtil.copyList(list, UserForRoleResponseDTO.class);
        LambdaQueryWrapper<GovStruct> structQryWrapper = new LambdaQueryWrapper<>();
        structQryWrapper.in(GovStruct::getStructCode, userQueryRequestDTO.getBelongStructCode(), userQueryRequestDTO.getBelongDeptCode())
                .eq(GovStruct::getCompanyId, userQueryRequestDTO.getCompanyId());
        Optional<List<GovStruct>> govStructList = Optional.ofNullable(govStructService.list(structQryWrapper));
        Map<String, String> structMap = govStructList.orElse(Lists.newArrayList()).stream().collect(Collectors.toMap(GovStruct::getStructCode, GovStruct::getStructName));
        userForRoleResponseDTOS.forEach(userForRoleResponseDTO -> {
            userForRoleResponseDTO.setUserStatusName(GovUserStatusEnum.getDescByCode(userForRoleResponseDTO.getUserStatus()));
            userForRoleResponseDTO.setBelongStructName(structMap.get(userForRoleResponseDTO.getBelongStructCode()));
            userForRoleResponseDTO.setBelongDeptName(structMap.get(userForRoleResponseDTO.getBelongDeptCode()));

        });
        return BeanUtil.copyList(list, UserForRoleResponseDTO.class);
    }

    @Override
    public PageDTO<UserForRoleResponseDTO> getMemberPageList(GovUserQueryRequestDTO userQueryRequestDTO) {
        LambdaQueryWrapper<GovUser> wrapper = new LambdaQueryWrapper<GovUser>()
                .eq(GovUser::getCompanyId, userQueryRequestDTO.getCompanyId())
                .like(StringUtils.isNotBlank(userQueryRequestDTO.getUserName()), GovUser::getUserName, userQueryRequestDTO.getUserName())
                .eq(StringUtils.isNotBlank(userQueryRequestDTO.getMobile()), GovUser::getMobile, userQueryRequestDTO.getMobile())
                .like(StringUtils.isNotBlank(userQueryRequestDTO.getUserPosition()), GovUser::getUserPosition, userQueryRequestDTO.getUserPosition())
                .eq(StringUtils.isNotBlank(userQueryRequestDTO.getEmail()), GovUser::getEmail, userQueryRequestDTO.getEmail())
                .eq(StringUtils.isNotBlank(userQueryRequestDTO.getBelongStructCode()), GovUser::getBelongStructCode, userQueryRequestDTO.getBelongStructCode())
                .eq(StringUtils.isNotBlank(userQueryRequestDTO.getBelongDeptCode()), GovUser::getBelongDeptCode, userQueryRequestDTO.getBelongDeptCode());
        if (StringUtils.isNotBlank(userQueryRequestDTO.getRoleCode())) {
            RoleUserSimpleRequestDTO roleUserSimpleRequestDTO = new RoleUserSimpleRequestDTO();
            roleUserSimpleRequestDTO.setRoleCode(userQueryRequestDTO.getRoleCode());
            roleUserSimpleRequestDTO.setLoginCompanyId(userQueryRequestDTO.getLoginCompanyId());
            List<String> roleUserList = govUserRoleRelationService.getRoleUserList(roleUserSimpleRequestDTO);
            wrapper.in(GovUser::getUserCode, CollUtil.isNotEmpty(roleUserList) ? roleUserList : Lists.newArrayList("-1"));
        }
        //按照修改时间倒序
        wrapper.orderByDesc(GovUser::getUpdateDate);
        Page<GovUser> page = new Page<>(userQueryRequestDTO.getPage(), userQueryRequestDTO.getPageSize());
        Page<GovUser> list = page(page, wrapper);
        List<UserForRoleResponseDTO> userForRoleResponseDTOS = BeanUtil.copyList(list.getRecords(), UserForRoleResponseDTO.class);
        List<String> structCodes = userForRoleResponseDTOS.stream().map(UserForRoleResponseDTO::getBelongStructCode).collect(Collectors.toList());
        List<String> deptCodes = userForRoleResponseDTOS.stream().map(UserForRoleResponseDTO::getBelongDeptCode).collect(Collectors.toList());
        ArrayList<String> AllStructCodes = Lists.newArrayList();
        AllStructCodes.addAll(structCodes);
        AllStructCodes.addAll(deptCodes);
        LambdaQueryWrapper<GovStruct> structQryWrapper = new LambdaQueryWrapper<>();
        structQryWrapper.eq(GovStruct::getCompanyId, userQueryRequestDTO.getLoginCompanyId());
        structQryWrapper.in(GovStruct::getStructCode, CollUtil.isEmpty(AllStructCodes) ? Lists.newArrayList("-1") : AllStructCodes);
        Optional<List<GovStruct>> govStructList = Optional.ofNullable(govStructService.list(structQryWrapper));
        Map<String, String> structMap = govStructList.orElse(Lists.newArrayList()).stream().collect(Collectors.toMap(GovStruct::getStructCode, GovStruct::getStructName));
        userForRoleResponseDTOS.forEach(userForRoleResponseDTO -> {
            userForRoleResponseDTO.setUserStatusName(GovUserStatusEnum.getDescByCode(userForRoleResponseDTO.getUserStatus()));
            userForRoleResponseDTO.setBelongStructName(structMap.get(userForRoleResponseDTO.getBelongStructCode()));
            userForRoleResponseDTO.setBelongDeptName(structMap.get(userForRoleResponseDTO.getBelongDeptCode()));

        });
        return new PageDTO<>(userQueryRequestDTO.getPage(), userQueryRequestDTO.getPageSize(), (int) list.getTotal(), userForRoleResponseDTOS);
    }


    @Resource
    private GovDriverMapper govDriverMapper;

    /**
     * 用户停用
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean stopGovUser(GovUserStatusChangeReqDTO reqDTO) {
        GovUser govUser = checkUserExists(reqDTO.getLoginCompanyId(), reqDTO.getUserCode());
        // 需要关注司机数据, 如果当前用户是司机角色
        modifyUserStatus(govUser, reqDTO, GovUserStatusEnum.STOP.getCode(), DriverEnum.OfficeStatusEnum.RESIGN);
        return true;

    }

    /**
     * 启用用户
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean enableGovUser(GovUserStatusChangeReqDTO reqDTO) {
        GovUser govUser = checkUserExists(reqDTO.getLoginCompanyId(), reqDTO.getUserCode());
        //校验手机号
        checkMobile(govUser.getMobile(), govUser.getUserId());
        checkEmail(govUser.getEmail(), govUser.getUserId());
        checkCertNo(govUser);
        return modifyUserStatus(govUser, reqDTO, GovUserStatusEnum.NORMAL.getCode(), DriverEnum.OfficeStatusEnum.NORMAL);
    }

    private void checkCertNo(GovUser govUser) {
        if (Objects.equals(govUser.getUserType(), GovUserTypeEnum.DRIVER.getCode())
                || Objects.equals(govUser.getUserType(), GovUserTypeEnum.SOCIAL_DRIVER.getCode())) {
            GovDriver govDriver = govDriverMapper.selectOne(new LambdaQueryWrapper<GovDriver>()
                    .eq(GovDriver::getUserCode, govUser.getUserCode())
                    .eq(GovDriver::getCompanyId, govUser.getCompanyId()));
            if (Objects.isNull(govDriver)) {
                return;
            }
            if (StringUtils.isNotBlank(govDriver.getCertNo())) {
                Long count = govDriverMapper.selectCount(new LambdaQueryWrapper<GovDriver>()
                        .in(GovDriver::getOfficeStatus, Lists.newArrayList(DriverEnum.OfficeStatusEnum.NORMAL.getCode(),
                                DriverEnum.OfficeStatusEnum.HAVE_HOLIDAY.getCode(), DriverEnum.OfficeStatusEnum.ACTIVE_SERVICE.getCode()))
                        .eq(GovDriver::getCertNo, govDriver.getCertNo())
                        .ne(GovDriver::getId, govDriver.getId())
                        .eq(GovDriver::getCompanyId, govUser.getCompanyId()));
                if (Objects.nonNull(count) && count > 0) {
                    throw new ApiException(RestErrorCode.GOV_USER_ID_CARD_USED);
                }
            }

        }

    }

    private Boolean modifyUserStatus(GovUser govUser,
                                     GovUserStatusChangeReqDTO reqDTO,
                                     Integer userStatus,
                                     DriverEnum.OfficeStatusEnum officeStatus) {
        if (Objects.equals(govUser.getUserStatus(), userStatus)) {
            return true;
        }
        //如果是启用，需要判断有效和无效是否重复
        if (Objects.equals(userStatus, GovUserStatusEnum.NORMAL.getCode())) {
            checkMobile(govUser.getMobile(), govUser.getUserId());
            checkEmail(govUser.getEmail(), govUser.getUserId());
        }
        GovUser updateUser = new GovUser();
        updateUser.setUserStatus(userStatus);
        updateUser.setUpdateCode(reqDTO.getLoginUserCode());
        updateUser.setUpdateDate(new Date());
        updateUser.setUpdateName(reqDTO.getLoginUserName());
        LambdaQueryWrapper<GovUser> conditionWrapper = new LambdaQueryWrapper<>();
        conditionWrapper.eq(GovUser::getCompanyId, reqDTO.getLoginCompanyId()).eq(GovUser::getUserCode, reqDTO.getUserCode());
        baseMapper.update(updateUser, conditionWrapper);
        // 修改司机状态
        return modifyDriverStatus(reqDTO.getLoginCompanyId(), reqDTO.getUserCode(), reqDTO.getLoginUserCode(), reqDTO.getLoginUserName(), officeStatus);
    }

    private Boolean modifyDriverStatus(Integer companyId,
                                       String userCode,
                                       String loginUserCode,
                                       String loginUserName,
                                       DriverEnum.OfficeStatusEnum officeStatus) {
        // 如果存在司机角色，则需要修改司机状态
        GovDriver govDriver = govDriverMapper.selectOne(new LambdaQueryWrapper<GovDriver>()
                .eq(GovDriver::getUserCode, userCode).eq(GovDriver::getCompanyId, companyId));
        if (Objects.isNull(govDriver)) {
            return true;
        }
//        GovDriver updateDriver = new GovDriver();
//        updateDriver.setOfficeStatus(officeStatus.getCode());
//        updateDriver.setUpdateCode(loginUserCode);
//        updateDriver.setUpdateTime(new Date());
        // 需要添加企业信息
        LambdaUpdateWrapper<GovDriver> updateDriverWrapper = new LambdaUpdateWrapper<GovDriver>();
        updateDriverWrapper.eq(GovDriver::getId, govDriver.getId());
        updateDriverWrapper.set(GovDriver::getOfficeStatus, officeStatus.getCode());
        updateDriverWrapper.set(GovDriver::getUpdateCode, loginUserCode);
        updateDriverWrapper.set(GovDriver::getUpdateTime, new Date());
        updateDriverWrapper.set(GovDriver::getUpdateName, loginUserName);
        updateDriverWrapper.eq(GovDriver::getCompanyId, companyId);
        govDriverMapper.update(updateDriverWrapper);
        return true;

    }

    private GovUser checkUserExists(Integer companyId, String userCode) {
        LambdaQueryWrapper<GovUser> userQueryWrapper = new LambdaQueryWrapper<>();
        userQueryWrapper.eq(GovUser::getCompanyId, companyId).eq(GovUser::getUserCode, userCode);
        GovUser govUser = baseMapper.selectOne(userQueryWrapper);
        if (Objects.isNull(govUser)) {
            throw new ApiException(RestErrorCode.GOV_USER_NOT_EXIST);
        }
        //
        return govUser;
    }

    public List<GovUserNameDTO> getUserNameEnum(String name, Integer companyId) {
        List<GovUser> govUserList = this.lambdaQuery()
                .like(StringUtils.isNotBlank(name), GovUser::getUserName, name)
                .eq(GovUser::getCompanyId, companyId).last("limit 100").list();
        return govUserList.stream().map(user -> {
            GovUserNameDTO govUserNameDTO = new GovUserNameDTO();
            govUserNameDTO.setUserCode(user.getUserCode());
            govUserNameDTO.setUserName(user.getUserName());
            govUserNameDTO.setMobile(user.getMobile());
            govUserNameDTO.setNameAndMobile(user.getUserName() + "(" + user.getMobile() + ")");
            return govUserNameDTO;
        }).collect(Collectors.toList());
    }

    public PageDTO<GovUserListRespDTO> listGovUser(GovUserListReqDTO reqDTO) {
        if (Objects.isNull(reqDTO.getLoginDataPermType())
                || Objects.equals(reqDTO.getLoginDataPermType(), GovDataPermTypeEnum.SELF.getCode())) {
            return new PageDTO<>(reqDTO.getPage(), reqDTO.getPageSize(), 0, Lists.newArrayList());
        }

        LambdaQueryWrapper<GovUser> userQryWrapper = buildUserQueryWrapper(reqDTO);
        if (Objects.isNull(userQryWrapper)) {
            return new PageDTO<>(reqDTO.getPage(), reqDTO.getPageSize(), 0, Lists.newArrayList());
        }
        IPage<GovUser> userPage = baseMapper.selectPage(new Page<GovUser>(reqDTO.getPage(), reqDTO.getPageSize()), userQryWrapper);
        List<GovUser> govUserList = userPage.getRecords();
        if (CollectionUtils.isEmpty(govUserList)) {
            return new PageDTO<>(reqDTO.getPage(), reqDTO.getPageSize(), 0, Lists.newArrayList());
        }
        List<GovUserListRespDTO> govUserListRespList = BeanUtil.copyList(govUserList, GovUserListRespDTO.class);
        // 填充角色数据
        fillRoleName(govUserListRespList);
        // 填充数据权限
        fillUserDataName(govUserListRespList);
        // 填充部门名称
        fillStructName(govUserListRespList);
        return new PageDTO<>(reqDTO.getPage(), reqDTO.getPageSize(), Integer.parseInt(userPage.getTotal() + ""), govUserListRespList);
    }

    public PageDTO<GovUserDropDownDTO> dropdown(GovUserListReqDTO reqDTO) {
        if (Objects.isNull(reqDTO.getLoginDataPermType())
                || Objects.equals(reqDTO.getLoginDataPermType(), GovDataPermTypeEnum.SELF.getCode())) {
            return new PageDTO<>(reqDTO.getPage(), reqDTO.getPageSize(), 0, Lists.newArrayList());
        }
        LambdaQueryWrapper<GovUser> userQryWrapper = new LambdaQueryWrapper<GovUser>().eq(GovUser::getCompanyId, reqDTO.getLoginCompanyId());
        if (StringUtils.isNotBlank(reqDTO.getUserName())) {
            userQryWrapper.like(GovUser::getUserName, reqDTO.getUserName());
        }
        IPage<GovUser> userPage = baseMapper.selectPage(new Page<GovUser>(reqDTO.getPage(), reqDTO.getPageSize()), userQryWrapper);
        List<GovUser> govUserList = userPage.getRecords();
        if (CollectionUtils.isEmpty(govUserList)) {
            return new PageDTO<>(reqDTO.getPage(), reqDTO.getPageSize(), 0, Lists.newArrayList());
        }
        List<GovUserDropDownDTO> govUserListRespList = BeanUtil.copyList(govUserList, GovUserDropDownDTO.class);
        return new PageDTO<>(reqDTO.getPage(), reqDTO.getPageSize(), Integer.parseInt(userPage.getTotal() + ""), govUserListRespList);
    }

    @Override
    public List<GovOrderUserSearchRespDTO> searchUser(GovOrderUserSearchReqDTO reqDTO) {
        return govUserMapper.searchUser(reqDTO);
    }

    @Override
    public GovOrderUserSearchRespDTO getUserInfo(BaseDTO reqDTO) {
        GovOrderUserSearchRespDTO govOrderUserSearchRespDTO = new GovOrderUserSearchRespDTO();
        govOrderUserSearchRespDTO.setUserCode(reqDTO.getLoginUserCode());
        govOrderUserSearchRespDTO.setUserName(reqDTO.getLoginUserName());
        govOrderUserSearchRespDTO.setUserMobile(reqDTO.getLoginUserMobile());
        govOrderUserSearchRespDTO.setDeptName(reqDTO.getLoginUserBelongDeptName());
        govOrderUserSearchRespDTO.setDeptCode(reqDTO.getLoginUserBelongDeptCode());
        govOrderUserSearchRespDTO.setStructName(reqDTO.getLoginUserBelongStructName());
        govOrderUserSearchRespDTO.setStructCode(reqDTO.getLoginUserBelongStructCode());
        return govOrderUserSearchRespDTO;
    }

    @Override
    public boolean modifyPassword(Integer userId, String password) {
        String salt = RandomStringUtils.randomNumeric(15);
        String encryptedPassword = PasswordUtils.encryptedPassword(password, salt, 1);
        GovUser user = new GovUser();
        user.setSalt(salt);
        user.setLoginPassword(encryptedPassword);
        user.setUserId(userId);
        return this.updateById(user);
    }

    /**
     * 获取部门树的下级编码
     */
    private List<String> filterStructCodeList(String deptCode,
                                              Integer loginDataPermType,
                                              Integer loginCompanyId,
                                              List<String> dataCodeSet) {
        if (dataCodeSet == null) {
            dataCodeSet = Collections.emptyList();
        }
        return govStructService.getStructCodeByParentCode(deptCode, loginDataPermType, loginCompanyId, new HashSet<>(dataCodeSet));
    }

    /**
     * 数组的交集
     */
    private List<String> intersection(List<String> param1, List<String> param2) {
        if (CollectionUtils.isEmpty(param1) || CollectionUtils.isEmpty(param2)) {
            return Collections.emptyList();
        }

        HashSet<String> param1Set = new HashSet<>(param1);
        HashSet<String> param2Set = new HashSet<>(param2);
        param1Set.retainAll(param2Set);

        return new ArrayList<>(param1Set);
    }


    /**
     * 获取用户下的数据权限
     * 改方法只适用于指定部门类型的数据权限
     */

    private Pair<List<String> /*单位类型的部门编码*/, List<String> /*内设部门类型的编码*/> getUserData(Set<String> userDataSet) {

        // 登录态中指定的部门
        if (CollectionUtils.isEmpty(userDataSet)) {

            return Pair.of(Collections.emptyList(), Collections.emptyList());

        }

        //根据部门编码获取部门信息
        List<GovStruct> structList = govStructService.batchGetByCodes(new ArrayList<>(userDataSet));

        return this.filterUnitAndDept(structList);
    }


    /**
     * 拆分内设部门和单位
     */
    private Pair<List<String>, List<String>> filterUnitAndDept(List<GovStruct> structList) {
        //内设部门
        List<String> belongDeptCodeList = structList.stream()
                .filter(struct -> Objects.equals(struct.getStructType(), GovStructTypeEnum.DEPARTMENT.getCode()))
                .map(GovStruct::getStructCode).collect(Collectors.toList());


        //单位
        List<String> belongStructCodeList = structList.stream()
                .filter(struct -> Objects.equals(struct.getStructType(), GovStructTypeEnum.UNIT.getCode()))
                .map(GovStruct::getStructCode).collect(Collectors.toList());

        return Pair.of(belongStructCodeList, belongDeptCodeList);

    }


    /**
     * 获取某个部门下的所有单位和部门
     * 处理用户管理页面左侧的部门树
     *
     * @param structCode 页面左侧部门树，选定的部门编码
     */
    private Pair<List<String>, List<String>> getUnitAndDeptUnderCode(String structCode, Integer companyId) {

        if (StringUtils.isBlank(structCode) || companyId == null || companyId < 0) {

            return Pair.of(Collections.emptyList(), Collections.emptyList());

        }


        List<GovStruct> allChildStruct = govStructService.getAllChildStruct(structCode, companyId);

        return this.filterUnitAndDept(allChildStruct);
    }


    /**
     * 整理 用户列表的数据权限
     *
     * @param reqDTO           登陆信息
     * @param selectStructCode 页面左侧部门树，选定的部门编码
     * @return Pair<List < String> 单位类型的部门编码, List<String> 内设部门类型的编码>
     */
    @Override
    public Pair<List<String>, List<String>> getUserDataCode(BaseDTO reqDTO, String selectStructCode) {
        //数据权限是本人，不允许查询用户列表
        if (Objects.equals(reqDTO.getLoginDataPermType(), GovDataPermTypeEnum.SELF.getCode())) {
            return Pair.of(Collections.emptyList(), Collections.emptyList());
        }

        //处理页面左侧的树
        Pair<List<String> /*单位类型的部门编码*/, List<String> /*内设部门类型的编码*/> unitAndDeptUnderCode =
                this.getUnitAndDeptUnderCode(selectStructCode, reqDTO.getLoginCompanyId());

        //如果是指定部门，获取一下拆分一下指定部门的数据权限
        if (Objects.equals(reqDTO.getLoginDataPermType(), GovDataPermTypeEnum.ASSIGN_STRUCT.getCode())) {

            Pair<List<String>, List<String>> userData = this.getUserData(reqDTO.getDataCodeSet());
            //单位和部门取交集
            List<String> unitList = intersection(unitAndDeptUnderCode.getLeft(), userData.getLeft());
            List<String> deptList = intersection(unitAndDeptUnderCode.getRight(), userData.getRight());
            unitAndDeptUnderCode = Pair.of(unitList, deptList);

        }
        return unitAndDeptUnderCode;
    }


    private LambdaQueryWrapper<GovUser> buildUserQueryWrapper(GovUserListReqDTO reqDTO) {
        //数据权限是本人，不允许查询用户列表
        if (Objects.equals(reqDTO.getLoginDataPermType(), GovDataPermTypeEnum.SELF.getCode())) {
            return null;
        }

        //处理页面左侧的树
        String selectStructCode = reqDTO.getSelectStructCode();

        Pair<List<String> /*单位类型的部门编码*/, List<String> /*内设部门类型的编码*/> unitAndDeptUnderCode =
                this.getUnitAndDeptUnderCode(selectStructCode, reqDTO.getLoginCompanyId());

        //如果是指定部门，获取一下拆分一下指定部门的数据权限
        if (Objects.equals(reqDTO.getLoginDataPermType(), GovDataPermTypeEnum.ASSIGN_STRUCT.getCode())) {

            Pair<List<String>, List<String>> userData = this.getUserData(reqDTO.getDataCodeSet());

            //单位和部门取交集

            List<String> unitList = intersection(unitAndDeptUnderCode.getLeft(), userData.getLeft());

            List<String> deptList = intersection(unitAndDeptUnderCode.getRight(), userData.getRight());

            unitAndDeptUnderCode = Pair.of(unitList, deptList);

        }

        List<String> unitList = unitAndDeptUnderCode.getLeft();

        List<String> deptList = unitAndDeptUnderCode.getRight();

        if (CollectionUtils.isEmpty(unitList) && CollectionUtils.isEmpty(deptList)) {

            log.error("部门编码为空，无法查询");

            return null;
        }

        List<String> userCodeList = Lists.newLinkedList();
        if (CollectionUtils.isNotEmpty(reqDTO.getRoleCodeList())) {
            List<GovUserRoleRelation> roleRelations = govUserRoleRelationMapper.selectList(new LambdaQueryWrapper<GovUserRoleRelation>()
                    .eq(GovUserRoleRelation::getCompanyId, reqDTO.getLoginCompanyId())
                    .in(GovUserRoleRelation::getRoleCode, reqDTO.getRoleCodeList()).select(GovUserRoleRelation::getUserCode));
            if (CollectionUtils.isNotEmpty(roleRelations)) {
                userCodeList.addAll(roleRelations.stream().map(GovUserRoleRelation::getUserCode).distinct().collect(Collectors.toList()));
            }
        }
        LambdaQueryWrapper<GovUser> userQryWrapper = new LambdaQueryWrapper<>();

        if (reqDTO.getUserDataPermType() != null) {
            userQryWrapper.eq(GovUser::getDataPermType, reqDTO.getUserDataPermType());
        }


        //如果是按照数据权限查询，并且选定了某个指定的部门
        if (Objects.equals(reqDTO.getUserDataPermType(), GovDataPermTypeEnum.ASSIGN_STRUCT.getCode())
                && CollectionUtils.isNotEmpty(reqDTO.getUserDataList())) {

            //查询一下当前选定的部门，对应的人员编码
            LambdaQueryWrapper<GovDataRelation> relationWrapper = Wrappers.lambdaQuery(GovDataRelation.class)
                    .in(GovDataRelation::getRelatedCode, reqDTO.getUserDataList())
                    .eq(GovDataRelation::getCompanyId, reqDTO.getLoginCompanyId())
                    .eq(GovDataRelation::getRelationType, GovDataPermTypeEnum.ASSIGN_STRUCT.getCode());

            List<GovDataRelation> relations = govDataRelationMapper.selectList(relationWrapper);

            if (CollectionUtils.isEmpty(relations)) {

                //无需查询数据
                return null;

            }

            List<String> userCodesByRelation = relations.stream()
                    .map(GovDataRelation::getBussCode).collect(Collectors.toList());

            userCodeList.addAll(userCodesByRelation);
        }

        if (CollectionUtils.isNotEmpty(userCodeList)) {
            userQryWrapper.in(GovUser::getUserCode, userCodeList.stream().distinct().collect(Collectors.toList()));
        }

        userQryWrapper.eq(GovUser::getCompanyId, reqDTO.getLoginCompanyId());
        if (StringUtils.isNotBlank(reqDTO.getUserName())) {
            userQryWrapper.like(GovUser::getUserName, reqDTO.getUserName());
        }

        if (Objects.nonNull(reqDTO.getUserType())) {
            userQryWrapper.eq(GovUser::getUserType, reqDTO.getUserType());
        }
        if (StringUtils.isNotBlank(reqDTO.getUserPosition())) {
            userQryWrapper.eq(GovUser::getUserPosition, reqDTO.getUserPosition());
        }
        if (Objects.nonNull(reqDTO.getUserStatus())) {
            userQryWrapper.eq(GovUser::getUserStatus, reqDTO.getUserStatus());
        }

        //手机号
        if (StringUtils.isNotBlank(reqDTO.getMobile())) {
            userQryWrapper.eq(GovUser::getMobile, reqDTO.getMobile());
        }
        //邮箱
        if (StringUtils.isNotBlank(reqDTO.getEmail())) {
            userQryWrapper.eq(GovUser::getEmail, reqDTO.getEmail());
        }

        //所属单位
        if (StringUtils.isNotBlank(reqDTO.getBelongDeptCode())) {
            userQryWrapper.eq(GovUser::getBelongDeptCode, reqDTO.getBelongDeptCode());
        }

        //所属部门
        if (StringUtils.isNotBlank(reqDTO.getBelongStructCode())) {
            userQryWrapper.eq(GovUser::getBelongStructCode, reqDTO.getBelongStructCode());
        }


        if (CollectionUtils.isNotEmpty(unitList) && CollectionUtils.isNotEmpty(deptList)) {

            userQryWrapper.and(wrapper ->
                    wrapper.in(
                                    GovUser::getBelongDeptCode, unitList).or()
                            .in(GovUser::getBelongStructCode, deptList));

        } else if (CollectionUtils.isNotEmpty(unitList)) {

            userQryWrapper.in(GovUser::getBelongDeptCode, unitList);

        } else if (CollectionUtils.isNotEmpty(deptList)) {

            userQryWrapper.in(GovUser::getBelongStructCode, deptList);
        }
        userQryWrapper.in(GovUser::getUserType, Lists.newArrayList(GovUserTypeEnum.NORMAL.getCode(), GovUserTypeEnum.DRIVER.getCode()));
        userQryWrapper.orderByDesc(GovUser::getUpdateDate);
        return userQryWrapper;
    }

    private void fillStructName(List<GovUserListRespDTO> govUserList) {
        if (CollectionUtils.isEmpty(govUserList)) {
            return;
        }
        List<String> structCodeList = Lists.newArrayList();
        structCodeList.addAll(govUserList.stream().map(GovUserListRespDTO::getBelongStructCode).filter(StringUtils::isNotBlank).collect(Collectors.toList()));
        structCodeList.addAll(govUserList.stream().map(GovUserListRespDTO::getBelongDeptCode).filter(StringUtils::isNotBlank).collect(Collectors.toList()));
        if (structCodeList.isEmpty()) {
            return;
        }

        List<GovStruct> govStructList = govStructMapper.selectList(new LambdaQueryWrapper<GovStruct>()
                .eq(GovStruct::getCompanyId, govUserList.get(0).getCompanyId())
                .in(GovStruct::getStructCode, structCodeList));
        Map<String, GovStruct> structMap = govStructList.stream().collect(Collectors.toMap(GovStruct::getStructCode, Function.identity(), (k1, k2) -> k1));
        govUserList.forEach(user -> {
            if (StringUtils.isNotBlank(user.getBelongStructCode())) {
                GovStruct struct = structMap.get(user.getBelongStructCode());
                if (Objects.nonNull(struct)) {
                    user.setBelongStructName(struct.getStructName());
                }
            }
            if (StringUtils.isNotBlank(user.getBelongDeptCode())) {
                GovStruct struct = structMap.get(user.getBelongDeptCode());
                if (Objects.nonNull(struct)) {
                    user.setBelongDeptName(struct.getStructName());
                }
            }
        });
    }


    public void fillUserDataName(List<GovUserListRespDTO> govUserListRespList) {
        Integer companyId = govUserListRespList.get(0).getCompanyId();
        List<String> userCodeList = govUserListRespList.stream().map(GovUserListRespDTO::getUserCode).collect(Collectors.toList());
        List<GovDataRelation> govStaffRoleRelations = govDataRelationMapper.selectList(new LambdaQueryWrapper<GovDataRelation>()
                .eq(GovDataRelation::getCompanyId, companyId)
                .in(GovDataRelation::getBussCode, userCodeList));
        if (CollectionUtils.isEmpty(govStaffRoleRelations)) {
            return;
        }
        Map<String, Integer> relationTypeMap =
                govStaffRoleRelations.stream().collect(Collectors.toMap(GovDataRelation::getBussCode, GovDataRelation::getRelationType, (r1, r2) -> r1));
        Map<String, List<GovDataRelation>> relationMap =
                govStaffRoleRelations.stream().collect(Collectors.groupingBy(GovDataRelation::getBussCode));
        Map<String, GovStruct> assignStructMap = getAssignStructMap(govStaffRoleRelations);
        govUserListRespList.forEach(user -> {
            Integer relationType = relationTypeMap.get(user.getUserCode());
            if (Objects.isNull(relationType)) {
                return;
            }
            user.setUserDataPermType(relationType);
            if (Objects.equals(relationType, GovDataPermTypeEnum.SELF.getCode())) {
                user.setUserDataNameListStr(GovDataPermTypeEnum.SELF.getDesc());
            } else if (Objects.equals(relationType, GovDataPermTypeEnum.COMPANY.getCode())) {
                user.setUserDataNameListStr(GovDataPermTypeEnum.COMPANY.getDesc());
            } else if (Objects.equals(relationType, GovDataPermTypeEnum.ASSIGN_STRUCT.getCode())) {
                List<GovDataRelation> relations = relationMap.get(user.getUserCode());
                if (CollectionUtils.isEmpty(relations)) {
                    return;
                }
                String structNameListStr = relations.stream().map(relation ->
                        assignStructMap.get(relation.getRelatedCode()).getStructName()).collect(Collectors.joining(","));
                user.setUserDataNameListStr(structNameListStr);
            }
        });
    }

    private Map<String, GovStruct> getAssignStructMap(List<GovDataRelation> govStaffRoleRelations) {
        List<GovDataRelation> assignStructRelations = govStaffRoleRelations.stream().filter(relation ->
                Objects.equals(relation.getRelationType(), GovDataPermTypeEnum.ASSIGN_STRUCT.getCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(assignStructRelations)) {
            return Maps.newHashMap();
        }
        Integer companyId = assignStructRelations.get(0).getCompanyId();
        List<String> structCodeList = assignStructRelations.stream().map(GovDataRelation::getRelatedCode).distinct().collect(Collectors.toList());

        List<GovStruct> govStructList = govStructMapper.selectList(
                new LambdaQueryWrapper<GovStruct>().eq(GovStruct::getCompanyId, companyId).in(GovStruct::getStructCode, structCodeList));
        return govStructList.stream().collect(Collectors.toMap(GovStruct::getStructCode, Function.identity(), (s1, s2) -> s1));
    }


    public void fillRoleName(List<GovUserListRespDTO> govUserListRespList) {

        Integer companyId = govUserListRespList.get(0).getCompanyId();
        List<String> userCodeList = govUserListRespList.stream().map(GovUserListRespDTO::getUserCode).collect(Collectors.toList());

        List<GovUserRoleRelation> govStaffRoleRelations = govUserRoleRelationMapper.selectList(new LambdaQueryWrapper<GovUserRoleRelation>()
                .eq(GovUserRoleRelation::getCompanyId, companyId)
                .in(GovUserRoleRelation::getUserCode, userCodeList));
        if (CollectionUtils.isEmpty(govStaffRoleRelations)) {
            return;
        }
        List<String> roleCodeList =
                govStaffRoleRelations.stream().map(GovUserRoleRelation::getRoleCode).distinct().collect(Collectors.toList());
        List<GovRole> govRoleList = Lists.newArrayList();
        List<GovRole> govCustomRoleList = govRoleMapper.selectList(new LambdaQueryWrapper<GovRole>()
                .eq(GovRole::getCompanyId, companyId)
                .ne(GovRole::getRoleType, RoleTypeEnum.SYSTEM.getCode())
                .eq(GovRole::getRoleStatus, RoleStatusEnum.VALID.getCode())
                .in(GovRole::getRoleCode, roleCodeList));
        govRoleList.addAll(govCustomRoleList);
        List<GovRole> govSystemRoleList = govRoleMapper.selectList(new LambdaQueryWrapper<GovRole>()
                .eq(GovRole::getRoleType, RoleTypeEnum.SYSTEM.getCode())
                .eq(GovRole::getRoleStatus, RoleStatusEnum.VALID.getCode())
                .in(GovRole::getRoleCode, roleCodeList));
        govRoleList.addAll(govSystemRoleList);
        if (CollectionUtils.isEmpty(govRoleList)) {
            return;
        }
        Map<String, List<GovUserRoleRelation>> staffRoleMap = govStaffRoleRelations.stream().collect(Collectors.groupingBy(GovUserRoleRelation::getUserCode));
        Map<String, GovRole> roleMap =
                govRoleList.stream().collect(Collectors.toMap(GovRole::getRoleCode, Function.identity(), (v1, v2) -> v1));
        govUserListRespList.forEach(user -> {
            List<GovUserRoleRelation> relationList = staffRoleMap.get(user.getUserCode());
            if (CollectionUtils.isEmpty(relationList)) {
                return;
            }
            List<String> userRoleCodeList = relationList.stream().map(GovUserRoleRelation::getRoleCode).collect(Collectors.toList());
            List<String> userRoleNameList = userRoleCodeList.stream()
                    .filter(roleMap::containsKey)
                    .map(roleCode -> roleMap.get(roleCode).getRoleName())
                    .collect(Collectors.toList());
            user.setRoleCodeList(userRoleCodeList);
            user.setRoleNameListStr(StringUtils.join(userRoleNameList, ","));
        });
    }

    @Override
    public List<UserSimpleResponseDTO> getUserSelect(UserQueryRequestDTO userQueryRequestDTO) {
        LambdaQueryWrapper<GovUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(GovUser::getUserType, userQueryRequestDTO.getUserType());
        queryWrapper.eq(GovUser::getCompanyId, userQueryRequestDTO.getLoginCompanyId());
        queryWrapper.like(StringUtils.isNotBlank(userQueryRequestDTO.getUserName()), GovUser::getUserName, userQueryRequestDTO.getUserName());
        queryWrapper.eq(Objects.nonNull(userQueryRequestDTO.getUserStatus()), GovUser::getUserStatus, userQueryRequestDTO.getUserStatus());
        queryWrapper.last("LIMIT 100");
        List<GovUser> list = list(queryWrapper);
        return BeanUtil.copyList(list, UserSimpleResponseDTO.class);
    }

    public UserRoleAndPermRespDTO getRoleAndPermForCreate(GetRoleAndPermForCreateReqDTO reqDTO) {
        UserRoleAndPermRespDTO respDTO = new UserRoleAndPermRespDTO();
        if (Objects.nonNull(reqDTO.getBuildDataPerm()) && reqDTO.getBuildDataPerm()) {
            List<UserDataPermRespDTO> userDataPermList = buildDataPermList(reqDTO.getCompanyId());
            respDTO.setUserDataPermList(userDataPermList);
        }
        if (Objects.nonNull(reqDTO.getBuildRolePerm()) && reqDTO.getBuildRolePerm()) {
            List<UserRoleInfoRespDTO> userRoleInfoList = buildRoleInfoList(reqDTO.getCompanyId());
            respDTO.setUserRoleInfoList(userRoleInfoList);
        }
        return respDTO;
    }

    @Override
    public List<GovUser> getUserByUserCodeList(List<String> userCodeList) {
        return list(new LambdaQueryWrapper<GovUser>().in(GovUser::getUserCode, userCodeList));
    }

    @Override
    public PageDTO<CustomerStructInfoDTO> queryCustomerByPage(CustomerQueryReqDTO customerQueryReqDTO) {
        LambdaQueryWrapper<GovUser> wrapper = new LambdaQueryWrapper<>();

        if (StringUtils.isNotBlank(customerQueryReqDTO.getCustomerName()) || StringUtils.isNotBlank(customerQueryReqDTO.getMobile())) {

            wrapper.like(StringUtils.isNotBlank(customerQueryReqDTO.getCustomerName()), GovUser::getUserName, customerQueryReqDTO.getCustomerName());

            wrapper.like(StringUtils.isNotBlank(customerQueryReqDTO.getMobile()), GovUser::getMobile, customerQueryReqDTO.getMobile());

        } else if (StringUtils.isNotBlank(customerQueryReqDTO.getNameOrMobile())) {

            // 判断是否为手机号
            boolean isLikelyMobile = customerQueryReqDTO.getNameOrMobile().matches("\\d+");

            if (isLikelyMobile) {
                wrapper.like(GovUser::getMobile, customerQueryReqDTO.getNameOrMobile());
            } else {
                wrapper.like(GovUser::getUserName, customerQueryReqDTO.getNameOrMobile());
            }

        }

        wrapper.eq(GovUser::getCompanyId, customerQueryReqDTO.getLoginCompanyId());
        wrapper.eq(customerQueryReqDTO.getCustomerStatus() != null, GovUser::getUserStatus, customerQueryReqDTO.getCustomerStatus());


        Page<GovUser> page = new Page<>(customerQueryReqDTO.getPageNo(), customerQueryReqDTO.getPageSize());
        Page<GovUser> govUserPage = this.page(page, wrapper);
        List<GovUser> records = govUserPage.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return new PageDTO<>(customerQueryReqDTO.getPageNo(), customerQueryReqDTO.getPageSize(), (int) govUserPage.getTotal(), Collections.emptyList());
        }
        List<CustomerStructInfoDTO> customerStructInfoDTOS = new ArrayList<>();
        records.forEach(govUser -> {
            CustomerStructInfoDTO customerStructInfoDTO = new CustomerStructInfoDTO();
            customerStructInfoDTO.setCustomerId(govUser.getUserId());
            customerStructInfoDTO.setCustomerName(govUser.getUserName());
            customerStructInfoDTO.setNameAndMobile(govUser.getUserName() + "(" + govUser.getMobile() + ")");
            customerStructInfoDTO.setCustomerCode(govUser.getUserCode());
            customerStructInfoDTO.setMobile(govUser.getMobile());
            customerStructInfoDTO.setCustomerStatus(govUser.getUserStatus().byteValue());
            customerStructInfoDTOS.add(customerStructInfoDTO);
        });
        return new PageDTO<>(customerQueryReqDTO.getPageNo(), customerQueryReqDTO.getPageSize(), (int) govUserPage.getTotal(), customerStructInfoDTOS);
    }


    private List<UserDataPermRespDTO> buildDataPermList(Integer companyId) {
        // 初始化数据
        List<UserDataPermRespDTO> userDataPermList = initUserDataPermList();
        UserDataPermRespDTO assignStructPerm = userDataPermList.stream().filter(userDataPerm ->
                Objects.equals(userDataPerm.getDataPermType(), GovDataPermTypeEnum.ASSIGN_STRUCT.getCode())).collect(Collectors.toList()).get(0);
        assignStructPermProcess(Lists.newArrayList(), assignStructPerm, companyId);
        return userDataPermList;
    }

    private List<UserRoleInfoRespDTO> buildRoleInfoList(Integer companyId) {
        // 查询所有有效角色
        List<GovRole> govRoleList = this.getAllValidRoleList(companyId);
        if (CollectionUtils.isEmpty(govRoleList)) {
            return Lists.newArrayList();
        }
        Set<String> notAllowRoleCodeSet = getNotAllowRoleCodeSet();
        List<UserRoleInfoRespDTO> userRoleInfoList = new ArrayList<>(govRoleList.size());
        govRoleList.forEach(govRole -> {
            UserRoleInfoRespDTO userRoleInfoRespDTO = new UserRoleInfoRespDTO();
            userRoleInfoRespDTO.setRoleCode(govRole.getRoleCode());
            userRoleInfoRespDTO.setRoleName(govRole.getRoleName());
            userRoleInfoRespDTO.setRoleType(govRole.getRoleType());
            userRoleInfoRespDTO.setRoleTypeDesc(RoleTypeEnum.getDescByCode(govRole.getRoleType()));
            userRoleInfoRespDTO.setChecked(false);
            // 普通员工和司机角色不允许修改
            userRoleInfoRespDTO.setDisabled(notAllowRoleCodeSet.contains(govRole.getRoleCode()));
            userRoleInfoList.add(userRoleInfoRespDTO);
        });
        return userRoleInfoList;
    }

    @Override
    public List<GovOrderUserSearchRespDTO> searchUserV2(GovOrderUserV2SearchReqDTO reqDTO) {
        return govUserMapper.searchUserV2(reqDTO);
    }

    @Override
    public void fillInInfo(List<GovMessageTrackingSenderDTO> govMessageTrackingSenderDTO) {
        Set<String> receiverCodeSet = govMessageTrackingSenderDTO.get(0).getReceiverCodeSet();
        LambdaQueryWrapper<GovUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(GovUser::getUserCode, receiverCodeSet);
        List<GovUserDTO> govUserDTOS = BeanUtil.copyList(govUserMapper.selectList(wrapper), GovUserDTO.class);
        //调用消息接口，将消息发送
        List<GovMessageTrackingSenderReqDTO> collect = govMessageTrackingSenderDTO.stream().map(r -> {
            GovMessageTrackingSenderReqDTO govMessageTrackingSenderReqDTO = new GovMessageTrackingSenderReqDTO();
            govMessageTrackingSenderReqDTO.setGovUserDTOS(govUserDTOS);
            govMessageTrackingSenderReqDTO.setContent(r.getContent());
            govMessageTrackingSenderReqDTO.setRecordNo(r.getRecordNo());
            govMessageTrackingSenderReqDTO.setReceiverCodeSet(receiverCodeSet);
            govMessageTrackingSenderReqDTO.setMobileSet(r.getMobileSet());
            return govMessageTrackingSenderReqDTO;
        }).collect(Collectors.toList());
        messageTrackingSender.sendMessageTrackingLog(collect);
    }

    @Override
    public List<GovOrderUserSearchRespDTO> searchPassengers(GovOrderPassengersSearchReqDTO reqDTO) {
        return govUserMapper.searchPassengers(reqDTO);
    }
}




