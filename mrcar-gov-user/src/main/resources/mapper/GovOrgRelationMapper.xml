<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mrcar.gov.user.mapper.GovOrgRelationMapper">

    <resultMap id="BaseResultMap" type="com.mrcar.gov.user.domain.GovOrgRelation">
        <id property="id" column="id" jdbcType="INTEGER"/>
        <result property="createCode" column="create_code" jdbcType="VARCHAR"/>
        <result property="createName" column="create_name" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="companyName" column="company_name" jdbcType="VARCHAR"/>
        <result property="companyId" column="company_id" jdbcType="INTEGER"/>
        <result property="orgNo" column="org_no" jdbcType="VARCHAR"/>
        <result property="orgType" column="org_type" jdbcType="INTEGER"/>
        <result property="orgName" column="org_name" jdbcType="VARCHAR"/>
        <result property="orgStatus" column="org_status" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,create_code,create_name,
        create_time,company_name,company_id,
        org_no,org_type,org_name,
        org_status
    </sql>

    <select id="getListByCondition" resultType="com.mrcar.gov.common.dto.user.resp.GovOrgRelationRespDTO"
            parameterType="com.mrcar.gov.common.dto.user.req.GovOrgRelationListReq">
        select
        gor.org_no as orgNo,
        gor.org_name as orgName,
        gor.org_type as orgType,
        godr.dept_name as deptName,
        godr.id as id,
        godr.create_time as createTime,
        godr.create_dept as createDept,
        godr.create_name as createName
        from gov_org_relation gor left join gov_org_info goi on gor.org_no = goi.org_no left join
        gov_org_dept_relation godr on gor.org_no = godr.org_no
        <where>
            goi.org_status = 1
            <if test="req.orgName != null and req.orgName!=''">
                and gor.org_name like CONCAT('%',#{req.orgName,jdbcType=VARCHAR},'%')
            </if>
            <if test="req.cooperateStatus != null and req.cooperateStatus == 0 ">
                and goi.contract_end_date &lt; DATE_SUB(NOW(), INTERVAL 1 DAY)
            </if>
            <if test="req.cooperateStatus != null and req.cooperateStatus == 1 ">
                and goi.contract_end_date &gt; DATE_SUB(NOW(), INTERVAL 1 DAY)
            </if>
            <if test="req.companyId != null">
                and gor.company_id = #{req.companyId}
                and godr.company_id = #{req.companyId}
                and goi.company_id = #{req.companyId}
            </if>
            <if test="req.orgType != null">
                and gor.org_type = #{req.orgType}
            </if>
            <if test="req.deptCodeList != null and req.deptCodeList.size() != 0">
                and godr.dept_code in
                <foreach collection="req.deptCodeList" item="deptCode" open="(" separator="," close=")">
                    #{deptCode}
                </foreach>
            </if>
            <if test="req.orgContactorPhone != null and req.orgContactorPhone!=''">
                and goi.org_contactor_phone = #{req.orgContactorPhone}
            </if>
        </where>
    </select>
</mapper>
