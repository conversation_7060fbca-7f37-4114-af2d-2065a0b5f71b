<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mrcar.gov.user.mapper.GovOrgInfoMapper">

    <resultMap id="BaseResultMap" type="com.mrcar.gov.user.domain.GovOrgInfo">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="createCode" column="create_code" jdbcType="VARCHAR"/>
            <result property="createName" column="create_name" jdbcType="VARCHAR"/>
            <result property="createDeptCode" column="create_dept_code" jdbcType="VARCHAR"/>
            <result property="createDeptName" column="create_dept_name" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateCode" column="update_code" jdbcType="VARCHAR"/>
            <result property="updateName" column="update_name" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="companyName" column="company_name" jdbcType="VARCHAR"/>
            <result property="companyId" column="company_id" jdbcType="INTEGER"/>
            <result property="orgNo" column="org_no" jdbcType="VARCHAR"/>
            <result property="orgName" column="org_name" jdbcType="VARCHAR"/>
            <result property="logoUrl" column="logo_url" jdbcType="VARCHAR"/>
            <result property="orgStatus" column="org_status" jdbcType="INTEGER"/>
            <result property="orgType" column="org_type" jdbcType="INTEGER"/>
            <result property="orgProvinceCode" column="org_province_code" jdbcType="VARCHAR"/>
            <result property="orgProvinceName" column="org_province_name" jdbcType="VARCHAR"/>
            <result property="orgCityCode" column="org_city_code" jdbcType="VARCHAR"/>
            <result property="orgCityName" column="org_city_name" jdbcType="VARCHAR"/>
            <result property="orgAddressDetail" column="org_address_detail" jdbcType="VARCHAR"/>
            <result property="longitude" column="longitude" jdbcType="DECIMAL"/>
            <result property="latitude" column="latitude" jdbcType="DECIMAL"/>
            <result property="addressName" column="address_name" jdbcType="VARCHAR"/>
            <result property="orgContactorName" column="org_contactor_name" jdbcType="VARCHAR"/>
            <result property="orgContactorPhone" column="org_contactor_phone" jdbcType="VARCHAR"/>
            <result property="orgContactorCode" column="org_contactor_code" jdbcType="VARCHAR"/>
            <result property="contractBeginDate" column="contract_begin_date" jdbcType="DATE"/>
            <result property="contractEndDate" column="contract_end_date" jdbcType="DATE"/>
            <result property="invoiceType" column="invoice_type" jdbcType="INTEGER"/>
            <result property="invoiceOpenType" column="invoice_open_type" jdbcType="INTEGER"/>
            <result property="invoiceRate" column="invoice_rate" jdbcType="DECIMAL"/>
            <result property="creditCode" column="credit_code" jdbcType="VARCHAR"/>
            <result property="openBank" column="open_bank" jdbcType="VARCHAR"/>
            <result property="bankAccount" column="bank_account" jdbcType="VARCHAR"/>
            <result property="serviceTelephone" column="service_telephone" jdbcType="VARCHAR"/>
            <result property="awardedAreaCode" column="awarded_area_code" jdbcType="VARCHAR"/>
            <result property="awardedAreaName" column="awarded_area_name" jdbcType="VARCHAR"/>
            <result property="evaluationScore" column="evaluation_score" jdbcType="DECIMAL"/>
            <result property="evaluationNums" column="evaluation_nums" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,create_code,create_name,
        create_dept_code,create_dept_name,create_time,
        update_code,update_name,update_time,
        company_name,company_id,org_no,
        org_name,logo_url,org_status,
        org_type,org_province_code,org_province_name,
        org_city_code,org_city_name,org_address_detail,
        longitude,latitude,address_name,
        org_contactor_name,org_contactor_phone,org_contactor_code,
        contract_begin_date,contract_end_date,invoice_type,
        invoice_open_type,invoice_rate,credit_code,
        open_bank,bank_account,service_telephone,
        awarded_area_code,awarded_area_name,evaluation_score,
        evaluation_nums
    </sql>

    <select id="getListByConditionForPC" resultType="com.mrcar.gov.common.dto.user.resp.GovOrgInfoListRespDTO"
            parameterType="com.mrcar.gov.common.dto.user.req.GovOrgInfoListReq">
        select
        goi.id as id,
        goi.org_no as orgNo,
        goi.org_name as orgName,
        goi.awarded_area_name as awardedAreaName,
        goi.org_province_code as orgProvinceCode,
        goi.org_province_name as orgProvinceName,
        goi.org_city_code as orgCityCode,
        goi.org_city_name as orgCityName,
        gmge.support_repair as supportRepair,
        gmge.support_accident_repair as supportAccidentRepair,
        gmge.support_car_type as supportCarType,
        goi.service_telephone as serviceTelephone,
        goi.org_contactor_name as orgContactorName,
        goi.org_contactor_phone as orgContactorPhone,
        goi.org_contactor_code as orgContactorCode,
        DATE_FORMAT(goi.contract_begin_date, '%Y-%m-%d') as contractBeginDate,
        DATE_FORMAT(goi.contract_end_date, '%Y-%m-%d') as contractEndDate,
        goi.org_status as orgStatus,
        goi.org_address_detail as orgAddressDetail,
        goi.org_type as orgType,
        goi.update_name as updateName,
        goi.update_time as updateTime,
        goi.create_name as createName,
        goi.create_time as createTime,
        goi.awarded_area_name as awardedAreaName,
        goi.create_dept_name as createDeptName
        from gov_org_info goi left join gov_maintain_garage_extend gmge on goi.org_no = gmge.org_no
        <where>
            goi.company_id = #{req.loginCompanyId}
            <if test="req.orgContactorCode != null and req.orgContactorCode!=''">
                and goi.org_contactor_code = #{req.orgContactorCode}
            </if>
            <if test="req.orgName != null and req.orgName!=''">
                and goi.org_name like CONCAT('%',#{req.orgName,jdbcType=VARCHAR},'%')
            </if>
            <if test="req.orgNo!= null and req.orgNo!=''">
                and goi.org_no =#{req.orgNo}
            </if>
            <if test="req.supportRepair != null">
                and gmge.support_repair=#{req.supportRepair}
            </if>
            <if test="req.supportAccidentRepair != null">
                and gmge.support_accident_repair=#{req.supportAccidentRepair}
            </if>
            <if test="req.supportCarType != null and req.supportCarType!=''">
                <choose>
                    <when test="req.supportCarType.contains(','.toString())">
                        <foreach collection="req.supportCarType.split(',')" item="item" index="index" open=" "
                                 close=" ">
                            and FIND_IN_SET(#{item},gmge.support_car_type)
                        </foreach>
                    </when>
                    <otherwise>
                        and FIND_IN_SET(#{req.supportCarType},gmge.support_car_type)
                    </otherwise>
                </choose>
            </if>
            <if test="req.orgStatus != null">
                and goi.org_status =#{req.orgStatus}
            </if>
            <if test="req.orgProvinceCode != null and req.orgProvinceCode!=''">
                and goi.org_province_code = #{req.orgProvinceCode,jdbcType=VARCHAR}
            </if>
            <if test="req.orgProvinceName != null and req.orgProvinceName !=''">
                and goi.org_province_name = #{req.orgProvinceName,jdbcType=VARCHAR}
            </if>
            <if test="req.orgCityCode != null and req.orgCityCode !=''">
                and goi.org_city_code = #{req.orgCityCode,jdbcType=VARCHAR}
            </if>
            <if test="req.orgCityName != null and req.orgCityName !=''">
                and goi.org_city_name = #{req.orgCityName,jdbcType=VARCHAR}
            </if>
            <if test="req.createName != null and req.createName !=''">
                and goi.create_name = #{req.createName,jdbcType=VARCHAR}
            </if>
            <if test="req.createCode != null and req.createCode !=''">
                and goi.create_code = #{req.createCode,jdbcType=VARCHAR}
            </if>
            <if test="req.orgContactorName != null and req.orgContactorName !=''">
                and goi.org_contactor_name = #{req.orgContactorName,jdbcType=VARCHAR}
            </if>
            <if test="req.orgContactorPhone != null and req.orgContactorPhone !=''">
                and goi.org_contactor_phone = #{req.orgContactorPhone,jdbcType=VARCHAR}
            </if>
            <if test="req.awardedAreaCode != null and req.awardedAreaCode !=''">
                and goi.awarded_area_code = #{req.awardedAreaCode}
            </if>
            <if test="req.createTimeBegin != null  and req.createTimeBegin!=''">
                and goi.create_time <![CDATA[>=]]> #{req.createTimeBegin}
            </if>
            <if test="req.createTimeEnd != null  and req.createTimeEnd!=''">
                and goi.create_time <![CDATA[<=]]> #{req.createTimeEnd}
            </if>
            <if test="req.createDeptCode != null and req.createDeptCode !=''">
                and goi.create_dept_code = #{req.createDeptCode}
            </if>
            <if test="req.awardedAreaCode != null and req.awardedAreaCode !=''">
                and goi.awarded_area_code = #{req.awardedAreaCode}
            </if>
            <if test="req.companyId != null">
                and goi.company_id = #{req.companyId}
            </if>
            <if test="req.orgType != null">
                and goi.org_type = #{req.orgType}
            </if>
        </where>
        order by goi.update_time desc
    </select>


    <select id="selectPageListForApplet"
            parameterType="com.mrcar.gov.common.dto.asset.maintenance.req.GovMaintainAppletListReq"
            resultType="com.mrcar.gov.common.dto.asset.maintenance.resp.GovMaintainGarageAppletRespDTO">
        select id,org_no garageNo,org_name garageName,evaluation_score evaluationScore,evaluation_nums
        evaluationNums,service_telephone serviceTelephone,org_contactor_phone contactorPhone,org_contactor_name
        contactorName,
        org_address_detail addressDetail,longitude,latitude,
        ROUND(ST_Distance_Sphere(POINT(#{req.longitude},#{req.latitude}), POINT(longitude, latitude)), 2) AS distance
        from gov_org_info
        <where>
            <if test="req.garageName != null and req.garageName!=''">
                and org_name like CONCAT('%',#{req.garageName},'%')
            </if>
            <if test="req.garageCityCode != null and req.garageCityCode !=''">
                and org_city_code = #{req.garageCityCode}
            </if>
            <if test="req.loginCompanyId != null and req.loginCompanyId !=''">
                and company_id=#{req.loginCompanyId}
            </if>
            and org_status = 1 and org_type = 1
        </where>
        <choose>
            <when test="req.sortField == 1">
                ORDER BY distance ASC
            </when>
            <otherwise>
                ORDER BY evaluation_score DESC
            </otherwise>
        </choose>
    </select>

    <select id="traverse" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include>
            from gov_org_info
        where id &gt; #{startId}
        order by id
        limit #{rows}
    </select>
</mapper>
