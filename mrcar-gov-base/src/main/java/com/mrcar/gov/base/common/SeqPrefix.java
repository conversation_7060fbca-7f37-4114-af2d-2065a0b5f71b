package com.mrcar.gov.base.common;

/**
 * <AUTHOR>
 * @description: 序列号前缀
 * @date 2024/11/7 14:57
 */
public class SeqPrefix {


    /**
     * 角色编码前缀
     */
    public static final String GOV_ROLE_CODE = "ROLE";

    public static final String GOV_VEHICLE_NO = "VEH";

    public static final String GOV_DEVICE_CODE = "DSM";

    //保单编码前缀
    public static final String GOV_WARRANTY_CODE = "BXD";
    /**
     * 车辆 -处置调拨
     */
    public static final String GOV_VEHICLE_DISPOSAL = "CZDB";
    /**
     * 车辆 -处置调拨
     */
    public static final String GOV_VEHICLE_CONFIG_UPDATE = "PBGX";

    /**
     * 车辆处置明细
     */
    public static final String GOV_VEHICLE_DISPOSAL_DETAIL = "CZMX";

    /**
     * 自定义流程编号
     */
    public static final String CUSTOM_WORKFLOW_PROCESS_NUMBER = "LC";


    /**
     * 消息管理-消息模版编号前缀
     */
    public static final String GOV_MSG_TEMPLATE_NUMBER = "TP";

    /**
     * 消息管理-公文消息编码前缀
     */
    public static final String GOV_MSG_NOTICE_NO_PREFIX = "AN";


    // 消息推送记录编号前缀
    public static final String GOV_MSG_PUSH_RECORD_NO_PREFIX = "MPR";

    // 消息推送批次号前缀
    public static final String GOV_MSG_PUSH_BATCH_NO_PREFIX = "MPB";


    // 消息推送日志编号前缀
    public static final String GOV_MSG_PUSH_LOG_NO_PREFIX = "MPL";

    // 消息推送日志编号前缀
    public static final String GOV_WORK_BASE_NO_PREFIX = "REP";
    //维保事故单
    public static final String GOV_ACCIDENT_PREFIX = "SG";

    // 行程单编号
    public static final String GOV_IOT_TRAVEL_PREFIX = "LT";


    //分时计价配置快照前缀
    public static final String GOV_TIME_SHARE_CONFIG_SNAPSHOT_PREFIX = "TSCS";

    //流程模型编号前缀
    public static final String GOV_WORKFLOW_MODEL_PREFIX = "WFM";

}
