package com.mrcar.gov.iot.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 车辆报警消息通知记录表
 * @TableName gov_car_warn_msg_notice_record
 */
@TableName(value ="gov_car_warn_msg_notice_record")
@Deprecated
public class GovCarWarnMsgNoticeRecord implements Serializable {
    /**
     * 记录id
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 围栏id(快照id)
     */
    private Integer fenceId;

    /**
     * 车辆车牌号
     */
    private String vehicleLicense;

    /**
     * 车辆车架号
     */
    private String vehicleVin;

    /**
     * 报警唯一编号(所有记录均生成报警编号，如果有任务，可以在围栏报警表中找到对应记录)
     */
    private String warnSn;

    /**
     * 报警类型；
     */
    private Integer warnType;

    /**
     * 通知类型：1公务车订单通知
     */
    private Integer noticeType;

    /**
     * 报警状态：1开始报警 2结束报警
     */
    private Integer warnStatus;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 完成时间
     */
    private Date finishTime;

    /**
     * 回栏id(快照id)
     */
    private Integer backFenceId;

    /**
     * 报警设备号
     */
    private String deviceNo;

    /**
     * 设备类型；1：有线；2：无线
     */
    private Integer deviceType;

    /**
     * 经度
     */
    private BigDecimal longitude;

    /**
     * 纬度
     */
    private BigDecimal latitude;

    /**
     * 报警位置
     */
    private String warnAddress;

    /**
     * 结束报警经度
     */
    private BigDecimal endLongitude;

    /**
     * 结束报警纬度
     */
    private BigDecimal endLatitude;

    /**
     * 报警位置
     */
    private String endWarnAddress;

    /**
     * 订单里程（公里）
     */
    private BigDecimal tripMileage;

    /**
     * 报警消息类型 1-有任务订单 2-无任务订单
     */
    private Integer warnNoticeType;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 记录id
     */
    public Integer getId() {
        return id;
    }

    /**
     * 记录id
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 围栏id(快照id)
     */
    public Integer getFenceId() {
        return fenceId;
    }

    /**
     * 围栏id(快照id)
     */
    public void setFenceId(Integer fenceId) {
        this.fenceId = fenceId;
    }

    /**
     * 车辆车牌号
     */
    public String getVehicleLicense() {
        return vehicleLicense;
    }

    /**
     * 车辆车牌号
     */
    public void setVehicleLicense(String vehicleLicense) {
        this.vehicleLicense = vehicleLicense;
    }

    /**
     * 车辆车架号
     */
    public String getVehicleVin() {
        return vehicleVin;
    }

    /**
     * 车辆车架号
     */
    public void setVehicleVin(String vehicleVin) {
        this.vehicleVin = vehicleVin;
    }

    /**
     * 报警唯一编号(所有记录均生成报警编号，如果有任务，可以在围栏报警表中找到对应记录)
     */
    public String getWarnSn() {
        return warnSn;
    }

    /**
     * 报警唯一编号(所有记录均生成报警编号，如果有任务，可以在围栏报警表中找到对应记录)
     */
    public void setWarnSn(String warnSn) {
        this.warnSn = warnSn;
    }

    /**
     * 报警类型；
     */
    public Integer getWarnType() {
        return warnType;
    }

    /**
     * 报警类型；
     */
    public void setWarnType(Integer warnType) {
        this.warnType = warnType;
    }

    /**
     * 通知类型：1公务车订单通知
     */
    public Integer getNoticeType() {
        return noticeType;
    }

    /**
     * 通知类型：1公务车订单通知
     */
    public void setNoticeType(Integer noticeType) {
        this.noticeType = noticeType;
    }

    /**
     * 报警状态：1开始报警 2结束报警
     */
    public Integer getWarnStatus() {
        return warnStatus;
    }

    /**
     * 报警状态：1开始报警 2结束报警
     */
    public void setWarnStatus(Integer warnStatus) {
        this.warnStatus = warnStatus;
    }

    /**
     * 开始时间
     */
    public Date getStartTime() {
        return startTime;
    }

    /**
     * 开始时间
     */
    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    /**
     * 完成时间
     */
    public Date getFinishTime() {
        return finishTime;
    }

    /**
     * 完成时间
     */
    public void setFinishTime(Date finishTime) {
        this.finishTime = finishTime;
    }

    /**
     * 回栏id(快照id)
     */
    public Integer getBackFenceId() {
        return backFenceId;
    }

    /**
     * 回栏id(快照id)
     */
    public void setBackFenceId(Integer backFenceId) {
        this.backFenceId = backFenceId;
    }

    /**
     * 报警设备号
     */
    public String getDeviceNo() {
        return deviceNo;
    }

    /**
     * 报警设备号
     */
    public void setDeviceNo(String deviceNo) {
        this.deviceNo = deviceNo;
    }

    /**
     * 设备类型；1：有线；2：无线
     */
    public Integer getDeviceType() {
        return deviceType;
    }

    /**
     * 设备类型；1：有线；2：无线
     */
    public void setDeviceType(Integer deviceType) {
        this.deviceType = deviceType;
    }

    /**
     * 经度
     */
    public BigDecimal getLongitude() {
        return longitude;
    }

    /**
     * 经度
     */
    public void setLongitude(BigDecimal longitude) {
        this.longitude = longitude;
    }

    /**
     * 纬度
     */
    public BigDecimal getLatitude() {
        return latitude;
    }

    /**
     * 纬度
     */
    public void setLatitude(BigDecimal latitude) {
        this.latitude = latitude;
    }

    /**
     * 报警位置
     */
    public String getWarnAddress() {
        return warnAddress;
    }

    /**
     * 报警位置
     */
    public void setWarnAddress(String warnAddress) {
        this.warnAddress = warnAddress;
    }

    /**
     * 结束报警经度
     */
    public BigDecimal getEndLongitude() {
        return endLongitude;
    }

    /**
     * 结束报警经度
     */
    public void setEndLongitude(BigDecimal endLongitude) {
        this.endLongitude = endLongitude;
    }

    /**
     * 结束报警纬度
     */
    public BigDecimal getEndLatitude() {
        return endLatitude;
    }

    /**
     * 结束报警纬度
     */
    public void setEndLatitude(BigDecimal endLatitude) {
        this.endLatitude = endLatitude;
    }

    /**
     * 报警位置
     */
    public String getEndWarnAddress() {
        return endWarnAddress;
    }

    /**
     * 报警位置
     */
    public void setEndWarnAddress(String endWarnAddress) {
        this.endWarnAddress = endWarnAddress;
    }

    /**
     * 订单里程（公里）
     */
    public BigDecimal getTripMileage() {
        return tripMileage;
    }

    /**
     * 订单里程（公里）
     */
    public void setTripMileage(BigDecimal tripMileage) {
        this.tripMileage = tripMileage;
    }

    /**
     * 报警消息类型 1-有任务订单 2-无任务订单
     */
    public Integer getWarnNoticeType() {
        return warnNoticeType;
    }

    /**
     * 报警消息类型 1-有任务订单 2-无任务订单
     */
    public void setWarnNoticeType(Integer warnNoticeType) {
        this.warnNoticeType = warnNoticeType;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        GovCarWarnMsgNoticeRecord other = (GovCarWarnMsgNoticeRecord) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getFenceId() == null ? other.getFenceId() == null : this.getFenceId().equals(other.getFenceId()))
            && (this.getVehicleLicense() == null ? other.getVehicleLicense() == null : this.getVehicleLicense().equals(other.getVehicleLicense()))
            && (this.getVehicleVin() == null ? other.getVehicleVin() == null : this.getVehicleVin().equals(other.getVehicleVin()))
            && (this.getWarnSn() == null ? other.getWarnSn() == null : this.getWarnSn().equals(other.getWarnSn()))
            && (this.getWarnType() == null ? other.getWarnType() == null : this.getWarnType().equals(other.getWarnType()))
            && (this.getNoticeType() == null ? other.getNoticeType() == null : this.getNoticeType().equals(other.getNoticeType()))
            && (this.getWarnStatus() == null ? other.getWarnStatus() == null : this.getWarnStatus().equals(other.getWarnStatus()))
            && (this.getStartTime() == null ? other.getStartTime() == null : this.getStartTime().equals(other.getStartTime()))
            && (this.getFinishTime() == null ? other.getFinishTime() == null : this.getFinishTime().equals(other.getFinishTime()))
            && (this.getBackFenceId() == null ? other.getBackFenceId() == null : this.getBackFenceId().equals(other.getBackFenceId()))
            && (this.getDeviceNo() == null ? other.getDeviceNo() == null : this.getDeviceNo().equals(other.getDeviceNo()))
            && (this.getDeviceType() == null ? other.getDeviceType() == null : this.getDeviceType().equals(other.getDeviceType()))
            && (this.getLongitude() == null ? other.getLongitude() == null : this.getLongitude().equals(other.getLongitude()))
            && (this.getLatitude() == null ? other.getLatitude() == null : this.getLatitude().equals(other.getLatitude()))
            && (this.getWarnAddress() == null ? other.getWarnAddress() == null : this.getWarnAddress().equals(other.getWarnAddress()))
            && (this.getEndLongitude() == null ? other.getEndLongitude() == null : this.getEndLongitude().equals(other.getEndLongitude()))
            && (this.getEndLatitude() == null ? other.getEndLatitude() == null : this.getEndLatitude().equals(other.getEndLatitude()))
            && (this.getEndWarnAddress() == null ? other.getEndWarnAddress() == null : this.getEndWarnAddress().equals(other.getEndWarnAddress()))
            && (this.getTripMileage() == null ? other.getTripMileage() == null : this.getTripMileage().equals(other.getTripMileage()))
            && (this.getWarnNoticeType() == null ? other.getWarnNoticeType() == null : this.getWarnNoticeType().equals(other.getWarnNoticeType()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getFenceId() == null) ? 0 : getFenceId().hashCode());
        result = prime * result + ((getVehicleLicense() == null) ? 0 : getVehicleLicense().hashCode());
        result = prime * result + ((getVehicleVin() == null) ? 0 : getVehicleVin().hashCode());
        result = prime * result + ((getWarnSn() == null) ? 0 : getWarnSn().hashCode());
        result = prime * result + ((getWarnType() == null) ? 0 : getWarnType().hashCode());
        result = prime * result + ((getNoticeType() == null) ? 0 : getNoticeType().hashCode());
        result = prime * result + ((getWarnStatus() == null) ? 0 : getWarnStatus().hashCode());
        result = prime * result + ((getStartTime() == null) ? 0 : getStartTime().hashCode());
        result = prime * result + ((getFinishTime() == null) ? 0 : getFinishTime().hashCode());
        result = prime * result + ((getBackFenceId() == null) ? 0 : getBackFenceId().hashCode());
        result = prime * result + ((getDeviceNo() == null) ? 0 : getDeviceNo().hashCode());
        result = prime * result + ((getDeviceType() == null) ? 0 : getDeviceType().hashCode());
        result = prime * result + ((getLongitude() == null) ? 0 : getLongitude().hashCode());
        result = prime * result + ((getLatitude() == null) ? 0 : getLatitude().hashCode());
        result = prime * result + ((getWarnAddress() == null) ? 0 : getWarnAddress().hashCode());
        result = prime * result + ((getEndLongitude() == null) ? 0 : getEndLongitude().hashCode());
        result = prime * result + ((getEndLatitude() == null) ? 0 : getEndLatitude().hashCode());
        result = prime * result + ((getEndWarnAddress() == null) ? 0 : getEndWarnAddress().hashCode());
        result = prime * result + ((getTripMileage() == null) ? 0 : getTripMileage().hashCode());
        result = prime * result + ((getWarnNoticeType() == null) ? 0 : getWarnNoticeType().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", fenceId=").append(fenceId);
        sb.append(", vehicleLicense=").append(vehicleLicense);
        sb.append(", vehicleVin=").append(vehicleVin);
        sb.append(", warnSn=").append(warnSn);
        sb.append(", warnType=").append(warnType);
        sb.append(", noticeType=").append(noticeType);
        sb.append(", warnStatus=").append(warnStatus);
        sb.append(", startTime=").append(startTime);
        sb.append(", finishTime=").append(finishTime);
        sb.append(", backFenceId=").append(backFenceId);
        sb.append(", deviceNo=").append(deviceNo);
        sb.append(", deviceType=").append(deviceType);
        sb.append(", longitude=").append(longitude);
        sb.append(", latitude=").append(latitude);
        sb.append(", warnAddress=").append(warnAddress);
        sb.append(", endLongitude=").append(endLongitude);
        sb.append(", endLatitude=").append(endLatitude);
        sb.append(", endWarnAddress=").append(endWarnAddress);
        sb.append(", tripMileage=").append(tripMileage);
        sb.append(", warnNoticeType=").append(warnNoticeType);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}