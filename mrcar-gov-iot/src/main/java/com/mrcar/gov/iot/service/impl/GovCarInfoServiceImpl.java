package com.mrcar.gov.iot.service.impl;


import java.math.BigDecimal;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;

import com.mrcar.gov.common.dto.iot.resp.GdTravelDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.izu.framework.exception.ApiException;
import com.izu.framework.resp.InfoCode;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.util.BeanUtil;
import com.mrcar.gov.common.constant.tsdb.BceConstants;
import com.mrcar.gov.common.dto.car.CarLocationTrailPageReqDTO;
import com.mrcar.gov.common.dto.device.req.CarLocationTrailReqDTO;
import com.mrcar.gov.common.dto.device.resp.BdTravelDTO;
import com.mrcar.gov.common.dto.device.resp.DeviceHistoryTraceDTO;
import com.mrcar.gov.common.dto.device.resp.GovVehicleStatusDTO;
import com.mrcar.gov.common.dto.device.resp.LocationsDetailDTO;
import com.mrcar.gov.common.dto.device.resp.PointDetailDTO;
import com.mrcar.gov.common.dto.iot.TravelDTO;
import com.mrcar.gov.common.dto.iot.resp.CarStatusVo;
import com.mrcar.gov.common.dto.tbox.TboxLogReqDTO;
import com.mrcar.gov.common.enums.device.CoordinateEnum;
import com.mrcar.gov.common.enums.device.GovGpsDeviceEnum;
import com.mrcar.gov.common.util.DateUtils;
import com.mrcar.gov.common.util.JsonUtil;
import com.mrcar.gov.common.util.LbsLocationUtil;
import com.mrcar.gov.iot.service.CarLocationService;
import com.mrcar.gov.iot.service.CarnetExecutorService;
import com.mrcar.gov.iot.service.GovCarInfoService;
import com.mrcar.gov.iot.service.GovDeviceMongoService;
import com.mrcar.iot.domain.GovVehicleLocation;
import com.mrcar.iot.domain.GpsDeviceMeta;

import cn.hutool.core.collection.CollUtil;

@Service
public class GovCarInfoServiceImpl implements GovCarInfoService{
    private static final Logger logger = LoggerFactory.getLogger(GovCarInfoServiceImpl.class);

	@Resource
	private GovDeviceMongoService govDeviceTraceService;
	
	@Autowired
	private CarnetExecutorService carnetExecutorService;
	
	@Autowired
	private CarLocationService carLocationService;
	
	@Autowired
	private GovDeviceMongoService govDeviceMongoService;
	
//    private int CAR_LOCATION_FILTER_COUNT=50;
    @NacosValue(value = "${gps.filter.count}", autoRefreshed = true)
    private Integer carLocationFilterCount;
	@Override
	public DeviceHistoryTraceDTO queryHistoryTrace(CarLocationTrailReqDTO carLocationTrailReqDTO) throws Exception {
		
		
		logger.info("msg=查询轨迹数据,,method=queryHistoryTrace,,carLocationTrailReqDTO={}", JSONObject.toJSONString(carLocationTrailReqDTO));

		Integer deviceType = GovGpsDeviceEnum.DeviceTypeEnum.VIDEO.getCode();
        String deviceId = carLocationTrailReqDTO.getSimNo();
        String type = carLocationTrailReqDTO.getDeviceType();
        String cardNo=carLocationTrailReqDTO.getCarNo();
        if(StringUtils.isNotBlank(type)){
            carLocationTrailReqDTO.setDeviceType(type);
        }else {
            carLocationTrailReqDTO.setDeviceType(String.valueOf(deviceType));
        }
        String coordinate = carLocationTrailReqDTO.getCoordinate();
        if(StringUtils.isBlank(coordinate)){
            carLocationTrailReqDTO.setCoordinate("BAIDU");
        }else {
            carLocationTrailReqDTO.setCoordinate(coordinate);
        }

        Date start = new Date();
        logger.info("msg1=查询轨迹数据,,method=queryHistoryTrace,,startTime={}",start.getTime());
        //替换为查mongo
        BdTravelDTO travelDTO = null;
		try {
			travelDTO = getBdTravelDTO(carLocationTrailReqDTO);
		} catch (Exception e) {
			logger.error("msg=获取getBdTravelDTO异常,,method=getHistoryTrace,,exception={}",e);
			e.printStackTrace();
			throw e;
		}
		
		DeviceHistoryTraceDTO deviceHistoryTraceDTO = null;
        if(travelDTO!=null) {
        	deviceHistoryTraceDTO = BeanUtil.copyObject(travelDTO, DeviceHistoryTraceDTO.class);
            deviceHistoryTraceDTO.setVehicleLicense(cardNo);
            deviceHistoryTraceDTO.setDeviceId(deviceId);
            if(CollUtil.isEmpty(travelDTO.getPointList())){
                deviceHistoryTraceDTO.setPointList(null);
                return deviceHistoryTraceDTO;
            }
            deviceHistoryTraceDTO.setPointList(BeanUtil.copyList(travelDTO.getPointList(), PointDetailDTO.class));
            deviceHistoryTraceDTO.setTrackTime(DateUtils.getHourMinutes(deviceHistoryTraceDTO.getEndCreateDate(), deviceHistoryTraceDTO.getStartCreateDate()));
            deviceHistoryTraceDTO.setDistanceDesc(String.valueOf(deviceHistoryTraceDTO.getDistance()));
            if(!Objects.equals(type,deviceType.toString())){
                return deviceHistoryTraceDTO;
            }
        }
        Date endTime = new Date();

        logger.info("msg1=查询轨迹数据,,method=queryHistoryTrace,,endTime={},,excecTime={}",endTime.getTime(),(endTime.getTime()-start.getTime()));

		return deviceHistoryTraceDTO;
	}
	
	@Override
	public BdTravelDTO getBdTravelDTO(CarLocationTrailReqDTO carLocationTrailReqDTO) throws Exception {
		  BdTravelDTO bdTravelDTO = null;
		 carLocationTrailReqDTO.setDistinct(true);
	     carLocationTrailReqDTO.setStopInfo(true);
		
	     String carNo = carLocationTrailReqDTO.getCarNo();
	     String deviceType = carLocationTrailReqDTO.getDeviceType();
	     Date beginCreateDate = DateUtils.format2Date(carLocationTrailReqDTO.getBeginCreateDate(), DateUtils.TIME_FORMAT);
	     Date endCreateDate = DateUtils.format2Date(carLocationTrailReqDTO.getEndCreateDate(), DateUtils.TIME_FORMAT);

		//如果选到未来的时间，以当前时间为准
		if(Objects.nonNull(endCreateDate) && endCreateDate.after(new Date())){
			endCreateDate =  new Date();
		}

	     String deviceId = carLocationTrailReqDTO.getSimNo();
	     String vehicleVin = carLocationTrailReqDTO.getVehicleVin();
	     Boolean sampling =  Objects.isNull(carLocationTrailReqDTO.getSampling()) ? false : carLocationTrailReqDTO.getSampling();
	     Integer sample = Objects.isNull(carLocationTrailReqDTO.getSample()) ? 10 : carLocationTrailReqDTO.getSample();
	     Boolean distinct = carLocationTrailReqDTO.getDistinct();
	     Boolean stopInfo = carLocationTrailReqDTO.getStopInfo();
	     String coordinate = carLocationTrailReqDTO.getCoordinate();
	     Integer locationFlag = null;
	     Boolean containSt = false;
	     //查询的最长时间跨度不能超过{0}天 TODO
	     Long diffDay = DateUtils.getDistanceTimeDays(beginCreateDate,endCreateDate);
	     if(diffDay>30) {
	    	 throw new ApiException(InfoCode.HTTP_PARAM_INVALID,"最长时间跨度大于30天");
	     }
	     if (StringUtils.isNotBlank(coordinate) && CoordinateEnum.getMsg(coordinate) == null) {
	    	 throw new ApiException(InfoCode.HTTP_PARAM_INVALID,"坐标系不支持");
         }
	     //无线不能算停车点，不准
         stopInfo = GovGpsDeviceEnum.DeviceTypeEnum.WIFI.getCode()==Integer.valueOf(deviceType)?false:stopInfo;
         List<LocationsDetailDTO> carLocations = trailShowCommon(carNo, deviceType, beginCreateDate, endCreateDate, deviceId, vehicleVin, sampling, sample, distinct, coordinate,stopInfo,locationFlag, carLocationTrailReqDTO);
         logger.info("msg=查询轨迹返回carLocations,,method=getBdTravelDTO,,carLocations.size={}",carLocations!=null?carLocations.size():null);
         if(!carLocations.isEmpty()) {
             LocationsDetailDTO firstLoc = carLocations.get(0);
             if(stopInfo) {
                 carLocationService.getLastStopInfo(carLocations,carLocationTrailReqDTO);
                 //需要停车点的情况下，是单线程获取,无需二次排序
                 //计算停车时长
                 carLocationService.calculateForLbsTaril(carLocations);
             }
             
             TravelDTO travelInfo = carLocationService.getTravelInfo(deviceType,coordinate,carLocations,beginCreateDate,containSt,firstLoc);
             
             bdTravelDTO = BeanUtil.copyObject(travelInfo, BdTravelDTO.class);
         }
		return bdTravelDTO;
	}



	@Override
	public GdTravelDTO getGdTravelDTO(CarLocationTrailReqDTO carLocationTrailReqDTO) throws Exception {
		GdTravelDTO gdTravelDTO = null;
		carLocationTrailReqDTO.setDistinct(true);
		carLocationTrailReqDTO.setStopInfo(true);

		String carNo = carLocationTrailReqDTO.getCarNo();
		String deviceType = carLocationTrailReqDTO.getDeviceType();
		Date beginCreateDate = DateUtils.format2Date(carLocationTrailReqDTO.getBeginCreateDate(), DateUtils.TIME_FORMAT);
		Date endCreateDate = DateUtils.format2Date(carLocationTrailReqDTO.getEndCreateDate(), DateUtils.TIME_FORMAT);

		//如果选到未来的时间，以当前时间为准
		if(Objects.nonNull(endCreateDate) && endCreateDate.after(new Date())){
			endCreateDate =  new Date();
		}

		String deviceId = carLocationTrailReqDTO.getSimNo();
		String vehicleVin = carLocationTrailReqDTO.getVehicleVin();
		Boolean sampling =  Objects.isNull(carLocationTrailReqDTO.getSampling()) ? false : carLocationTrailReqDTO.getSampling();
		Integer sample = Objects.isNull(carLocationTrailReqDTO.getSample()) ? 10 : carLocationTrailReqDTO.getSample();
		Boolean distinct = carLocationTrailReqDTO.getDistinct();
		Boolean stopInfo = carLocationTrailReqDTO.getStopInfo();
		String coordinate = carLocationTrailReqDTO.getCoordinate();
		Integer locationFlag = null;
		Boolean containSt = false;
		//查询的最长时间跨度不能超过{0}天 TODO
		Long diffDay = DateUtils.getDistanceTimeDays(beginCreateDate,endCreateDate);
		if(diffDay>30) {
			throw new ApiException(InfoCode.HTTP_PARAM_INVALID,"最长时间跨度大于30天");
		}
		if (StringUtils.isNotBlank(coordinate) && CoordinateEnum.getMsg(coordinate) == null) {
			throw new ApiException(InfoCode.HTTP_PARAM_INVALID,"坐标系不支持");
		}
		//无线不能算停车点，不准
		stopInfo = GovGpsDeviceEnum.DeviceTypeEnum.WIFI.getCode()==Integer.valueOf(deviceType)?false:stopInfo;
		List<LocationsDetailDTO> carLocations = trailShowCommon(carNo, deviceType, beginCreateDate, endCreateDate, deviceId, vehicleVin, sampling, sample, distinct, coordinate,stopInfo,locationFlag, carLocationTrailReqDTO);
		logger.info("msg=查询轨迹返回carLocations,,method=getGdTravelDTO,,carLocations.size={}",carLocations!=null?carLocations.size():null);
		if(!carLocations.isEmpty()) {
			LocationsDetailDTO firstLoc = carLocations.get(0);
			if(stopInfo) {
				carLocationService.getLastStopInfo(carLocations,carLocationTrailReqDTO);
				//需要停车点的情况下，是单线程获取,无需二次排序
				//计算停车时长
				carLocationService.calculateForLbsTaril(carLocations);
			}

			TravelDTO travelInfo = carLocationService.getTravelInfo(deviceType,coordinate,carLocations,beginCreateDate,containSt,firstLoc);

			logger.info("msg=轨迹信息,,method=getGdTravelDTO,,travelInfo={}",JSONObject.toJSONString(travelInfo));

			gdTravelDTO = BeanUtil.copyObject(travelInfo, GdTravelDTO.class);
		}
		return gdTravelDTO;
	}
	
	public List<LocationsDetailDTO> trailShowCommon(String carNo,String deviceType,
            Date beginCreateDate,Date endCreateDate,
            String deviceId,String vehicleVin,boolean sampling,
            Integer sample,boolean distinct,String coordinate,boolean stopInfo,Integer locationFlag,CarLocationTrailReqDTO carLocationTrailReqDTO) {
			//设置采样频率
			final int sampleSeconds = (sample!=null?sample:this.getSampling(beginCreateDate,endCreateDate));
			List<LocationsDetailDTO> carLocationList = new ArrayList<>();
			//如果需要停车信息,不能用多线程处理,有排序问题
			if(stopInfo) {
			   return this.trail(carNo,deviceType,beginCreateDate,endCreateDate,deviceId,vehicleVin,sampling,sample,distinct,coordinate,stopInfo,locationFlag, carLocationTrailReqDTO);
			}
			//无需开线程处理
			int threadSize = 1;
			if(threadSize==1) {
				return this.trail(carNo,deviceType,beginCreateDate,endCreateDate,deviceId,vehicleVin,sampling,sample,distinct,coordinate,stopInfo,locationFlag,carLocationTrailReqDTO);
			}
			//多线程处理，需要排序
		return carLocationList;
  }
	
	
	 //返回采集间隔 百度云使用目前为了不报错，先默认返回10
    private int getSampling(Date beginCreateDate,Date endCreateDate) {
        int sampleValue = 10;
        return sampleValue;
    }
    
    private int getDays( Date beginCreateDate,Date endCreateDate) {
        return (int)DateUtils.getDiffSeconds(beginCreateDate,endCreateDate)/60/60/24;
    }
    
    private List<LocationsDetailDTO> trail(String carNo,String deviceType,Date beginCreateDate,Date endCreateDate,
        String deviceId,String vehicleVin,boolean sampling,Integer sample,boolean distinct,String coordinate,boolean stopInfo,Integer locationFlag,CarLocationTrailReqDTO carLocationTrailReqDTO) {
		List<LocationsDetailDTO> list = new ArrayList<>();
		try {
			//此处替换为从mongo查询轨迹列表 list = carLocationTsdb.listDataPoints(carLocation,carLocationVo);
			// 统计查询时间
			long queryStart = System.currentTimeMillis();
			List<GovVehicleLocation> trackList =  govDeviceTraceService.queryTrackList(carLocationTrailReqDTO);
			logger.info("msg=查询轨迹返回数量,,method=GovCarInfoServiceImpl.trail,,carLocationTrailReqDTO={},,trackList.size={}",JsonUtil.toJson(carLocationTrailReqDTO),trackList!=null?trackList.size():null);
	        logger.info("查询轨迹统计分析, start : {}, end : {}, size : {}, cost : {}",
					carLocationTrailReqDTO.getBeginCreateDate(),
					carLocationTrailReqDTO.getEndCreateDate(),
					Optional.ofNullable(trackList).map(List::size).orElse(0),
					TimeUnit.MILLISECONDS.toSeconds(System.currentTimeMillis() - queryStart));
			//基于mongo查出轨迹封装返回对象
	        if(trackList!=null) {
	        	for(GovVehicleLocation deviceTrackData:trackList) {
	        	    Date timestamp = deviceTrackData.getTimestamp();
	        	    GpsDeviceMeta deviceMeta = deviceTrackData.getDeviceMeta();
	        	    String simNo = deviceMeta.getSimNo();
	        	    
	        	    BigDecimal longitude = deviceTrackData.getLongitude();//高德经度
	        	    BigDecimal latitude = deviceTrackData.getLatitude();//高德维度
	        	    BigDecimal latBaidu = deviceTrackData.getLatBaidu();
	        	    BigDecimal lngBaidu = deviceTrackData.getLngBaidu();
	        		//经度,84坐标")
	        	    BigDecimal longitudeMgs84 = deviceTrackData.getLongitudeMgs84();
	        		//维度,84坐标")
	        	    BigDecimal latitudeMgs84 = deviceTrackData.getLatitudeMgs84();
	        	    Integer elevation = deviceTrackData.getElevation();
	        	    Integer snr = deviceTrackData.getSnr();
	        	    Integer pAcc = deviceTrackData.getPAcc();
	        	    Integer speed = deviceTrackData.getSpeed();
	        	    Integer locationFlag2 = deviceTrackData.getLocationFlag();
	        	    Integer direction = deviceTrackData.getDirection();
	        	    Integer deviceType2 = deviceMeta.getDeviceType();
	        	    String deviceTypeMsg = GovGpsDeviceEnum.DeviceTypeEnum.getMsg(deviceType2);
	        	    /** 方向角描述  */
	        	    String manfactCode = "";
	        	    String locationName = "";
	        	    Boolean  stopPoint=false;
	        	    Integer  stopDuration=0;
	        	    String   stopDurationDetail = "";
	        	    String  locationFlagName = "";
	        	    
	        		LocationsDetailDTO locationsDetailDTO = new LocationsDetailDTO();
	        		locationsDetailDTO.setLongitude(longitude);
	        		locationsDetailDTO.setLatitude(latitude);
	        		locationsDetailDTO.setLngBaidu(lngBaidu);
	        		locationsDetailDTO.setLatBaidu(latBaidu);
	        		locationsDetailDTO.setLngWgs84(longitudeMgs84);
	        		locationsDetailDTO.setLatWgs84(latitudeMgs84);
	        		locationsDetailDTO.setSpeed(new BigDecimal(speed));
	        		locationsDetailDTO.setDirection(direction);
	        		locationsDetailDTO.setDirectionMsg(locationsDetailDTO.getDirectionMsg());
	        		locationsDetailDTO.setSnr(snr);
	        		locationsDetailDTO.setCreateDate(timestamp);
	        		locationsDetailDTO.setCarNo(carNo);
	        		locationsDetailDTO.setDeviceId(simNo);
	        		locationsDetailDTO.setVehicleVin(deviceMeta.getVehicleVin());
	        		locationsDetailDTO.setManfactCode(manfactCode);
	        		locationsDetailDTO.setDeviceType(String.valueOf(deviceType2));
	        		locationsDetailDTO.setDeviceTypeMsg(deviceTypeMsg);
	        		locationsDetailDTO.setLocationName(locationName);
	        		locationsDetailDTO.setLocationFlag(locationFlag);
	        		locationsDetailDTO.setLocationFlagName(locationFlagName);
	        		locationsDetailDTO.setStopPoint(stopPoint);
	        		locationsDetailDTO.setStopDuration(stopDuration);
	        		locationsDetailDTO.setStopDurationDetail(stopDurationDetail);
	        		
	        		list.add(locationsDetailDTO);
	        	}
	        }
	        
//			if(!deviceType.equals(String.valueOf(GovGpsDeviceEnum.DeviceTypeEnum.WIFI.getCode()))){
//				//修正定位
//				logger.info("msg=轨迹查询修正前数据量,,method=GovCarInfoServiceImpl.trail,,list.size={}",list.size());
//				list = locationsFilter(list,beginCreateDate,carNo,deviceId);
//				logger.info("msg=轨迹查询修正后数据量,,method=GovCarInfoServiceImpl.trail,,list.size={}",list.size());
//			}
			return list;
		}catch (Exception e) {
			logger.error("车辆轨迹回溯carNo={},出现异常",carNo,e);
		}
	  return  list;
   }

    /**
     * 针对定位进行过滤，飘点定位，重复停车点定位
     * @param list
     * @param beginCreateDate 时间范围的开始时间，计算停车时长
     * @param carNo
     * @param deviceId
     */
    public List<LocationsDetailDTO> locationsFilter(List<LocationsDetailDTO> list,Date beginCreateDate,String carNo,String deviceId){
        //针对停车点进行修正
    	logger.info("msg=针对停车点进行修正前,,method=locationsFilter,,list.size={}",list!=null?list.size():null);
    	LbsLocationUtil.compareLocation(list);
    	logger.info("msg=针对停车点进行修正后,,method=locationsFilter,,list.size={}",list!=null?list.size():null);
        //去除重复停车点（相同停车点保留停车开始点），计算停车时长，
    	logger.info("msg=去除重复停车点修正前,,method=locationsFilter,,list.size={}",list!=null?list.size():null);
    	LbsLocationUtil.distinceStopPoint(list);
    	logger.info("msg=去除重复停车点修正后,,method=locationsFilter,,list.size={}",list!=null?list.size():null);
        //停车时长小于3分钟的停车点更改为非停车点,保留停车点和行驶点
    	logger.info("msg=停车时长小于3分钟的停车点更改为非停车点,保留停车点和行驶点修正前,,method=locationsFilter,,list.size={}",list!=null?list.size():null);
    	LbsLocationUtil.calculateStopDuration(list, beginCreateDate);
    	logger.info("msg=停车时长小于3分钟的停车点更改为非停车点,保留停车点和行驶点修正后,,method=locationsFilter,,list.size={}",list!=null?list.size():null);
        //行驶点和静止点纠正，删除漂移点，重新累计停车时长
    	
        int beforeSize = list.size();
        //此处修改了 list引用
    	logger.info("msg=过滤次数限制修正前,,method=locationsFilter,,beforeSize={},,carLocationFilterCount={}",beforeSize,carLocationFilterCount);
        list = LbsLocationUtil.locationFilter(list,carLocationFilterCount);
        logger.info("msg=过滤次数限制修正后,,method=locationsFilter,,list.size={},,carLocationFilterCount={}",list!=null?list.size():null,carLocationFilterCount);
        int afterSize = list.size();
        if(afterSize < beforeSize){
            logger.warn("车牌号={},sim号={}发生定位漂移，请关注,,method=locationsFilter",carNo,deviceId);
            //针对停车点进行修正
            logger.info("msg=针对停车点进行修正修正前,,method=locationsFilter,,list.size={}",list!=null?list.size():null);
            LbsLocationUtil.compareLocation(list);
            logger.info("msg=针对停车点进行修正修正后,,method=locationsFilter,,list.size={}",list!=null?list.size():null);
            //纠正完成后的数据修复，删除重复的停车点
            logger.info("msg=纠正完成后的数据修复，删除重复的停车点修正前,,method=locationsFilter,,list.size={}",list!=null?list.size():null);
            LbsLocationUtil.distinceStopPoint(list);
            logger.info("msg=纠正完成后的数据修复，删除重复的停车点修正后,,method=locationsFilter,,list.size={}",list!=null?list.size():null);
            //纠正完成后的数据重新计算停车时长
            logger.info("msg=纠正完成后的数据重新计算停车时长修正前,,method=locationsFilter,,list.size={}",list!=null?list.size():null);
            LbsLocationUtil.calculateStopDuration(list, beginCreateDate);
            logger.info("msg=纠正完成后的数据重新计算停车时长修正后,,method=locationsFilter,,list.size={}",list!=null?list.size():null);
        }
        
        return list;
    }
    
    public List<Date> getPartition(Date beginCreateDate,Date endCreateDate ) {
        List<Date> dateList = new ArrayList<>();
        try {
            String dateFormat = null;
            // 2019-04-22 00:00:00
            // 2019-05-01
            // 2019-06-01
            // 2019-07-01
            // 2019-07-22 23:59:59
            //查询可能
            dateList.add(beginCreateDate);
            while (org.apache.commons.lang3.time.DateUtils.addDays(beginCreateDate, 10).before(endCreateDate)) {
                Date date = org.apache.commons.lang3.time.DateUtils.addDays(beginCreateDate, 10);
                dateFormat = DateUtils.format(date, DateUtils.TIME_FORMAT);
                date = DateUtils.parse(dateFormat, DateUtils.TIME_FORMAT);
                beginCreateDate = date;
                dateList.add(date);
            }
            dateList.add(endCreateDate);
            //dateList.add(DateUtils.getEndOfDate(endCreateDate));
        }catch (ParseException parseException) {
            parseException.printStackTrace();
        }
        return dateList;
    }


	@Override
	public PageDTO<LocationsDetailDTO> queryTrackByPage(CarLocationTrailPageReqDTO carLocationTrailPageReqDTO)throws Exception {

		Integer page = carLocationTrailPageReqDTO.getPageNo();
		Integer pageSize = carLocationTrailPageReqDTO.getPageSize();
		String carNo = carLocationTrailPageReqDTO.getVehicleLicense();
		String deviceId = carLocationTrailPageReqDTO.getSimNo();
		String vehicleVin = carLocationTrailPageReqDTO.getVehicleVin();
		String beginCreateDateStr = carLocationTrailPageReqDTO.getStartTime();
		String endCreateDateStr = carLocationTrailPageReqDTO.getEndTime();
		Date beginCreateDate = DateUtils.format2Date(beginCreateDateStr, DateUtils.TIME_FORMAT);
		Date endCreateDate = DateUtils.format2Date(endCreateDateStr, DateUtils.TIME_FORMAT);
		//如果选到未来的时间，以当前时间为准
		if(Objects.nonNull(endCreateDate) && endCreateDate.after(new Date())){
			endCreateDate =  new Date();
		}
		//1.查询mongo并转换为LocationsDetailDTO
		PageDTO<LocationsDetailDTO> pageDTO =getLocationDetailPageDTO(carLocationTrailPageReqDTO);
		//2.对结果进行转换
		List<LocationsDetailDTO> resultList = pageDTO.getResult();
        if(resultList==null||resultList.isEmpty()) {
            return pageDTO;
        }
        //默认写死参数
        Boolean stopInfo = carLocationTrailPageReqDTO.getStopInfo();
        Boolean loctionDetail = carLocationTrailPageReqDTO.getLoctionDetail();
        String coordinate = carLocationTrailPageReqDTO.getCoordinate();
        
        //之前老接口,分页查轨迹order排序是写死的升序，一期快速迁移代码，保证逻辑过来先写死，下方代码不动
        String order = "Asc";
        //需要停车点信息
        if(stopInfo) {
        	CarLocationTrailReqDTO carLocationTrailReqDTO = new CarLocationTrailReqDTO();
        	carLocationTrailReqDTO.setCarNo(carLocationTrailPageReqDTO.getVehicleLicense());
        	carLocationTrailReqDTO.setSimNo(carLocationTrailPageReqDTO.getSimNo());
        	carLocationTrailReqDTO.setVehicleVin(carLocationTrailPageReqDTO.getVehicleVin());
        	carLocationTrailReqDTO.setBeginCreateDate(carLocationTrailPageReqDTO.getStartTime());
        	carLocationTrailReqDTO.setEndCreateDate(carLocationTrailPageReqDTO.getEndTime());
        	carLocationTrailReqDTO.setSort(true);
            carLocationService.getLastStopInfo(resultList,carLocationTrailReqDTO);
            
            CarLocationTrailPageReqDTO nextPageReqDTO = BeanUtil.copyObject(carLocationTrailPageReqDTO, CarLocationTrailPageReqDTO.class);
            nextPageReqDTO.setPageNo(pageSize+1);
            PageDTO<LocationsDetailDTO> pageNext =getLocationDetailPageDTO(nextPageReqDTO);
            
            if(pageNext!=null && pageNext.getResult()!=null&&!pageNext.getResult().isEmpty()) {
            	List<LocationsDetailDTO> resultNextList = pageNext.getResult();
                resultList.add(resultNextList.get(0));
                //初始化停信息
                carLocationService.calculateForLbsPage(resultList);
                //carLocationService.calculateForLbsPageV2(resultList);
                resultList.remove(resultList.size()-1);
            }else {
                //初始化停信息
                carLocationService.calculateForLbsPage(resultList);
            }
        }
        if(loctionDetail) {
            long start = System.currentTimeMillis();
            List<LocationsDetailDTO> addressDetailList = carLocationService.getAddressDetail(resultList,pageSize);
            addressDetailList = addressDetailList.stream().filter(item -> carLocationService.isLocationValidateBinded(item)).collect(Collectors.toList());
            if(BceConstants.DESC.equals(order)||StringUtils.isBlank(order)) {
                addressDetailList.sort((c1,c2)->c2.getCreateDate().compareTo(c1.getCreateDate()));
            }
            if(BceConstants.ASC.equals(order)) {
                addressDetailList.sort((c1,c2)->c1.getCreateDate().compareTo(c2.getCreateDate()));
            }
            if(!CollectionUtils.isEmpty(addressDetailList)){
                pageDTO.setResult(addressDetailList);
            }
        }
        RestResponse response =  RestResponse.success(pageDTO);
        if(stopInfo) {
            return this.adjustLocationStopInfo(page,pageSize,carNo,deviceId,vehicleVin,beginCreateDate,endCreateDate,order,loctionDetail,stopInfo,response);
        }
		return pageDTO;
	}

	
	public PageDTO adjustLocationStopInfo(Integer page,Integer pageSize,String carNo,String deviceId,String vehicleVin,Date beginCreateDate,Date endCreateDate,String order,boolean loctionDetail,boolean stopInfo,RestResponse response) throws Exception{
		PageDTO data = (PageDTO) response.getData();
		List<LocationsDetailDTO> resultList = data.getResult();
		if (resultList.isEmpty()) {
			return data;
		}
		final Integer pageSizeMaxValue = 1000;
		//说明是导出操作
		if(pageSize>=pageSizeMaxValue){
			return data;
		}
		int page2 = (page*pageSize%pageSizeMaxValue==0)? (page*pageSize/pageSizeMaxValue):(page*pageSize/pageSizeMaxValue)+1;
		
		String beginCreateDateStr = DateUtils.format(beginCreateDate, DateUtils.TIME_FORMAT);
		String endCreateDateStr = DateUtils.format(endCreateDate, DateUtils.TIME_FORMAT);
		
		CarLocationTrailPageReqDTO nextCarLocationTrailPageReqDTO = new CarLocationTrailPageReqDTO();
		nextCarLocationTrailPageReqDTO.setVehicleLicense(carNo);
		nextCarLocationTrailPageReqDTO.setSimNo(deviceId);
		nextCarLocationTrailPageReqDTO.setVehicleVin(vehicleVin);
		nextCarLocationTrailPageReqDTO.setStartTime(beginCreateDateStr);
		nextCarLocationTrailPageReqDTO.setEndTime(endCreateDateStr);
		nextCarLocationTrailPageReqDTO.setSort(true);//对应order=Asc
		nextCarLocationTrailPageReqDTO.setPageNo(page2);
		nextCarLocationTrailPageReqDTO.setPageSize(pageSizeMaxValue);
		nextCarLocationTrailPageReqDTO.setLoctionDetail(false);
		nextCarLocationTrailPageReqDTO.setCoordinate(null);
		nextCarLocationTrailPageReqDTO.setStopInfo(stopInfo);
		PageDTO<LocationsDetailDTO> dataPage = this.queryTrackByPage(nextCarLocationTrailPageReqDTO);
		List<LocationsDetailDTO> resultListPage = dataPage.getResult();
		if (resultListPage.isEmpty()) {
		return data;
		}
		resultList.forEach(r -> {
			Optional<LocationsDetailDTO> first = resultListPage.stream().filter(t -> t.getCreateDate().compareTo(r.getCreateDate()) == 0).findFirst();
			if (first.isPresent()) {
			LocationsDetailDTO locationsDetailDTO = first.get();
			r.setStopPoint(locationsDetailDTO.getStopPoint());
			r.setStopPointDesc(locationsDetailDTO.getStopPoint()? "是" : "否");
			r.setStopDuration(locationsDetailDTO.getStopDuration());
			}
		});
		data.setResult(resultList);
		return data;
	}
	
	

	private PageDTO<LocationsDetailDTO> getLocationDetailPageDTO(CarLocationTrailPageReqDTO carLocationTrailPageReqDTO) {
		
		PageDTO<LocationsDetailDTO> respPage = new PageDTO<LocationsDetailDTO>();
		
		PageDTO<GovVehicleLocation> deviceTrackPage = govDeviceTraceService.queryTrackByPage(carLocationTrailPageReqDTO);
		if(deviceTrackPage!=null && deviceTrackPage.getResult()!=null && !deviceTrackPage.getResult().isEmpty()) {
			respPage.setPage(deviceTrackPage.getPage());
			respPage.setPageSize(deviceTrackPage.getPageSize());
			respPage.setTotal(deviceTrackPage.getTotal());
			List<LocationsDetailDTO> locationList = new ArrayList<>();
			
			List<GovVehicleLocation> deviceTrackDataDTOList = deviceTrackPage.getResult();
			for(GovVehicleLocation govVehicleLocation :deviceTrackDataDTOList) {
				
				Date createDate = govVehicleLocation.getTimestamp();
				GpsDeviceMeta deviceMeta = govVehicleLocation.getDeviceMeta();
				Integer deviceType = deviceMeta.getDeviceType();
				
				//copy属性
				LocationsDetailDTO locationsDetailDTO = BeanUtil.copyObject(govVehicleLocation, LocationsDetailDTO.class);
				//特殊设置
				locationsDetailDTO.setCreateDate(createDate);
				locationsDetailDTO.setCarNo(deviceMeta.getVehicleLicense());
				locationsDetailDTO.setDeviceId(deviceMeta.getSimNo());
				locationsDetailDTO.setVehicleVin(deviceMeta.getVehicleVin());
				locationsDetailDTO.setDeviceType(String.valueOf(deviceType));
				locationsDetailDTO.setLocationFlag(govVehicleLocation.getLocationFlag());
				locationsDetailDTO.setStopPointDesc(locationsDetailDTO.getStopPoint() ? "是" : "否");
				locationsDetailDTO.setSpeed(Optional.ofNullable(govVehicleLocation.getSpeed()).map(BigDecimal::new).orElse(BigDecimal.ZERO));
				locationList.add(locationsDetailDTO);
			}
			respPage.setResult(locationList);
		}
		
		return respPage;
	}


	@Override
	public PageDTO<GovVehicleStatusDTO> queryTBoxLogByPage(TboxLogReqDTO tboxLogReqDTO) throws Exception {

		logger.info("msg=查询状态历史,,method=queryTBoxLogByPage,,tboxLogReqDTO={}",JsonUtil.toJson(tboxLogReqDTO));
		String beginCreateDateStr = tboxLogReqDTO.getBeginCreateDate();
		String endCreateDateStr = tboxLogReqDTO.getEndCreateDate();
		Date beginCreateDate = DateUtils.format2Date(beginCreateDateStr, DateUtils.TIME_FORMAT);
		Date endCreateDate = DateUtils.format2Date(endCreateDateStr, DateUtils.TIME_FORMAT);
		//如果选到未来的时间，以当前时间为准
		if(Objects.nonNull(endCreateDate) && endCreateDate.after(new Date())){
			endCreateDate =  new Date();
		}
		String carNo = tboxLogReqDTO.getCarNo();
		String simNo = tboxLogReqDTO.getSimNo();
		String vehicleVin = tboxLogReqDTO.getVehicleVin();
		String vehicleVinString = tboxLogReqDTO.getVehicleVinString();
		String operateBussCode = tboxLogReqDTO.getOperateBussCode();
		Integer page = tboxLogReqDTO.getPage();
		Integer pageSize = tboxLogReqDTO.getPageSize();
		Boolean asc = tboxLogReqDTO.getAsc();
		String businessLine = tboxLogReqDTO.getBusinessLine();
		 //设置分页条件
        try {
            CarStatusVo carStatusVo = new CarStatusVo();
            carStatusVo.setCarNo(carNo);
            carStatusVo.setDeviceId(simNo);
            carStatusVo.setVehicleVin(vehicleVin);
            if(StringUtils.isNotBlank(vehicleVinString)){
                List<String> list = Arrays.asList(vehicleVinString.split(","));
                carStatusVo.setVehicleVinSet(new HashSet<>(list));
            }
            if(StringUtils.isNotBlank(operateBussCode)){
                List<String> operateBussCodelist = Arrays.asList(operateBussCode.split(","));
                carStatusVo.setOperateBussCodeList(operateBussCodelist);
            }
            endCreateDate = endCreateDate.after(new Date())?new Date():endCreateDate;
            carStatusVo.setBeginCreateDate(beginCreateDate);
            carStatusVo.setEndCreateDate(endCreateDate);
            if(Objects.nonNull(asc) && asc){
                carStatusVo.setAsc(true);
            }else {
                carStatusVo.setAsc(false);
            }
            //设置数量
            long start = System.currentTimeMillis();
            PageDTO<GovVehicleStatusDTO> pageDTO = govDeviceMongoService.queryVehicleStatusByPage(page, pageSize, carStatusVo);
            logger.info("pageDTO  耗时={}ms",System.currentTimeMillis()-start);
            return pageDTO;
        }catch (Exception e) {
            logger.error("车辆状态列表 异常={}",e);
            throw e;
        }
	}
    
    
	public DeviceHistoryTraceDTO queryHistoryTrace(String vehicleNo,String vehicleLicense, String vehicleVin, String deviceNo, String beginCreateDate, String endCreateDate) throws Exception {
		CarLocationTrailReqDTO reqDTO = new CarLocationTrailReqDTO();
		reqDTO.setVehicleNo(vehicleNo);
		reqDTO.setCarNo(vehicleLicense);
		reqDTO.setVehicleVin(vehicleVin);
		reqDTO.setDeviceNo(deviceNo);
		reqDTO.setBeginCreateDate(beginCreateDate);
		reqDTO.setEndCreateDate(endCreateDate);
		return this.queryHistoryTrace(reqDTO);
	}
}
