package com.mrcar.gov.iot.service;

import com.mrcar.iot.domain.GovVehicleLocation;
import com.mrcar.iot.domain.GovVehicleStatus;

import java.util.Date;
import java.util.List;

/**
 * 车辆设备Service.
 *
 * <AUTHOR>
 * @date 2024-11-21
 */
public interface VehicleDeviceService {
    /**
     * 根据设备编号查询最新的点位信息
     */
    GovVehicleLocation getOneLatestLocation(String deviceSysNo);

    /**
     * 根据设备编号批量查询最新的点位信息
     */
    List<GovVehicleLocation> getLatestLocations(List<String> deviceSysNos);

    /**
     * 根据设备编号批量查询最新的点位信息
     */
    List<GovVehicleLocation> getLatestLocations(List<String> deviceSysNos, boolean loadFromDisk);

    /**
     * 从磁盘数据中获取设备的最新点位数据
     */
    List<GovVehicleLocation> getLatestLocationsByDisk(List<String> deviceSysNos);

    /**
     * 根据设备编号批量查询最新的状态信息
     */
    List<GovVehicleStatus> getLatestStatus(List<String> deviceSysNos);

    /**
     * 查询设备一段时间内的状态列表
     */
    List<GovVehicleStatus> getDeviceStatus(String deviceSysNo, Date start, Date end);

    /**
     * 查询一段时间内的设备轨迹 主要是用于在线离线状态筛选车辆 没有缓存 使用时注意超时
     */
    List<GovVehicleLocation> queryDeviceLocation(Date startDate, Date endDate);

    List<String> getLocationVehicle(Date startDate, Date endDate);

}
