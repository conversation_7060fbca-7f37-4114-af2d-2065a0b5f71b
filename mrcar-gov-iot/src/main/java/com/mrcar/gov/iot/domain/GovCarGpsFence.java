package com.mrcar.gov.iot.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 电子围栏表
 * @TableName gov_car_gps_fence
 */
@TableName(value ="gov_car_gps_fence")
public class GovCarGpsFence implements Serializable {
    /**
     * 电子围栏id
     */
    @TableId(type = IdType.AUTO)
    private Integer fenceId;

    /**
     * 电子围栏名称
     */
    private String fenceName;

    /**
     * 城市CODE
     */
    private Integer cityCode;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 围栏所属企业ID
     */
    private Integer companyId;

    /**
     * 围栏类型：1：圆形；2：多边形；3：行政区划
     */
    private Integer fenceType;

    /**
     * 圆形围栏中心点坐标
     */
    private String center;

    /**
     * 圆形围栏半径 <=5000
     */
    private String radius;

    /**
     * 多边形围栏坐标对
     */
    private String points;

    /**
     * 报警类型；报警类型；1：出栏报警；2：入栏报警 3：超速报警 11:出栏记录
     */
    private Integer warnType;

    /**
     * 指定日期
     */
    private String fixedDate;

    /**
     * 指定时段
     */
    private String time;

    /**
     * 围栏状态；1：正常；2：停用；4：删除
     */
    private Integer fenceStatus;

    /**
     * 围栏地址关键字
     */
    private String fenceAddress;

    /**
     * 创建人ID
     */
    private Integer createId;

    /**
     * 创建人姓名
     */
    private String createName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人ID
     */
    private Integer updateId;

    /**
     * 修改人姓名
     */
    private String updateName;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 启用备注
     */
    private String startComment;

    /**
     * 启用人ID
     */
    private Integer startId;

    /**
     * 启用人姓名
     */
    private String startName;

    /**
     * 启用时间；格式：yyyy-MM-dd HH:mm:ss
     */
    private Date startTime;

    /**
     * 停用围栏备注
     */
    private String stopComment;

    /**
     * 停用人ID
     */
    private Integer stopId;

    /**
     * 停用人姓名
     */
    private String stopName;

    /**
     * 停用时间；格式：yyyy-MM-dd HH:mm:ss
     */
    private Date stopTime;

    /**
     * 删除备注
     */
    private String deleteComment;

    /**
     * 删除人ID
     */
    private Integer deleteId;

    /**
     * 删除人姓名
     */
    private String deleteName;

    /**
     * 删除时间；格式：yyyy-MM-dd HH:mm:ss
     */
    private Date deleteTime;

    /**
     * 指派备注
     */
    private String assignComment;

    /**
     * 指派人ID
     */
    private Integer assginId;

    /**
     * 指派人姓名
     */
    private String assginName;

    /**
     * 指派时间；格式：yyyy-MM-dd HH:mm:ss
     */
    private Date assginTime;

    /**
     * 超速报警阈值
     */
    private BigDecimal overSpeed;

    /**
     * 备注
     */
    private String remark;

    /**
     * 行政区CODE
     */
    private Integer regionalismCode;

    /**
     * 行政区名称
     */
    private String regionalismName;

    /**
     * 指派车辆数
     */
    private Integer vehicleCount;

    /**
     * 报警豁免状态：1:无任务；2：任务中，多个以英文逗号分隔
     */
    private String exemptStatus;

    /**
     * 电子围栏快照表ID
     */
    private Integer snapId;

    /**
     * 所属部门id
     */
    private Integer departmentId;

    /**
     * 所属部门名称
     */
    private String departmentName;

    /**
     * 自助取还校验开关 0：关闭 1：开启
     */
    private Integer selfOperateOpen;

    /**
     * 报警自动处理开关 0：关闭 1：开启
     */
    private Integer autoDealOpen;

    /**
     * 报警阈值时长 单位：分钟
     */
    private Integer effectiveDuration;

    /**
     * 围栏业务类型；1：出栏报警；2：入栏报警 3：超速报警 4:公务用车
     */
    private Integer fenceBusinessType;

    /**
     * 电子围栏所属部门code
     */
    private String departmentCode;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 电子围栏id
     */
    public Integer getFenceId() {
        return fenceId;
    }

    /**
     * 电子围栏id
     */
    public void setFenceId(Integer fenceId) {
        this.fenceId = fenceId;
    }

    /**
     * 电子围栏名称
     */
    public String getFenceName() {
        return fenceName;
    }

    /**
     * 电子围栏名称
     */
    public void setFenceName(String fenceName) {
        this.fenceName = fenceName;
    }

    /**
     * 城市CODE
     */
    public Integer getCityCode() {
        return cityCode;
    }

    /**
     * 城市CODE
     */
    public void setCityCode(Integer cityCode) {
        this.cityCode = cityCode;
    }

    /**
     * 城市名称
     */
    public String getCityName() {
        return cityName;
    }

    /**
     * 城市名称
     */
    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    /**
     * 围栏所属企业ID
     */
    public Integer getCompanyId() {
        return companyId;
    }

    /**
     * 围栏所属企业ID
     */
    public void setCompanyId(Integer companyId) {
        this.companyId = companyId;
    }

    /**
     * 围栏类型：1：圆形；2：多边形；3：行政区划
     */
    public Integer getFenceType() {
        return fenceType;
    }

    /**
     * 围栏类型：1：圆形；2：多边形；3：行政区划
     */
    public void setFenceType(Integer fenceType) {
        this.fenceType = fenceType;
    }

    /**
     * 圆形围栏中心点坐标
     */
    public String getCenter() {
        return center;
    }

    /**
     * 圆形围栏中心点坐标
     */
    public void setCenter(String center) {
        this.center = center;
    }

    /**
     * 圆形围栏半径 <=5000
     */
    public String getRadius() {
        return radius;
    }

    /**
     * 圆形围栏半径 <=5000
     */
    public void setRadius(String radius) {
        this.radius = radius;
    }

    /**
     * 多边形围栏坐标对
     */
    public String getPoints() {
        return points;
    }

    /**
     * 多边形围栏坐标对
     */
    public void setPoints(String points) {
        this.points = points;
    }

    /**
     * 报警类型；报警类型；1：出栏报警；2：入栏报警 3：超速报警 11:出栏记录
     */
    public Integer getWarnType() {
        return warnType;
    }

    /**
     * 报警类型；报警类型；1：出栏报警；2：入栏报警 3：超速报警 11:出栏记录
     */
    public void setWarnType(Integer warnType) {
        this.warnType = warnType;
    }

    /**
     * 指定日期
     */
    public String getFixedDate() {
        return fixedDate;
    }

    /**
     * 指定日期
     */
    public void setFixedDate(String fixedDate) {
        this.fixedDate = fixedDate;
    }

    /**
     * 指定时段
     */
    public String getTime() {
        return time;
    }

    /**
     * 指定时段
     */
    public void setTime(String time) {
        this.time = time;
    }

    /**
     * 围栏状态；1：正常；2：停用；4：删除
     */
    public Integer getFenceStatus() {
        return fenceStatus;
    }

    /**
     * 围栏状态；1：正常；2：停用；4：删除
     */
    public void setFenceStatus(Integer fenceStatus) {
        this.fenceStatus = fenceStatus;
    }

    /**
     * 围栏地址关键字
     */
    public String getFenceAddress() {
        return fenceAddress;
    }

    /**
     * 围栏地址关键字
     */
    public void setFenceAddress(String fenceAddress) {
        this.fenceAddress = fenceAddress;
    }

    /**
     * 创建人ID
     */
    public Integer getCreateId() {
        return createId;
    }

    /**
     * 创建人ID
     */
    public void setCreateId(Integer createId) {
        this.createId = createId;
    }

    /**
     * 创建人姓名
     */
    public String getCreateName() {
        return createName;
    }

    /**
     * 创建人姓名
     */
    public void setCreateName(String createName) {
        this.createName = createName;
    }

    /**
     * 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 修改人ID
     */
    public Integer getUpdateId() {
        return updateId;
    }

    /**
     * 修改人ID
     */
    public void setUpdateId(Integer updateId) {
        this.updateId = updateId;
    }

    /**
     * 修改人姓名
     */
    public String getUpdateName() {
        return updateName;
    }

    /**
     * 修改人姓名
     */
    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    /**
     * 修改时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 修改时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 启用备注
     */
    public String getStartComment() {
        return startComment;
    }

    /**
     * 启用备注
     */
    public void setStartComment(String startComment) {
        this.startComment = startComment;
    }

    /**
     * 启用人ID
     */
    public Integer getStartId() {
        return startId;
    }

    /**
     * 启用人ID
     */
    public void setStartId(Integer startId) {
        this.startId = startId;
    }

    /**
     * 启用人姓名
     */
    public String getStartName() {
        return startName;
    }

    /**
     * 启用人姓名
     */
    public void setStartName(String startName) {
        this.startName = startName;
    }

    /**
     * 启用时间；格式：yyyy-MM-dd HH:mm:ss
     */
    public Date getStartTime() {
        return startTime;
    }

    /**
     * 启用时间；格式：yyyy-MM-dd HH:mm:ss
     */
    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    /**
     * 停用围栏备注
     */
    public String getStopComment() {
        return stopComment;
    }

    /**
     * 停用围栏备注
     */
    public void setStopComment(String stopComment) {
        this.stopComment = stopComment;
    }

    /**
     * 停用人ID
     */
    public Integer getStopId() {
        return stopId;
    }

    /**
     * 停用人ID
     */
    public void setStopId(Integer stopId) {
        this.stopId = stopId;
    }

    /**
     * 停用人姓名
     */
    public String getStopName() {
        return stopName;
    }

    /**
     * 停用人姓名
     */
    public void setStopName(String stopName) {
        this.stopName = stopName;
    }

    /**
     * 停用时间；格式：yyyy-MM-dd HH:mm:ss
     */
    public Date getStopTime() {
        return stopTime;
    }

    /**
     * 停用时间；格式：yyyy-MM-dd HH:mm:ss
     */
    public void setStopTime(Date stopTime) {
        this.stopTime = stopTime;
    }

    /**
     * 删除备注
     */
    public String getDeleteComment() {
        return deleteComment;
    }

    /**
     * 删除备注
     */
    public void setDeleteComment(String deleteComment) {
        this.deleteComment = deleteComment;
    }

    /**
     * 删除人ID
     */
    public Integer getDeleteId() {
        return deleteId;
    }

    /**
     * 删除人ID
     */
    public void setDeleteId(Integer deleteId) {
        this.deleteId = deleteId;
    }

    /**
     * 删除人姓名
     */
    public String getDeleteName() {
        return deleteName;
    }

    /**
     * 删除人姓名
     */
    public void setDeleteName(String deleteName) {
        this.deleteName = deleteName;
    }

    /**
     * 删除时间；格式：yyyy-MM-dd HH:mm:ss
     */
    public Date getDeleteTime() {
        return deleteTime;
    }

    /**
     * 删除时间；格式：yyyy-MM-dd HH:mm:ss
     */
    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    /**
     * 指派备注
     */
    public String getAssignComment() {
        return assignComment;
    }

    /**
     * 指派备注
     */
    public void setAssignComment(String assignComment) {
        this.assignComment = assignComment;
    }

    /**
     * 指派人ID
     */
    public Integer getAssginId() {
        return assginId;
    }

    /**
     * 指派人ID
     */
    public void setAssginId(Integer assginId) {
        this.assginId = assginId;
    }

    /**
     * 指派人姓名
     */
    public String getAssginName() {
        return assginName;
    }

    /**
     * 指派人姓名
     */
    public void setAssginName(String assginName) {
        this.assginName = assginName;
    }

    /**
     * 指派时间；格式：yyyy-MM-dd HH:mm:ss
     */
    public Date getAssginTime() {
        return assginTime;
    }

    /**
     * 指派时间；格式：yyyy-MM-dd HH:mm:ss
     */
    public void setAssginTime(Date assginTime) {
        this.assginTime = assginTime;
    }

    /**
     * 超速报警阈值
     */
    public BigDecimal getOverSpeed() {
        return overSpeed;
    }

    /**
     * 超速报警阈值
     */
    public void setOverSpeed(BigDecimal overSpeed) {
        this.overSpeed = overSpeed;
    }

    /**
     * 备注
     */
    public String getRemark() {
        return remark;
    }

    /**
     * 备注
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * 行政区CODE
     */
    public Integer getRegionalismCode() {
        return regionalismCode;
    }

    /**
     * 行政区CODE
     */
    public void setRegionalismCode(Integer regionalismCode) {
        this.regionalismCode = regionalismCode;
    }

    /**
     * 行政区名称
     */
    public String getRegionalismName() {
        return regionalismName;
    }

    /**
     * 行政区名称
     */
    public void setRegionalismName(String regionalismName) {
        this.regionalismName = regionalismName;
    }

    /**
     * 指派车辆数
     */
    public Integer getVehicleCount() {
        return vehicleCount;
    }

    /**
     * 指派车辆数
     */
    public void setVehicleCount(Integer vehicleCount) {
        this.vehicleCount = vehicleCount;
    }

    /**
     * 报警豁免状态：1:无任务；2：任务中，多个以英文逗号分隔
     */
    public String getExemptStatus() {
        return exemptStatus;
    }

    /**
     * 报警豁免状态：1:无任务；2：任务中，多个以英文逗号分隔
     */
    public void setExemptStatus(String exemptStatus) {
        this.exemptStatus = exemptStatus;
    }

    /**
     * 电子围栏快照表ID
     */
    public Integer getSnapId() {
        return snapId;
    }

    /**
     * 电子围栏快照表ID
     */
    public void setSnapId(Integer snapId) {
        this.snapId = snapId;
    }

    /**
     * 所属部门id
     */
    public Integer getDepartmentId() {
        return departmentId;
    }

    /**
     * 所属部门id
     */
    public void setDepartmentId(Integer departmentId) {
        this.departmentId = departmentId;
    }

    /**
     * 所属部门名称
     */
    public String getDepartmentName() {
        return departmentName;
    }

    /**
     * 所属部门名称
     */
    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    /**
     * 自助取还校验开关 0：关闭 1：开启
     */
    public Integer getSelfOperateOpen() {
        return selfOperateOpen;
    }

    /**
     * 自助取还校验开关 0：关闭 1：开启
     */
    public void setSelfOperateOpen(Integer selfOperateOpen) {
        this.selfOperateOpen = selfOperateOpen;
    }

    /**
     * 报警自动处理开关 0：关闭 1：开启
     */
    public Integer getAutoDealOpen() {
        return autoDealOpen;
    }

    /**
     * 报警自动处理开关 0：关闭 1：开启
     */
    public void setAutoDealOpen(Integer autoDealOpen) {
        this.autoDealOpen = autoDealOpen;
    }

    /**
     * 报警阈值时长 单位：分钟
     */
    public Integer getEffectiveDuration() {
        return effectiveDuration;
    }

    /**
     * 报警阈值时长 单位：分钟
     */
    public void setEffectiveDuration(Integer effectiveDuration) {
        this.effectiveDuration = effectiveDuration;
    }

    /**
     * 围栏业务类型；1：出栏报警；2：入栏报警 3：超速报警 4:公务用车
     */
    public Integer getFenceBusinessType() {
        return fenceBusinessType;
    }

    /**
     * 围栏业务类型；1：出栏报警；2：入栏报警 3：超速报警 4:公务用车
     */
    public void setFenceBusinessType(Integer fenceBusinessType) {
        this.fenceBusinessType = fenceBusinessType;
    }

    /**
     * 电子围栏所属部门code
     */
    public String getDepartmentCode() {
        return departmentCode;
    }

    /**
     * 电子围栏所属部门code
     */
    public void setDepartmentCode(String departmentCode) {
        this.departmentCode = departmentCode;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        GovCarGpsFence other = (GovCarGpsFence) that;
        return (this.getFenceId() == null ? other.getFenceId() == null : this.getFenceId().equals(other.getFenceId()))
            && (this.getFenceName() == null ? other.getFenceName() == null : this.getFenceName().equals(other.getFenceName()))
            && (this.getCityCode() == null ? other.getCityCode() == null : this.getCityCode().equals(other.getCityCode()))
            && (this.getCityName() == null ? other.getCityName() == null : this.getCityName().equals(other.getCityName()))
            && (this.getCompanyId() == null ? other.getCompanyId() == null : this.getCompanyId().equals(other.getCompanyId()))
            && (this.getFenceType() == null ? other.getFenceType() == null : this.getFenceType().equals(other.getFenceType()))
            && (this.getCenter() == null ? other.getCenter() == null : this.getCenter().equals(other.getCenter()))
            && (this.getRadius() == null ? other.getRadius() == null : this.getRadius().equals(other.getRadius()))
            && (this.getPoints() == null ? other.getPoints() == null : this.getPoints().equals(other.getPoints()))
            && (this.getWarnType() == null ? other.getWarnType() == null : this.getWarnType().equals(other.getWarnType()))
            && (this.getFixedDate() == null ? other.getFixedDate() == null : this.getFixedDate().equals(other.getFixedDate()))
            && (this.getTime() == null ? other.getTime() == null : this.getTime().equals(other.getTime()))
            && (this.getFenceStatus() == null ? other.getFenceStatus() == null : this.getFenceStatus().equals(other.getFenceStatus()))
            && (this.getFenceAddress() == null ? other.getFenceAddress() == null : this.getFenceAddress().equals(other.getFenceAddress()))
            && (this.getCreateId() == null ? other.getCreateId() == null : this.getCreateId().equals(other.getCreateId()))
            && (this.getCreateName() == null ? other.getCreateName() == null : this.getCreateName().equals(other.getCreateName()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateId() == null ? other.getUpdateId() == null : this.getUpdateId().equals(other.getUpdateId()))
            && (this.getUpdateName() == null ? other.getUpdateName() == null : this.getUpdateName().equals(other.getUpdateName()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getStartComment() == null ? other.getStartComment() == null : this.getStartComment().equals(other.getStartComment()))
            && (this.getStartId() == null ? other.getStartId() == null : this.getStartId().equals(other.getStartId()))
            && (this.getStartName() == null ? other.getStartName() == null : this.getStartName().equals(other.getStartName()))
            && (this.getStartTime() == null ? other.getStartTime() == null : this.getStartTime().equals(other.getStartTime()))
            && (this.getStopComment() == null ? other.getStopComment() == null : this.getStopComment().equals(other.getStopComment()))
            && (this.getStopId() == null ? other.getStopId() == null : this.getStopId().equals(other.getStopId()))
            && (this.getStopName() == null ? other.getStopName() == null : this.getStopName().equals(other.getStopName()))
            && (this.getStopTime() == null ? other.getStopTime() == null : this.getStopTime().equals(other.getStopTime()))
            && (this.getDeleteComment() == null ? other.getDeleteComment() == null : this.getDeleteComment().equals(other.getDeleteComment()))
            && (this.getDeleteId() == null ? other.getDeleteId() == null : this.getDeleteId().equals(other.getDeleteId()))
            && (this.getDeleteName() == null ? other.getDeleteName() == null : this.getDeleteName().equals(other.getDeleteName()))
            && (this.getDeleteTime() == null ? other.getDeleteTime() == null : this.getDeleteTime().equals(other.getDeleteTime()))
            && (this.getAssignComment() == null ? other.getAssignComment() == null : this.getAssignComment().equals(other.getAssignComment()))
            && (this.getAssginId() == null ? other.getAssginId() == null : this.getAssginId().equals(other.getAssginId()))
            && (this.getAssginName() == null ? other.getAssginName() == null : this.getAssginName().equals(other.getAssginName()))
            && (this.getAssginTime() == null ? other.getAssginTime() == null : this.getAssginTime().equals(other.getAssginTime()))
            && (this.getOverSpeed() == null ? other.getOverSpeed() == null : this.getOverSpeed().equals(other.getOverSpeed()))
            && (this.getRemark() == null ? other.getRemark() == null : this.getRemark().equals(other.getRemark()))
            && (this.getRegionalismCode() == null ? other.getRegionalismCode() == null : this.getRegionalismCode().equals(other.getRegionalismCode()))
            && (this.getRegionalismName() == null ? other.getRegionalismName() == null : this.getRegionalismName().equals(other.getRegionalismName()))
            && (this.getVehicleCount() == null ? other.getVehicleCount() == null : this.getVehicleCount().equals(other.getVehicleCount()))
            && (this.getExemptStatus() == null ? other.getExemptStatus() == null : this.getExemptStatus().equals(other.getExemptStatus()))
            && (this.getSnapId() == null ? other.getSnapId() == null : this.getSnapId().equals(other.getSnapId()))
            && (this.getDepartmentId() == null ? other.getDepartmentId() == null : this.getDepartmentId().equals(other.getDepartmentId()))
            && (this.getDepartmentName() == null ? other.getDepartmentName() == null : this.getDepartmentName().equals(other.getDepartmentName()))
            && (this.getSelfOperateOpen() == null ? other.getSelfOperateOpen() == null : this.getSelfOperateOpen().equals(other.getSelfOperateOpen()))
            && (this.getAutoDealOpen() == null ? other.getAutoDealOpen() == null : this.getAutoDealOpen().equals(other.getAutoDealOpen()))
            && (this.getEffectiveDuration() == null ? other.getEffectiveDuration() == null : this.getEffectiveDuration().equals(other.getEffectiveDuration()))
            && (this.getFenceBusinessType() == null ? other.getFenceBusinessType() == null : this.getFenceBusinessType().equals(other.getFenceBusinessType()))
            && (this.getDepartmentCode() == null ? other.getDepartmentCode() == null : this.getDepartmentCode().equals(other.getDepartmentCode()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getFenceId() == null) ? 0 : getFenceId().hashCode());
        result = prime * result + ((getFenceName() == null) ? 0 : getFenceName().hashCode());
        result = prime * result + ((getCityCode() == null) ? 0 : getCityCode().hashCode());
        result = prime * result + ((getCityName() == null) ? 0 : getCityName().hashCode());
        result = prime * result + ((getCompanyId() == null) ? 0 : getCompanyId().hashCode());
        result = prime * result + ((getFenceType() == null) ? 0 : getFenceType().hashCode());
        result = prime * result + ((getCenter() == null) ? 0 : getCenter().hashCode());
        result = prime * result + ((getRadius() == null) ? 0 : getRadius().hashCode());
        result = prime * result + ((getPoints() == null) ? 0 : getPoints().hashCode());
        result = prime * result + ((getWarnType() == null) ? 0 : getWarnType().hashCode());
        result = prime * result + ((getFixedDate() == null) ? 0 : getFixedDate().hashCode());
        result = prime * result + ((getTime() == null) ? 0 : getTime().hashCode());
        result = prime * result + ((getFenceStatus() == null) ? 0 : getFenceStatus().hashCode());
        result = prime * result + ((getFenceAddress() == null) ? 0 : getFenceAddress().hashCode());
        result = prime * result + ((getCreateId() == null) ? 0 : getCreateId().hashCode());
        result = prime * result + ((getCreateName() == null) ? 0 : getCreateName().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateId() == null) ? 0 : getUpdateId().hashCode());
        result = prime * result + ((getUpdateName() == null) ? 0 : getUpdateName().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getStartComment() == null) ? 0 : getStartComment().hashCode());
        result = prime * result + ((getStartId() == null) ? 0 : getStartId().hashCode());
        result = prime * result + ((getStartName() == null) ? 0 : getStartName().hashCode());
        result = prime * result + ((getStartTime() == null) ? 0 : getStartTime().hashCode());
        result = prime * result + ((getStopComment() == null) ? 0 : getStopComment().hashCode());
        result = prime * result + ((getStopId() == null) ? 0 : getStopId().hashCode());
        result = prime * result + ((getStopName() == null) ? 0 : getStopName().hashCode());
        result = prime * result + ((getStopTime() == null) ? 0 : getStopTime().hashCode());
        result = prime * result + ((getDeleteComment() == null) ? 0 : getDeleteComment().hashCode());
        result = prime * result + ((getDeleteId() == null) ? 0 : getDeleteId().hashCode());
        result = prime * result + ((getDeleteName() == null) ? 0 : getDeleteName().hashCode());
        result = prime * result + ((getDeleteTime() == null) ? 0 : getDeleteTime().hashCode());
        result = prime * result + ((getAssignComment() == null) ? 0 : getAssignComment().hashCode());
        result = prime * result + ((getAssginId() == null) ? 0 : getAssginId().hashCode());
        result = prime * result + ((getAssginName() == null) ? 0 : getAssginName().hashCode());
        result = prime * result + ((getAssginTime() == null) ? 0 : getAssginTime().hashCode());
        result = prime * result + ((getOverSpeed() == null) ? 0 : getOverSpeed().hashCode());
        result = prime * result + ((getRemark() == null) ? 0 : getRemark().hashCode());
        result = prime * result + ((getRegionalismCode() == null) ? 0 : getRegionalismCode().hashCode());
        result = prime * result + ((getRegionalismName() == null) ? 0 : getRegionalismName().hashCode());
        result = prime * result + ((getVehicleCount() == null) ? 0 : getVehicleCount().hashCode());
        result = prime * result + ((getExemptStatus() == null) ? 0 : getExemptStatus().hashCode());
        result = prime * result + ((getSnapId() == null) ? 0 : getSnapId().hashCode());
        result = prime * result + ((getDepartmentId() == null) ? 0 : getDepartmentId().hashCode());
        result = prime * result + ((getDepartmentName() == null) ? 0 : getDepartmentName().hashCode());
        result = prime * result + ((getSelfOperateOpen() == null) ? 0 : getSelfOperateOpen().hashCode());
        result = prime * result + ((getAutoDealOpen() == null) ? 0 : getAutoDealOpen().hashCode());
        result = prime * result + ((getEffectiveDuration() == null) ? 0 : getEffectiveDuration().hashCode());
        result = prime * result + ((getFenceBusinessType() == null) ? 0 : getFenceBusinessType().hashCode());
        result = prime * result + ((getDepartmentCode() == null) ? 0 : getDepartmentCode().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", fenceId=").append(fenceId);
        sb.append(", fenceName=").append(fenceName);
        sb.append(", cityCode=").append(cityCode);
        sb.append(", cityName=").append(cityName);
        sb.append(", companyId=").append(companyId);
        sb.append(", fenceType=").append(fenceType);
        sb.append(", center=").append(center);
        sb.append(", radius=").append(radius);
        sb.append(", points=").append(points);
        sb.append(", warnType=").append(warnType);
        sb.append(", fixedDate=").append(fixedDate);
        sb.append(", time=").append(time);
        sb.append(", fenceStatus=").append(fenceStatus);
        sb.append(", fenceAddress=").append(fenceAddress);
        sb.append(", createId=").append(createId);
        sb.append(", createName=").append(createName);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateId=").append(updateId);
        sb.append(", updateName=").append(updateName);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", startComment=").append(startComment);
        sb.append(", startId=").append(startId);
        sb.append(", startName=").append(startName);
        sb.append(", startTime=").append(startTime);
        sb.append(", stopComment=").append(stopComment);
        sb.append(", stopId=").append(stopId);
        sb.append(", stopName=").append(stopName);
        sb.append(", stopTime=").append(stopTime);
        sb.append(", deleteComment=").append(deleteComment);
        sb.append(", deleteId=").append(deleteId);
        sb.append(", deleteName=").append(deleteName);
        sb.append(", deleteTime=").append(deleteTime);
        sb.append(", assignComment=").append(assignComment);
        sb.append(", assginId=").append(assginId);
        sb.append(", assginName=").append(assginName);
        sb.append(", assginTime=").append(assginTime);
        sb.append(", overSpeed=").append(overSpeed);
        sb.append(", remark=").append(remark);
        sb.append(", regionalismCode=").append(regionalismCode);
        sb.append(", regionalismName=").append(regionalismName);
        sb.append(", vehicleCount=").append(vehicleCount);
        sb.append(", exemptStatus=").append(exemptStatus);
        sb.append(", snapId=").append(snapId);
        sb.append(", departmentId=").append(departmentId);
        sb.append(", departmentName=").append(departmentName);
        sb.append(", selfOperateOpen=").append(selfOperateOpen);
        sb.append(", autoDealOpen=").append(autoDealOpen);
        sb.append(", effectiveDuration=").append(effectiveDuration);
        sb.append(", fenceBusinessType=").append(fenceBusinessType);
        sb.append(", departmentCode=").append(departmentCode);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}