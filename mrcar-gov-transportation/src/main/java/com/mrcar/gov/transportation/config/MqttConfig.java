package com.mrcar.gov.transportation.config;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.mrcar.gov.mqtt.bus.Authenticator;
import com.mrcar.gov.mqtt.bus.BusController;
import com.mrcar.gov.mqtt.bus.event.DeviceMessageListener;
import com.mrcar.gov.mqtt.core.MQTTServer;
import com.mrcar.gov.mqtt.support.config.BrokerConfig;
import com.mrcar.gov.mqtt.support.config.NettyConfig;
import org.eclipse.paho.client.mqttv3.MqttClient;
import org.eclipse.paho.client.mqttv3.MqttConnectOptions;
import org.eclipse.paho.client.mqttv3.persist.MemoryPersistence;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;

import static org.eclipse.paho.client.mqttv3.MqttConnectOptions.MQTT_VERSION_3_1_1;

@Configuration
public class MqttConfig {

    @NacosValue("${tcp.mqtt.port}")
    private Integer port;
    @NacosValue("${mqtt.mongo.address}")
    private String dbConnectionString;
    @NacosValue("${mqtt.mongo.database}")
    private String database;

    @Bean
    public BrokerConfig brokerConfig() {
        BrokerConfig config = new BrokerConfig();
        config.setConnectionString(this.dbConnectionString);
        config.setDatabase(this.database);
        return config;
    }

    @Bean
    public NettyConfig nettyConfig() {
        NettyConfig config = new NettyConfig();
        config.setStartWebsocket(false);
        config.setStartHttp(false);
        config.setStartSslTcp(false);
        config.setStartSslWebsocket(false);
        // 设置mqtt端口
        config.setTcpPort(this.port);
        return config;
    }

    @Bean(initMethod = "start", destroyMethod = "shutdown")
    public BusController mqttController(BrokerConfig brokerConfig,
                                        DeviceMessageListener listener,
                                        Authenticator authenticator) {
        BusController controller = new BusController(brokerConfig, authenticator);
        controller.registerMessageListener(listener);
        return controller;
    }

    @Bean(initMethod = "start", destroyMethod = "shutdown")
    public MQTTServer mqttServer(BusController mqttController,
                                 BrokerConfig brokerConfig,
                                 NettyConfig nettyConfig) {
        return new MQTTServer(mqttController, brokerConfig, nettyConfig);
    }

    /**
     * 服务端MQTT客户端连接
     */
    @Bean(destroyMethod = "close")
    @DependsOn(value = "mqttServer")
    public MqttClient mqttClient() throws Exception {
        String brokerAddress = String.format("tcp://127.0.0.1:%s", this.port);
        MqttClient client = new MqttClient(
                brokerAddress,
                "server",
                new MemoryPersistence());
        MqttConnectOptions options = new MqttConnectOptions();
        options.setCleanSession(true);
        options.setMqttVersion(MQTT_VERSION_3_1_1);
        client.connect(options);
        return client;
    }

}
