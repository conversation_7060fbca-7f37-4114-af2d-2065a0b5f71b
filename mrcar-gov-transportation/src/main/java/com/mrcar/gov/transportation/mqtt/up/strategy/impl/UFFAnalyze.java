package com.mrcar.gov.transportation.mqtt.up.strategy.impl;

import com.alibaba.fastjson.JSON;
import com.izu.mq.core.MQProducer;
import com.mrcar.gov.model.mqtt.enums.MqttVehicleExceptionEnum;
import com.mrcar.gov.model.mqtt.mq.MqttTopic;
import com.mrcar.gov.model.mqtt.mq.VehicleExceptionMqDTO;
import com.mrcar.gov.transportation.mqtt.enums.ProtocolEnum;
import com.mrcar.gov.transportation.mqtt.enums.UCodeIdEnum;
import com.mrcar.gov.transportation.mqtt.up.bean.UFF;
import com.mrcar.gov.transportation.mqtt.up.strategy.TboxMsgProtocolBaseAnalyze;
import io.netty.buffer.ByteBuf;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class UFFAnalyze extends TboxMsgProtocolBaseAnalyze<UFF> {
    @Autowired
    private MQProducer mqProducer;
    @Override
    public UFF analyzeAction(String terminalId,ByteBuf byteBuf) {
        UFF uff = new UFF();
        uff.setExceptionCode(byteBuf.readByte());
        uff.setExceptionStatus(byteBuf.readByte());
        return uff;
    }

    @Override
    public void doAfterAnalyzeAction(String terminalId,UFF uff) {
        VehicleExceptionMqDTO mqDTO = new VehicleExceptionMqDTO();
        mqDTO.setSimNo(terminalId);
        mqDTO.setExceptionType(MqttVehicleExceptionEnum.ExceptionTypeEnum.getEnumByProCdoe(uff.getExceptionCode().intValue()).getCode());
        mqDTO.setExceptionStatus(MqttVehicleExceptionEnum.ExceptionStatusEnum.getEnumByProCdoe(uff.getExceptionStatus().intValue()).getCode());

        if(MqttVehicleExceptionEnum.ExceptionTypeEnum.DEVICE_TOKE_OFF.getCode() == mqDTO.getExceptionType()){
            if(MqttVehicleExceptionEnum.ExceptionStatusEnum.ON.getCode() == mqDTO.getExceptionStatus()){
                mqProducer.publishMessage(MqttTopic.TOPIC_VEHICLE_EXCEPTION_TOPIC,
                        MqttTopic.DEFAULT_TAG, terminalId ,JSON.toJSONString(mqDTO));
                log.info("terminalId:{},vehicle exception 发生,发送mq:{}",terminalId,JSON.toJSONString(mqDTO));
            }else {
                mqProducer.publishMessage(MqttTopic.TOPIC_VEHICLE_EXCEPTION_RELEASE_TOPIC,
                        MqttTopic.DEFAULT_TAG, terminalId ,JSON.toJSONString(mqDTO));
                log.info("terminalId:{},vehicle exception 解除,发送mq:{}",terminalId,JSON.toJSONString(mqDTO));
            }
        }


    }

    @Override
    public Byte getCodeId() {
        return UCodeIdEnum.EXCEPTION_REPORT.getCode();
    }

    @Override
    public String getProtocol() {
        return ProtocolEnum.DEFAULT.getMsg();
    }
}
