package com.mrcar.gov.rest.service.asset.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.izu.framework.exception.ApiException;
import com.izu.framework.resp.InfoCode;
import com.izu.framework.web.util.BeanUtil;
import com.mrcar.gov.asset.domain.GovTransactionApply;
import com.mrcar.gov.asset.domain.GovVehiclePlan;
import com.mrcar.gov.asset.service.impl.GovTransactionApplyServiceImpl;
import com.mrcar.gov.common.constant.business.GovTransactionApplyEnum;
import com.mrcar.gov.common.dto.business.request.GovApproveReplyReqDTO;
import com.mrcar.gov.common.dto.business.request.GovTransactionApplySaveDTO;
import com.mrcar.gov.common.dto.business.response.GovTransactionApplyRespDTO;
import com.mrcar.gov.common.dto.workflow.enums.BpmProcessInstanceResultEnum;
import com.mrcar.gov.common.dto.workflow.enums.ModelEnum;
import com.mrcar.gov.common.dto.workflow.instance.ApplyStartDTO;
import com.mrcar.gov.common.dto.workflow.instance.ApplyStartSwitchDTO;
import com.mrcar.gov.common.dto.workflow.instance.BpmMessageSendApproveResultDTO;
import com.mrcar.gov.workflow.service.impl.WorkflowApprovalService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class GovTransactionApplyServiceExImpl extends GovTransactionApplyServiceImpl {

    @Resource
    private WorkflowApprovalService workflowApprovalService;


    @Transactional(rollbackFor = Exception.class)
    @Override
    public String saveAndSubmit(GovTransactionApplySaveDTO reqDTO) {

        if (CollectionUtils.isEmpty(reqDTO.getGovApplyVehicleDTOList())) {
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "车辆信息不能为空");
        }

        if (reqDTO.getApplyType() == GovTransactionApplyEnum.ApplyTypeEnum.UPDATE.getCode()) {
            Set<Object> vehicleNoSet = reqDTO.getGovApplyVehicleDTOList().stream()
                    .map(item -> item.getGovVehiclePlan().getVehicleNo()).collect(Collectors.toSet());
            //更新需要检查车辆信息
            if (vehicleNoSet.isEmpty()) {
                throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "更新车辆不得为空");
            }
        }
        ApplyStartSwitchDTO applyStartSwitchDTO = new ApplyStartSwitchDTO();
        if (Objects.equals(reqDTO.getApplyType(), GovTransactionApplyEnum.ApplyTypeEnum.UPDATE.getCode())) {
            applyStartSwitchDTO.setBusinessType(ModelEnum.BusinessTypeEnum.UPDATE_APPROVAL.getCode());
        } else if (Objects.equals(reqDTO.getApplyType(), GovTransactionApplyEnum.ApplyTypeEnum.CONFIGURE.getCode())) {
            applyStartSwitchDTO.setBusinessType(ModelEnum.BusinessTypeEnum.EQUIPMENT_UPDATE_APPROVAL.getCode());
        }

        applyStartSwitchDTO.setLoginCompanyId(reqDTO.getLoginCompanyId());
        applyStartSwitchDTO.setLoginDeptId(reqDTO.getLoginUserBelongDeptId());
        // flag  : true 表示流程已经启动了 需要走审批流程
        Boolean flag = workflowApprovalService.isApprovalActive(applyStartSwitchDTO);
        GovTransactionApply govTransactionApply = this.saveTransactionApply(reqDTO, GovTransactionApplyEnum.ApplyStatusEnum.COMPLETED.getCode());
        String applyNo = govTransactionApply.getApplyNo();
        if (flag) {
            // 保存申请信息
            //  提交申请信息 workflow 发起流程
            ApplyStartDTO applyStartDTO = BeanUtil.copyObject(reqDTO, ApplyStartDTO.class);
            applyStartDTO.setBusinessType(applyStartSwitchDTO.getBusinessType());
            applyStartDTO.setBusinessNo(applyNo);
            GovTransactionApplyRespDTO applyDetail = this.getApplyDetail(applyNo);
            Map<String, Object> applyInfo = new HashMap<>();
            applyInfo.put("applyNo", applyNo);
            applyInfo.put("applyRemark", reqDTO.getApplyRemark());
            applyInfo.put("applyStructName", reqDTO.getApplyStructName());
            String variables = JSON.toJSONString(applyInfo);
            applyStartDTO.setVariables(variables);
            applyStartDTO.setLoginDeptId(reqDTO.getLoginUserBelongDeptId());
            applyStartDTO.setLoginDeptName(reqDTO.getLoginUserBelongDeptName());
            String approvalId = workflowApprovalService.startApproval(applyStartDTO);
            log.info("配备更新提交审批流成功approvalId={}", approvalId);
            GovTransactionApply save = new GovTransactionApply();
            save.setApplyStatus(GovTransactionApplyEnum.ApplyStatusEnum.APPROVAL_IN_PROGRESS.getCode());
            save.setApproveId(approvalId);
            // 更新申请信息
            this.update(save, new UpdateWrapper<GovTransactionApply>().lambda().eq(GovTransactionApply::getApplyNo, applyNo));
        } else {
            // 这里需要回添  批复信息
            List<GovVehiclePlan> govVehiclePlans = govVehiclePlanMapper.selectList(new QueryWrapper<GovVehiclePlan>().lambda().eq(GovVehiclePlan::getApplyNo, applyNo));
            List<GovTransactionApplySaveDTO.GovApplyVehicleReqDTO> govApplyVehicleDTOList = govVehiclePlans.stream().map(govVehiclePlan -> {
                GovApproveReplyReqDTO govApproveReplyReqDTO = new GovApproveReplyReqDTO();
                govApproveReplyReqDTO.setVehiclePlanId(govVehiclePlan.getId());
                govApproveReplyReqDTO.setApplyNo(govVehiclePlan.getApplyNo());
                govApproveReplyReqDTO.setUseAttributeId(govVehiclePlan.getUseAttributeId());
                govApproveReplyReqDTO.setVehicleType(govVehiclePlan.getVehicleType());
                govApproveReplyReqDTO.setFuelType(govVehiclePlan.getFuelType());
                govApproveReplyReqDTO.setVehicleBarePrice(govVehiclePlan.getVehicleBarePrice());
                govApproveReplyReqDTO.setOutputVolume(govVehiclePlan.getOutputVolume());
                govApproveReplyReqDTO.setVehicleNum(govVehiclePlan.getVehicleNum());
                govApproveReplyReqDTO.setDisposalMethod(govVehiclePlan.getDisposalMethod());
                govApproveReplyReqDTO.setTransferInStructCode(govVehiclePlan.getTransferInStructCode());
                govApproveReplyReqDTO.setTransferInStructName(govVehiclePlan.getTransferInStructName());
                govApproveReplyReqDTO.setCompanyId(reqDTO.getLoginCompanyId());

                GovTransactionApplySaveDTO.GovApplyVehicleReqDTO govApplyVehicleDTO = new GovTransactionApplySaveDTO.GovApplyVehicleReqDTO();
                govApplyVehicleDTO.setGovApproveReply(govApproveReplyReqDTO);
                return govApplyVehicleDTO;
            }).collect(Collectors.toList());
            // 删除旧 车辆批复信息
            reqDTO.setGovApplyVehicleDTOList(govApplyVehicleDTOList);
            reqDTO.setApplyNo(applyNo);
            // 更新申请信息 修改为待批复上传 因为后面需要自动走批复上传的流程
            GovTransactionApply save = new GovTransactionApply();
            save.setApplyStatus(GovTransactionApplyEnum.ApplyStatusEnum.AWAITING_APPROVAL_FEEDBACK.getCode());
            this.update(save, new UpdateWrapper<GovTransactionApply>().lambda().eq(GovTransactionApply::getApplyNo, applyNo));
            this.upLoadReply(reqDTO);
        }
        return applyNo;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void approveResult(BpmMessageSendApproveResultDTO bpmMessageSendApproveResultDTO) {
        Byte result = bpmMessageSendApproveResultDTO.getResult();
        if (result == null) {
            log.warn("审批结果为空");
            return;
        }
        log.info("配备更新接收到审批信息: {}", JSON.toJSONString(bpmMessageSendApproveResultDTO));
        String applyNo = bpmMessageSendApproveResultDTO.getBusinessNo();
        GovTransactionApply save = new GovTransactionApply();
        save.setApproveDate(new Date());
        if (Objects.equals(result, BpmProcessInstanceResultEnum.APPROVE.getResult())) {
            //审批通过
            //修改状态  上传批复
            save.setApplyStatus(GovTransactionApplyEnum.ApplyStatusEnum.AWAITING_APPROVAL_FEEDBACK.getCode());
        } else if (Objects.equals(result, BpmProcessInstanceResultEnum.REJECT.getResult())) {
            //REJECT 不通过
            //修改状态  审批 驳回
            save.setApplyStatus(GovTransactionApplyEnum.ApplyStatusEnum.APPROVAL_REJECTED.getCode());
        } else if (Objects.equals(result, BpmProcessInstanceResultEnum.CANCEL.getResult())) {
            //CANCEL 取消
            //修改状态  待提交
            save.setApplyStatus(GovTransactionApplyEnum.ApplyStatusEnum.PENDING_SUBMISSION.getCode());
        }
        this.updateByApplyNo(applyNo, save);
    }

}
