package com.mrcar.gov.rest.service.asset.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.izu.framework.exception.ApiException;
import com.izu.framework.resp.InfoCode;
import com.izu.framework.response.PageDTO;
import com.izu.framework.util.BeanUtil;
import com.mrcar.gov.asset.common.RelationStatusEnum;
import com.mrcar.gov.asset.domain.GovCarRefuelingRecord;
import com.mrcar.gov.asset.domain.GovOilConsumption;
import com.mrcar.gov.asset.domain.GovVehicleDriverRelation;
import com.mrcar.gov.asset.service.GovCarRefuelingRecordService;
import com.mrcar.gov.asset.service.GovGpsVehicleRelationService;
import com.mrcar.gov.asset.service.GovVehicleBaseInfoService;
import com.mrcar.gov.asset.service.GovVehicleDriverRelationService;
import com.mrcar.gov.asset.utils.DateUtil;
import com.mrcar.gov.base.service.SequenceGenerator;
import com.mrcar.gov.common.constant.RestErrorCode;
import com.mrcar.gov.common.constant.asset.AssetNoEnum;
import com.mrcar.gov.common.constant.asset.FuelTypeEnum;
import com.mrcar.gov.common.constant.asset.OilConsumptionEnum;
import com.mrcar.gov.common.constant.user.GovCommonJudgeEnum;
import com.mrcar.gov.common.constant.user.GovDataPermTypeEnum;
import com.mrcar.gov.common.constant.user.GovStructTypeEnum;
import com.mrcar.gov.common.dto.asset.CarRefuelingRecordDTO;
import com.mrcar.gov.common.dto.asset.CarRefuelingRecordDetailDTO;
import com.mrcar.gov.common.dto.asset.relation.resp.GpsVehicleRelationListRespDTO;
import com.mrcar.gov.common.dto.asset.request.*;
import com.mrcar.gov.common.dto.asset.response.VehicleInfoDTO;
import com.mrcar.gov.common.dto.asset.response.VehicleLightListRespDTO;
import com.mrcar.gov.common.dto.device.req.CarLocationTrailReqDTO;
import com.mrcar.gov.common.dto.device.req.CarTravelDistanceReqDTO;
import com.mrcar.gov.common.dto.device.resp.DeviceHistoryTraceDTO;
import com.mrcar.gov.common.dto.device.resp.GovVehicleStatusDTO;
import com.mrcar.gov.common.dto.iot.FuelInfo;
import com.mrcar.gov.common.dto.iot.req.CarLocationOilReqDTO;
import com.mrcar.gov.common.dto.iot.resp.CarLocationOilResDTO;
import com.mrcar.gov.common.dto.tbox.TboxLogReqDTO;
import com.mrcar.gov.common.dto.workflow.enums.BpmProcessInstanceResultEnum;
import com.mrcar.gov.common.dto.workflow.enums.ModelEnum;
import com.mrcar.gov.common.dto.workflow.instance.ApplyStartDTO;
import com.mrcar.gov.common.dto.workflow.instance.ApplyStartSwitchDTO;
import com.mrcar.gov.common.dto.workflow.instance.BpmProcessInstanceCancelReqDTO;
import com.mrcar.gov.common.enums.device.GovGpsDeviceEnum;
import com.mrcar.gov.common.util.DateUtils;
import com.mrcar.gov.common.util.LbsFuelUtil;
import com.mrcar.gov.common.util.LbsUtil;
import com.mrcar.gov.iot.service.GovCarInfoService;
import com.mrcar.gov.iot.service.GovDeviceMongoService;
import com.mrcar.gov.rest.service.asset.CarRefuelingRecordService;
import com.mrcar.gov.user.domain.GovStruct;
import com.mrcar.gov.user.service.GovDataPermService;
import com.mrcar.gov.user.service.GovStructService;
import com.mrcar.gov.workflow.service.impl.WorkflowApprovalService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CarRefuelingRecordServiceImpl implements CarRefuelingRecordService {

    private static final long DAYS31SECONDS = 31*24*60*60;

    private static final BigDecimal km_100 = new BigDecimal(100);

    @Resource
    private GovCarRefuelingRecordService govCarRefuelingRecordService;

    @Resource
    private WorkflowApprovalService workflowApprovalService;

    @Resource
    private GovVehicleBaseInfoService govVehicleBaseInfoService;

    @Resource
    private GovGpsVehicleRelationService govGpsVehicleRelationService;

    @Resource
    private GovCarInfoService govCarInfoService;

    @Resource
    private GovStructService govStructService;

    @Resource
    private GovVehicleDriverRelationService govVehicleDriverRelationService;

    @Resource
    private SequenceGenerator sequenceGenerator;

    @Resource
    private GovDeviceMongoService govDeviceMongoService;

    @Resource
    private GovDataPermService govDataPermService;



    private void baseParamIdCheck(Integer id) {
        if (id == null)
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "请求参数缺失");
    }

    /**
     * 审批撤销
     * @param reqDTO
     */
    @Override
    public void withdrawal(AppOilConsumptionReqBaseDTO reqDTO) {
        this.baseParamIdCheck(reqDTO.getId());

        GovCarRefuelingRecord govCarRefuelingRecord = govCarRefuelingRecordService.getByPrimaryKey(reqDTO.getId());
        if (govCarRefuelingRecord == null) {
            throw new ApiException(RestErrorCode.REFUEL_RECORD_NOT_EXIST_ERROR);
        }

        log.info("加油当前审批状态：{}", govCarRefuelingRecord.getApprovalStatus());
        if (Integer.valueOf(OilConsumptionEnum.ApprovalStatusEnum.APPROVAL_WITHDRAWAL.getCode())
                .equals(govCarRefuelingRecord.getApprovalStatus())) {
            return;
        }

        try {
            boolean b = doWithdrawal(reqDTO, govCarRefuelingRecord);
            if (b) {
                govCarRefuelingRecordService.withdrawal(reqDTO);
            }
        } catch (Exception e) {
            log.error("加油审批撤回异常"+ e.getMessage(), e);
            throw new ApiException(RestErrorCode.REFUEL_OPERATION_FAILED_ERROR);
        }
    }

    /**
     * 撤回审批
     */
    private boolean doWithdrawal(AppOilConsumptionReqBaseDTO dto, GovCarRefuelingRecord record) {
        BpmProcessInstanceCancelReqDTO cancelReqVO = new BpmProcessInstanceCancelReqDTO() {{
            setId(record.getApprovalId());
            setLoginUserId(dto.getLoginUserId());
            setLoginUserCode(dto.getLoginUserCode());
            setLoginUserName(dto.getLoginUserName());
            setLoginCompanyId(dto.getLoginCompanyId());
            setLoginCompanyName(dto.getLoginCompanyName());
            setLoginDeptId(dto.getLoginUserBelongDeptId());
            setLoginDeptName(dto.getLoginUserBelongDeptName());
            setReason(StringUtils.isNotBlank(dto.getCancelReason()) ? dto.getCancelReason() : "用户撤回");
        }};
        boolean b = workflowApprovalService.cancelApproval(cancelReqVO) != null;
        log.info("加油审批撤销结果:{}，审批id:{}", b, dto.getApprovalId());
        return b;
    }


    /**
     * 查询下级部门
     * @param parentStructCode
     * @param loginDataPermType
     * @param loginCompanyId
     * @param dataCodeSet
     * @return
     */
    private List<String> getStructCode(String parentStructCode, Integer loginDataPermType, Integer loginCompanyId, Set<String> dataCodeSet) {
        return govStructService.getStructCodeByParentCode(parentStructCode, loginDataPermType, loginCompanyId, dataCodeSet);
    }

    /**
     * 保存加油记录
     * @param dto
     * @return
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public CarRefuelingRecordDetailDTO save(AppCarRefuelingRecordReqDTO dto) {
        this.baseCheck4Save(dto);

        QryVehicleInfoReqDTO qryVehicleInfoReqDTO = new QryVehicleInfoReqDTO();
        qryVehicleInfoReqDTO.setVehicleLicense(dto.getVehicleLicense());
        qryVehicleInfoReqDTO.setCompanyId(dto.getLoginCompanyId());
        List<VehicleInfoDTO> vehicleInfoDTOS = govVehicleBaseInfoService.qryVehicleInfo(qryVehicleInfoReqDTO);
        if (CollectionUtils.isEmpty(vehicleInfoDTOS)) {
            throw new ApiException(RestErrorCode.GPS_VEHICLE_NOT_FIND);
        }
        // 根据加油时间校验重复记录
        this.checkDuplicateRecord(vehicleInfoDTOS.get(0), dto);

        GovCarRefuelingRecord record = this.buildCarRefuelingRecord(dto);
        this.setVehicleInfo(record, vehicleInfoDTOS.get(0));

        // 查询和车辆绑定的司机
        Wrapper wrapper = Wrappers.lambdaQuery(GovVehicleDriverRelation.class)
                .eq(GovVehicleDriverRelation::getVehicleNo, vehicleInfoDTOS.get(0).getVehicleNo())
                .eq(GovVehicleDriverRelation::getRelationStatus, RelationStatusEnum.NORMAL.getCode());
        GovVehicleDriverRelation one = govVehicleDriverRelationService.getOne(wrapper);

        if (one != null) {
            record.setUserCode(one.getUserCode());
            record.setUserName(one.getUserName());
        }
        GovOilConsumption govOilConsumption = null;
        try {
            govOilConsumption = buildOilConsumption(dto, vehicleInfoDTOS.get(0));
        } catch (Exception e) {
            log.error("保存-油耗异常" + e.getMessage(), e);
//            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "解析油量失败");
        }
        CarRefuelingRecordDetailDTO detailDTO = govCarRefuelingRecordService.save(dto, record, govOilConsumption);
        // 首次加油及60天加油不支持分析 异常不提示
        if (com.mrcar.gov.asset.common.OilConsumptionEnum.WarnTypeEnum.FIRST_ADD_REFUEL.getCode().toString().
                equals(record.getWarnType())
                || com.mrcar.gov.asset.common.OilConsumptionEnum.WarnTypeEnum.MORE_THAN_31_DAYS.getCode().toString().
                equals(record.getWarnType())
                || com.mrcar.gov.asset.common.OilConsumptionEnum.WarnTypeEnum.ADD_RECORD.getCode().toString().
                equals(record.getWarnType())) {
            detailDTO.setWarnType(StringUtils.EMPTY);
            detailDTO.setWarnTypeValue(null);
        }
        return detailDTO;
    }

    /**
     * 重复数据校验
     */
    private void checkDuplicateRecord(VehicleInfoDTO vehicleInfoDTO, AppCarRefuelingRecordReqDTO dto) {
        // 重复记录校验
        Wrapper wrapper = Wrappers.lambdaQuery(GovCarRefuelingRecord.class)
                .eq(GovCarRefuelingRecord::getVehicleLicense, vehicleInfoDTO.getVehicleLicense())
                .eq(GovCarRefuelingRecord::getRefuelingTime, dto.getRefuelingTime())
                .eq(GovCarRefuelingRecord::getStatus, 1)
                .eq(GovCarRefuelingRecord::getCompanyId, dto.getLoginCompanyId())
                .in(GovCarRefuelingRecord::getApprovalStatus,
                        Arrays.asList(OilConsumptionEnum.ApprovalStatusEnum.APPROVED.getCode(),
                                OilConsumptionEnum.ApprovalStatusEnum.APPROVAL_PENDING.getCode()));
        long count = govCarRefuelingRecordService.count(wrapper);
        if (count > 0) {
            throw new ApiException(RestErrorCode.REFUEL_RECORD_EXIST_ERROR);
        }
        // 仪表盘里程不能小于已加油记录中最大的仪表盘里程
//        BigDecimal maxDashboardMileage = govCarRefuelingRecordService.getMaxDashboardMileage(dto);
//        if (maxDashboardMileage != null && maxDashboardMileage.compareTo(dto.getDashboardMileage()) >= 0) {
//            throw new ApiException(RestErrorCode.REFUEL_DASH_BOARD_MILEAGE_ERROR);
//
//        }


    }

    private void baseCheck4Save(AppCarRefuelingRecordReqDTO dto) {
        if (StringUtils.isBlank(dto.getVoucherUrl())) {
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "加油小票不能为空");
        }
        if (StringUtils.isBlank(dto.getDashboardUrl())) {
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "仪表盘照片不能为空");
        }
        if (StringUtils.isBlank(dto.getVehicleLicense())) {
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "车牌号不能为空");
        }
        if (StringUtils.isBlank(dto.getRefuelingTime())) {
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "加油时间不能为空");
        }
        if (dto.getAddValue() == null) {
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "加油升数不能为空");
        }
        if (dto.getAddValue().compareTo(BigDecimal.ZERO) <= 0) {
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "加油升数不能小于等于0");
        }
        if (dto.getAddValue().compareTo(BigDecimal.valueOf(100)) > 0) {
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "加油升数不能大于100L");
        }
        if (dto.getAddFee() == null) {
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "加油金额不能为空");
        }
        if (dto.getAddFee().compareTo(BigDecimal.ZERO) <= 0) {
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "加油金额不能小于等于0");
        }
        if (dto.getAddFee().compareTo(BigDecimal.valueOf(1000)) > 0) {
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "加油金额不能小大于1000元");
        }
        if (dto.getDashboardMileage() == null) {
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "仪表盘里程不能为空");
        }
        if (dto.getDashboardMileage().compareTo(BigDecimal.ZERO) <= 0) {
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "仪表盘里程不能小于等于0");
        }
        if (dto.getDashboardMileage().compareTo(BigDecimal.valueOf(1000000)) > 0) {
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "仪表盘里程不能大于1000000KM");
        }
        if (dto.getPayType() == null ||
                StringUtils.isBlank(OilConsumptionEnum.PayTypeEnum.getValueByCode(Byte.valueOf(dto.getPayType().toString())))) {
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "支付方式不存在");
        }
//        if (StringUtils.isBlank(dto.getRefuelingAddress())) {
//            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "加油地址不能为空");
//        }
        if (dto.getCooperationOrgStatus() == null) {
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "合作机构不能为空");
        }
        if (GovCommonJudgeEnum.YES.getCode().equals(dto.getCooperationOrgStatus())
                && StringUtils.isAnyBlank(dto.getOrgNo(), dto.getOrgName())) {
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "合作机构名称不能为空");
        }
    }

    @Override
     public PageDTO<CarRefuelingRecordDTO> listPage(CarRefuelingRecordPageReqDTO dto) {

        //数据权限是本人，无数据权限
        if (Objects.isNull(dto.getLoginDataPermType())
                ||Objects.equals(dto.getLoginDataPermType(), GovDataPermTypeEnum.SELF.getCode())) {
            return new PageDTO<>(dto.getPage(), dto.getPageSize(), 0, Lists.newArrayList());
        }
        Pair<List<String>, List<String>> unitAndDeptUnderCode = Pair.of(Lists.newArrayList(), Lists.newArrayList());
        if(StringUtils.isNotBlank(dto.getVehicleBelongDeptCode())){
            unitAndDeptUnderCode = govDataPermService.getUnitAndDeptUnderCode(
                    dto.getVehicleBelongDeptCode(), dto.getLoginCompanyId());
            // 选中的没有单位
            if(CollectionUtils.isEmpty(unitAndDeptUnderCode.getLeft())){
                return new PageDTO<>(dto.getPage(), dto.getPageSize(), 0, Lists.newArrayList());
            }

            dto.setVehicleBelongDeptCodes(unitAndDeptUnderCode.getLeft());
        }
        // 指定部门
        if (Objects.equals(dto.getLoginDataPermType(), GovDataPermTypeEnum.ASSIGN_STRUCT.getCode())) {

            Pair<List<String>, List<String>> assignStructUnitAndDept = govDataPermService.getUnitAndDeptByAssignStructCode(dto.getDataCodeSet());
            unitAndDeptUnderCode = govDataPermService.unitAndDeptIntersection(unitAndDeptUnderCode, assignStructUnitAndDept);
            if(CollectionUtils.isEmpty(unitAndDeptUnderCode.getLeft())){
                return new PageDTO<>(dto.getPage(), dto.getPageSize(), 0, Lists.newArrayList());
            }

            dto.setVehicleBelongDeptCodes(unitAndDeptUnderCode.getLeft());
        }

        log.info("请求参数：{}", JSONObject.toJSONString(dto));
        return govCarRefuelingRecordService.listPage(dto);
    }



    @Override
    public void listPageExport(HttpServletRequest request, HttpServletResponse response, CarRefuelingRecordPageReqDTO dto) {

        //数据权限是本人，无数据权限
        if (Objects.isNull(dto.getLoginDataPermType())
                ||Objects.equals(dto.getLoginDataPermType(), GovDataPermTypeEnum.SELF.getCode())) {
            dto.setExportEmptyFile(true);
        }
        Pair<List<String>, List<String>> unitAndDeptUnderCode = Pair.of(Lists.newArrayList(), Lists.newArrayList());
        if(StringUtils.isNotBlank(dto.getVehicleBelongDeptCode())){
            unitAndDeptUnderCode = govDataPermService.getUnitAndDeptUnderCode(
                    dto.getVehicleBelongDeptCode(), dto.getLoginCompanyId());
            // 选中的没有单位
            if(CollectionUtils.isEmpty(unitAndDeptUnderCode.getLeft())){
                dto.setExportEmptyFile(true);
            }

            dto.setVehicleBelongDeptCodes(unitAndDeptUnderCode.getLeft());
        }
        // 指定部门
        if (Objects.equals(dto.getLoginDataPermType(), GovDataPermTypeEnum.ASSIGN_STRUCT.getCode())) {

            Pair<List<String>, List<String>> assignStructUnitAndDept = govDataPermService.getUnitAndDeptByAssignStructCode(dto.getDataCodeSet());
            unitAndDeptUnderCode = govDataPermService.unitAndDeptIntersection(unitAndDeptUnderCode, assignStructUnitAndDept);
            if(CollectionUtils.isEmpty(unitAndDeptUnderCode.getLeft())){
                dto.setExportEmptyFile(true);
            }

            dto.setVehicleBelongDeptCodes(unitAndDeptUnderCode.getLeft());
        }
        govCarRefuelingRecordService.listPageExport(request, response, dto);
    }

    /**
     * 构建加油记录
     * @param dto
     * @return
     */
    private GovCarRefuelingRecord buildCarRefuelingRecord(AppCarRefuelingRecordReqDTO dto) {
        GovCarRefuelingRecord carRefuelingRecord = BeanUtil.copyObject(dto, GovCarRefuelingRecord.class);

        carRefuelingRecord.setPayType(dto.getPayType());
        carRefuelingRecord.setCreateId(dto.getLoginUserId());
        carRefuelingRecord.setCreateName(dto.getLoginUserName());

        carRefuelingRecord.setUpdateId(dto.getLoginUserId());
        carRefuelingRecord.setUpdateName(dto.getLoginUserName());

        carRefuelingRecord.setCompanyId(dto.getLoginCompanyId());
        carRefuelingRecord.setRefuelingCode(sequenceGenerator.generate(new Date(), AssetNoEnum.CAR_REFUELING_RECORD.getCode()));

        carRefuelingRecord.setRefuelingTime(DateUtil.stringToDate(dto.getRefuelingTime(), DateUtil.TIME_FORMAT));
        carRefuelingRecord.setDashboardTime(DateUtil.stringToDate(dto.getDashboardTime(), DateUtil.TIME_FORMAT));

        carRefuelingRecord.setVoucherTime(DateUtil.stringToDate(dto.getVoucherTime(), DateUtil.TIME_FORMAT));
        carRefuelingRecord.setCompanyId(dto.getLoginCompanyId());
        dto.setRefuelingCode(carRefuelingRecord.getRefuelingCode());


        return carRefuelingRecord;
    }

      /**
     * 设置保存车辆相关信息
     */
    protected void setVehicleInfo(GovCarRefuelingRecord record, VehicleInfoDTO vehicleInfoDTO) {

        record.setVehicleVin(vehicleInfoDTO.getVehicleVin());
        record.setVehicleBelongDeptCode(vehicleInfoDTO.getVehicleBelongDeptCode());
        record.setVehicleBelongDeptName(vehicleInfoDTO.getVehicleBelongDeptName());

        record.setVehicleManageDeptCode(vehicleInfoDTO.getVehicleManageDeptCode());
        record.setVehicleManageDeptName(vehicleInfoDTO.getVehicleManageDeptName());

        record.setVehicleUseDeptCode(vehicleInfoDTO.getVehicleUseDeptCode());
        record.setVehicleUseDeptName(vehicleInfoDTO.getVehicleUseDeptName());

        record.setVehicleUseStructCode(vehicleInfoDTO.getVehicleUseStructCode());
        record.setVehicleUseStructName(vehicleInfoDTO.getVehicleUseStructName());
        record.setManageCarType(vehicleInfoDTO.getManageCarType());



    }

    /**
     * 保存加油记录-并提提交审核
     * @param reqDTO
     */
    @Override
    public void toSave(AppOilConsumptionReqBaseDTO reqDTO) {
        this.baseParamIdCheck(reqDTO.getId());

        GovCarRefuelingRecord record = govCarRefuelingRecordService.getByPrimaryKey(reqDTO.getId());
        if (record == null) {
            throw new ApiException(RestErrorCode.REFUEL_RECORD_NOT_EXIST_ERROR);
        }
        reqDTO.setBusinessNo(record.getRefuelingCode());
        // 发起审批
        GovCarRefuelingRecord carRefuelingRecord = new GovCarRefuelingRecord();
        carRefuelingRecord.setStatus( 1);
        carRefuelingRecord.setUpdateId(reqDTO.getLoginUserId());
        carRefuelingRecord.setUpdateName(reqDTO.getLoginUserName());
        // 提交审批
        this.setApprovalId(carRefuelingRecord, reqDTO, Boolean.TRUE);
        govCarRefuelingRecordService.toSave(reqDTO, carRefuelingRecord);
    }


    private void setApprovalId(GovCarRefuelingRecord carRefuelingRecord, AppOilConsumptionReqBaseDTO dto, Boolean isWarn) {
        dto.setBusinessType(ModelEnum.BusinessTypeDefaultEnum.REFUELING_APPROVAL.getCode());
        Map<String, Object> map = new HashMap<>();
        map.put("isWarn", isWarn);
        if (StringUtils.isNotBlank(carRefuelingRecord.getRemark())) {
            map.put("applyRemark", carRefuelingRecord.getRemark());
        }
        startApproval(dto, map);
        carRefuelingRecord.setApprovalId(dto.getApprovalId());
        carRefuelingRecord.setApprovalStatus(Integer.valueOf(dto.getApprovalStatus()));
        if(StringUtils.isEmpty(dto.getApprovalId())){
            carRefuelingRecord.setApprovalTime(new Date());
        }
    }

    /**
     * 发起审批流
     */
    protected void startApproval(AppOilConsumptionReqBaseDTO dto, Map<String, Object> variables) {
        // 如果没有配审批流直接审批通过
        if (!workflowApprovalService.isApprovalActive(new ApplyStartSwitchDTO() {{
            setBusinessType(dto.getBusinessType());
            setLoginCompanyId(dto.getLoginCompanyId());
            setLoginDeptId(dto.getLoginUserBelongDeptId());
        }})) {
            dto.setApprovalStatus(OilConsumptionEnum.ApprovalStatusEnum.APPROVED.getCode());
            return ;
        }
        //发起审批
        ApplyStartDTO applyStartDTO = new ApplyStartDTO();
        applyStartDTO.setBusinessNo(dto.getBusinessNo());
        applyStartDTO.setBusinessType(dto.getBusinessType());
        applyStartDTO.setLoginUserId(dto.getLoginUserId());
        applyStartDTO.setLoginUserName(dto.getLoginUserName());
        applyStartDTO.setLoginCompanyId(dto.getLoginCompanyId());
        applyStartDTO.setLoginCompanyName(dto.getLoginCompanyName());
        applyStartDTO.setLoginDeptId(dto.getLoginUserBelongDeptId());
        applyStartDTO.setLoginDeptName(dto.getLoginUserBelongDeptName());

        variables.put("vehicleId",dto.getVehicleId());
        variables.put("vehicleVin",dto.getVehicleVin());
        // 流程实例变量设置
        applyStartDTO.setVariables(JSON.toJSONString(variables));
        //审批id
        String processId = workflowApprovalService.startApproval(applyStartDTO);
        log.info("加油发起审批 单号={}，审批实例ID={}", applyStartDTO.getBusinessNo(), processId );
        if(StringUtils.isNotBlank(processId)){
            dto.setApprovalStatus(BpmProcessInstanceResultEnum.PROCESS.getResult());
            dto.setApprovalId(processId);
        }
    }


    private GovOilConsumption buildOilConsumption(AppCarRefuelingRecordReqDTO dto, VehicleInfoDTO vehicleInfoDTO) {
        GovOilConsumption oilConsumption = new GovOilConsumption();
        dto.setCreateTime(new Date());
        // 查询上一个加油申请
        GovCarRefuelingRecord lastRecord = govCarRefuelingRecordService.selectLastRecord(dto);
        Date maxRefuelingTime = govCarRefuelingRecordService.selectMaxRefuelingTime(dto);
        if (lastRecord != null && maxRefuelingTime != null) {
            // 设置补录工单
            Date refuelingTime = DateUtil.stringToDate(dto.getRefuelingTime(), DateUtil.TIME_FORMAT);
            if (DateUtil.compareDate(refuelingTime, maxRefuelingTime) < 1) {
                oilConsumption.setWarnType(OilConsumptionEnum.WarnTypeEnum.ADD_RECORD.getCode() + "");
                log.info("补录加油记录");
            }

            oilConsumption.setLastRefuelingCode(lastRecord.getRefuelingCode());
            oilConsumption.setLastAddValue(lastRecord.getAddValue());
            oilConsumption.setLastAddFee(lastRecord.getAddFee());
            oilConsumption.setLastDashboardMileage(lastRecord.getDashboardMileage());
            oilConsumption.setLastRefuelingTime(lastRecord.getRefuelingTime());
            oilConsumption.setRefuelingCode(dto.getRefuelingCode());
            oilConsumption.setIntervalDays(DateUtil.daysBetween(lastRecord.getRefuelingTime(), refuelingTime, 1));

            BigDecimal abs = dto.getDashboardMileage().subtract(oilConsumption.getLastDashboardMileage()).abs();
            oilConsumption.setDashboardMileage(abs);
            // 查找上次gps返点和本次gps返点里程
            CarLocationOilReqDTO oilReqDTO = new CarLocationOilReqDTO();
            oilReqDTO.setVehicleLicense(dto.getVehicleLicense());
            oilReqDTO.setVehicleVin(vehicleInfoDTO.getVehicleVin());
            oilReqDTO.setDashboardMileage(dto.getDashboardMileage());
            oilReqDTO.setAddValue(dto.getAddValue());
            oilReqDTO.setStartTime(DateUtil.format(lastRecord.getRefuelingTime(), DateUtil.TIME_FORMAT));
            oilReqDTO.setEndTime(DateUtil.format(refuelingTime, DateUtil.TIME_FORMAT));
            oilReqDTO.setVehicleNo(vehicleInfoDTO.getVehicleNo());
            try {

                CarLocationOilResDTO carLocationOilResDTO = getOilDistance(oilReqDTO);
                // 仪表盘油耗 本次录入的油耗/ 形式里程
                oilConsumption.setOilConsumption(dto.getAddValue());
                if (oilConsumption.getDashboardMileage().compareTo(BigDecimal.ZERO) != 0) {
                    oilConsumption.setDashboardOilConsumption(divide(oilConsumption.getOilConsumption(), divide(oilConsumption.getDashboardMileage(), km_100)));
                }
                // gps 油耗
                if (carLocationOilResDTO != null && StringUtils.isNotBlank(carLocationOilResDTO.getSumOil())) {
                    log.info("累计油耗L={}，gpsMileage：{}，gpsOilConsumption：{}", carLocationOilResDTO.getSumOil(),
                            carLocationOilResDTO.getTravelMile(), carLocationOilResDTO.getAvgOil());
                    oilConsumption.setGpsMileage(stringToBigDecimal(carLocationOilResDTO.getTravelMile()));
                    oilConsumption.setGpsOilConsumption(stringToBigDecimal(carLocationOilResDTO.getAvgOil()));
                }



            } catch (Exception e) {
                log.error("查找上次gps返点和本次gps返点里程 异常" + e.getMessage(), e);

            }
            long duration = DateUtils.getDuration(lastRecord.getRefuelingTime(), refuelingTime);
            log.info("两次加油时间差值：{}", duration);
            // 计算两次差值，如果超过31天,则不支持分析
            if(duration > DAYS31SECONDS){
                oilConsumption.setWarnType(OilConsumptionEnum.WarnTypeEnum.MORE_THAN_31_DAYS.getCode().toString());
            } else {
                oilConsumption.setWarnType(getWarnType(oilConsumption));
            }

        } else {
            log.info("首次加油");
            oilConsumption.setWarnType(OilConsumptionEnum.WarnTypeEnum.FIRST_ADD_REFUEL.getCode().toString());
        }
        return oilConsumption;
    }

    public static BigDecimal stringToBigDecimal(String bigDecimal) {
        return StringUtils.isNotEmpty(bigDecimal) ? new BigDecimal(bigDecimal) : null;
    }

    public static BigDecimal divide(BigDecimal num1, BigDecimal num2) {
        return num1.divide(num2, 2, RoundingMode.HALF_UP);
    }

    private String getWarnType(GovOilConsumption oilConsumption) {
        List<String> warnTypes = new ArrayList<>();

        warnTypes.add(calculateWarnType(oilConsumption.getGpsOilConsumption(), OilConsumptionEnum.RecordEnum.GPS));
        warnTypes.add(calculateWarnType(oilConsumption.getDashboardOilConsumption(), OilConsumptionEnum.RecordEnum.DASHBOARD));
        warnTypes.add(oilConsumption.getWarnType());
        return warnTypes.stream().filter(StringUtils::isNotBlank).sorted().collect(Collectors.joining(","));
    }

    private String calculateWarnType(BigDecimal oilConsumption, OilConsumptionEnum.RecordEnum recordEnum) {
        if(Objects.isNull(oilConsumption)){
            return "";
        }
        Map<OilConsumptionEnum.RecordEnum, OilConsumptionEnum.WarnTypeEnum> highMap = new HashMap<>(2);
        highMap.put(OilConsumptionEnum.RecordEnum.GPS, OilConsumptionEnum.WarnTypeEnum.HIGH_GPS);
        highMap.put(OilConsumptionEnum.RecordEnum.DASHBOARD, OilConsumptionEnum.WarnTypeEnum.HIGH_DASHBOARD);
        Map<OilConsumptionEnum.RecordEnum, OilConsumptionEnum.WarnTypeEnum> lowMap = new HashMap<>(2);
        lowMap.put(OilConsumptionEnum.RecordEnum.GPS, OilConsumptionEnum.WarnTypeEnum.LOW_GPS);
        lowMap.put(OilConsumptionEnum.RecordEnum.DASHBOARD, OilConsumptionEnum.WarnTypeEnum.LOW_DASHBOARD);
        Byte code = null;
        if (
                oilConsumption.compareTo(BigDecimal.valueOf(16)) > 0) {
            code = highMap.get(recordEnum).getCode();
        } if (oilConsumption.compareTo(BigDecimal.valueOf(7)) < 0) {
            code = lowMap.get(recordEnum).getCode();
        }
        return code == null ? "" : code.toString();
    }



    public CarLocationOilResDTO getOilDistance(CarLocationOilReqDTO carLocationOilReqDTO) throws Exception {
        CarLocationOilResDTO carLocationOilResDTO = new CarLocationOilResDTO();
        if(StringUtils.isNotBlank(carLocationOilReqDTO.getVehicleVin())){
            String startTime = carLocationOilReqDTO.getStartTime();
            String endTime = carLocationOilReqDTO.getEndTime();

            Date startDate = DateUtils.parse(startTime,DateUtils.TIME_FORMAT);
            Date endDate = DateUtils.parse(endTime,DateUtils.TIME_FORMAT);
            VehicleLightListRespDTO vehicleDTO = getVehicleInfo(carLocationOilReqDTO.getVehicleVin(),carLocationOilReqDTO.getVehicleLicense());
            carLocationOilResDTO.setVol(vehicleDTO.getOilTankVolume());
            //计算油耗百分比
            int fuelVolume = 0;
            if(StringUtils.isNotBlank(vehicleDTO.getOilTankVolume())){
                fuelVolume = Integer.parseInt(vehicleDTO.getOilTankVolume());
            }
            log.info("邮箱容积：{}，动力类型：{}", fuelVolume, vehicleDTO.getFuelType());

            //判断如果查询时间超过31天则取仪表盘里程和加油升数
            if(DateUtils.getDuration(startDate,endDate) > DAYS31SECONDS){
                log.info("超过31天...");
                carLocationOilResDTO.setSumOil(carLocationOilReqDTO.getAddValue().toString());
                carLocationOilResDTO.setTravelMile(carLocationOilReqDTO.getDashboardMileage().toString());
                carLocationOilResDTO.setTravelMileNoDistinct(carLocationOilReqDTO.getDashboardMileage().toString());
                BigDecimal avgOil = carLocationOilReqDTO.getAddValue().multiply(new BigDecimal(100))
                        .divide(carLocationOilReqDTO.getDashboardMileage(),2,BigDecimal.ROUND_HALF_UP);
                carLocationOilResDTO.setAvgOil(avgOil.toString());
                carLocationOilResDTO.setVol(fuelVolume+"");
                return carLocationOilResDTO;
            }

            List<GpsVehicleRelationListRespDTO> list = govGpsVehicleRelationService.getCurrentGovGpsVehicleRelationTboxListByVehicleNo(
                    Collections.singletonList(carLocationOilReqDTO.getVehicleNo()));

            List<GpsVehicleRelationListRespDTO> collect = list.stream().filter(
                    deviceBindResDTO ->
                            GovGpsDeviceEnum.DeviceTypeEnum.TBOX.getCode().equals(deviceBindResDTO.getDeviceType())).
                    collect(Collectors.toList());
            if(!CollectionUtils.isEmpty(collect)){
                GpsVehicleRelationListRespDTO deviceBindResDTO = collect.get(0);
                CarLocationTrailReqDTO carLocationTrailReqDTO=new CarLocationTrailReqDTO();
                carLocationTrailReqDTO.setCarNo(vehicleDTO.getVehicleLicense());
                carLocationTrailReqDTO.setDeviceType(
                        deviceBindResDTO.getDeviceType()==null?(GovGpsDeviceEnum.DeviceTypeEnum.TBOX.getCode()+""):(deviceBindResDTO.getDeviceType()+""));
                carLocationTrailReqDTO.setBeginCreateDate(startTime);
                carLocationTrailReqDTO.setEndCreateDate(endTime);
                carLocationTrailReqDTO.setSimNo(deviceBindResDTO.getSimNo());
                carLocationTrailReqDTO.setCoordinate("BAIDU");
                carLocationTrailReqDTO.setDeviceNo(deviceBindResDTO.getDeviceNo());
                carLocationTrailReqDTO.setDeviceSysNo(deviceBindResDTO.getDeviceSysNo());
                carLocationTrailReqDTO.setVehicleVin(vehicleDTO.getVehicleVin());
                BigDecimal carDistance=BigDecimal.ZERO;
                try {
                    DeviceHistoryTraceDTO deviceHistoryTraceDTO = govCarInfoService.queryHistoryTrace(carLocationTrailReqDTO);
                    if (deviceHistoryTraceDTO != null) {
                        //解析查询轨迹返回值
                        // 判空处理
                        carDistance = Optional.ofNullable(deviceHistoryTraceDTO.getDistance()).orElse(BigDecimal.ZERO);
                    }
                }catch (Exception e){
                    log.error("getOilDistance-error,e=",e);
                }
                log.info("车机行驶距离1，carDistance：{}", carDistance);
                BigDecimal realMile = carDistance;
                carLocationOilResDTO.setTravelMileNoDistinct(realMile.toString());

                CarTravelDistanceReqDTO distanceReqDTO = new CarTravelDistanceReqDTO();
                distanceReqDTO.setVehicleNo(carLocationOilReqDTO.getVehicleNo());
                distanceReqDTO.setCarNo(carLocationOilReqDTO.getVehicleLicense());
                distanceReqDTO.setSimNo(deviceBindResDTO.getSimNo());
                distanceReqDTO.setBeginCreateDate(carLocationOilReqDTO.getStartTime());
                distanceReqDTO.setEndCreateDate(carLocationOilReqDTO.getEndTime());


                List<String> deviceNos = collect.stream().map(
                                GpsVehicleRelationListRespDTO::getDeviceNo).collect(Collectors.toList());
                distanceReqDTO.setDeviceNoList(deviceNos);
                Double distance = govDeviceMongoService.calculateTravelDistanceForCarTbox(distanceReqDTO);
                // 这里返回得是m，转换成km
                distance = distance != null ? distance * 0.001 : 0;

//                BigDecimal distance = CarnetRpc.getCarTboxDistanceV2(deviceBindResDTO.getVehicleLicense(),carLocationOilReqDTO.getStartTime(),carLocationOilReqDTO.getEndTime());
                carLocationOilResDTO.setTravelMile(BigDecimal.valueOf(distance).setScale(2,BigDecimal.ROUND_HALF_UP).toString());
                log.info("车机行驶距离2，distance：{}", carLocationOilResDTO.getTravelMile());

                //电车不参与计算油耗
                if(!FuelTypeEnum.ELECTRICITY.getCode().equals(vehicleDTO.getFuelType())
                        && fuelVolume > 1){
                    //耗油百分比,fuelVolume油箱容量
                    carLocationOilReqDTO.setSimNo(deviceBindResDTO.getSimNo());
                    //查询起点前30分钟
                    Date start30M = DateUtils.addDateMinute(startDate,-30);
                    carLocationOilReqDTO.setStartTime(DateUtils.format(start30M,DateUtils.TIME_FORMAT));
                    //查询终点后30分钟
                    Date end30M = DateUtils.addDateMinute(endDate,30);
                    carLocationOilReqDTO.setEndTime(DateUtils.format(end30M,DateUtils.TIME_FORMAT));


                    TboxLogReqDTO tboxLogReqDTO = new TboxLogReqDTO();
                    tboxLogReqDTO.setPage(1);
                    tboxLogReqDTO.setPageSize(10000);
                    tboxLogReqDTO.setSimNo(deviceBindResDTO.getSimNo());
                    tboxLogReqDTO.setBeginCreateDate(DateUtils.format(start30M,DateUtils.TIME_FORMAT));
                    tboxLogReqDTO.setEndCreateDate(DateUtils.format(end30M,DateUtils.TIME_FORMAT));

                    com.izu.framework.web.rest.response.PageDTO<GovVehicleStatusDTO> pageDTO = null;
                    try {
                        pageDTO = govCarInfoService.queryTBoxLogByPage(tboxLogReqDTO);
                    } catch (Exception e) {
                        log.error("getOilDistance-error,e="+ e.getMessage(),e);
                    }
                    if (pageDTO == null) {
                        log.info("车机状态数据为空，simNo：{}，vehicleNo：{}", deviceBindResDTO.getSimNo(),
                                carLocationOilReqDTO.getVehicleNo());
                        return carLocationOilResDTO;
                    }

                    List<GovVehicleStatusDTO> result = pageDTO.getResult();

                    //方法1计算两次加油间是否为加满油，如果起始和截止点前后出现超过80的剩余油量则认为加满
                    boolean isExists = LbsFuelUtil.isExists(result);
                    if(isExists && carLocationOilReqDTO.getAddValue() != null
                            && carLocationOilReqDTO.getAddValue().compareTo(BigDecimal.ZERO) > 0){
                        BigDecimal useOil = carLocationOilReqDTO.getAddValue();
                        carLocationOilResDTO.setSumOil(useOil.toString());
                        BigDecimal avgOil = realMile.compareTo(BigDecimal.ZERO) == 0 ?
                                BigDecimal.ZERO : useOil.multiply(new BigDecimal(100)).divide(realMile,2,BigDecimal.ROUND_HALF_UP);
                        carLocationOilResDTO.setAvgOil(avgOil.toString());
                        log.info("计算油耗分析方法1,useFuel={},avgOil={},fuelVolume={},realMile={}",useOil,avgOil,fuelVolume,realMile);
                        return carLocationOilResDTO;
                    }
                    result = result.stream()
                            .filter(carStatusDTO -> carStatusDTO.getCreateDate().after(startDate) && carStatusDTO.getCreateDate().before(endDate))
                            .collect(Collectors.toList());
                    //方法2计算两次加油间的油量，通过降噪算法
                    int useFuel1 = this.getUsedFuel(result,fuelVolume);
                    //方法3计算两次加油间的引擎状态，获取剩余油量
                    int useFuel2 = LbsFuelUtil.calculateUseFuel(result);
                    log.info("两种方法计算油耗,useFuel1={},useFuel2={},fuelVolume={},realMile={}",useFuel1,useFuel2,fuelVolume,realMile);
                    BigDecimal useOil1 = new BigDecimal(useFuel1).multiply(new BigDecimal(fuelVolume)).divide(new BigDecimal(100),2,BigDecimal.ROUND_HALF_UP);
                    BigDecimal avgOil1 = realMile.compareTo(BigDecimal.ZERO) == 0 ?
                            BigDecimal.ZERO : useOil1.multiply(new BigDecimal(100)).divide(realMile,2,BigDecimal.ROUND_HALF_UP);

                    BigDecimal useOil2 = new BigDecimal(useFuel2).multiply(new BigDecimal(fuelVolume)).divide(new BigDecimal(100),2,BigDecimal.ROUND_HALF_UP);
                    BigDecimal avgOil2 = realMile.compareTo(BigDecimal.ZERO) == 0 ?
                            BigDecimal.ZERO : useOil2.multiply(new BigDecimal(100)).divide(realMile,2,BigDecimal.ROUND_HALF_UP);
                    //两次计算的平均油耗和10作比较，取最接近的
                    if(avgOil1.subtract(BigDecimal.TEN).abs().compareTo(avgOil2.subtract(BigDecimal.TEN).abs()) > 0){
                        carLocationOilResDTO.setSumOil(useOil2.toString());
                        carLocationOilResDTO.setAvgOil(avgOil2.toString());
                    }else{
                        carLocationOilResDTO.setSumOil(useOil1.toString());
                        carLocationOilResDTO.setAvgOil(avgOil1.toString());
                    }
                    BigDecimal avgOil = new BigDecimal(carLocationOilResDTO.getAvgOil());
                    if(avgOil.compareTo(new BigDecimal(7)) < 0 || avgOil.compareTo(new BigDecimal(16)) > 0){
                        String warn = "两种算法的油耗都超出正常范围请核对!".concat(carLocationOilReqDTO.toString()).concat("useFuel1").concat(useFuel1 + "").concat("useFuel2").concat(useFuel2 + "");
                        log.info(warn);
                    }
                }
            } else {
                log.info("没有绑定车机 vehicleNo：{}", carLocationOilReqDTO.getVehicleNo());
            }
        }
        return carLocationOilResDTO;
    }

    /**
     * 查询车辆信息
     * @param vin
     * @param vehicleLicense
     * @return
     */
    public VehicleLightListRespDTO getVehicleInfo(String vin,String vehicleLicense){
        VehicleLightListReqDTO reqDTO = new VehicleLightListReqDTO();
        reqDTO.setVehicleVin(vin);
        reqDTO.setVehicleLicense(vehicleLicense);
        PageDTO<VehicleLightListRespDTO> vehicleInfoDTOS = govVehicleBaseInfoService.lightListVehicle(reqDTO);
        return CollectionUtils.isEmpty(vehicleInfoDTOS.getResult()) ?
                new VehicleLightListRespDTO() : vehicleInfoDTOS.getResult().get(0);
    }

        /**
     * 计算油耗百分比
     * @param list
     * @return
     */
    public int getUsedFuel(List<GovVehicleStatusDTO> list,int fuelV){
        //计算油耗
        List<FuelInfo> fuelList = getUsedFuelDetail(list,fuelV);
        //百分比
        int useFuel = new LbsUtil().calculateFuel(fuelList);
        return useFuel;
    }

        public List<FuelInfo> getUsedFuelDetail(List<GovVehicleStatusDTO> list,int fuelV){
        //计算油耗
        List<FuelInfo> fuelList = list.stream().filter(carStatusDTO -> carStatusDTO.getRemainingFuel() != null
                && !"-1".equals(carStatusDTO.getRemainingFuel()))
                .sorted(Comparator.comparing(GovVehicleStatusDTO::getCreateDate))
                .map(carStatusDTO -> {
                    FuelInfo fuelInfo = new FuelInfo();
                    fuelInfo.setFuel(carStatusDTO.getRemainingFuel());
                    fuelInfo.setCreateDate(carStatusDTO.getCreateDate());
                    fuelInfo.setFuelV(fuelV);
                    return fuelInfo;
                })
                .collect(Collectors.toList());
        return fuelList;
    }

}
