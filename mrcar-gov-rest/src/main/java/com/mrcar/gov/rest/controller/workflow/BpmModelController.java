package com.mrcar.gov.rest.controller.workflow;

import com.izu.framework.resp.RestResp;
import com.izu.framework.response.PageDTO;
import com.izu.framework.web.validate.verify.Verify;
import com.mrcar.gov.common.dto.workflow.model.*;
import com.mrcar.gov.common.util.workflow.IoUtils;
import com.mrcar.gov.workflow.convert.BpmModelConvert;
import com.mrcar.gov.workflow.service.impl.BpmModelService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import java.io.ByteArrayOutputStream;
import java.io.StringReader;
import java.util.List;

/**
 * 工作流-流程模型
 * <p>
 * 该控制器提供了与流程模型管理相关的接口，包括模型的获取、创建、修改、导入、部署等操作。
 * </p>
 */
@Validated
@RestController
@RequestMapping("/bpm/model")
@Slf4j
public class BpmModelController {

    @Resource
    private BpmModelService modelService;

    /**
     * 获得模型分页
     *
     * @param pageReqDTO 模型分页请求参数
     * @return 分页结果
     */
    @PostMapping("/page")
    public RestResp<PageDTO<BpmModelPageItemRespDTO>> getModelPage(@RequestBody BpmModelPageReqDTO pageReqDTO) {
        return RestResp.ok(modelService.getModelPage(pageReqDTO));
    }

    /**
     * 获得模型
     *
     * @param modelUpdateReqDTO 模型更新请求参数，包含模型ID
     * @return 模型信息
     */
    @PostMapping("/get")
    public RestResp<BpmModelRespDTO> getModel(@RequestBody BpmModelUpdateReqDTO modelUpdateReqDTO) {
        return RestResp.ok(modelService.getModel(modelUpdateReqDTO.getId()));
    }

    /**
     * 新建模型
     *
     * @param createRetDTO 新建模型请求参数
     * @return 新建模型的ID
     */
    @PostMapping("/create")
    public RestResp<String> createModel(@Valid @RequestBody BpmModelCreateReqDTO createRetDTO) {
        return RestResp.ok(modelService.createModel(createRetDTO, null));
    }


    /**
     * 创建本单位模型
     *
     * @param
     * @return 新建模型的ID
     */
    @PostMapping("/createCurrentUnit")
    public RestResp<String> createThisUnitModel(@Valid @RequestBody  BpmModelCurrentUnitCreateReqDTO req) {
        return RestResp.ok(modelService.createCurrentUnitModel(req));
    }


    /**
     * 修改模型
     *
     * @param modelUpdateReqDTO 模型更新请求参数
     * @return 更新操作是否成功
     */
    @PostMapping("/update")
    public RestResp<Boolean> updateModel(@Valid @RequestBody BpmModelUpdateReqDTO modelUpdateReqDTO) {
        modelService.updateModel(modelUpdateReqDTO);
        return RestResp.ok(true);
    }

    /**
     * 导入模型
     *
     * @param importReqDTO 模型导入请求参数
     * @param bpmnFile     导入的 BPMN 文件
     * @return 导入模型的ID
     * @throws Exception 文件解析异常
     */
    @PostMapping("/import")
    public RestResp<String> importModel(@Valid BpmModeImportReqDTO importReqDTO, MultipartFile bpmnFile) throws Exception {
        importReqDTO.setBpmnFile(IoUtils.readUtf8(bpmnFile.getInputStream(), false));
        BpmModelCreateReqDTO createReqDTO = BpmModelConvert.INSTANCE.convert(importReqDTO);
        // 更改 BPMN 文件中的流程标识
        String bpmnXML = parseXML(importReqDTO);
        // 读取文件
        return RestResp.ok(modelService.createModel(createReqDTO, bpmnXML));
    }

    /**
     * 部署模型
     *
     * @param modelUpdateReqDTO 模型更新请求参数，包含模型ID
     * @return 部署操作是否成功
     */
    @PostMapping("/deploy")
    public RestResp<Boolean> deployModel(@Valid @RequestBody BpmModelUpdateReqDTO modelUpdateReqDTO) {
        modelService.deployModel(modelUpdateReqDTO);
        return RestResp.ok(true);
    }

    /**
     * 修改模型的状态
     *
     * @param reqDTO 模型状态更新请求参数
     * @return 更新操作是否成功
     */
    @PostMapping("/update-state")
    public RestResp<Boolean> updateModelState(@Valid @RequestBody BpmModelUpdateStateReqDTO reqDTO) {
        modelService.updateModelState(reqDTO);
        return RestResp.ok(true);
    }

    /**
     * 删除模型
     *
     * @param modelUpdateReqDTO 模型更新请求参数，包含模型ID
     * @return 删除操作是否成功
     */
    @PostMapping("/delete")
    public RestResp<Boolean> deleteModel(@Valid @RequestBody BpmModelUpdateReqDTO modelUpdateReqDTO) {
        modelService.deleteModel(modelUpdateReqDTO);
        return RestResp.ok(true);
    }

    /**
     * 初始化业务流程模型
     *
     * @param companyId 企业ID
     * @return 初始化操作是否成功
     */
    @Deprecated
    @PostMapping("/init")
    public RestResp<Boolean> initModelToCompany(@Verify(param = "companyId", rule = "required") Integer companyId) {
        return RestResp.ok(modelService.initModelToCompany(companyId));
    }

    /**
     * 修改审批模型的批量审批开关
     *
     * @param bpmModelUpdateReqDTO 批量审批开关请求参数
     * @return 修改操作是否成功
     */
    @PostMapping("/batchApproval")
    public RestResp<Boolean> switchModelBatchApproval(@RequestBody BpmModelUpdateReqDTO bpmModelUpdateReqDTO) {
        return RestResp.ok(modelService.switchModelBatchApproval(bpmModelUpdateReqDTO));
    }

    /**
     * 搜索业务流程
     *
     * @param bpmModelSearchConnectProcessReqDTO 流程搜索请求参数
     * @return 业务流程的列表
     */
    @PostMapping("/searchProcess")
    public RestResp<List<BpmModelSearchConnectProcessRespDTO>> searchProcess(@RequestBody BpmModelSearchConnectProcessReqDTO bpmModelSearchConnectProcessReqDTO) {
        return RestResp.ok(modelService.searchProcess(bpmModelSearchConnectProcessReqDTO));
    }

    /**
     * 运营端获得模型分页
     *
     * @param pageReqDTO 模型分页请求参数
     * @return 分页结果
     */

    @Deprecated
    @PostMapping("/providerPage")
    public RestResp<PageDTO<BpmProviderModelPageReqDTO>> getProviderModelPage(@RequestBody BpmModelPageReqDTO pageReqDTO) {
        return RestResp.ok(modelService.getProviderModelPage(pageReqDTO));
    }

    /**
     * 解析 XML 文件，更新流程标识
     *
     * @param importReqDTO 导入请求参数，包含 BPMN 文件内容
     * @return 更新后的 XML 字符串
     */
    private String parseXML(BpmModeImportReqDTO importReqDTO) {
        StringReader sr = new StringReader(importReqDTO.getBpmnFile());
        InputSource is = new InputSource(sr);
        String parseXmlStr = null;
        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document document = builder.parse(is);
            Element mxGraphModel = document.getDocumentElement();
            NodeList root = mxGraphModel.getChildNodes();
            for (int i = 0; i < root.getLength(); i++) {
                Node nodeRoot = root.item(i);
                if ("bpmn2:process".equals(nodeRoot.getNodeName())) {
                    Element element = (Element) nodeRoot;
                    element.setAttribute("id", importReqDTO.getKey());
                    element.setAttribute("name", importReqDTO.getName());
                    TransformerFactory transformerFactory = TransformerFactory.newInstance();
                    Transformer transformer = transformerFactory.newTransformer();
                    transformer.setOutputProperty("encoding", "UTF8");
                    ByteArrayOutputStream stream = new ByteArrayOutputStream();
                    transformer.transform(new DOMSource(document), new StreamResult(stream));
                    parseXmlStr = stream.toString();
                    break;
                }
            }
        } catch (Exception e) {
            log.error("BPMN 文件解析异常", e);
        }
        return parseXmlStr;
    }
}
