package com.mrcar.gov.rest.service.config.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.izu.framework.web.util.BeanUtil;
import com.mrcar.gov.common.dto.config.*;
import com.mrcar.gov.common.dto.config.GovCompanyConfigItemValueDTO;
import com.mrcar.gov.common.dto.config.req.GovCompanyConfigItemValueReqDTO;
import com.mrcar.gov.common.dto.config.req.GovCompanyConfigItemValueSaveBatchDTO;
import com.mrcar.gov.common.dto.config.req.GovCompanyConfigValueReqDTO;
import com.mrcar.gov.common.enums.config.PublicConfigEnum;
import com.mrcar.gov.config.domain.GovCompanyConfigValue;
import com.mrcar.gov.config.domain.GovPublicBusinessConfig;
import com.mrcar.gov.config.domain.GovPublicBusinessConfigItem;
import com.mrcar.gov.config.domain.GovPublicCategoryConfig;
import com.mrcar.gov.config.mapper.GovCompanyConfigValueMapper;
import com.mrcar.gov.config.mapper.GovPublicBusinessConfigItemMapper;
import com.mrcar.gov.config.mapper.GovPublicCategoryConfigMapper;
import com.mrcar.gov.config.mapper.GovPublicBusinessConfigMapper;
import com.mrcar.gov.rest.service.config.GovPublicBusinessConfigService;
import com.mrcar.gov.user.domain.GovStruct;
import com.mrcar.gov.user.service.GovStructService;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【gov_public_business_config(公共配置业务表)】的数据库操作Service实现
 * @createDate 2025-04-15 19:46:00
 */
@Service
public class GovPublicBusinessConfigServiceImpl extends ServiceImpl<GovPublicBusinessConfigMapper, GovPublicBusinessConfig>
        implements GovPublicBusinessConfigService {

    @Resource
    private GovPublicBusinessConfigItemMapper businessConfigItemMapper;
    @Resource
    private GovPublicBusinessConfigMapper businessConfigMapper;
    @Resource
    private GovCompanyConfigValueMapper companyConfigValueMapper;
    @Resource
    private GovPublicCategoryConfigMapper categoryConfigMapper;
    @Resource
    private GovStructService govStructService;


    @Override
    @Transactional
    public void clearStructConfig(Integer loginCompanyId, String structCode) {
        // 删除 整个业务线的
        LambdaQueryWrapper<GovCompanyConfigValue> deleteWrapper = new LambdaQueryWrapper<GovCompanyConfigValue>()
                .eq(GovCompanyConfigValue::getCompanyId, loginCompanyId)
                .eq(GovCompanyConfigValue::getStructCode, structCode);
        companyConfigValueMapper.delete(deleteWrapper);
    }

    @Override
    public Map<String, Object> selectByStructCodeAndBusinessCode(GovCompanyConfigValueReqDTO req) {

        Map<String, Object> resultMap = Maps.newHashMap();
        for (String businessConfigCode : req.getBusinessConfigCodes()) {
            List<GovCompanyConfigItemValueDTO> list = this.selectByDeptCodeAndBusinessCodeAndItemCode(req.getLoginCompanyId(), req.getLoginUserBelongDeptCode(), businessConfigCode, null);

            for (GovCompanyConfigItemValueDTO govCompanyConfigItemValueDTO : list) {
                String configValue = govCompanyConfigItemValueDTO.getConfigValue();
                Object possibleValues = govCompanyConfigItemValueDTO.getPossibleValues();
                Integer type = govCompanyConfigItemValueDTO.getType();
                Set<Integer> set = Sets.newHashSet(
                        PublicConfigEnum.BusinessConfigItemTypeEnum.RADIO.getCode(),
                        PublicConfigEnum.BusinessConfigItemTypeEnum.SWITCH.getCode(),
                        PublicConfigEnum.BusinessConfigItemTypeEnum.CHECKBOX.getCode(),
                        PublicConfigEnum.BusinessConfigItemTypeEnum.SELECT.getCode()
                );

                if (set.contains(type)) {
                    if (StringUtils.isNotEmpty(configValue)) {
                        Set<String> collect = Arrays.stream(configValue.split(",")).collect(Collectors.toSet());
                        List<JSONObject> jsonObjects = JSONArray.parseArray(possibleValues.toString(), JSONObject.class);
                        List<JSONObject> value = jsonObjects.stream().filter(item -> collect.contains(item.getString("value"))).collect(Collectors.toList());
                        govCompanyConfigItemValueDTO.setPossibleValues(value);
                    }
                }
            }
            resultMap.put(businessConfigCode, list);
        }
        return resultMap;
    }

    @Override
    public Map<String, Object> getSourceInfo(Integer loginCompanyId, String structCode) {
        GovStruct currentGovStruct = govStructService.getGovStructByCode(structCode);
        Map<String, Object> result = new HashMap<>();
        result.put("currentGovStructId", currentGovStruct.getId());
        result.put("currentGovStructCode", currentGovStruct.getStructCode());
        result.put("currentGovStructName", currentGovStruct.getStructName());

        GovStruct sourceGovStruct = findCompanyConfigValueQueryStructCode(loginCompanyId, structCode);
        if (sourceGovStruct != null) {
            result.put("sourceGovStructId", sourceGovStruct.getId());
            result.put("sourceGovStructCode", sourceGovStruct.getStructCode());
            result.put("sourceGovStructName", sourceGovStruct.getStructName());
        } else {
            result.put("sourceGovStructId", 0);
            result.put("sourceGovStructCode", "system");
            result.put("sourceGovStructName", "系统默认");
        }

        if (Objects.equals(result.get("sourceGovStructCode"), result.get("currentGovStructCode"))) {
            // 来源相同
            result.put("updateButton", true);
            result.put("createButton", false);
            if (currentGovStruct.getParentId() == null || currentGovStruct.getParentId() == -1) {
                result.put("clearButton", false);
            } else {
                result.put("clearButton", true);
            }

        } else {
            result.put("updateButton", false);
            result.put("clearButton", false);
            result.put("createButton", true);
        }
        return result;
    }

    @Override
    public List<GovCompanyConfigItemValueListDTO> getConfigTable(Integer loginCompanyId, String structCode) {

        List<GovPublicCategoryConfigDTO> tree = getAllConfigItemAndValueByCompanyId(loginCompanyId, structCode);
        List<GovCompanyConfigItemValueListDTO> resultList = new ArrayList<>();
        for (GovPublicCategoryConfigDTO categoryConfigDTO : tree) {
            List<GovPublicBusinessConfigDTO> businessConfigDTOList = categoryConfigDTO.getBusinessConfigDTOList();
            for (int i = 0; i < businessConfigDTOList.size(); i++) {
                GovPublicBusinessConfigDTO businessConfigDTO = businessConfigDTOList.get(i);
                List<GovCompanyConfigItemValueDTO> companyConfigItemValueDTOList = businessConfigDTO.getCompanyConfigItemValueDTOList();
                for (GovCompanyConfigItemValueDTO companyConfigItemValueDTO : companyConfigItemValueDTOList) {
                    GovCompanyConfigItemValueListDTO itemValueListDTO = new GovCompanyConfigItemValueListDTO();
                    BeanUtil.copyProperties(companyConfigItemValueDTO, itemValueListDTO);
                    itemValueListDTO.setCategoryCode(categoryConfigDTO.getCategoryCode());
                    itemValueListDTO.setCategoryName(categoryConfigDTO.getCategoryName());
                    itemValueListDTO.setSort(categoryConfigDTO.getSort());
                    itemValueListDTO.setBusinessLink(businessConfigDTO.getBusinessLink());
                    itemValueListDTO.setBusinessConfigCode(businessConfigDTO.getBusinessConfigCode());
                    itemValueListDTO.setBusinessConfigName(businessConfigDTO.getBusinessConfigName());
                    if (i == 0) {
                        itemValueListDTO.setMergeInfo(new GovCompanyConfigItemValueListDTO.MergeInfo(businessConfigDTOList.size(), 1));
                    } else {
                        itemValueListDTO.setMergeInfo(new GovCompanyConfigItemValueListDTO.MergeInfo(0, 0));
                    }
                    String configLabel = getConcatConfigLabel(itemValueListDTO);
                    itemValueListDTO.setConfigLabel(configLabel);
                    resultList.add(itemValueListDTO);
                }
            }
        }
        return resultList;
    }


    /**
     * 获取配置项的标签
     */
    private String getConcatConfigLabel(GovCompanyConfigItemValueListDTO itemValueListDTO) {
        Object possibleValues = itemValueListDTO.getPossibleValues();
        Integer type = itemValueListDTO.getType();
        Set<Integer> set = Sets.newHashSet(
                PublicConfigEnum.BusinessConfigItemTypeEnum.RADIO.getCode(),
                PublicConfigEnum.BusinessConfigItemTypeEnum.SWITCH.getCode(),
                PublicConfigEnum.BusinessConfigItemTypeEnum.CHECKBOX.getCode(),
                PublicConfigEnum.BusinessConfigItemTypeEnum.SELECT.getCode()
        );

        String configValue = itemValueListDTO.getConfigValue();
        String configLabel = configValue;
        if (set.contains(type)) {
            Map<String, String> valueMap = new HashMap<>();
            JSONArray jsonArray = JSONArray.parseArray(possibleValues.toString());
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                valueMap.put(jsonObject.getString("value"), jsonObject.getString("label"));
            }
            if (StringUtils.isNotEmpty(configValue)) {
                String[] split = configValue.split(",");
                configLabel = Arrays.stream(split).map(valueMap::get).collect(Collectors.joining(","));
            }
        }

        return configLabel;
    }

    @Override
    public List<GovPublicCategoryConfigDTO> getAllConfigItemAndValueByCompanyId(Integer companyId, String structCode) {
        //查询 公司配置项 的  structCode
        String queryStructCode = null;
        GovStruct struct = findCompanyConfigValueQueryStructCode(companyId, structCode);
        if (struct != null) {
            queryStructCode = struct.getStructCode();
        }
        // 获取 所有配置分类
        List<GovPublicCategoryConfig> categoryConfigList = categoryConfigMapper.selectList(new LambdaQueryWrapper<GovPublicCategoryConfig>().orderByAsc(GovPublicCategoryConfig::getSort));
        // 获取所有业务线配置
        List<String> categoryCodeList = categoryConfigList.stream().map(GovPublicCategoryConfig::getCategoryCode).collect(Collectors.toList());
        List<GovPublicBusinessConfig> businessConfigList = businessConfigMapper.selectList(new LambdaQueryWrapper<GovPublicBusinessConfig>().in(GovPublicBusinessConfig::getCategoryCode, categoryCodeList));
        Map<String, List<GovPublicBusinessConfig>> businessConfigMap = businessConfigList.stream().collect(Collectors.groupingBy(GovPublicBusinessConfig::getCategoryCode));
        // 获取所有业务线配置的配置项
        List<String> businessCodeList = businessConfigList.stream().map(GovPublicBusinessConfig::getBusinessConfigCode).collect(Collectors.toList());
        List<GovPublicBusinessConfigItem> businessConfigItemList = businessConfigItemMapper.selectList(new LambdaQueryWrapper<GovPublicBusinessConfigItem>().in(GovPublicBusinessConfigItem::getBusinessConfigCode, businessCodeList));
        Map<String, List<GovPublicBusinessConfigItem>> businessConfigItemMap = businessConfigItemList.stream().collect(Collectors.groupingBy(GovPublicBusinessConfigItem::getBusinessConfigCode));

        // 查询 该公司下配置项的值
        List<GovCompanyConfigValue> companyConfigValueList = companyConfigValueMapper.selectList(
                new LambdaQueryWrapper<GovCompanyConfigValue>()
                        .eq(GovCompanyConfigValue::getCompanyId, companyId)
                        .eq(GovCompanyConfigValue::getStructCode, queryStructCode)
        );
        Map<String, GovCompanyConfigValue> companyConfigValueMap = companyConfigValueList.stream().collect(Collectors.toMap(o -> o.getBusinessConfigCode() + "-" + o.getBusinessConfigItemCode(), Function.identity(), (o1, o2) -> o1));

        List<GovPublicCategoryConfigDTO> result = new ArrayList<>();
        for (GovPublicCategoryConfig govPublicCategoryConfig : categoryConfigList) {
            GovPublicCategoryConfigDTO govPublicCategoryConfigDTO = new GovPublicCategoryConfigDTO();
            String categoryCode = govPublicCategoryConfig.getCategoryCode();
            govPublicCategoryConfigDTO.setCategoryCode(categoryCode);
            govPublicCategoryConfigDTO.setCategoryName(govPublicCategoryConfig.getCategoryName());
            // 获取该分类下所有业务线配置
            List<GovPublicBusinessConfigDTO> businessConfigDTOList = new ArrayList<>();
            List<GovPublicBusinessConfig> govPublicBusinessConfigs = businessConfigMap.get(categoryCode);
            if (CollectionUtil.isNotEmpty(govPublicBusinessConfigs)) {
                businessConfigDTOList = govPublicBusinessConfigs.stream().map(govPublicBusinessConfig -> {
                    GovPublicBusinessConfigDTO businessConfigDTO = BeanUtil.copyObject(govPublicBusinessConfig, GovPublicBusinessConfigDTO.class);
                    // 获取该业务线下所有配置项 和 值
                    List<GovPublicBusinessConfigItem> govPublicBusinessConfigItems = businessConfigItemMap.get(govPublicBusinessConfig.getBusinessConfigCode());
                    List<GovCompanyConfigItemValueDTO> companyConfigItemValueDTOList = new ArrayList<>();
                    if (CollectionUtil.isNotEmpty(govPublicBusinessConfigItems)) {
                        companyConfigItemValueDTOList = govPublicBusinessConfigItems.stream().map(item -> {
                            GovCompanyConfigItemValueDTO companyConfigItemValueDTO = BeanUtil.copyObject(item, GovCompanyConfigItemValueDTO.class);
                            //填充 值
                            String businessConfigCode = item.getBusinessConfigCode();
                            String businessConfigItemCode = item.getItemCode();
                            String key = businessConfigCode + "-" + businessConfigItemCode;
                            if (companyConfigValueMap.containsKey(key)) {
                                GovCompanyConfigValue govCompanyConfigValue = companyConfigValueMap.get(key);
                                companyConfigItemValueDTO.setConfigValue(govCompanyConfigValue.getConfigValue());
                            } else {
                                companyConfigItemValueDTO.setConfigValue(item.getDefaultValue());
                            }
                            return companyConfigItemValueDTO;
                        }).collect(Collectors.toList());
                    }
                    businessConfigDTO.setCompanyConfigItemValueDTOList(companyConfigItemValueDTOList);
                    return businessConfigDTO;
                }).collect(Collectors.toList());
            }
            govPublicCategoryConfigDTO.setBusinessConfigDTOList(businessConfigDTOList);
            result.add(govPublicCategoryConfigDTO);
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateCompanyConfigItemVue(GovCompanyConfigItemValueSaveBatchDTO req) {
        Integer loginCompanyId = req.getLoginCompanyId();
        Integer structId = req.getStructId();
        String structCode = req.getStructCode();
        List<GovCompanyConfigItemValueReqDTO> valueList = req.getValueList();

        List<GovCompanyConfigValue> saveList = valueList.stream().map(o -> {
            String businessConfigCode = o.getBusinessConfigCode();
            String itemCode = o.getItemCode();
            String configValue = StringUtils.isEmpty(o.getConfigValue()) ? "": o.getConfigValue();
            // 批量插入
            GovCompanyConfigValue save = new GovCompanyConfigValue();
            save.setCompanyId(loginCompanyId);
            save.setStructId(structId);
            save.setStructCode(structCode);
            save.setBusinessConfigCode(businessConfigCode);
            save.setBusinessConfigItemCode(itemCode);
            save.setConfigValue(configValue);
            return save;
        }).collect(Collectors.toList());

        // 删除 整个业务线的
        LambdaQueryWrapper<GovCompanyConfigValue> deleteWrapper = new LambdaQueryWrapper<GovCompanyConfigValue>()
                .eq(GovCompanyConfigValue::getCompanyId, loginCompanyId)
                .eq(GovCompanyConfigValue::getStructId, structId)
                .eq(GovCompanyConfigValue::getStructCode, structCode);
        companyConfigValueMapper.delete(deleteWrapper);

        companyConfigValueMapper.insertBatch(saveList);


    }

    @Override
    public List<GovCompanyConfigItemValueDTO> selectByDeptCodeAndBusinessCodeAndItemCode(Integer companyId, String deptCode, String
            businessConfigCode, String businessConfigItemCode) {
        //查询 公司配置项 的  structCode
        String queryStructCode = null;
        GovStruct struct = findCompanyConfigValueQueryStructCode(companyId, deptCode);
        if (struct != null) {
            queryStructCode = struct.getStructCode();
        }
        return companyConfigValueMapper.selectByStructCodeAndBusinessCodeAndItemCode(queryStructCode, businessConfigCode, businessConfigItemCode);
    }


    /**
     * 查询 公司配置项 的  structCode
     * 本 structCode 配置了 就 返回本 structCode
     * 否则 查询上级部门 structCode 直到找到最上级
     * @param companyId 登录人公司Id
     * @param structCode 选中的 structCode
     * @return 找不到就返回 null 表明取 默认值
     */
    private GovStruct findCompanyConfigValueQueryStructCode(Integer companyId, String structCode) {

        // 预加载公司所有单位（避免重复查询）
        List<GovStruct> govStructs = govStructService.getBaseMapper().selectList(
                new LambdaQueryWrapper<GovStruct>()
                        .in(GovStruct::getStructType, 1, 2)
                        .eq(GovStruct::getCompanyId, companyId)
                        .eq(GovStruct::getStatus, 1));

        // 转为ID映射便于快速查找
        Map<Integer, String> structIdMap = govStructs.stream()
                .collect(Collectors.toMap(GovStruct::getId, GovStruct::getStructCode));

        Map<String, GovStruct> structCodeMap = govStructs.stream()
                .collect(Collectors.toMap(GovStruct::getStructCode, Function.identity()));

        // 查询 该公司所有部门是否配置了业务线配置
        Set<String> structCodeSet = companyConfigValueMapper.selectStructCodeGroupByCompanyId(companyId);

        while (true) {
            GovStruct currentStruct = structCodeMap.get(structCode);
            if (currentStruct == null) {
                throw new RuntimeException("此组织架构不存在");
            }
            if (structCodeSet.contains(structCode)) {
                // 该部门已有配置 查询自己部门的即可
                return currentStruct;
            } else {
                // 查询上级部门 的
                Integer parentId = currentStruct.getParentId();
                structCode = structIdMap.get(parentId);
                if (parentId == null || parentId == -1) {
                    return null;
                }
            }
        }
    }

}




