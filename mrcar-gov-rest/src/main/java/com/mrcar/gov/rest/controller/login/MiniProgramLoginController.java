package com.mrcar.gov.rest.controller.login;

import com.izu.framework.exception.ApiException;
import com.izu.framework.resp.InfoCode;
import com.izu.framework.resp.RestResp;
import com.mrcar.gov.common.constant.user.GovUserStatusEnum;
import com.mrcar.gov.common.dto.session.GovUserSessionInfoDTO;
import com.mrcar.gov.common.security.LoginUser;
import com.mrcar.gov.common.security.SecurityUtil;
import com.mrcar.gov.common.security.annotation.Anonymous;
import com.mrcar.gov.rest.dto.login.MiniProgramUserLoginReqDTO;
import com.mrcar.gov.rest.util.SessionUtil;
import com.mrcar.gov.user.domain.GovUser;
import com.mrcar.gov.user.utils.PasswordUtils;
import com.mrcar.thirdparty.wechat.MiniProgramHelperService;
import com.mrcar.thirdparty.wechat.MiniProgramPhoneNumberResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Date;
import java.util.Objects;

import static com.mrcar.gov.common.constant.login.LoginEnum.*;
import static com.mrcar.gov.common.constant.RestErrorCode.*;

/**
 * 微信小程序端登录功能.
 *
 * <AUTHOR>
 * @date 2024-11-08
 */
@RestController
@RequestMapping("/login/miniprogram")
public class MiniProgramLoginController extends AbstractLoginController {

    @Autowired
    private MiniProgramHelperService miniProgramService;

    /**
     * 用户登录
     */
    @Anonymous
    @PostMapping("/login")
    public RestResp<GovUserSessionInfoDTO> login(@RequestBody MiniProgramUserLoginReqDTO reqDTO,
                                                 HttpServletRequest request,
                                                 HttpServletResponse response) throws IOException {

        // 认证方式
        AuthStrategyEnum strategy = AuthStrategyEnum.of(reqDTO.getAuthStrategy());

        if (Objects.isNull(strategy)) {
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID);
        }

        // 手机号
        String mobile = reqDTO.getMobile();
        // 用户信息
        GovUser user = null;

        // 判断账号是否被锁定
        if (loginService.isAccountLock(mobile)) {
            return RestResp.build(render(LOGIN_ACCOUNT_LOCKED, loginService.getAccountLockTime(mobile)));
        }

        switch (strategy) {
            case MINIPROGRAM:
            {
                // 小程序登录
                MiniProgramPhoneNumberResult result =
                        this.miniProgramService.getUserPhoneNumber(reqDTO.getThirdToken());

                if (result.getErrcode() != 0) {
                    throw new ApiException(LOGIN_MINIPROGRAM_ERROR);
                }

                mobile = result.getPurePhoneNumber();

                // 查询用户信息
                user = this.loginService.getUserByMobile(mobile);
                if (Objects.isNull(user)) {
                    return RestResp.build(LOGIN_PHONE_NOT_EXIST);
                }

                if (Objects.equals(user.getUserStatus(), GovUserStatusEnum.STOP.getCode())) {
                    return RestResp.build(LOGIN_PHONE_UNAVAILABLE);
                }

                break;
            }
            case PASSWORD:
            {
                // 密码登录
                // 查询用户信息
                user = this.loginService.getUserByMobile(mobile);
                if (Objects.isNull(user)) {
                    return RestResp.build(LOGIN_PHONE_NOT_EXIST);
                }
                if (Objects.equals(user.getUserStatus(), GovUserStatusEnum.STOP.getCode())) {
                    return RestResp.build(LOGIN_PHONE_UNAVAILABLE);
                }

                // 比较密码
                String password = PasswordUtils.encryptedPassword(reqDTO.getPassword(), user.getSalt(), 1);
                if (!Objects.equals(password, user.getLoginPassword())) {
                    String message = loginService.triggerAccountLoginFailure(mobile);
                    return RestResp.build(LOGIN_PASSWORD_ERROR.getStatus(), message, null);
                }
                break;
            }
            case VERIFYCODE:
            {
                // 验证码登录
                // 检查验证码
                if (!this.loginService.checkVerifyCode(mobile, reqDTO.getVerifyCode())) {
                    return RestResp.build(LOGIN_VERIFY_CODE_ERROR);
                }
                // 查询用户信息
                user = this.loginService.getUserByMobile(mobile);
                if (Objects.isNull(user)) {
                    return RestResp.build(LOGIN_PHONE_NOT_EXIST);
                }

                if (Objects.equals(user.getUserStatus(),
                        GovUserStatusEnum.STOP.getCode())) {
                    return RestResp.build(LOGIN_PHONE_UNAVAILABLE);
                }

                break;
            }
            default:
                throw new ApiException(InfoCode.HTTP_PARAM_INVALID);
        }

        // 清除锁定状态
        this.loginService.unLockAccount(mobile);
        // 返回token信息
        LoginUser loginUser = buildLoginUser(user);
        String token = SecurityUtil.login(loginUser);
        response.setHeader(HEADER_SET_TOKEN, token);

        // 返回用户信息
        return RestResp.ok(loginService.getLoginUserFromCache(mobile));
    }

    /**
     * 发送验证码
     */
    @Anonymous
    @PostMapping("/sendVerificationCode")
    public RestResp<String> sendVerifyCode(@RequestParam(value = "mobileNum", required = true) String mobileNum) {

        // 查询用户信息
        GovUser user = this.loginService.getUserByMobile(mobileNum);
        if (Objects.isNull(user)) {
            return RestResp.build(LOGIN_PHONE_NOT_EXIST);
        }
        if (Objects.equals(user.getUserStatus(), GovUserStatusEnum.STOP.getCode())) {
            return RestResp.build(LOGIN_PHONE_UNAVAILABLE);
        }

        this.loginService.sendVerifyCode(mobileNum);
        return RestResp.ok();
    }


    /**
     * 登录用户的详细用户信息
     */
    @GetMapping("/userInfo")
    public GovUserSessionInfoDTO userInfo() {

        return SessionUtil.currentUser();
    }


    /**
     * 退出登录
     */
    @PostMapping("/logout")
    public RestResp<String> logout() {
        SessionUtil.logout();
        return RestResp.ok();
    }

}
