package com.mrcar.gov.rest.controller.config;

import com.izu.framework.resp.RestResp;
import com.mrcar.gov.common.dto.config.GovCompanyConfigItemValueListDTO;
import com.mrcar.gov.common.dto.config.req.GovCompanyConfigItemValueReqDTO;
import com.mrcar.gov.common.dto.config.req.GovCompanyConfigItemValueSaveBatchDTO;
import com.mrcar.gov.common.dto.config.req.GovCompanyConfigValueReqDTO;
import com.mrcar.gov.common.dto.config.GovPublicCategoryConfigDTO;
import com.mrcar.gov.common.enums.config.PublicConfigEnum;
import com.mrcar.gov.common.security.annotation.Anonymous;
import com.mrcar.gov.rest.service.config.GovPublicBusinessConfigService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 公共配置业务
 * <AUTHOR>
 */
@RestController
@RequestMapping("/public/business/config")
public class GovPublicBusinessConfigController {

    @Resource
    private GovPublicBusinessConfigService govPublicBusinessConfigService;


    /**
     * 获取 当前单位下所有业务线下的配置项 的   来源信息
     */
    @RequestMapping("/getSourceInfo")
    private RestResp<Map<String, Object>> getSourceInfo(@Validated @RequestBody GovCompanyConfigValueReqDTO req) {
        return RestResp.ok(govPublicBusinessConfigService.getSourceInfo(req.getLoginCompanyId(), req.getStructCode()));
    }


    /**
     * 获取所有配置项和配置值 列表
     */
    @RequestMapping("/getConfigTable")
    private RestResp<List<GovCompanyConfigItemValueListDTO>> getConfigTable(@Validated @RequestBody GovCompanyConfigValueReqDTO req) {
        return RestResp.ok(govPublicBusinessConfigService.getConfigTable(req.getLoginCompanyId(), req.getStructCode()));
    }


    /**
     * 获取所有配置项和配置值
     */
    @RequestMapping("/getAllConfigItemAndValueByCompanyId")
    private RestResp<List<GovPublicCategoryConfigDTO>> getAllConfigItemAndValueByCompanyId(@Validated @RequestBody GovCompanyConfigValueReqDTO req) {
        return RestResp.ok(govPublicBusinessConfigService.getAllConfigItemAndValueByCompanyId(req.getLoginCompanyId(), req.getStructCode()));
    }


    /**
     * 修改公司配置项的值
     */
    @RequestMapping("/saveCompanyConfigItemVue")
    private RestResp<Boolean> saveCompanyConfigItemVue(@Validated @RequestBody GovCompanyConfigItemValueSaveBatchDTO req) {
        govPublicBusinessConfigService.updateCompanyConfigItemVue(req);
        return RestResp.ok(true);
    }

    /**
     *  获取单位 业务线 配置项
     */
    @RequestMapping("/selectByStructCodeAndBusinessCode")
    private RestResp<Map<String, Object>> selectByStructCodeAndBusinessCode(@RequestBody GovCompanyConfigValueReqDTO req) {
        String[] businessConfigCodes = req.getBusinessConfigCodes();
        if (businessConfigCodes == null) {
            return RestResp.fail("业务配置编码不能为空");
        }
        for (String businessConfigCode : businessConfigCodes) {
            PublicConfigEnum.BusinessConfigEnum configEnum = PublicConfigEnum.BusinessConfigEnum.getByCode(businessConfigCode);
            if (configEnum == null){
                return RestResp.fail("业务配置编码不正确");
            }
        }
        Map<String, Object> resultMap =govPublicBusinessConfigService.selectByStructCodeAndBusinessCode(req);
        return RestResp.ok(resultMap);
    }

    /**
     * 清楚 当前单位下所有业务线下的配置
     */
    @RequestMapping("/clearStructConfig")
    private RestResp<Boolean> clearStructConfig(@Validated @RequestBody GovCompanyConfigValueReqDTO req) {
        govPublicBusinessConfigService.clearStructConfig(req.getLoginCompanyId(), req.getStructCode());
        return RestResp.ok(true);
    }
}
