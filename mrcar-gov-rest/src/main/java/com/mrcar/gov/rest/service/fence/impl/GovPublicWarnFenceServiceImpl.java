package com.mrcar.gov.rest.service.fence.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.izu.cache.core.DistributedLock;
import com.izu.cache.core.redis.RedisCache;
import com.mrcar.gov.asset.domain.GovGpsDevice;
import com.mrcar.gov.asset.domain.GovVehicleBaseInfo;
import com.mrcar.gov.asset.service.GovGpsDeviceService;
import com.mrcar.gov.asset.service.GovVehicleBaseInfoService;
import com.mrcar.gov.base.common.HolidayDayTypeEnum;
import com.mrcar.gov.base.domain.HolidayConfig;
import com.mrcar.gov.base.service.HolidayConfigService;
import com.mrcar.gov.common.constant.asset.CarWorkingStatusEnum;
import com.mrcar.gov.common.constant.asset.VehicleStatusEnum;
import com.mrcar.gov.common.constant.fence.*;
import com.mrcar.gov.common.constant.iot.HolidayTypeEnum;
import com.mrcar.gov.common.dto.asset.VehicleBaseInfoCache;
import com.mrcar.gov.common.dto.device.DeviceRealtimeData;
import com.mrcar.gov.common.dto.device.DeviceStatusInfo;
import com.mrcar.gov.common.dto.device.VehicleDeviceRealtimeStatus;
import com.mrcar.gov.common.dto.device.VehicleGpsLocation;
import com.mrcar.gov.common.dto.device.resp.DeviceHistoryTraceDTO;
import com.mrcar.gov.common.dto.iot.resp.CarGpsFenceDTO;
import com.mrcar.gov.common.dto.iot.resp.OfficialVehicleWarnRecordDTO;
import com.mrcar.gov.common.enums.device.FenceBusinessTypeEnum;
import com.mrcar.gov.common.enums.device.GovGpsDeviceEnum;
import com.mrcar.gov.common.enums.device.GovWarnEnum;
import com.mrcar.gov.common.eventbus.EventBusDispatcher;
import com.mrcar.gov.common.eventbus.events.GovPublicVehicleInFenceEvent;
import com.mrcar.gov.common.eventbus.events.GovPublicVehicleOutFenceEvent;
import com.mrcar.gov.common.util.DateUtils;
import com.mrcar.gov.iot.domain.CarWarnFenceHoliday;
import com.mrcar.gov.iot.domain.GovCarGpsFence;
import com.mrcar.gov.iot.domain.GovWarnMsgNoticeRecord;
import com.mrcar.gov.iot.service.*;
import com.mrcar.gov.rest.service.asset.VehicleBaseInfoCacheService;
import com.mrcar.gov.rest.service.fence.GovPublicWarnFenceService;
import com.mrcar.gov.rest.service.fence.WarnSnGenerator;
import com.mrcar.gov.rest.service.order.GovPublicCreateOrderService;
import com.mrcar.iot.domain.GovVehicleLocation;
import com.mrcar.iot.domain.GovVehicleStatus;
import com.mrcar.iot.domain.GpsDeviceMeta;
import com.mrcar.thirdparty.baidu.AddressResult;
import com.mrcar.thirdparty.baidu.BaiduMapApiClient;
import org.apache.commons.lang3.StringUtils;
import org.locationtech.jts.geom.*;
import org.locationtech.jts.operation.distance.DistanceOp;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.mrcar.gov.common.constant.device.DeviceConstant.REDIS_NULL_VALUE;

@Service
public class GovPublicWarnFenceServiceImpl implements GovPublicWarnFenceService {

    private static final Logger logger = LoggerFactory.getLogger(GovPublicWarnFenceServiceImpl.class);

    @Autowired
    private VehicleBaseInfoCacheService vehicleBaseInfoCacheService;
    @Autowired
    private RedisCache cache;
    @Autowired
    private GovCarGpsFenceService carGpsFenceService;
    @Autowired
    private GovGpsDeviceService gpsDeviceService;
    @Autowired
    private GovWarnMsgNoticeRecordService warnMsgNoticeRecordService;
    @Autowired
    private DistributedLock lock;
    @Autowired
    private BaiduMapApiClient baiduMapApiClient;
    @Autowired
    private GovVehicleBaseInfoService vehicleBaseInfoService;
    @Autowired
    private EventBusDispatcher dispatcher;
    @Autowired
    private WarnSnGenerator generator;
    @Autowired
    private VehicleDeviceService deviceService;
    @Autowired
    private GovCarInfoService carInfoService;

    @Resource
    private CarWarnFenceHolidayService carWarnFenceHolidayService;

    @Resource
    private HolidayConfigService holidayConfigService;

    @Resource
    private GovPublicCreateOrderService govPublicCreateOrderService;


    //获取配置日期
    @NacosValue(value = "${holiday.config:2024-01-01}", autoRefreshed = true)
    private  String holiday_config = "2024-01-01";


    private static final String REDIS_KEY_HISTORY_LOCATION = "mrcar:iot:fence:history:location:%s";
    private static final String REDIS_KEY_NOTICE_MESSAGE = "mrcar:iot:fence:warn:notice:%s";
    private static final String REDIS_KEY_GOV_FENCE = "mrcar:iot:fence:gov:%s";
    private static final int TRAIL_BACK_MAX_SIZE = 3;

    private static final String REDIS_LOCK_KEY = "mrcar:fence:lock:%s";

    @Value("${spring.profiles.active}")
    private String profile;

    @Override
    public void triggerVehicleLocation(VehicleGpsLocation location) {
        //logger.info("triggerVehicleLocation-收到车辆位置信息 : {}", JSON.toJSONString(location));
        // 按车辆查询围栏信息
        VehicleBaseInfoCache vehicle = vehicleBaseInfoCacheService.queryByVin(location.getVehicleVin());
        if (Objects.isNull(vehicle)) {
            logger.info("车架号 : {}  未找到车辆信息", location.getVehicleVin());
            return;
        }
        if (Objects.equals(vehicle.getWorkStatus(),
                CarWorkingStatusEnum.NO_OPERATE.getCode())) {
            logger.info("车架号 : {}  不满足运营状态", location.getVehicleVin());
            return;
        }
        if (Objects.equals(location.getDeviceType(),
                String.valueOf(GovGpsDeviceEnum.DeviceTypeEnum.WIFI.getCode()))) {
            logger.info("车架号 : {}  触发围栏，但是是WIFI设备，不处理", location.getVehicleVin());
            return;
        }
        // 触发公务用车逻辑
        this.onNewVehiclePoint(vehicle, location);
    }

    @Override
    public boolean forceEndWarnBySn(String warnSn, String vehicleVin) {

        String lockKey = formatRedisLockKey(vehicleVin);

        if (!this.lock.lock(lockKey)) {
            return false;
        }

        try {

            // 查询车辆信息
            GovVehicleBaseInfo vehicle =
                    vehicleBaseInfoService.getOne(
                            new LambdaQueryWrapper<GovVehicleBaseInfo>()
                                    .eq(GovVehicleBaseInfo::getVehicleVin, vehicleVin)
                                    .eq(GovVehicleBaseInfo::getVehicleStatus, VehicleStatusEnum.USABLE.getCode()));

            if (Objects.isNull(vehicle)) {
                return false;
            }

            // 查询通知记录 | 报警已结束
            GovWarnMsgNoticeRecord noticeRecord = queryNoticeRecordByWarnSn(warnSn);
            if (Objects.isNull(noticeRecord) ||
                    Objects.equals(noticeRecord.getWarnStatus(),
                            WarnMsgNoticeWarnStatusEnum.FINISH.getStatus())) {
                return false;
            }

            // 有效的点位
            List<VehicleGpsLocation> historyPoints = this.getHistoryPoints(vehicle.getVehicleVin());
            List<CarGpsPosition> historyPosition =
                    historyPoints.stream()
                            .map(this::convertLocationToPoint)
                            .collect(Collectors.toList());
            CarGpsPosition effectedPosition = null;
            if (!historyPosition.isEmpty()) {
                effectedPosition = historyPosition.get(historyPosition.size() - 1);
            } else {
                // 查询车辆状态
                DeviceRealtimeData deviceStatus =
                        this.checkVehicleRealtimeStatus(vehicle.getVehicleNo()).getDeviceStatus();
                if (deviceStatus != null) {
                    effectedPosition = this.convertLocationToPoint(
                            convertVehicleStatusToLocation(deviceStatus));
                }
            }
            // 查询最近的围栏
            List<GovCarGpsFence> candidateFences =
                    this.findCandidateFenceByStructCode(vehicle.getVehicleBelongDeptCode());
            // 转化几何
            List<WarnPolygonFence> fences = doConvertFenceToPolygon(candidateFences);
            // 查找最近的围栏信息
            WarnPolygonFence effectedFence =
                    findNearestPolygon(effectedPosition, fences);
            if (Objects.isNull(effectedFence)) {
                return false;
            }

            // 关闭报警围栏记录
            this.doEndWarnRecord(this.toVehicleCache(vehicle), effectedPosition, effectedFence, noticeRecord);
            return true;

        } finally {
            this.lock.unlock(lockKey);
        }

    }

    @Override
    public VehicleDeviceRealtimeStatus checkVehicleRealtimeStatus(String vehicleNo) {

        // 查询车辆的设备信息
        List<GovGpsDevice> devices = this.gpsDeviceService.list(
                new LambdaQueryWrapper<GovGpsDevice>()
                        .eq(GovGpsDevice::getVehicleNo, vehicleNo)
                        .eq(GovGpsDevice::getBindStatus, GovGpsDeviceEnum.DeviceBindStatusEnum.BIND.getCode())
                        .eq(GovGpsDevice::getDeleteStatus, GovGpsDeviceEnum.DeviceDeleteStatusEnum.NORMAL.getCode()));

        // 最新点位数据
        DeviceRealtimeData location = this.findLatestDeviceLocation(devices);
        // 最新状态数据
        DeviceStatusInfo status = this.findLatestDeviceStatus(devices);

        // 查询车辆信息
        GovVehicleBaseInfo vehicle =
                vehicleBaseInfoService.getOne(
                        new LambdaQueryWrapper<GovVehicleBaseInfo>()
                                .eq(GovVehicleBaseInfo::getVehicleNo, vehicleNo));

        // 候选围栏信息
        List<GovCarGpsFence> candidateFences = Collections.emptyList();

        if (vehicle != null) {
            candidateFences = this.findCandidateFenceByStructCode(vehicle.getVehicleBelongDeptCode());
        }

        return this.doCheckVehicleRealtimeStatus(vehicle, location, status, candidateFences);
    }

    @Override
    public OfficialVehicleWarnRecordDTO queryByWarnSn(String warnSn) {
        GovWarnMsgNoticeRecord record =
                this.warnMsgNoticeRecordService.getOne(
                        new LambdaQueryWrapper<GovWarnMsgNoticeRecord>()
                                .eq(GovWarnMsgNoticeRecord::getWarnSn, warnSn));
        
        if (record == null) {
            return null;
        }

        OfficialVehicleWarnRecordDTO result = new OfficialVehicleWarnRecordDTO();
        result.setWarnSn(record.getWarnSn());

        result.setInSnapId(record.getBackFenceId());
        result.setInFenceTime(record.getFinishTime());
        result.setInFenceLat(record.getEndLatitude());
        result.setInFenceLng(record.getEndLongitude());
        result.setInFenceAddress(record.getEndWarnAddress());

        result.setOutSnapId(record.getFenceId());
        result.setOutFenceTime(record.getStartTime());
        result.setOutFenceLat(record.getLatitude());
        result.setOutFenceLng(record.getLongitude());
        result.setOutFenceAddress(record.getWarnAddress());

        result.setVehicleLicense(record.getVehicleLicense());
        result.setVehicleVin(record.getVehicleVin());
        result.setDeviceNo(record.getDeviceNo());
        result.setDeviceType(record.getDeviceType());
        result.setNoticeType(record.getWarnNoticeType());
        result.setWarnStatus(record.getWarnStatus());

        result.setWarnType(record.getWarnType());
        result.setWarnTypeStr(GovWarnEnum.WarnType.getValueStrByValue(record.getWarnType()));

        return result;
    }


    private VehicleDeviceRealtimeStatus doCheckVehicleRealtimeStatus(GovVehicleBaseInfo vehicle,
                                                                     DeviceRealtimeData realtimeInfo,
                                                                     DeviceStatusInfo statusInfo,
                                                                     List<GovCarGpsFence> candidateFences) {

        // 拼接车辆结果
        VehicleDeviceRealtimeStatus result = new VehicleDeviceRealtimeStatus();

        result.setDeviceStatus(realtimeInfo);
        result.setStatusInfo(statusInfo);

        // 围栏信息
        result.setCandidateFences(
                candidateFences.stream().map(s -> {
                    CarGpsFenceDTO dto = new CarGpsFenceDTO();
                    BeanUtils.copyProperties(s, dto);
                    FenceBusinessTypeEnum businessType = FenceBusinessTypeEnum.getEnumByCode(s.getFenceBusinessType());
                    if (businessType != null) {
                        dto.setFenceBusinessTypeStr(businessType.getMsg());
                    }
                    return dto;
                }).collect(Collectors.toList()));

        // 转化几何
        List<WarnPolygonFence> fences = doConvertFenceToPolygon(candidateFences);

        // 设备信息为空
        if (Objects.isNull(realtimeInfo) || fences.isEmpty()
                || Objects.isNull(realtimeInfo.getLatBaidu())
                || Objects.isNull(realtimeInfo.getLngBaidu())) {
            result.setInFence(false);
            result.setNearestFence(null);
            return result;
        }

        Point point =
                this.renderPoint(
                        new Coordinate(realtimeInfo.getLngBaidu().doubleValue(),
                                realtimeInfo.getLatBaidu().doubleValue()));
        CarGpsPosition position = new CarGpsPosition(point, null);

        // 报警地址
        String address = null;
        try {
            AddressResult info = baiduMapApiClient.pointToAddress(
                    realtimeInfo.getLatBaidu(), realtimeInfo.getLngBaidu());
            address = info.getAddress();
        } catch (IOException e) {
            logger.warn("查询百度逆地理位置失败", e);
        }

        result.setAddress(address);

        // 检查围栏与点位关系
        WarnLocationBehavior behavior = this.isInFence(position, fences);

        if (behavior == WarnLocationBehavior.IN) {
            // 在围栏内
            result.setInFence(true);

            // 所在的围栏信息
            WarnPolygonFence fence = fences.stream()
                    .filter(s -> s.polygon.contains(point))
                    .findFirst()
                    .orElse(null);

            if (Objects.nonNull(fence)) {
                CarGpsFenceDTO dto = new CarGpsFenceDTO();
                BeanUtils.copyProperties(fence.fence, dto);
                FenceBusinessTypeEnum businessType = FenceBusinessTypeEnum.getEnumByCode(dto.getFenceBusinessType());
                if (businessType != null) {
                    dto.setFenceBusinessTypeStr(businessType.getMsg());
                }
                result.setNearestFence(dto);
            }
        } else {
            // 在围栏外
            result.setInFence(false);
            // 找出最近的围栏信息
            WarnPolygonFence fence = findNearestPolygon(position, fences);

            CarGpsFenceDTO dto = new CarGpsFenceDTO();
            BeanUtils.copyProperties(fence.fence, dto);

            result.setNearestFence(dto);
        }

        return result;
    }

    // 查找设备的状态信息
    private DeviceStatusInfo findLatestDeviceStatus(List<GovGpsDevice> devices) {

        // 车机设备
        GovGpsDevice device = devices.stream()
                .filter(s -> Objects.equals(s.getDeviceType(),
                        GovGpsDeviceEnum.DeviceTypeEnum.TBOX.getCode()))
                .findFirst().orElse(null);

        if (device == null) {
            return null;
        }

        List<GovVehicleStatus> list =
                deviceService.getLatestStatus(Collections.singletonList(device.getDeviceSysNo()));

        if (list.isEmpty()) {
            return null;
        }

        GovVehicleStatus source = list.get(0);

        DeviceStatusInfo target = new DeviceStatusInfo();
        if(ObjectUtil.isNull(source)){
            return null;
        }

        GpsDeviceMeta meta = source.getDeviceMeta();
        BeanUtils.copyProperties(meta, target);

        BeanUtils.copyProperties(source, target);
        return target;
    }

    // 根据优先级, 查询设备的最新点位
    private DeviceRealtimeData findLatestDeviceLocation(List<GovGpsDevice> devices) {

        // 设备编码
        List<String> deviceSysNos = devices.stream()
                .map(GovGpsDevice::getDeviceSysNo)
                .collect(Collectors.toList());

        // 查询最新点位
        Map<String, GovVehicleLocation> locations =
                deviceService.getLatestLocations(deviceSysNos)
                        .stream().collect(Collectors.toMap(s -> s.getDeviceMeta().getDeviceSysNo(), Function.identity()));

        // 车机设备
        GovGpsDevice device = devices.stream()
                .filter(s -> Objects.equals(s.getDeviceType(),
                        GovGpsDeviceEnum.DeviceTypeEnum.TBOX.getCode()))
                .findFirst().orElse(null);
        if (device != null) {
            // 点位
            GovVehicleLocation location = locations.get(device.getDeviceSysNo());
            if (location != null) {
                return convertDeviceRealtimeData(location);
            }
        }

        // 有线设备
        device = devices.stream()
                .filter(s -> Objects.equals(s.getDeviceType(),
                        GovGpsDeviceEnum.DeviceTypeEnum.WIRED.getCode()))
                .findFirst().orElse(null);

        if (device != null) {
            // 点位
            GovVehicleLocation location = locations.get(device.getDeviceSysNo());
            if (location != null) {
                return convertDeviceRealtimeData(location);
            }
        }

        return null;
    }

    private DeviceRealtimeData convertDeviceRealtimeData(GovVehicleLocation source) {
        DeviceRealtimeData target = new DeviceRealtimeData();
        target.setTimestamp(source.getTimestamp());

        GpsDeviceMeta meta = source.getDeviceMeta();
        BeanUtils.copyProperties(meta, target);

        BeanUtils.copyProperties(source, target);
        return target;
    }

    private GovWarnMsgNoticeRecord queryNoticeRecordByWarnSn(String warnSn) {
        return this.warnMsgNoticeRecordService.getOne(
                        new LambdaQueryWrapper<GovWarnMsgNoticeRecord>()
                                .eq(GovWarnMsgNoticeRecord::getWarnSn, warnSn));
    }

    private VehicleGpsLocation convertVehicleStatusToLocation(DeviceRealtimeData deviceStatus) {
        VehicleGpsLocation location = new VehicleGpsLocation();
        location.setCarNo(deviceStatus.getVehicleLicense());
        location.setDeviceId(deviceStatus.getSimNo());
        location.setSimNo(deviceStatus.getSimNo());
        location.setVehicleVin(deviceStatus.getVehicleVin());
        location.setLngBaidu(deviceStatus.getLngBaidu());
        location.setLatBaidu(deviceStatus.getLatBaidu());
        location.setLongitude(deviceStatus.getLongitude());
        location.setLatitude(deviceStatus.getLatitude());
        location.setSpeed(String.valueOf(deviceStatus.getSpeed()));
        location.setDirection(String.valueOf(deviceStatus.getDirection()));
        location.setCreateDate(deviceStatus.getTimestamp());
        return location;
    }

    /**
     * 处理车辆新上报的点位
     */
    private void onNewVehiclePoint(VehicleBaseInfoCache vehicle,
                                   VehicleGpsLocation location) {

        String lockKey = formatRedisLockKey(vehicle.getVehicleVin());

        if (!this.lock.lock(lockKey)) {
            logger.info("没有获取到锁,vin:{}", vehicle.getVehicleVin());
            return;
        }

        try {

            // 查询历史点位
            List<VehicleGpsLocation> historyPoints = getHistoryPoints(vehicle.getVehicleVin());
            if (isOlderPoint(vehicle, location, historyPoints)) {
                logger.info("公务车车架 {} 点位太老，不做处理", vehicle.getVehicleVin());
                return;
            }

            // 查找车辆对应的围栏信息
            List<GovCarGpsFence> candidates = this.findCandidateFenceUsingCache(vehicle.getVehicleBelongDeptCode());
            List<WarnPolygonFence> fences = doConvertFenceToPolygon(candidates);
            if (fences.isEmpty()) {
                logger.info("找不到对应的围栏信息, vin : {}", vehicle.getVehicleVin());
                return;
            }

            // 判断定位点与围栏的关系
            CarGpsPosition position = this.convertLocationToPoint(location);
            List<CarGpsPosition> historyPosition =
                    historyPoints.stream()
                            .map(this::convertLocationToPoint)
                            .collect(Collectors.toList());

            List<CarGpsPosition> allPosition = new ArrayList<>();
            allPosition.addAll(historyPosition);
            allPosition.add(position);

            WarnLocationBehavior behavior = isAllInFence(allPosition, fences);

            logger.info("公务车车架 {} 行为 {} date {}", vehicle.getVehicleVin(), behavior.name(), location.getCreateDate());
            switch (behavior) {
                case IN:
                {
                    doInFence(vehicle, position, historyPosition, fences);
                    break;
                }
                case OUT:
                {
                    doOutFence(vehicle, position, historyPosition, fences);
                    break;
                }
                default:
                    break;
            }

            // 更新最新点位
            this.appendCurrentPoint(vehicle, location);

        } finally {
            this.lock.unlock(lockKey);
        }

    }

    // 坐标点在围栏内
    private void doInFence(VehicleBaseInfoCache vehicle,
                           CarGpsPosition position,
                           List<CarGpsPosition> historyPosition,
                           List<WarnPolygonFence> fences) {
        // 检查当前是否存在报警信息
        GovWarnMsgNoticeRecord noticeRecord = this.queryRunningWarnRecordUsingCache(vehicle);
        if (Objects.isNull(noticeRecord)) {
            logger.info("公务车车架 {} 无报警开始记录", vehicle.getVehicleVin());
            return;
        }
        // 有效的点位
        CarGpsPosition effectedPosition =
                historyPosition.isEmpty() ? position : historyPosition.get(0);
        // 进入的围栏
        WarnPolygonFence effectedFence = fences.stream()
                .filter(s -> s.polygon.contains(effectedPosition.point))
                .findFirst()
                .orElse(null);
        if (Objects.isNull(effectedFence)) {
            logger.info("公务车车架 {} 无可进入围栏", vehicle.getVehicleVin());
            return;
        }
        // 关闭报警围栏记录
        this.doEndWarnRecord(vehicle, effectedPosition, effectedFence, noticeRecord);
        // 发送消息
        this.doSendInWarnNotify(noticeRecord, effectedFence);
    }

    private void doOutFence(VehicleBaseInfoCache vehicle,
                            CarGpsPosition position,
                            List<CarGpsPosition> historyPosition,
                            List<WarnPolygonFence> fences) {
        // 检查当前是否存在报警信息
        GovWarnMsgNoticeRecord noticeRecord = this.queryRunningWarnRecordUsingCache(vehicle);
        if (Objects.nonNull(noticeRecord)) {
            logger.info("公务车车架 {} 已存在报警记录", vehicle.getVehicleVin());
            return;
        }
        // 有效的点位
        CarGpsPosition effectedPosition =
                historyPosition.isEmpty() ? position : historyPosition.get(0);
        // 有效的围栏
        // 找最近的围栏点
        WarnPolygonFence effectedFence = findNearestPolygon(effectedPosition, fences);
        if (Objects.isNull(effectedFence)) {
            logger.info("公务车车架 {} 无可出围栏", vehicle.getVehicleVin());
            return;
        }
        // 查询设备信息
        GovGpsDevice device = queryVehicleDevice(vehicle, effectedPosition.location);
        if (Objects.isNull(device)) {
            logger.info("公务车车架 {} 无设备信息", vehicle.getVehicleVin());
            return;
        }
        // 开始报警围栏记录
        noticeRecord = this.doStartWarnRecord(vehicle, effectedPosition, effectedFence, device);
        // 发送消息
        this.doSendOutWarnNotify(noticeRecord, effectedFence);
    }

    // 发送报警通知
    private void doSendInWarnNotify(GovWarnMsgNoticeRecord noticeRecord,
                                    WarnPolygonFence effectedFence) {
        GovPublicVehicleInFenceEvent event = new GovPublicVehicleInFenceEvent();
        event.setWarnSn(noticeRecord.getWarnSn());
        event.setFenceSnapId(noticeRecord.getBackFenceId());
        event.setVehicleLicense(noticeRecord.getVehicleLicense());
        event.setVehicleVin(noticeRecord.getVehicleVin());
        event.setDeviceNo(noticeRecord.getDeviceNo());
        // 考虑存储simNo
        event.setSimNo(noticeRecord.getDeviceNo());
        event.setDeviceType(noticeRecord.getDeviceType());
        event.setInFenceTime(noticeRecord.getFinishTime());
        event.setLatitude(noticeRecord.getEndLatitude());
        event.setLongitude(noticeRecord.getEndLongitude());
        event.setAddress(noticeRecord.getEndWarnAddress());
        event.setEffectiveDuration(effectedFence.fence.getEffectiveDuration());
        dispatcher.dispatcher(event);
    }

    private void doSendOutWarnNotify(GovWarnMsgNoticeRecord noticeRecord,
                                     WarnPolygonFence effectedFence) {
        GovPublicVehicleOutFenceEvent event = new GovPublicVehicleOutFenceEvent();
        event.setWarnSn(noticeRecord.getWarnSn());
        event.setFenceSnapId(noticeRecord.getFenceId());
        event.setVehicleLicense(noticeRecord.getVehicleLicense());
        event.setVehicleVin(noticeRecord.getVehicleVin());
        event.setDeviceNo(noticeRecord.getDeviceNo());
        // 考虑存储simNo
        event.setSimNo(noticeRecord.getDeviceNo());
        event.setDeviceType(noticeRecord.getDeviceType());
        event.setOutFenceTime(noticeRecord.getStartTime());
        event.setLatitude(noticeRecord.getLatitude());
        event.setLongitude(noticeRecord.getLongitude());
        event.setAddress(noticeRecord.getWarnAddress());
        event.setEffectiveDuration(effectedFence.fence.getEffectiveDuration());
        dispatcher.dispatcher(event);
    }

    // 开始报警记录
    private GovWarnMsgNoticeRecord doStartWarnRecord(VehicleBaseInfoCache vehicle,
                                                     CarGpsPosition effectedPosition,
                                                     WarnPolygonFence  effectedFence,
                                                     GovGpsDevice device) {
        GovCarGpsFence fence = effectedFence.fence; // 围栏
        VehicleGpsLocation location = effectedPosition.location; // 位置

        logger.info("公务公车开始出栏报警, vin : {}, latitude : {}, longitude : {}, fence : {}",
                vehicle.getVehicleVin(), location.getLatBaidu(), location.getLngBaidu(), fence.getFenceId());

        // 尝试锁定车辆的状态
        boolean lock = this.tryLockCarWorkingStatus(vehicle.getVehicleNo());

        try {
            // 生成报警编号
            String warnSn = this.generator.next(
                    WarnSnGenerator.WarnType.OFFICIAL_VEHICLES_WARN_OUT, location.getCreateDate());
            // 报警地址
            String address = null;

            try {
                AddressResult result = baiduMapApiClient.pointToAddress(
                        location.getLatBaidu(), location.getLngBaidu());
                if (result.getStatus() == 0) {
                    address = result.getAddress();
                }
            } catch (IOException e) {
                logger.warn("查询百度逆地理位置失败", e);
            }

            // 生成报警通知
            GovWarnMsgNoticeRecord noticeRecord = new GovWarnMsgNoticeRecord();
            noticeRecord.setCreateTime(new Date());
            noticeRecord.setUpdateTime(new Date());
            noticeRecord.setFenceId(fence.getSnapId()); // 围栏快照id
            noticeRecord.setVehicleLicense(vehicle.getVehicleLicense());
            noticeRecord.setVehicleVin(vehicle.getVehicleVin());
            noticeRecord.setWarnSn(warnSn); // 报警编号
            noticeRecord.setWarnType(fence.getWarnType());
            noticeRecord.setNoticeType(CarWarnMsgNoticeTypeEnum.GOV_ORDER_NOTICE.getType());
            noticeRecord.setWarnStatus(WarnMsgNoticeWarnStatusEnum.START.getStatus());
            noticeRecord.setStartTime(location.getCreateDate());
            noticeRecord.setLatitude(location.getLatBaidu());
            noticeRecord.setLongitude(location.getLngBaidu());
            noticeRecord.setWarnAddress(address);
            noticeRecord.setDeviceNo(device.getDeviceNo());
            noticeRecord.setDeviceType(device.getDeviceType());
            // 判断是否为无任务订单
            noticeRecord.setWarnNoticeType(lock ?
                    CarWarnNoticeOrderTypeEnum.NO_ORDER_WARN.getValue() :
                    CarWarnNoticeOrderTypeEnum.ORDER_WARN.getValue());

            this.warnMsgNoticeRecordService.save(noticeRecord);
            //生成节假日报警记录
            this.saveHolidayOutFenceWarn(warnSn,address,vehicle,device, fence,location);

            // 删除缓存
            this.clearWarnRecordCache(vehicle.getVehicleVin());

            return noticeRecord;
        } catch (Exception e) {
            logger.error("开始车辆报警记录失败, vin : {}", vehicle.getVehicleVin(), e);
            if (lock) {
                // 回滚车辆任务状态
                this.tryReleaseCarWorkingStatus(vehicle.getVehicleNo());
            }
            throw e;
        }
    }

    // 结束报警记录
    private void doEndWarnRecord(VehicleBaseInfoCache vehicle,
                                 CarGpsPosition effectedPosition,
                                 WarnPolygonFence effectedFence,
                                 GovWarnMsgNoticeRecord noticeRecord) {
        VehicleGpsLocation location = effectedPosition.location;
        GovCarGpsFence fence = effectedFence.fence;

        logger.info("公务公车结束入栏, vin : {}, latitude : {}, longitude : {}, fence : {}",
                vehicle.getVehicleVin(), location.getLatBaidu(), location.getLngBaidu(), fence.getFenceId());

        // 报警地址
        String address = null;
        try {
            AddressResult result = baiduMapApiClient.pointToAddress(location.getLatBaidu(), location.getLngBaidu());
            if (result.getStatus() == 0) {
                address = result.getAddress();
            }
        } catch (IOException e) {
            logger.warn("查询百度逆地理位置失败", e);
        }

        // 里程信息
        BigDecimal mileage =
                computeVehicleMileage(
                        vehicle.getVehicleNo(),
                        vehicle.getVehicleLicense(),
                        vehicle.getVehicleVin(),
                        location.getDeviceId(),
                        location.getDeviceType(),
                        noticeRecord.getStartTime(),
                        location.getCreateDate());
        // 修改报警通知
        noticeRecord.setFinishTime(location.getCreateDate());
        noticeRecord.setBackFenceId(fence.getSnapId());
        noticeRecord.setEndLatitude(location.getLatBaidu());
        noticeRecord.setEndLongitude(location.getLngBaidu());
        noticeRecord.setEndWarnAddress(address);
        noticeRecord.setTripMileage(mileage);
        noticeRecord.setWarnStatus(WarnMsgNoticeWarnStatusEnum.FINISH.getStatus());
        // 更新报警通知
        warnMsgNoticeRecordService.updateById(noticeRecord);


        updateHolidayFenceWarn( noticeRecord,vehicle,fence);

        // 删除缓存
        this.clearWarnRecordCache(vehicle.getVehicleVin());
    }

    private BigDecimal computeVehicleMileage(String vehicleNo,
                                             String carNo,
                                             String vin,
                                             String deviceId,
                                             String deviceType,
                                             Date startTime,
                                             Date endTime) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            DeviceHistoryTraceDTO trace =
                    carInfoService.queryHistoryTrace(vehicleNo, carNo,
                            vin, deviceId, format.format(startTime), format.format(endTime));
            return Optional.ofNullable(trace.getDistance()).orElse(BigDecimal.ZERO);
        } catch (Exception e) {
            logger.warn("查询里程数据有误", e);
            return BigDecimal.ZERO;
        }
    }

    private boolean tryLockCarWorkingStatus(String vehicleNo) {
        return vehicleBaseInfoService.tryLockVehicleWorkingStatus(vehicleNo);
    }

    private boolean tryReleaseCarWorkingStatus(String vehicleNo) {
        return vehicleBaseInfoService.releaseVehicleWorkingStatus(vehicleNo);
    }

    // 通过redis缓存查询围栏报警信息
    private GovWarnMsgNoticeRecord queryRunningWarnRecordUsingCache(VehicleBaseInfoCache vehicle) {
        String key = String.format(REDIS_KEY_NOTICE_MESSAGE, vehicle.getVehicleVin());
        Object value = this.cache.getCacheObject(key);
        if (Objects.nonNull(value)) {
            String sValue = Objects.toString(value);
            if (Objects.equals(sValue, REDIS_NULL_VALUE)) {
                return null;
            }
            return JSON.parseObject(sValue, GovWarnMsgNoticeRecord.class);
        }
        GovWarnMsgNoticeRecord record = this.queryRunningWarnRecord(vehicle);
        if (Objects.nonNull(record)) {
            this.cache.setCacheObject(key, JSON.toJSONString(record), 5, TimeUnit.MINUTES);
            return record;
        } else {
            this.cache.setCacheObject(key, REDIS_NULL_VALUE, 5, TimeUnit.MINUTES);
            return null;
        }
    }

    private void clearWarnRecordCache(String vehicleVin) {
        String key = String.format(REDIS_KEY_NOTICE_MESSAGE, vehicleVin);
        this.cache.deleteObject(key);
    }

    // 查询正在进行的围栏报警
    private GovWarnMsgNoticeRecord queryRunningWarnRecord(VehicleBaseInfoCache vehicle) {
        return this.warnMsgNoticeRecordService.getOne(
                new LambdaQueryWrapper<GovWarnMsgNoticeRecord>()
                        .eq(GovWarnMsgNoticeRecord::getVehicleVin, vehicle.getVehicleVin())
                        .eq(GovWarnMsgNoticeRecord::getWarnStatus, WarnStatusEnum.START.getCode())
                        .eq(GovWarnMsgNoticeRecord::getNoticeType, 1));
    }

    private List<VehicleGpsLocation> getHistoryPoints(String vehicleVin) {
        @SuppressWarnings("unchecked")
        List<Object> list = this.cache.getRedisTemplate().opsForList()
                .range(formatRedisHistoryLocationKey(vehicleVin), -(TRAIL_BACK_MAX_SIZE - 1), -1);
        if (list == null) {
            return Collections.emptyList();
        }
        return list.stream()
                .map(s -> JSON.parseObject(Objects.toString(s), VehicleGpsLocation.class))
                .collect(Collectors.toList());
    }

    @SuppressWarnings("unchecked")
    private void appendCurrentPoint(VehicleBaseInfoCache vehicle, VehicleGpsLocation location) {
        String key = formatRedisHistoryLocationKey(vehicle.getVehicleVin());
        this.cache.getRedisTemplate().opsForList().rightPush(key, JSON.toJSONString(location));
        this.cache.getRedisTemplate().opsForList().trim(key, -TRAIL_BACK_MAX_SIZE,  -1);
        this.cache.expire(key, 1, TimeUnit.HOURS);
    }

    // 判断是否为历史点
    private boolean isOlderPoint(VehicleBaseInfoCache vehicle,
                                 VehicleGpsLocation location,
                                 List<VehicleGpsLocation> historyPoints) {
        return !historyPoints.isEmpty() &&
                location.getCreateDate().before(historyPoints.get(historyPoints.size() - 1).getCreateDate());
    }

    private String formatRedisHistoryLocationKey(String vehicleVin) {
        return String.format(REDIS_KEY_HISTORY_LOCATION, vehicleVin);
    }

    private String formatRedisLockKey(String vehicleVin) {
        return String.format(REDIS_LOCK_KEY, vehicleVin);
    }

    // 从redis缓存查询围栏信息
    @SuppressWarnings("unchecked")
    private List<GovCarGpsFence> findCandidateFenceUsingCache(String deptCode) {
        // 先尝试查询缓存记录
        String key = String.format(REDIS_KEY_GOV_FENCE, deptCode);
        List<Object> list = this.cache.getRedisTemplate().opsForList().range(key, 0, -1);
        if (Objects.nonNull(list) && !list.isEmpty()) {
            return list.stream()
                    .map(s -> JSON.parseObject(Objects.toString(s), GovCarGpsFence.class))
                    .collect(Collectors.toList());
        }
        List<GovCarGpsFence> fences = this.findCandidateFenceByStructCode(deptCode);
        // TODO 空集合处理
        if (!fences.isEmpty()) {
            this.cache.getRedisTemplate().opsForList().rightPushAll(key,
                    fences.stream().map(JSON::toJSONString).collect(Collectors.toList()));
            this.cache.expire(key, 5, TimeUnit.MINUTES);
        }
        return fences;
    }

    // 根据部门查找对应的围栏信息
    private List<GovCarGpsFence> findCandidateFenceByStructCode(String deptCode) {
        return this.carGpsFenceService.list(
                new LambdaQueryWrapper<GovCarGpsFence>()
                        .eq(GovCarGpsFence::getDepartmentCode, deptCode)
                        .eq(GovCarGpsFence::getFenceBusinessType, FenceBusinessTypeEnum.OFFICIAL_VEHICLE.getCode())
                        .eq(GovCarGpsFence::getFenceStatus, FenceStatusEnum.NORMAL.getCode()));
    }

    // 查询绑定设备信息
    private GovGpsDevice queryVehicleDevice(VehicleBaseInfoCache vehicle,
                                            VehicleGpsLocation location) {
        List<GovGpsDevice> devices =
                this.gpsDeviceService.list(
                        new LambdaQueryWrapper<GovGpsDevice>()
                                .eq(GovGpsDevice::getSimNo, location.getSimNo())
                                .eq(GovGpsDevice::getVehicleNo, vehicle.getVehicleNo())
                                .eq(GovGpsDevice::getCompanyId, vehicle.getCompanyId())
                                .eq(GovGpsDevice::getBindStatus, GovGpsDeviceEnum.DeviceBindStatusEnum.BIND.getCode()));

        if (devices == null || devices.isEmpty()) {
            return null;
        } else {
            return devices.get(0);
        }
    }

    private GovGpsDevice getVehicleDevice(VehicleBaseInfoCache vehicle,
                                            String deviceNo) {
        List<GovGpsDevice> devices =
                this.gpsDeviceService.list(
                        new LambdaQueryWrapper<GovGpsDevice>()
                                .eq(GovGpsDevice::getDeviceNo, deviceNo)
                                .eq(GovGpsDevice::getVehicleNo, vehicle.getVehicleNo())
                                .eq(GovGpsDevice::getCompanyId, vehicle.getCompanyId())
                                .eq(GovGpsDevice::getBindStatus, GovGpsDeviceEnum.DeviceBindStatusEnum.BIND.getCode()));

        if (devices == null || devices.isEmpty()) {
            return null;
        } else {
            return devices.get(0);
        }
    }

    // 当前坐标点是否在围栏内, 满足一个即可
    private WarnLocationBehavior isInFence(CarGpsPosition position,
                                           List<WarnPolygonFence> fences) {
        if (Objects.isNull(position)) {
            return WarnLocationBehavior.UNDEFINED;
        }

        boolean result = fences.stream().anyMatch(s -> s.polygon.contains(position.point));
        return result ? WarnLocationBehavior.IN : WarnLocationBehavior.OUT;
    }

    private Point renderPoint(Coordinate coordinate) {
        GeometryFactory factory = new GeometryFactory();
        return factory.createPoint(coordinate);
    }

    private WarnPolygonFence findNearestPolygon(CarGpsPosition position,
                                                List<WarnPolygonFence> fences) {
        if (position == null) {
            return null;
        }

        double min = Double.MAX_VALUE;
        WarnPolygonFence result = null;
        for (WarnPolygonFence fence : fences) {
            double distance = DistanceOp.distance(position.point, fence.polygon);
            if (distance < min) {
                min = distance;
                result = fence;
            }
        }
        return result;
    }

    // 当前坐标点是否在围栏内, 满足一个即可
    private WarnLocationBehavior isAllInFence(List<CarGpsPosition> positions,
                                              List<WarnPolygonFence> fences) {
        // 必须满足连续定位点
        if (positions.size() < TRAIL_BACK_MAX_SIZE) {
            logger.info("公务车上报点位不满足数量 {}", TRAIL_BACK_MAX_SIZE);
            return WarnLocationBehavior.UNDEFINED;
        }
        // TODO 保证满足同一围栏
        List<Boolean> inFenceList = new ArrayList<>();
        for (CarGpsPosition position : positions) {
            inFenceList.add(fences.stream().anyMatch(s -> s.polygon.contains(position.point)));
        }
        if (inFenceList.stream().allMatch(s -> s)) {
            // 一直在圈内
            return WarnLocationBehavior.IN;
        } else if (inFenceList.stream().noneMatch(s -> s)) {
            // 一直在圈外
            return WarnLocationBehavior.OUT;
        } else {
            return WarnLocationBehavior.UNDEFINED;
        }
    }

    private CarGpsPosition convertLocationToPoint(VehicleGpsLocation location) {
        GeometryFactory factory = new GeometryFactory();
        Point point = factory.createPoint(
                new Coordinate(location.getLngBaidu().doubleValue(),
                        location.getLatBaidu().doubleValue()));
        return new CarGpsPosition(point, location);
    }

    private List<WarnPolygonFence> doConvertFenceToPolygon(List<GovCarGpsFence> fences) {
        List<WarnPolygonFence> polygons = new ArrayList<>();
        for (GovCarGpsFence fence : fences) {
            if (fence.getFenceType() ==
                    FenceType.ROUND.getValue()) {
                // 圆形区域
                String[] pairs = fence.getCenter().split(",");
                Polygon polygon = this.renderCircleFence(
                        new Coordinate(Double.parseDouble(pairs[0]),
                                Double.parseDouble(pairs[1])), new BigDecimal(fence.getRadius()));
                polygons.add(new WarnPolygonFence(polygon, fence));
            } else if (fence.getFenceType() ==
                    FenceType.POLYGON.getValue()) {
                String[] points = fence.getPoints().split(";");
                Coordinate[] coordinates =
                        Arrays.stream(points).map(s -> {
                            String[] pairs = s.split(",");
                            return new Coordinate(Double.parseDouble(pairs[0]), Double.parseDouble(pairs[1]));
                        }).toArray(Coordinate[]::new);
                Polygon polygon = this.renderPolygonFence(coordinates);
                polygons.add(new WarnPolygonFence(polygon, fence));
            }
        }
        return polygons;
    }

    private VehicleBaseInfoCache toVehicleCache(GovVehicleBaseInfo record) {
        VehicleBaseInfoCache result = new VehicleBaseInfoCache();
        BeanUtils.copyProperties(record, result);
        return result;
    }

    private Polygon renderPolygonFence(Coordinate[] coordinates) {
        GeometryFactory factory = new GeometryFactory();
        return factory.createPolygon(coordinates);
    }

    private Polygon renderCircleFence(Coordinate center, BigDecimal radius) {
        GeometryFactory factory = new GeometryFactory();
        // 默认生成64个边
        int numEdges = 64;
        // 创建圆形的坐标点
        Coordinate[] coordinates = new Coordinate[numEdges + 1];
        for (int i = 0; i < numEdges; i++) {
            double angle = 2 * Math.PI * i / numEdges;
            double dx = radius.doubleValue() * Math.cos(angle);
            double dy = radius.doubleValue() * Math.sin(angle);
            // 转换为经纬度
            double dLat = dy / (6371000 * Math.PI / 180.0);
            double dLon = dx / (6371000 * Math.cos(Math.toRadians(center.y)) * Math.PI / 180.0);
            coordinates[i] = new Coordinate(center.x + dLon, center.y + dLat);
        }
        coordinates[numEdges] = coordinates[0]; // 闭合多边形
        LinearRing ring = factory.createLinearRing(coordinates);
        return new Polygon(ring, null, factory);
    }


    enum WarnLocationBehavior {
        IN,
        OUT,
        UNDEFINED,
    }

    static class WarnPolygonFence {
        // 围栏的多边形区域
        Polygon polygon;
        // 对应的围栏记录
        GovCarGpsFence fence;

        WarnPolygonFence(Polygon polygon, GovCarGpsFence fence) {
            this.polygon = polygon;
            this.fence = fence;
        }
    }

    static class CarGpsPosition {
        // 坐标点
        Point point;
        // 车辆信息
        VehicleGpsLocation location;

        CarGpsPosition(Point point, VehicleGpsLocation location) {
            this.point = point;
            this.location = location;
        }
    }

    private void saveHolidayOutFenceWarn(String warnSn,String address,VehicleBaseInfoCache vehicle,GovGpsDevice device,GovCarGpsFence fence,VehicleGpsLocation location ){
        logger.info("节假日报警记录保存:{} 日期 {}",warnSn,location.getCreateDate());
        if(govPublicCreateOrderService.noNeedVerifyOrder(vehicle.getUseAttribute())){
            logger.info("无需审核的车辆，不生成无任务报警记录:{},getUseAttribute,{}",warnSn,vehicle.getUseAttribute());
            return;
        }
        //生成节假日报警记录
//        if(!Objects.equals(profile, ProfileEnum.ONLINE.getProfile())){
//            Date createDate = DateUtils.formatDate(holiday_config+" "+DateUtils.getCurrentTimeAsString(), DateUtils.TIME_FORMAT);
//            location.setCreateDate(createDate);
//        }


        String holidayType = getHolidayType(location.getCreateDate(), location.getCreateDate());

        if(StringUtils.isBlank(holidayType)){
            logger.info("节假日记录没有命中，时间={}",location.getCreateDate());
            return;
        }
        CarWarnFenceHoliday carWarnFenceHoliday = new CarWarnFenceHoliday();
        carWarnFenceHoliday.setVehicleLicense(vehicle.getVehicleLicense());
        carWarnFenceHoliday.setVehicleVin(vehicle.getVehicleVin());
        carWarnFenceHoliday.setSimNo(device.getSimNo());
        carWarnFenceHoliday.setDeviceNo(device.getDeviceNo());
        carWarnFenceHoliday.setDeviceType(device.getDeviceType());
        // 节假日类型
        carWarnFenceHoliday.setHolidayType(holidayType);
        carWarnFenceHoliday.setCompanyId(vehicle.getCompanyId());
        carWarnFenceHoliday.setStructCode(vehicle.getVehicleBelongDeptCode());
        carWarnFenceHoliday.setStructName(vehicle.getVehicleBelongDeptName());
        carWarnFenceHoliday.setManfactId(device.getManufactId());
        carWarnFenceHoliday.setManfactName(device.getManufactName());
        carWarnFenceHoliday.setModelId(device.getModelId());
        carWarnFenceHoliday.setModelName(device.getModelName());
        carWarnFenceHoliday.setFenceId(fence.getSnapId());
        carWarnFenceHoliday.setFenceName(fence.getFenceName());
        carWarnFenceHoliday.setFenceType(fence.getFenceType());
        carWarnFenceHoliday.setWarnType(fence.getWarnType());
        carWarnFenceHoliday.setLongitude(location.getLngBaidu());
        carWarnFenceHoliday.setLatitude(location.getLatBaidu());
        carWarnFenceHoliday.setWarnAddress(address);
        carWarnFenceHoliday.setWarnStartTime(location.getCreateDate());
        carWarnFenceHoliday.setWarnStatus(GovWarnEnum.WarnStatusEnum.UNHANDLED.getCode());
        carWarnFenceHoliday.setCreateTime(new Date());
        carWarnFenceHoliday.setUpdateTime(new Date());
//            carWarnFenceHoliday.setBelongCityCode("");
//            carWarnFenceHoliday.setBelongCityName("");
        carWarnFenceHoliday.setVehicleModelCode(vehicle.getVehicleSeriesId()+"");
        carWarnFenceHoliday.setVehicleModelName(vehicle.getVehicleSeriesName());

        carWarnFenceHoliday.setEffectiveDuration(fence.getEffectiveDuration());
        carWarnFenceHoliday.setWarnSn(warnSn);
        carWarnFenceHoliday.setFenceBusinessType(FenceBusinessTypeEnum.OFFICIAL_VEHICLE.getCode());
        carWarnFenceHolidayService.save(carWarnFenceHoliday);
    }

    private void saveHolidayInFenceWarn(String warnSn,GovWarnMsgNoticeRecord noticeRecord,VehicleBaseInfoCache vehicle,GovCarGpsFence fence,String holidayType ){
        if(govPublicCreateOrderService.noNeedVerifyOrder(vehicle.getUseAttribute())){
            logger.info("无需审核的车辆，不生成无任务报警结束记录:{},getUseAttribute,{}",warnSn,vehicle.getUseAttribute());
            return;
        }
        //生成节假日报警记录
        GovGpsDevice device = getVehicleDevice(vehicle, noticeRecord.getDeviceNo());
        if (Objects.isNull(device)) {
            logger.info("公务车车架 {} 无设备信息", vehicle.getVehicleVin());
            return;
        }
        CarWarnFenceHoliday carWarnFenceHoliday = new CarWarnFenceHoliday();
        carWarnFenceHoliday.setVehicleLicense(vehicle.getVehicleLicense());
        carWarnFenceHoliday.setVehicleVin(vehicle.getVehicleVin());
        carWarnFenceHoliday.setSimNo(device.getSimNo());
        carWarnFenceHoliday.setDeviceNo(device.getDeviceNo());
        carWarnFenceHoliday.setDeviceType(device.getDeviceType());
        // 节假日类型
        carWarnFenceHoliday.setHolidayType(holidayType);
        carWarnFenceHoliday.setCompanyId(vehicle.getCompanyId());
        carWarnFenceHoliday.setStructCode(vehicle.getVehicleBelongDeptCode());
        carWarnFenceHoliday.setStructName(vehicle.getVehicleBelongDeptName());
        carWarnFenceHoliday.setManfactId(device.getManufactId());
        carWarnFenceHoliday.setManfactName(device.getManufactName());
        carWarnFenceHoliday.setModelId(device.getModelId());
        carWarnFenceHoliday.setModelName(device.getModelName());
        carWarnFenceHoliday.setFenceId(fence.getSnapId());
        carWarnFenceHoliday.setFenceName(fence.getFenceName());
        carWarnFenceHoliday.setFenceType(fence.getFenceType());
        carWarnFenceHoliday.setWarnType(fence.getWarnType());
        carWarnFenceHoliday.setLongitude(noticeRecord.getLongitude());
        carWarnFenceHoliday.setLatitude(noticeRecord.getLatitude());
        carWarnFenceHoliday.setWarnAddress(noticeRecord.getWarnAddress());
        carWarnFenceHoliday.setWarnStartTime(noticeRecord.getStartTime());
        carWarnFenceHoliday.setWarnStatus(GovWarnEnum.WarnStatusEnum.UNHANDLED.getCode());
        carWarnFenceHoliday.setCreateTime(new Date());
        carWarnFenceHoliday.setUpdateTime(new Date());
        carWarnFenceHoliday.setVehicleModelCode(vehicle.getVehicleSeriesId()+"");
        carWarnFenceHoliday.setVehicleModelName(vehicle.getVehicleSeriesName());
        carWarnFenceHoliday.setEffectiveDuration(fence.getEffectiveDuration());
        carWarnFenceHoliday.setWarnSn(warnSn);
        carWarnFenceHoliday.setFenceBusinessType(FenceBusinessTypeEnum.OFFICIAL_VEHICLE.getCode());

        //结束信息
        carWarnFenceHoliday.setEndLongitude(noticeRecord.getEndLongitude());
        carWarnFenceHoliday.setEndLatitude(noticeRecord.getEndLatitude());
        carWarnFenceHoliday.setEndWarnAddress(noticeRecord.getEndWarnAddress());
        carWarnFenceHoliday.setTripMileage(noticeRecord.getTripMileage());
        carWarnFenceHoliday.setWarnEndTime(noticeRecord.getFinishTime());
        carWarnFenceHoliday.setBackFenceId(fence.getSnapId());
        carWarnFenceHoliday.setBackFenceType(fence.getFenceType());
        carWarnFenceHoliday.setBackFenceName(fence.getFenceName());

        carWarnFenceHolidayService.save(carWarnFenceHoliday);
    }

    private void updateHolidayFenceWarn(GovWarnMsgNoticeRecord noticeRecord,VehicleBaseInfoCache vehicle,GovCarGpsFence fence){
        String warnSn=noticeRecord.getWarnSn();
        logger.info("节假日报警记录更新:{} 日期:",warnSn,noticeRecord.getFinishTime());
        if(govPublicCreateOrderService.noNeedVerifyOrder(vehicle.getUseAttribute())){
            logger.info("无需审核的车辆，不生成更新报警记录:{},getUseAttribute,{}",warnSn,vehicle.getUseAttribute());
            return;
        }
        LambdaQueryWrapper<CarWarnFenceHoliday> queryWrapper=new LambdaQueryWrapper<>();
        queryWrapper.eq(CarWarnFenceHoliday::getWarnSn,warnSn);
        CarWarnFenceHoliday carWarnFenceHoliday = carWarnFenceHolidayService.getOne(queryWrapper);
//        if(!Objects.equals(profile, ProfileEnum.ONLINE.getProfile())){
//            Date createDate = DateUtils.formatDate(holiday_config+" "+DateUtils.getCurrentTimeAsString(), DateUtils.TIME_FORMAT);
//            location.setCreateDate(createDate);
//        }

        String holidayType = getHolidayType(noticeRecord.getStartTime(), noticeRecord.getFinishTime());
        if(StringUtils.isBlank(holidayType)){
            logger.info("节假日记录没有命中 时间={},warnSn={}",noticeRecord.getFinishTime(),warnSn);
            return;
        }
        if(carWarnFenceHoliday ==null){
            logger.info("节假日记录不存在 则新增:{}",warnSn);
            //没有记录要先创建 处理报警跨天的问题
            saveHolidayInFenceWarn(warnSn,noticeRecord,vehicle,fence, holidayType);
            return;
        }
        if(Objects.nonNull(carWarnFenceHoliday)){
            CarWarnFenceHoliday update=new CarWarnFenceHoliday();
            update.setId(carWarnFenceHoliday.getId());
            update.setEndLongitude(noticeRecord.getEndLongitude());
            update.setEndLatitude(noticeRecord.getEndLatitude());
            update.setEndWarnAddress(noticeRecord.getEndWarnAddress());
            update.setTripMileage(noticeRecord.getTripMileage());
            //节假日类型
            update.setHolidayType(holidayType);
            update.setWarnEndTime(noticeRecord.getFinishTime());
            update.setBackFenceId(fence.getSnapId());
            update.setBackFenceType(fence.getFenceType());
            update.setBackFenceName(fence.getFenceName());
            carWarnFenceHolidayService.updateById(update);
        }

    }
    /**
     * 计算节假日
     */
    private String getHolidayType(Date startDate, Date endDate) {
        if (startDate == null) {
            return "";
        }
        if (endDate == null) {
            endDate = new Date();
        }

        Integer year = DateUtils.getYear(startDate);
        List<HolidayConfig> holidayConfigs = holidayConfigService.getHolidayConfigByYear(year);

        Set<String> ajustDateSet = holidayConfigs.stream()
                .filter(e -> Objects.equals(e.getDayType(), HolidayDayTypeEnum.ADJUSTMENT_DAY.getCode()))
                .map(day -> DateUtils.format(day.getCalendarDay(), DateUtils.DATE_FORMAT))
                .collect(Collectors.toSet());

        Map<String, String> holidayMap = holidayConfigs.stream()
                .filter(e -> Objects.equals(e.getDayType(), HolidayDayTypeEnum.HOLIDAY.getCode()))
                .collect(Collectors.toMap(
                        k -> DateUtils.format(k.getCalendarDay(), DateUtils.DATE_FORMAT),
                        v -> v.getHolidayName(),
                        (a, b) -> a));

        Set<Integer> holidayTypes = new HashSet<>();
        List<String> datesBetween = DateUtils.getDatesBetween(startDate, endDate);

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtils.DATE_FORMAT);

        for (String date : datesBetween) {
            try {
                LocalDate localDate = LocalDate.parse(date, formatter);
                DayOfWeek dayOfWeek = localDate.getDayOfWeek();

                if (dayOfWeek == DayOfWeek.SATURDAY && !ajustDateSet.contains(date)) {
                    holidayTypes.add(HolidayTypeEnum.SATURDAY.getCode());
                } else if (dayOfWeek == DayOfWeek.SUNDAY && !ajustDateSet.contains(date)) {
                    holidayTypes.add(HolidayTypeEnum.SUNDAY.getCode());
                } else if (holidayMap.containsKey(date)) {
                    holidayTypes.add(HolidayTypeEnum.getCodeByDesc(holidayMap.get(date)));
                }
            } catch (Exception e) {
                logger.error("getHolidayType-日期格式化失败：{}", date, e);
            }
        }

        if (!holidayTypes.isEmpty()) {
            return String.join(",", holidayTypes.stream().map(String::valueOf).collect(Collectors.toList()));
        } else {
            return "";
        }
    }

}
