package com.mrcar.gov.rest.controller.tool;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.google.common.collect.Lists;
import com.izu.framework.resp.RestResp;
import com.mrcar.gov.asset.domain.GovVehicleBaseInfo;
import com.mrcar.gov.asset.domain.GovCarRefuelingRecord;
import com.mrcar.gov.asset.domain.GovOilConsumption;
import com.mrcar.gov.asset.service.GovCarRefuelingRecordService;
import com.mrcar.gov.asset.service.GovOilConsumptionService;
import com.mrcar.gov.asset.service.GovVehicleBaseInfoService;
import com.mrcar.gov.common.dto.BaseDTO;
import com.mrcar.gov.user.domain.GovUser;
import com.mrcar.gov.user.mapper.GovUserMapper;
import com.mrcar.gov.user.service.GovUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.lang.StringUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.Reader;
import java.nio.charset.StandardCharsets;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 加油记录初始化工具控制器
 */
@RestController
@RequestMapping("/refuelingRecord/initTool")
@Slf4j
public class RefuelingRecordInitToolController {

    @Resource
    private GovVehicleBaseInfoService govVehicleBaseInfoService;

    @Resource
    private GovCarRefuelingRecordService govCarRefuelingRecordService;

    @Resource
    private GovOilConsumptionService govOilConsumptionService;
    @Resource
    private GovUserService govUserService;

    /**
     * 初始化加油记录数据
     * 从CSV文件中读取加油记录数据并初始化
     */
    @PostMapping("/initRefuelingRecord")
    public RestResp<String> initRefuelingRecord(@RequestBody BaseDTO baseDTO) {
        // 需要补充驾驶员信息
        Map<String, Integer> refuelingCodeMap = new HashMap<>();
        //
        List<GovUser> govUserList = govUserService.list(new LambdaQueryWrapper<GovUser>()
                .eq(GovUser::getCompanyId, baseDTO.getLoginCompanyId()).in(GovUser::getUserType, Lists.newArrayList(1, 2)));
        Map<String, List<GovUser>> govUserMap = govUserList.stream().collect(Collectors.groupingBy(GovUser::getBelongDeptCode));
        try {
            // 读取加油记录CSV文件
            ClassPathResource refuelingResource = new ClassPathResource("20250618car_refueling_record.csv");
            Reader refuelingReader = new InputStreamReader(refuelingResource.getInputStream(), StandardCharsets.UTF_8);
            CSVParser refuelingParser = new CSVParser(refuelingReader, CSVFormat.DEFAULT.withFirstRecordAsHeader());

            // 读取油耗CSV文件
            ClassPathResource consumptionResource = new ClassPathResource("20250618oil_consumption.csv");
            Reader consumptionReader = new InputStreamReader(consumptionResource.getInputStream(), StandardCharsets.UTF_8);
            CSVParser consumptionParser = new CSVParser(consumptionReader, CSVFormat.DEFAULT.withFirstRecordAsHeader());

            // 解析加油记录数据
            Map<String, List<GovCarRefuelingRecord>> refuelingMap = new HashMap<>();
            for (CSVRecord record : refuelingParser) {
                GovCarRefuelingRecord refuelingRecord = new GovCarRefuelingRecord();
                // 设置默认值
                refuelingRecord.setStatus(1);
                refuelingRecord.setApprovalStatus(1);
                refuelingRecord.setPayType(1);
                refuelingRecord.setCooperationOrgStatus(0);
                refuelingRecord.setOrgName("");
                refuelingRecord.setCreateId(0);
                refuelingRecord.setCreateName("system");
                refuelingRecord.setUpdateId(0);
                refuelingRecord.setUpdateName("system");
                refuelingRecord.setApprovalId("");

                // 解析字段，处理空值
                String refuelingCode = record.get("refueling_code");
                refuelingRecord.setRefuelingCode(StringUtils.isBlank(refuelingCode) ? "" : refuelingCode);

                String vehicleLicense = record.get("vehicle_license");
                refuelingRecord.setVehicleLicense(StringUtils.isBlank(vehicleLicense) ? "" : vehicleLicense);

                String addValue = record.get("add_value");
                refuelingRecord.setAddValue(StringUtils.isBlank(addValue) ? BigDecimal.ZERO : new BigDecimal(addValue));

                String addFee = record.get("add_fee");
                refuelingRecord.setAddFee(StringUtils.isBlank(addFee) ? BigDecimal.ZERO : new BigDecimal(addFee));

                String dashboardMileage = record.get("dashboard_mileage");
                refuelingRecord.setDashboardMileage(StringUtils.isBlank(dashboardMileage) ? BigDecimal.ZERO : new BigDecimal(dashboardMileage));

                String refuelingTime = record.get("refueling_time");
                if(StringUtils.isNotBlank(refuelingTime)) {
                    try {
                        refuelingRecord.setRefuelingTime(new SimpleDateFormat("yyyy-MM-dd").parse(refuelingTime));
                    } catch (Exception e) {
                        log.warn("解析加油时间失败: {}", refuelingTime);
//                        refuelingRecord.setRefuelingTime(new Date());
                    }
                } else {
//                    refuelingRecord.setRefuelingTime(new Date());
                }

                String refuelingAddress = record.get("refueling_address");
                refuelingRecord.setRefuelingAddress(StringUtils.isBlank(refuelingAddress) ? "" : refuelingAddress);

                String voucherUrl = record.get("voucher_url");
                refuelingRecord.setVoucherUrl(StringUtils.isBlank(voucherUrl) ? "" : voucherUrl);

                String voucherTime = record.get("voucher_time");
                if(StringUtils.isNotBlank(voucherTime)) {
                    try {
                        refuelingRecord.setVoucherTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(voucherTime));
                    } catch (Exception e) {
                        log.warn("解析小票时间失败: {}", voucherTime);
//                        refuelingRecord.setVoucherTime(new Date());
                    }
                } else {
//                    refuelingRecord.setVoucherTime(new Date());
                }

                String dashboardTime = record.get("dashboard_time");
                if(StringUtils.isNotBlank(dashboardTime)) {
                    try {
                        refuelingRecord.setDashboardTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(dashboardTime));
                    } catch (Exception e) {
                        log.warn("解析仪表盘时间失败: {}", dashboardTime);
//                        refuelingRecord.setDashboardTime(new Date());
                    }
                } else {
//                    refuelingRecord.setDashboardTime(new Date());
                }

                String payType = record.get("pay_type");
                refuelingRecord.setPayType(StringUtils.isBlank(payType) ? 0 : Integer.parseInt(payType));

                String status = record.get("status");
                refuelingRecord.setStatus(StringUtils.isBlank(status) ? 1 : Integer.parseInt(status));

                String approvalStatus = record.get("approval_status");
                refuelingRecord.setApprovalStatus(StringUtils.isBlank(approvalStatus) ? 1 : Integer.parseInt(approvalStatus));

                String cancelReason = record.get("cancel_reason");
                refuelingRecord.setCancelReason(StringUtils.isBlank(cancelReason) ? "" : cancelReason);

                String warnType = record.get("warn_type");
                refuelingRecord.setWarnType(StringUtils.isBlank(warnType) ? "" : warnType);

                String remark = record.get("remark");
                refuelingRecord.setRemark(StringUtils.isBlank(remark) ? "" : remark);

                String createTime = record.get("create_time");
                if(StringUtils.isNotBlank(createTime)) {
                    try {
                        refuelingRecord.setCreateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(createTime));
                    } catch (Exception e) {
                        log.warn("解析创建时间失败: {}", createTime);
//                        refuelingRecord.setCreateTime(new Date());
                    }
                } else {
//                    refuelingRecord.setCreateTime(new Date());
                }

                String updateTime = record.get("update_time");
                if(StringUtils.isNotBlank(updateTime)) {
                    try {
                        refuelingRecord.setUpdateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(updateTime));
                    } catch (Exception e) {
                        log.warn("解析更新时间失败: {}", updateTime);
//                        refuelingRecord.setUpdateTime(new Date());
                    }
                } else {
//                    refuelingRecord.setUpdateTime(new Date());
                }

                String approvalTime = record.get("approval_date");
                if(StringUtils.isNotBlank(approvalTime)) {
                    try {
                        refuelingRecord.setApprovalTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(approvalTime));
                    } catch (Exception e) {
                        log.warn("解析审批时间失败: {}", approvalTime);
                    }
                }

                refuelingMap.computeIfAbsent(refuelingRecord.getVehicleLicense(), k -> new ArrayList<>()).add(refuelingRecord);
            }
            refuelingMap.forEach((key, val)-> {
                val.sort(Comparator.comparing(GovCarRefuelingRecord::getRefuelingCode));
            });


            // 解析油耗数据
            Map<String, List<GovOilConsumption>> consumptionMap = new HashMap<>();
            for (CSVRecord record : consumptionParser) {
                GovOilConsumption consumption = new GovOilConsumption();
                // 设置默认值
                consumption.setStatus(1);

                String refuelingCode = record.get("refueling_code");
                consumption.setRefuelingCode(StringUtils.isBlank(refuelingCode) ? "" : refuelingCode);

                String lastRefuelingCode = record.get("last_refueling_code");
                consumption.setLastRefuelingCode(StringUtils.isBlank(lastRefuelingCode) ? "" : lastRefuelingCode);

                String lastRefuelingTime = record.get("last_refueling_time");
                if(StringUtils.isNotBlank(lastRefuelingTime)) {
                    try {
                        consumption.setLastRefuelingTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(lastRefuelingTime));
                    } catch (Exception e) {
                        log.warn("解析上次加油时间失败: {}", lastRefuelingTime);
//                        consumption.setLastRefuelingTime(new Date());
                    }
                } else {
//                    consumption.setLastRefuelingTime(new Date());
                }

                String lastAddValue = record.get("last_add_value");
                consumption.setLastAddValue(StringUtils.isBlank(lastAddValue) ? BigDecimal.ZERO : new BigDecimal(lastAddValue));

                String lastDashboardMileage = record.get("last_dashboard_mileage");
                consumption.setLastDashboardMileage(StringUtils.isBlank(lastDashboardMileage) ? BigDecimal.ZERO : new BigDecimal(lastDashboardMileage));

                String lastAddFee = record.get("last_add_fee");
                consumption.setLastAddFee(StringUtils.isBlank(lastAddFee) ? BigDecimal.ZERO : new BigDecimal(lastAddFee));

                String intervalDays = record.get("interval_days");
                consumption.setIntervalDays(StringUtils.isBlank(intervalDays) ? BigDecimal.ZERO : new BigDecimal(intervalDays));

                String oilConsumption = record.get("oil_consumption");
                consumption.setOilConsumption(StringUtils.isBlank(oilConsumption) ? BigDecimal.ZERO : new BigDecimal(oilConsumption));

                String dashboardMileage = record.get("dashboard_mileage");
                consumption.setDashboardMileage(StringUtils.isBlank(dashboardMileage) ? BigDecimal.ZERO : new BigDecimal(dashboardMileage));

                String gpsMileage = record.get("gps_mileage");
                consumption.setGpsMileage(StringUtils.isBlank(gpsMileage) ? BigDecimal.ZERO : new BigDecimal(gpsMileage));

                String dashboardOilConsumption = record.get("dashboard_oil_consumption");
                consumption.setDashboardOilConsumption(StringUtils.isBlank(dashboardOilConsumption) ? BigDecimal.ZERO : new BigDecimal(dashboardOilConsumption));

                String gpsOilConsumption = record.get("gps_oil_consumption");
                consumption.setGpsOilConsumption(StringUtils.isBlank(gpsOilConsumption) ? BigDecimal.ZERO : new BigDecimal(gpsOilConsumption));

                String warnType = record.get("warn_type");
                consumption.setWarnType(StringUtils.isBlank(warnType) ? "" : warnType);

                String status = record.get("status");
                consumption.setStatus(StringUtils.isBlank(status) ? 1 : Integer.parseInt(status));

                String createTime = record.get("create_time");
                if(StringUtils.isNotBlank(createTime)) {
                    try {
                        consumption.setCreateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(createTime));
                    } catch (Exception e) {
                        log.warn("解析创建时间失败: {}", createTime);
                        consumption.setCreateTime(new Date());
                    }
                } else {
                    consumption.setCreateTime(new Date());
                }

                consumptionMap.computeIfAbsent(consumption.getRefuelingCode(), k -> new ArrayList<>()).add(consumption);
            }

            // 获取所有车辆信息
            List<GovVehicleBaseInfo> vbList = govVehicleBaseInfoService.getBaseMapper().selectList(
                new LambdaQueryWrapper<GovVehicleBaseInfo>()
                    .eq(GovVehicleBaseInfo::getCompanyId, baseDTO.getLoginCompanyId())
                    .eq(GovVehicleBaseInfo::getCreateCode, "0")
            );

            // 获取所有车牌号
            List<String> allVehicleLicenses = new ArrayList<>(refuelingMap.keySet());
            Random random = new Random();
            int idx = 0;
            // 遍历车辆信息，随机分配加油记录和油耗数据
            for (GovVehicleBaseInfo vb : vbList) {
                if (allVehicleLicenses.isEmpty()) {
                    break;
                }
                // 随机选择一个车牌号
                int randomIndex = idx % allVehicleLicenses.size();
                idx++;
                String randomLicense = allVehicleLicenses.get(randomIndex);

                // 获取对应的加油记录和油耗数据
                List<GovCarRefuelingRecord> refuelingRecords = refuelingMap.get(randomLicense);
                List<String> oldRefuelingCodeList = new ArrayList<>(refuelingRecords.size());
                List<GovCarRefuelingRecord> newRefuelingRecords = new ArrayList<>(refuelingRecords.size());
                List<GovOilConsumption> newOilConsumptions = new ArrayList<>(refuelingRecords.size());
                Map<String, String> oldToNewRefuelingCodeMap = new HashMap<>();
                refuelingRecords.forEach(refuelingRecord -> {
                    GovCarRefuelingRecord newRefuelingRecord =  JSON.parseObject(JSON.toJSONString(refuelingRecord), GovCarRefuelingRecord.class);

                    // code需要从新生成。
                    String refuelingCode = refuelingRecord.getRefuelingCode();
                    String key = refuelingCode.substring(0, 9);
                    Integer value = refuelingCodeMap.get(key);
                    if(Objects.isNull(value)) {
                        value = 0;

                    }
                    value++;
                    refuelingCodeMap.put(key, value);

                    String newRefuelingCode = key + StringUtils.leftPad(value + "", 7, "0");

                    newRefuelingRecord.setRefuelingCode(newRefuelingCode);
                    newRefuelingRecord.setVehicleLicense(vb.getVehicleLicense());
                    newRefuelingRecord.setVehicleVin(vb.getVehicleVin());
                    newRefuelingRecord.setCompanyId(vb.getCompanyId());
                    newRefuelingRecord.setVehicleBelongDeptCode(vb.getVehicleBelongDeptCode());
                    newRefuelingRecord.setVehicleBelongDeptName(vb.getVehicleBelongDeptName());
                    newRefuelingRecord.setVehicleUseDeptCode(vb.getVehicleUseDeptCode());
                    newRefuelingRecord.setVehicleUseDeptName(vb.getVehicleUseDeptName());
                    newRefuelingRecord.setManageCarType(vb.getManageCarType());
                    newRefuelingRecord.setVehicleManageDeptName(vb.getVehicleManageDeptName());
                    newRefuelingRecord.setVehicleManageDeptCode(vb.getVehicleManageDeptCode());


                    newRefuelingRecord.setCooperationOrgStatus(0);
                    newRefuelingRecord.setOrgName("");
                    // 司机信息补充 TODO
                    newRefuelingRecord.setUserCode("");
                    newRefuelingRecord.setUserName("");
                    List<GovUser> deptUserList = govUserMap.get(vb.getVehicleBelongDeptCode());
                    if(CollectionUtils.isNotEmpty(deptUserList)) {
                        int randomUserIndex = random.nextInt(deptUserList.size());
                        GovUser randomUser = deptUserList.get(randomUserIndex);
                        newRefuelingRecord.setUserCode(randomUser.getUserCode());
                        newRefuelingRecord.setUserName(randomUser.getUserName());
                    }
                    // 添加
                    newRefuelingRecords.add(newRefuelingRecord);
                    oldRefuelingCodeList.add(refuelingCode);
                    // 记录新老号码的关系
                    oldToNewRefuelingCodeMap.put(refuelingCode, newRefuelingCode);

                });

                oldRefuelingCodeList.forEach(oldRefuelingCode -> {
                    List<GovOilConsumption> oilConsumptionList = consumptionMap.get(oldRefuelingCode);
                    if(CollectionUtils.isEmpty(oilConsumptionList)) {
                        return;
                    }
                    for(GovOilConsumption oldOil : oilConsumptionList) {
                        GovOilConsumption newGovOilConsumption =  JSON.parseObject(JSON.toJSONString(oldOil), GovOilConsumption.class);
                        // 肯定有
                        String newRefuelingCode = oldToNewRefuelingCodeMap.get(oldRefuelingCode);
                        if(StringUtils.isBlank(newRefuelingCode)) {
                            return;
                        }
                        newGovOilConsumption.setRefuelingCode(newRefuelingCode);

                        String lastOldRefuelingCode = newGovOilConsumption.getLastRefuelingCode();

                        String newLastRefuelingCode = oldToNewRefuelingCodeMap.get(lastOldRefuelingCode);
                        if(StringUtils.isBlank(newLastRefuelingCode)) {
                            // code需要从新生成。
                            String key = lastOldRefuelingCode.substring(0, 9);
                            Integer value = refuelingCodeMap.get(key);
                            if(Objects.isNull(value)) {
                                value = 0;
                            }
                            value++;
                            refuelingCodeMap.put(key, value);

                            newLastRefuelingCode = key + StringUtils.leftPad(value + "", 7, "0");
                            oldToNewRefuelingCodeMap.put(lastOldRefuelingCode, newLastRefuelingCode);
                        }
                        newGovOilConsumption.setLastRefuelingCode(newLastRefuelingCode);

                        newOilConsumptions.add(newGovOilConsumption);
                    }

                });

                if(CollectionUtils.isNotEmpty(newRefuelingRecords)){
                    govCarRefuelingRecordService.saveBatch(newRefuelingRecords);
                }
                if(CollectionUtils.isNotEmpty(newOilConsumptions)){
                    govOilConsumptionService.saveBatch(newOilConsumptions);
                }
            }
            return RestResp.ok("加油记录初始化成功");
        } catch (Exception e) {
            log.error("加油记录初始化失败", e);
            return RestResp.fail("加油记录初始化失败：" + e.getMessage());
        }
    }
}
