package com.mrcar.gov.rest.service.apply.impl;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.alibaba.nacos.common.utils.JacksonUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Range;
import com.izu.framework.exception.ApiException;
import com.izu.framework.resp.InfoCode;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.util.BeanUtil;
import com.mrcar.gov.asset.domain.GovGpsDevice;
import com.mrcar.gov.asset.domain.GovGpsModel;
import com.mrcar.gov.asset.domain.GovGpsVehicleRelation;
import com.mrcar.gov.asset.domain.GovVehicleBaseInfo;
import com.mrcar.gov.asset.service.GovGpsDeviceService;
import com.mrcar.gov.asset.service.GovGpsModelService;
import com.mrcar.gov.asset.service.GovGpsVehicleRelationService;
import com.mrcar.gov.asset.service.GovVehicleBaseInfoService;
import com.mrcar.gov.base.service.SequenceGenerator;
import com.mrcar.gov.common.constant.PushMessageUtil;
import com.mrcar.gov.common.constant.asset.UseAttributeEnum;
import com.mrcar.gov.common.constant.asset.VehicleSourceTypeEnum;
import com.mrcar.gov.common.constant.asset.WorkStatusEnum;
import com.mrcar.gov.common.constant.business.GovMsgSceneEnum;
import com.mrcar.gov.common.constant.order.*;
import com.mrcar.gov.common.dto.BaseDTO;
import com.mrcar.gov.common.dto.apply.req.*;
import com.mrcar.gov.common.dto.apply.resp.*;
import com.mrcar.gov.common.dto.config.GovCompanyConfigItemValueDTO;
import com.mrcar.gov.common.dto.device.VehicleDeviceRealtimeStatus;
import com.mrcar.gov.common.dto.device.req.CarTravelDistanceReqDTO;
import com.mrcar.gov.common.dto.device.resp.DeviceHistoryTraceDTO;
import com.mrcar.gov.common.dto.order.msg.OfficialVehicleInFenceMsgDTO;
import com.mrcar.gov.common.dto.order.msg.OfficialVehicleOutFenceMsgDTO;
import com.mrcar.gov.common.dto.order.req.GovOrderUserInfoDTO;
import com.mrcar.gov.common.dto.order.req.TimeShareConfigQueryReqDTO;
import com.mrcar.gov.common.dto.order.resp.TimeShareConfigDetailRespDTO;
import com.mrcar.gov.common.dto.order.resp.TimeShareConfigRespDTO;
import com.mrcar.gov.common.enums.config.PublicConfigEnum;
import com.mrcar.gov.common.enums.config.PublicConfigValueEnum;
import com.mrcar.gov.common.enums.device.GovGpsDeviceEnum;
import com.mrcar.gov.common.enums.device.GovPublicCarLockTypeEnum;
import com.mrcar.gov.common.enums.device.GovPublicCarOperateTypeEnum;
import com.mrcar.gov.common.enums.device.GovWarnEnum;
import com.mrcar.gov.common.service.api.MessageTrackingSender;
import com.mrcar.gov.common.util.DateUtils;
import com.mrcar.gov.common.util.EmojiUtils;
import com.mrcar.gov.common.util.StrUtils;
import com.mrcar.gov.common.util.gps.BluetoothUtil;
import com.mrcar.gov.config.domain.GovCityDic;
import com.mrcar.gov.config.service.GovCityDicService;
import com.mrcar.gov.rest.service.config.GovPublicBusinessConfigService;
import com.mrcar.gov.iot.domain.GovCarWarnFence;
import com.mrcar.gov.iot.service.*;
import com.mrcar.gov.iot.utils.BaiduUtils;
import com.mrcar.gov.model.mqtt.req.CommandReqDTO;
import com.mrcar.gov.model.mqtt.resp.CommandRespDTO;
import com.mrcar.gov.order.domain.*;
import com.mrcar.gov.order.mapper.GovPublicCarOrderMapper;
import com.mrcar.gov.order.service.*;
import com.mrcar.gov.rest.service.apply.GovOrderService;
import com.mrcar.gov.rest.service.fence.GovPublicWarnFenceService;
import com.mrcar.gov.user.domain.GovCompany;
import com.mrcar.gov.user.domain.GovDriver;
import com.mrcar.gov.user.service.GovCompanyService;
import com.mrcar.gov.user.service.GovDriverService;
import com.mrcar.iot.domain.GovVehicleLocation;
import com.mrcar.thirdparty.baidu.AddressResult;
import com.mrcar.thirdparty.baidu.BaiduMapApiClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description:
 * @date 2024/12/30 15:08
 */
@Service
@Slf4j
public class GovOrderServiceImpl implements GovOrderService {

    @Resource
    private GovPublicCarOrderService govPublicCarOrderService;

    @Resource
    private GovCarInfoService govCarInfoService;

    @Resource
    private GovPublicCarOrderUserInfoService govPublicCarOrderUserInfoService;

    @Resource
    private GovOrderOperationLogService govOrderOperationLogService;

    @Resource
    private GovVehicleBaseInfoService govVehicleBaseInfoService;

    @Resource
    private GovDriverService govDriverService;

    @Resource
    private GovPublicCarOrderAddressInfoService govPublicCarOrderAddressInfoService;

    @Resource
    private GovPublicCarOrderVehicleInfoService govPublicCarOrderVehicleInfoService;

    @Resource
    private GovPublicCarOrderMapper govPublicCarOrderMapper;
    @Resource
    private SequenceGenerator sequenceGenerator;
    @Resource
    private GovCompanyService govCompanyService;

    @Resource
    private GovDeviceMongoService govDeviceMongoService;
    @Resource
    private GovGpsDeviceService govGpsDeviceService;
    @Resource
    private VehicleDeviceService vehicleDeviceService;
    @Resource
    protected GovPublicWarnFenceService govPublicWarnFenceService;

    @Autowired
    private GovBillingConfigurationSnapshootService govBillingConfigurationSnapshootService;

    @Autowired
    private GovPublicCarApplyService govPublicCarApplyService;

    @Autowired
    private GovSocVehicleFeeService govSocVehicleFeeService;

    @Autowired
    private GovSocVehicleFeeDetailService govSocVehicleFeeDetailService;

    @Resource
    private CarWarnFenceHolidayService carWarnFenceHolidayService;

    @Resource
    private GovCarWarnFenceService govCarWarnFenceService;

    @Resource
    private GovCityDicService govCityDicService;

    @Resource
    private GovTimeShareConfigSnapshotService govTimeShareConfigSnapshotService;

    @Resource
    private GovTimeShareConfigService govTimeShareConfigService;
    @Resource
    private GovPublicBusinessConfigService govPublicBusinessConfigService;

    @Resource
    private GovGpsVehicleRelationService govGpsVehicleRelationService;


    /**
     * 公务用车对公 子订单前缀
     */
    public static final String PUBLIC_GOV_CAR_ORDER_PREFIX = "GWYCZDD";
    /**
     * 公务用车对公 子订单前缀
     */
    public static final String PUBLIC_SOC_GOV_CAR_ORDER_PREFIX = "SHZLZDD";
    /**
     * 车机指令日志编号
     */
    public static final String PUBLIC_CAR_COMMAND_PREFIX = "LOG";

    @Autowired
    private MessageTrackingSender messageTrackingSender;
    private static final String START_ERROR_MESSAGE = "行程车辆上一单尚未点击结束，如确认已交接车辆，您可点击强制开始。";

    @Resource
    private BaiduMapApiClient baiduMapApiClient;

    @Resource
    private GovSocTimeShareFeeService govSocTimeShareFeeService;
    //车机4.0
    public static final String BLUE_SERVICEUUID_TERMINAL124 = "0000fff0-0000-1000-8000-00805f9b34fb";
    public static final String BLUE_CHARACTERUUID_TERMINAL124 = "0000fff6-0000-1000-8000-00805f9b34fb";
    //车机5.0
    public static final String BLUE_SERVICEUUID_TERMINAL5 = "0000fff0-0000-1000-8000-00805f9b34fb";
    public static final String BLUE_CHARACTERUUID_TERMINAL5 = "0000fff1-0000-1000-8000-00805f9b34fb";

    @Resource
    private GovGpsModelService govGpsModelService;

    @NacosValue(value = "${mrcar.gov.transportation.url}", autoRefreshed = true)
    private  String transportationUrl;

    //开锁
    public static final String OPEN_LOCK_URL ="/api/mqtt/command/unLock";
    //关锁
    public static final String CLOSE_LOCK_URL ="/api/mqtt/command/validateAndlock";

    @Resource
    private GovPublicCarCommandOperateLogService govPublicCarCommandOperateLogService;


    @Override
    public void cancelOrder(OrderNoReqDTO param) {
        Date now = new Date();
        String orderNo = param.getOrderNo();
        //获取订单实体
        //判断订单状态是否为待出发状态
        GovPublicCarOrder order = govPublicCarOrderService.getOne(new LambdaQueryWrapper<GovPublicCarOrder>().eq(GovPublicCarOrder::getOrderNo, orderNo).last("limit 1"));
        if (!order.getOrderStatus().equals(GovPublicCarOrderStatusEnum.PENDING_DEPARTURE.getCode())) {
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "该订单状态不允许取消，请刷新后重试");
        }
        //更新订单状态为已取消
        LambdaUpdateWrapper<GovPublicCarOrder> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(GovPublicCarOrder::getOrderNo, orderNo)
                .set(GovPublicCarOrder::getOrderStatus, GovPublicCarOrderStatusEnum.CANCELED.getCode())
                .set(GovPublicCarOrder::getCancellationTime, now)
                .set(GovPublicCarOrder::getUpdateCode, param.getLoginUserCode())
                .set(GovPublicCarOrder::getUpdateName, param.getLoginUserName());
        govPublicCarOrderService.update(updateWrapper);
        //插入订单取消操作人
        GovPublicCarOrderUserInfo cancelUser = new GovPublicCarOrderUserInfo();
        cancelUser.setOrderNo(orderNo);
        cancelUser.setUserType(GovPublicCarOrderUserTypeEnum.ORDER_CANCEL_OPERATOR.getCode());
        cancelUser.setUserCode(param.getLoginUserCode());
        cancelUser.setUserName(param.getLoginUserName());
        cancelUser.setUserMobile(param.getLoginUserMobile());
        cancelUser.setStructCode(param.getLoginUserBelongStructCode());
        cancelUser.setStructName(param.getLoginUserBelongStructName());
        cancelUser.setDeptCode(param.getLoginUserBelongDeptCode());
        cancelUser.setDeptName(param.getLoginUserBelongDeptName());
        cancelUser.setCompanyId(param.getLoginCompanyId());
        cancelUser.setCompanyName(param.getLoginCompanyName());
        cancelUser.setApplyNo(order.getApplyNo());
        govPublicCarOrderUserInfoService.save(cancelUser);
        //插入日志
        GovOrderOperationLog operationLog = new GovOrderOperationLog();
        operationLog.setOrderNo(order.getOrderNo());
        operationLog.setOperationType(GovPublicCarOrderOperationTypeEnum.CANCEL.getCode());
        operationLog.setOperationDescription(GovPublicCarOrderOperationTypeEnum.CANCEL.getName());
        operationLog.setOrderStatus(GovPublicCarOrderStatusEnum.CANCELED.getCode());
        operationLog.setOrderStatusName(GovPublicCarOrderStatusEnum.CANCELED.getName());
        operationLog.setOperatorCode(param.getLoginUserCode());
        operationLog.setOperatorName(param.getLoginUserName());
        operationLog.setOperatorMobile(param.getLoginUserMobile());
        govOrderOperationLogService.save(operationLog);
        //给司机发送取消信息
        try {
            pushCancelMessage(order);
        } catch (Exception e) {
            log.info("发送消息异常，{}", e.getMessage());
        }
    }

    private void pushCancelMessage(GovPublicCarOrder order) {
        //获取申请单
        GovPublicCarApply apply = govPublicCarApplyService.getOne(new LambdaQueryWrapper<GovPublicCarApply>().eq(GovPublicCarApply::getApplyNo, order.getApplyNo()).last("limit 1"));
        if (ObjectUtil.equals(apply.getDrivingType(), GovPublicCarDrivingTypeEnum.DRIVER_DRIVE.getCode()) || ObjectUtil.equals(apply.getDrivingType(), GovPublicCarDrivingTypeEnum.UNIT_DRIVE.getCode())) {
            //查询司机
            GovPublicCarOrderUserInfo govPublicCarOrderDriverInfo = govPublicCarOrderUserInfoService.getOne(new LambdaQueryWrapper<GovPublicCarOrderUserInfo>()
                    .eq(GovPublicCarOrderUserInfo::getOrderNo, order.getOrderNo())
                    .eq(GovPublicCarOrderUserInfo::getUserType, GovPublicCarOrderUserTypeEnum.DRIVER.getCode()).last("limit 1"));
            GovPublicCarOrderUserInfo passenger = govPublicCarOrderUserInfoService.getOne(new LambdaQueryWrapper<GovPublicCarOrderUserInfo>()
                    .eq(GovPublicCarOrderUserInfo::getApplyNo, order.getApplyNo())
                    .eq(GovPublicCarOrderUserInfo::getUserType, GovPublicCarOrderUserTypeEnum.PRIMARY_RIDER.getCode()).last("limit 1"));
            //查询车辆信息
            GovPublicCarOrderVehicleInfo govPublicCarOrderVehicleInfo = govPublicCarOrderVehicleInfoService.getOne(new LambdaQueryWrapper<GovPublicCarOrderVehicleInfo>()
                    .eq(GovPublicCarOrderVehicleInfo::getOrderNo, order.getOrderNo()).last("limit 1"));
            Map<String, String> extendParamMap = new HashMap<>();
            //自驾不发送司机消息
            extendParamMap.put(PushMessageUtil.MSG_PARAM_APPLY_START_TIME, DateUtils.format(apply.getExpectedPickupTime(), DateUtils.TIME_FORMAT_V3));
            extendParamMap.put(PushMessageUtil.MSG_PARAM_APPLY_END_TIME, DateUtils.format(apply.getExpectedReturnTime(), DateUtils.TIME_FORMAT_V3));
            extendParamMap.put(PushMessageUtil.MSG_PARAM_APPLY_START_ADDRESS, apply.getEstimatedDepartureShortLocation());
            extendParamMap.put(PushMessageUtil.MSG_PARAM_APPLY_END_ADDRESS, apply.getEstimatedDestinationShortLocation());
            MessageTrackingSender.MessageTrackingParam pushParam = new MessageTrackingSender.MessageTrackingParam();
            pushParam.setCompanyId(order.getCompanyId());
            pushParam.setScene(GovMsgSceneEnum.TRIP_CANCEL_NOTICE_DRIVER);
            pushParam.setReceiverCodeSet(new HashSet<>(Collections.singletonList(govPublicCarOrderDriverInfo.getUserCode())));
            extendParamMap.put(PushMessageUtil.MSG_PARAM_APPLY_PASSENGER_NAME, passenger.getUserName());
            extendParamMap.put(PushMessageUtil.MSG_PARAM_APPLY_PASSENGER_PHONE, passenger.getUserMobile());
            extendParamMap.put(PushMessageUtil.MSG_PARAM_APPLY_VEHICLE_LICENSE, govPublicCarOrderVehicleInfo.getVehicleLicense());
            pushParam.setExtendParamMap(extendParamMap);
            pushParam.setMobile(new HashSet<>());
            messageTrackingSender.sendMessageTracking(pushParam);
        }
    }

    @Override
    @Transactional
    public GovPublicOrderStartRespDTO startOrder(OrderNoReqDTO param) {
        GovPublicOrderStartRespDTO respDTO = new GovPublicOrderStartRespDTO();
        Date now = new Date();
        String orderNo = param.getOrderNo();
        //查询订单
        GovPublicCarOrder order = govPublicCarOrderService.getOne(new LambdaQueryWrapper<GovPublicCarOrder>().eq(GovPublicCarOrder::getOrderNo, orderNo).last("limit 1"));
        //校验订单状态是否为待出发状态
        if (!order.getOrderStatus().equals(GovPublicCarOrderStatusEnum.PENDING_DEPARTURE.getCode())) {
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "该订单状态不允许出发，请刷新后重试");
        }
        String vehicleNo = order.getVehicleNo();
        //查询该车关联的订单是否有未完成的订单
        List<GovPublicCarOrder> list = govPublicCarOrderService.list(new LambdaQueryWrapper<GovPublicCarOrder>().eq(GovPublicCarOrder::getVehicleNo, vehicleNo).eq(GovPublicCarOrder::getOrderStatus, GovPublicCarOrderStatusEnum.IN_USE.getCode()));
        //存在用车中的订单，逻辑则直接结束，并返回给前端
        if (CollectionUtils.isNotEmpty(list)) {
            respDTO.setResult(Boolean.FALSE);
            respDTO.setMessage(START_ERROR_MESSAGE);
            return respDTO;
        }
        //修改订单状态
        LambdaUpdateWrapper<GovPublicCarOrder> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(GovPublicCarOrder::getOrderNo, orderNo)
                .set(GovPublicCarOrder::getOrderStatus, GovPublicCarOrderStatusEnum.IN_USE.getCode())
                .set(GovPublicCarOrder::getOrderStartTime, now)
                .set(GovPublicCarOrder::getUpdateCode, param.getLoginUserCode())
                .set(GovPublicCarOrder::getUpdateName, param.getLoginUserName());
        govPublicCarOrderService.update(updateWrapper);
        //插入订单开始操作人
        GovPublicCarOrderUserInfo startUser = new GovPublicCarOrderUserInfo();
        startUser.setOrderNo(orderNo);
        startUser.setUserType(GovPublicCarOrderUserTypeEnum.ORDER_START_OPERATOR.getCode());
        startUser.setUserCode(param.getLoginUserCode());
        startUser.setUserName(param.getLoginUserName());
        startUser.setUserMobile(param.getLoginUserMobile());
        startUser.setStructCode(param.getLoginUserBelongStructCode());
        startUser.setStructName(param.getLoginUserBelongStructName());
        startUser.setDeptCode(param.getLoginUserBelongDeptCode());
        startUser.setDeptName(param.getLoginUserBelongDeptName());
        startUser.setCompanyId(param.getLoginCompanyId());
        startUser.setCompanyName(param.getLoginCompanyName());
        startUser.setApplyNo(order.getApplyNo());
        govPublicCarOrderUserInfoService.save(startUser);
        //插入地址信息
        buildAndUpdateStartAddress(vehicleNo, order);
        //插入操作日志
        GovOrderOperationLog operationLog = new GovOrderOperationLog();
        operationLog.setOrderNo(order.getOrderNo());
        operationLog.setOperationType(GovPublicCarOrderOperationTypeEnum.START_TRIP.getCode());
        operationLog.setOperationDescription(GovPublicCarOrderOperationTypeEnum.START_TRIP.getName());
        operationLog.setOrderStatus(GovPublicCarOrderStatusEnum.IN_USE.getCode());
        operationLog.setOrderStatusName(GovPublicCarOrderStatusEnum.IN_USE.getName());
        operationLog.setOperatorCode(param.getLoginUserCode());
        operationLog.setOperatorName(param.getLoginUserName());
        operationLog.setOperatorMobile(param.getLoginUserMobile());
        govOrderOperationLogService.save(operationLog);
        //修改车辆状态以及司机状态为任务中
        //修改车辆任务状态
        LambdaUpdateWrapper<GovVehicleBaseInfo> baseInfoLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        baseInfoLambdaUpdateWrapper.eq(GovVehicleBaseInfo::getVehicleNo, vehicleNo)
                .set(GovVehicleBaseInfo::getWorkStatus, WorkStatusEnum.TASK_ING.getCode());
        govVehicleBaseInfoService.update(baseInfoLambdaUpdateWrapper);
        //更新司机任务状态为任务中
        occupationDriverStatus(order.getOrderNo());
        return respDTO;
    }

    private void buildAndUpdateStartAddress(String vehicleNo, GovPublicCarOrder order) {
        List<GovGpsDevice> govGpsDevices = govGpsDeviceService.list(new LambdaQueryWrapper<GovGpsDevice>().eq(GovGpsDevice::getVehicleNo, vehicleNo)
                .eq(GovGpsDevice::getBindStatus, GovGpsDeviceEnum.DeviceBindStatusEnum.BIND.getCode())
                .in(GovGpsDevice::getDeviceType, GovGpsDeviceEnum.DeviceTypeEnum.WIRED.getCode(), GovGpsDeviceEnum.DeviceTypeEnum.TBOX.getCode(), GovGpsDeviceEnum.DeviceTypeEnum.VIDEO.getCode()));
        if (CollectionUtils.isNotEmpty(govGpsDevices)) {
            List<String> deviceSysNos = govGpsDevices.stream().map(GovGpsDevice::getDeviceSysNo).collect(Collectors.toList());
            List<GovVehicleLocation> latestLocations = vehicleDeviceService.getLatestLocations(deviceSysNos);
            //并按时间倒叙,过滤时间三个小时以内
            List<GovVehicleLocation> latestLocationsFilter = latestLocations.stream()
                    .filter(s -> DateUtil.between(s.getTimestamp(), new Date(), DateUnit.HOUR) < 3)
                    .sorted(Comparator.comparing(GovVehicleLocation::getTimestamp).reversed()).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(latestLocationsFilter)) {
                GovVehicleLocation latestLocation = latestLocationsFilter.get(0);
                AddressResult addressResult = new AddressResult();
                try {
                    addressResult = baiduMapApiClient.pointToAddress(latestLocation.getLatBaidu(), latestLocation.getLngBaidu());
                } catch (Exception e) {
                    log.info("百度坐标转换地理位置失败，错误信息：{}", e.getMessage());
                }
                GovCityDic cityDic = new GovCityDic();
                if (ObjectUtil.isNotNull(addressResult)) {
                    //根据百度的citycode转化为mrcar自己的城市code
                    cityDic = govCityDicService.getOne(new LambdaQueryWrapper<GovCityDic>().eq(GovCityDic::getBaiduCode, addressResult.getCityCode()).last("limit 1"));
                    //查询是否已存在地址信息，如果没有，则新增，否则更新
                    long count = govPublicCarOrderAddressInfoService.count(new LambdaQueryWrapper<GovPublicCarOrderAddressInfo>().eq(GovPublicCarOrderAddressInfo::getOrderNo, order.getOrderNo()));
                    if (count == 0) {
                        //插入地址信息
                        GovPublicCarOrderAddressInfo orderAddress = new GovPublicCarOrderAddressInfo();
                        orderAddress.setOrderNo(order.getOrderNo());
                        orderAddress.setCompanyId(order.getCompanyId());
                        orderAddress.setActualDepartureLongLocation(addressResult.getAddress());
                        orderAddress.setActualDepartureLatitude(latestLocation.getLatBaidu());
                        orderAddress.setActualDepartureLongitude(latestLocation.getLngBaidu());
                        orderAddress.setActualDepartureShortLocation(addressResult.getAddress());
                        orderAddress.setApplyNo(order.getApplyNo());
                        if(ObjectUtil.isNotNull(cityDic)){
                            orderAddress.setStartCityCode(cityDic.getCityCode().toString());
                            orderAddress.setStartCityName(cityDic.getCityName());
                        }
                        govPublicCarOrderAddressInfoService.save(orderAddress);
                    } else {
                        govPublicCarOrderAddressInfoService.update(new LambdaUpdateWrapper<GovPublicCarOrderAddressInfo>()
                                .eq(GovPublicCarOrderAddressInfo::getOrderNo, order.getOrderNo())
                                .set(GovPublicCarOrderAddressInfo::getActualDepartureLongLocation, addressResult.getAddress())
                                .set(GovPublicCarOrderAddressInfo::getActualDepartureLatitude, latestLocation.getLatBaidu())
                                .set(GovPublicCarOrderAddressInfo::getActualDepartureLongitude, latestLocation.getLngBaidu())
                                .set(GovPublicCarOrderAddressInfo::getActualDepartureShortLocation, addressResult.getAddress())
                                .set(GovPublicCarOrderAddressInfo::getStartCityCode, cityDic.getCityCode().toString())
                                .set(GovPublicCarOrderAddressInfo::getStartCityName, cityDic.getCityName()));
                    }
                }
            } else {
                log.info("为获取到车辆设备定位，无法开始");
            }
        } else {
            log.info("为获取到车辆设备定位，无法开始");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void forceStartOrder(OrderNoReqDTO param) {
        Date now = new Date();
        String orderNo = param.getOrderNo();
        //查询订单
        GovPublicCarOrder order = govPublicCarOrderService.getOne(new LambdaQueryWrapper<GovPublicCarOrder>().eq(GovPublicCarOrder::getOrderNo, orderNo).last("limit 1"));
        String vehicleNo = order.getVehicleNo();
        //查询该车关联的订单是否有未完成的订单
        List<GovPublicCarOrder> list = govPublicCarOrderService.list(new LambdaQueryWrapper<GovPublicCarOrder>().eq(GovPublicCarOrder::getVehicleNo, vehicleNo).eq(GovPublicCarOrder::getOrderStatus, GovPublicCarOrderStatusEnum.IN_USE.getCode()));
        if (CollectionUtils.isNotEmpty(list)) {
            forceEndOrder(list, param, GovPublicCarOrderOperationTypeEnum.FORCE_END_TRIP);
        }
        //修改订单状态
        LambdaUpdateWrapper<GovPublicCarOrder> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(GovPublicCarOrder::getOrderNo, orderNo)
                .set(GovPublicCarOrder::getOrderStatus, GovPublicCarOrderStatusEnum.IN_USE.getCode())
                .set(GovPublicCarOrder::getOrderStartTime, now)
                .set(GovPublicCarOrder::getUpdateCode, param.getLoginUserCode())
                .set(GovPublicCarOrder::getUpdateName, param.getLoginUserName());
        govPublicCarOrderService.update(updateWrapper);
        //插入订单开始操作人
        GovPublicCarOrderUserInfo startUser = new GovPublicCarOrderUserInfo();
        startUser.setOrderNo(orderNo);
        startUser.setUserType(GovPublicCarOrderUserTypeEnum.ORDER_FORCE_START_OPERATOR.getCode());
        startUser.setUserCode(param.getLoginUserCode());
        startUser.setUserName(param.getLoginUserName());
        startUser.setUserMobile(param.getLoginUserMobile());
        startUser.setStructCode(param.getLoginUserBelongStructCode());
        startUser.setStructName(param.getLoginUserBelongStructName());
        startUser.setDeptCode(param.getLoginUserBelongDeptCode());
        startUser.setDeptName(param.getLoginUserBelongDeptName());
        startUser.setCompanyId(param.getLoginCompanyId());
        startUser.setCompanyName(param.getLoginCompanyName());
        startUser.setApplyNo(order.getApplyNo());
        govPublicCarOrderUserInfoService.save(startUser);
        //插入操作日志
        GovOrderOperationLog operationLog = new GovOrderOperationLog();
        operationLog.setOrderNo(order.getOrderNo());
        operationLog.setOperationType(GovPublicCarOrderOperationTypeEnum.FORCE_START_TRIP.getCode());
        operationLog.setOperationDescription(GovPublicCarOrderOperationTypeEnum.FORCE_START_TRIP.getName());
        operationLog.setOrderStatus(GovPublicCarOrderStatusEnum.IN_USE.getCode());
        operationLog.setOrderStatusName(GovPublicCarOrderStatusEnum.IN_USE.getName());
        operationLog.setOperatorCode(param.getLoginUserCode());
        operationLog.setOperatorName(param.getLoginUserName());
        operationLog.setOperatorMobile(param.getLoginUserMobile());
        govOrderOperationLogService.save(operationLog);
        //插入地址信息
        buildAndUpdateStartAddress(vehicleNo, order);
        //更新车辆和司机状态
        //修改车辆任务状态
        LambdaUpdateWrapper<GovVehicleBaseInfo> baseInfoLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        baseInfoLambdaUpdateWrapper.eq(GovVehicleBaseInfo::getVehicleNo, vehicleNo)
                .set(GovVehicleBaseInfo::getWorkStatus, WorkStatusEnum.TASK_ING.getCode());
        govVehicleBaseInfoService.update(baseInfoLambdaUpdateWrapper);
        //更新司机任务状态为任务中
        occupationDriverStatus(order.getOrderNo());


    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public GovPublicOrderStartRespDTO endOrder(OrderNoReqDTO param) {
        GovPublicOrderStartRespDTO respDTO = new GovPublicOrderStartRespDTO();
        String orderNo = param.getOrderNo();
        //查询该车辆是否支持远程开关锁
        //获取订单
        GovPublicCarOrder order = govPublicCarOrderService.getOne(new LambdaQueryWrapper<GovPublicCarOrder>().eq(GovPublicCarOrder::getOrderNo, orderNo).last("limit 1"));
        VehicleDeviceRealtimeStatus vehicleDeviceRealtimeStatus = govPublicWarnFenceService.checkVehicleRealtimeStatus(order.getVehicleNo());        //根据车辆编码查询车辆信息
        GovVehicleBaseInfo vehicleBaseInfo = govVehicleBaseInfoService.getOne(new LambdaQueryWrapper<GovVehicleBaseInfo>().eq(GovVehicleBaseInfo::getVehicleNo, order.getVehicleNo()));
        //是否支持开关锁模式
        if (Objects.equals(vehicleBaseInfo.getRemoteLock(), GovPublicCarRemoteLockEnum.OPEN.getCode())) {
            //校验车辆状态
            if(ObjectUtil.isNotNull(vehicleDeviceRealtimeStatus.getStatusInfo())){
                //校验车门是否关闭
                StringBuilder msg = new StringBuilder();
                if (vehicleDeviceRealtimeStatus.getStatusInfo().getDoorStatus()==1) {
                    respDTO.setResult(Boolean.FALSE);
                    msg.append("车门未关闭,结束失败;");
                }
                //校验车灯是否关闭
                if(vehicleDeviceRealtimeStatus.getStatusInfo().getLampStatus()==1){
                    respDTO.setResult(Boolean.FALSE);
                    msg.append("车灯未关闭,结束失败;");
                }
                if(vehicleDeviceRealtimeStatus.getStatusInfo().getEngineStatus()==1){
                    respDTO.setResult(Boolean.FALSE);
                    msg.append("车机未关闭,结束失败;");
                }
                if (!respDTO.getResult()){
                    respDTO.setMessage(msg.toString());
                    return respDTO;
                }

            }
        }

        GovPublicCarOrderUserInfo primaryUserInfo = govPublicCarOrderUserInfoService.getOne(new LambdaQueryWrapper<GovPublicCarOrderUserInfo>().eq(GovPublicCarOrderUserInfo::getApplyNo, order.getApplyNo()).eq(GovPublicCarOrderUserInfo::getUserType, GovPublicCarOrderUserTypeEnum.PRIMARY_RIDER.getCode()).last("limit 1"));
        //检查是否开启还车围栏校验
        List<GovCompanyConfigItemValueDTO> configItemValueDTOS = govPublicBusinessConfigService.selectByDeptCodeAndBusinessCodeAndItemCode(order.getCompanyId(),primaryUserInfo.getDeptCode() ,
                PublicConfigEnum.BusinessConfigEnum.HOUR_RETURN_FENCE_CHECK.getCode(), PublicConfigEnum.BusinessConfigItemEnum.RETURN_FENCE_CHECK_ITEM.getCode());
        GovCompanyConfigItemValueDTO configItemValueDTO = configItemValueDTOS.get(0);
        // 转化为枚举值
        Integer timeShareCalculateConfig = Integer.parseInt(configItemValueDTO.getConfigValue());
        //开启围栏校验
        if(ObjectUtil.equals(timeShareCalculateConfig, PublicConfigValueEnum.ReturnFenceCheckEnum.INSIDE_THE_FENCE_RETURN_CAR.getValue())){
            if (Objects.nonNull(vehicleDeviceRealtimeStatus) && Objects.nonNull(vehicleDeviceRealtimeStatus.getDeviceStatus())) {
                if (Objects.nonNull(vehicleDeviceRealtimeStatus.getNearestFence()) && !vehicleDeviceRealtimeStatus.getInFence()) {
                    respDTO.setResult(Boolean.FALSE);
                    StringBuilder message = new StringBuilder("车辆不在围栏还车区域;结束失败;请您将车驾回围栏后结束行程;");
                    if(CollectionUtils.isNotEmpty(vehicleDeviceRealtimeStatus.getCandidateFences())){
                        vehicleDeviceRealtimeStatus.getCandidateFences().forEach(
                                item -> {
                                    message.append(item.getFenceName());
                                    if(StringUtils.isNotEmpty(item.getFenceAddress())){
                                        message.append(":").append(item.getFenceAddress()).append(";");
                                    }else{
                                        message.append(";");
                                    }
                                }
                        );

                    }
                    respDTO.setMessage(message.toString());
                    return respDTO;
                }
            }
        }
        //调用结束
        if (Objects.equals(order.getOrderStatus(), GovPublicCarOrderStatusEnum.IN_USE.getCode())) {
            forceEndOrder(Collections.singletonList(order), param, GovPublicCarOrderOperationTypeEnum.END_TRIP);
        }
        return respDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void forceEndOrder(OrderNoReqDTO param) {
        String orderNo = param.getOrderNo();
        //获取订单
        GovPublicCarOrder order = govPublicCarOrderService.getOne(new LambdaQueryWrapper<GovPublicCarOrder>().eq(GovPublicCarOrder::getOrderNo, orderNo).last("limit 1"));
        //调用结束
        if (Objects.equals(order.getOrderStatus(), GovPublicCarOrderStatusEnum.IN_USE.getCode())) {
            forceEndOrder(Collections.singletonList(order), param, GovPublicCarOrderOperationTypeEnum.FORCE_END_TRIP);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void verifyOrder(VerifyOrderReqDTO param) {
        //获取订单
        GovPublicCarOrder order = govPublicCarOrderService.getOne(new LambdaQueryWrapper<GovPublicCarOrder>().eq(GovPublicCarOrder::getOrderNo, param.getOrderNo()).last("limit 1"));
        if (ObjectUtil.isNull(order)) {
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "订单不存在");
        }
        if (!Objects.equals(order.getVerifyStatus(), GovPublicCarVerifyStatusEnum.UNVERIFIED.getCode())) {
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "该订单不需要进行核实");
        }
        if (EmojiUtils.containsEmoji(param.getOrderUserMemo())) {
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "备注中不允许包含表情");
        }
        //修改订单状态为已核实
        LambdaUpdateWrapper<GovPublicCarOrder> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(GovPublicCarOrder::getOrderNo, order.getOrderNo())
                .set(GovPublicCarOrder::getVerifyStatus, GovPublicCarVerifyStatusEnum.VERIFIED.getCode())
                .set(GovPublicCarOrder::getVerifierTime, new Date())
                .set(GovPublicCarOrder::getCarUseReason, param.getCarUseReason())
                .set(GovPublicCarOrder::getOrderUserMemo, param.getOrderUserMemo())
                .set(GovPublicCarOrder::getUpdateCode, param.getLoginUserCode())
                .set(GovPublicCarOrder::getUpdateName, param.getLoginUserName());
        govPublicCarOrderService.update(updateWrapper);
        //插入人员信息（核实人）（司机）（用车人）
        buildNoTaskUsers(param);
        //插入操作日志
        GovOrderOperationLog operationLog = new GovOrderOperationLog();
        operationLog.setOrderNo(order.getOrderNo());
        operationLog.setOperationType(GovPublicCarOrderOperationTypeEnum.CHECK_ORDER.getCode());
        operationLog.setOperationDescription(GovPublicCarOrderOperationTypeEnum.CHECK_ORDER.getName());
        operationLog.setOrderStatus(order.getOrderStatus());
        operationLog.setOrderStatusName(GovPublicCarOrderStatusEnum.getName(order.getOrderStatus()));
        operationLog.setOperatorCode(param.getLoginUserCode());
        operationLog.setOperatorName(param.getLoginUserName());
        operationLog.setOperatorMobile(param.getLoginUserMobile());
        govOrderOperationLogService.save(operationLog);
        // 报警编号
        //查询地址信息
        GovPublicCarOrderAddressInfo addressInfo = govPublicCarOrderAddressInfoService.getOne(new LambdaQueryWrapper<GovPublicCarOrderAddressInfo>().eq(GovPublicCarOrderAddressInfo::getOrderNo, order.getOrderNo()).last("limit 1"));
        String warnSn = addressInfo.getAlarmCode();
        GovCarWarnFence warnFence = new GovCarWarnFence();
        warnFence.setWarnSn(warnSn);
        warnFence.setDealId(param.getLoginUserId());
        warnFence.setDealName(param.getLoginUserName());
        warnFence.setDealType(GovWarnEnum.WarnDealTypeEnum.GOV_ORDER_NO_TASK_VERIFIED.getCode());
        warnFence.setDealExplain(param.getCarUseReason());
        warnFence.setDealTime(new Date());
        warnFence.setWarnStatus(GovWarnEnum.WarnStatusEnum.HANDLED.getCode());
        govCarWarnFenceService.updateDealStatusByWarnSn(warnFence);
        //更新节假日报警记录
        carWarnFenceHolidayService.updateHolidayFenceWarnVerifyOrder(warnSn,
                param.getLoginUserCode(),
                param.getLoginUserName(),
                param.getDriverInfo() == null ? "" : param.getDriverInfo().getUserCode(),
                param.getDriverInfo() == null ? "" : param.getDriverInfo().getUserName(),
                param.getDriverInfo() == null ? "" : param.getDriverInfo().getUserMobile());
    }

    @Override
    public void outFence(OfficialVehicleOutFenceMsgDTO param) {
        String vehicleVin = param.getVehicleVin();
        AddressResult addressResult = new AddressResult();
        try {
            addressResult = baiduMapApiClient.pointToAddress(param.getLatitude(), param.getLongitude());
        } catch (Exception e) {
            log.info("百度坐标转换地理位置失败，错误信息：{}", e.getMessage());
        }
        GovCityDic cityDic = new GovCityDic();
        if (ObjectUtil.isNotNull(addressResult)) {
            //根据百度的citycode转化为mrcar自己的城市code
            cityDic = govCityDicService.getOne(new LambdaQueryWrapper<GovCityDic>().eq(GovCityDic::getBaiduCode, addressResult.getCityCode()).last("limit 1"));
        }
        GovVehicleBaseInfo govVehicleBaseInfo = govVehicleBaseInfoService.getOne(new LambdaQueryWrapper<GovVehicleBaseInfo>().eq(GovVehicleBaseInfo::getVehicleVin, vehicleVin).last("limit 1"));
        if (govVehicleBaseInfo == null) {
            log.info("出栏：车辆信息未找到");
            return;
        }
        //查看是否有用车中的订单
        GovPublicCarOrder order = govPublicCarOrderService.getOne(new LambdaQueryWrapper<GovPublicCarOrder>().eq(GovPublicCarOrder::getVehicleVin, vehicleVin).eq(GovPublicCarOrder::getOrderStatus, GovPublicCarOrderStatusEnum.IN_USE.getCode()).last("limit 1"));
        if (ObjectUtil.isNotNull(order)) {
            //更新订单上的驶出围栏时间
            updateOrderTime(param, order);
        } else {
            //查看是否有存在待出发的订单（出栏时刻-4h,出栏时刻+4h）
            Date startTime = DateUtil.offset(param.getOutFenceTime(), DateField.HOUR_OF_DAY, -4);
            Date endTime = DateUtil.offset(param.getOutFenceTime(), DateField.HOUR_OF_DAY, 4);
            List<String> orderNoList = govPublicCarOrderMapper.selectPendingOrderByOutFence(startTime, endTime, vehicleVin);
            if (CollectionUtils.isEmpty(orderNoList) || orderNoList.size() > 1) {
                //生成无任务订单
                generateNoTaskOrder(param, govVehicleBaseInfo, cityDic);
            } else {
                //自动开始订单
                autoStartOrder(param, order, orderNoList, cityDic);
            }
        }
    }

    @Override
    public void inFence(OfficialVehicleInFenceMsgDTO param) {
        String vehicleVin = param.getVehicleVin();
        GovVehicleBaseInfo govVehicleBaseInfo = govVehicleBaseInfoService.getOne(new LambdaQueryWrapper<GovVehicleBaseInfo>().eq(GovVehicleBaseInfo::getVehicleVin, vehicleVin).last("limit 1"));
        if (govVehicleBaseInfo == null) {
            log.info("入栏：车辆信息未找到");
            return;
        }
        //查询用车中的订单
        GovPublicCarOrder order = govPublicCarOrderService.getOne(new LambdaQueryWrapper<GovPublicCarOrder>().eq(GovPublicCarOrder::getVehicleVin, vehicleVin).eq(GovPublicCarOrder::getOrderStatus, GovPublicCarOrderStatusEnum.IN_USE.getCode()).last("limit 1"));
        if (ObjectUtil.isNull(order)) {
            log.info("入栏：用车中的订单未找到");
            return;
        }
        AddressResult addressResult = new AddressResult();
        try {
            addressResult = baiduMapApiClient.pointToAddress(param.getLatitude(), param.getLongitude());
        } catch (Exception e) {
            log.info("百度坐标转换地理位置失败，错误信息：{}", e.getMessage());
        }
        GovCityDic cityDic = new GovCityDic();
        if (ObjectUtil.isNotNull(addressResult)) {
            //根据百度的citycode转化为mrcar自己的城市code
            cityDic = govCityDicService.getOne(new LambdaQueryWrapper<GovCityDic>().eq(GovCityDic::getBaiduCode, addressResult.getCityCode()).last("limit 1"));
        }
        Integer orderType = order.getOrderType();
        //无任务用车
        if (ObjectUtil.equals(orderType, GovPublicCarOrderTypeEnum.NO_TASK_USE_CAR_TYPE.getCode())) {
            //更新订单信息
            LambdaUpdateWrapper<GovPublicCarOrder> updateOrder = new LambdaUpdateWrapper<>();
            updateOrder.eq(GovPublicCarOrder::getOrderNo, order.getOrderNo())
                    .set(GovPublicCarOrder::getOrderStatus, GovPublicCarOrderStatusEnum.COMPLETED.getCode())
                    .set(GovPublicCarOrder::getOrderEndTime, param.getInFenceTime())
                    .set(GovPublicCarOrder::getReturnLotEntryTime, param.getInFenceTime());
            //无任务订单入栏的时候要去计算总里程
            CarTravelDistanceReqDTO carLocationTrailReqDTO = new CarTravelDistanceReqDTO();
            carLocationTrailReqDTO.setVehicleNo(order.getVehicleNo());
            carLocationTrailReqDTO.setCarNo(order.getVehicleLicense());
            if (StringUtils.isNotEmpty(param.getSimNo())) {
                carLocationTrailReqDTO.setSimNo(param.getSimNo());
            } else {
                carLocationTrailReqDTO.setSimNo(param.getDeviceNo());
            }
            carLocationTrailReqDTO.setDeviceNoList(Collections.singletonList(param.getDeviceNo()));
            carLocationTrailReqDTO.setBeginCreateDate(DateUtils.format(order.getOrderStartTime(), DateUtils.TIME_FORMAT));
            carLocationTrailReqDTO.setEndCreateDate(DateUtils.format(param.getInFenceTime(), DateUtils.TIME_FORMAT));
            try {
                Double distance = govDeviceMongoService.calculateTravelDistanceForCarTbox(carLocationTrailReqDTO);
                if (ObjectUtil.isNotNull(distance)) {
                    updateOrder.set(GovPublicCarOrder::getTotalMileage, new BigDecimal(distance))
                            .set(GovPublicCarOrder::getOutInFenceTotalMileage, new BigDecimal(distance));
                }
            } catch (Exception e) {
                log.error("公务用车-无任务用车-车辆进入围栏-计算里程失败，车架号：{}", vehicleVin, e);
            }
            govPublicCarOrderService.update(updateOrder);
            LambdaUpdateWrapper<GovPublicCarOrderAddressInfo> addressInfoLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            addressInfoLambdaUpdateWrapper.eq(GovPublicCarOrderAddressInfo::getOrderNo, order.getOrderNo())
                    .set(GovPublicCarOrderAddressInfo::getActualDestinationLatitude, param.getLatitude())
                    .set(GovPublicCarOrderAddressInfo::getActualDestinationLongitude, param.getLongitude())
                    .set(GovPublicCarOrderAddressInfo::getActualDestinationLongLocation, param.getAddress())
                    .set(GovPublicCarOrderAddressInfo::getActualDestinationShortLocation, param.getAddress())
                    .set(GovPublicCarOrderAddressInfo::getReturnFenceId, param.getFenceSnapId())
                    .set(GovPublicCarOrderAddressInfo::getReturnLatitude, param.getLatitude())
                    .set(GovPublicCarOrderAddressInfo::getReturnLongitude, param.getLongitude())
                    .set(GovPublicCarOrderAddressInfo::getReturnLocation, param.getAddress())
                    .set(GovPublicCarOrderAddressInfo::getAlarmCode, param.getWarnSn())
                    .set(ObjectUtil.isNotNull(cityDic), GovPublicCarOrderAddressInfo::getEndCityCode, ObjectUtil.isNotNull(cityDic) ? cityDic.getCityCode().toString() : "")
                    .set(ObjectUtil.isNotNull(cityDic), GovPublicCarOrderAddressInfo::getEndCityName, ObjectUtil.isNotNull(cityDic) ? cityDic.getCityName() : "");
            govPublicCarOrderAddressInfoService.update(addressInfoLambdaUpdateWrapper);
            //更新车辆上的设备信息
            LambdaUpdateWrapper<GovPublicCarOrderVehicleInfo> vehicleInfoLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            vehicleInfoLambdaUpdateWrapper.eq(GovPublicCarOrderVehicleInfo::getOrderNo, order.getOrderNo())
                    .set(GovPublicCarOrderVehicleInfo::getDeviceId, param.getDeviceNo())
                    .set(GovPublicCarOrderVehicleInfo::getDeviceType, param.getDeviceType());
            govPublicCarOrderVehicleInfoService.update(vehicleInfoLambdaUpdateWrapper);
            //插入开始行程日志
            GovOrderOperationLog orderOperationLogOut = new GovOrderOperationLog();
            orderOperationLogOut.setOrderNo(order.getOrderNo());
            orderOperationLogOut.setOperationType(GovPublicCarOrderOperationTypeEnum.IN_FENCE.getCode());
            orderOperationLogOut.setOperationDescription(GovPublicCarOrderOperationTypeEnum.IN_FENCE.getName());
            orderOperationLogOut.setOperationTime(param.getInFenceTime());
            orderOperationLogOut.setOrderStatus(order.getOrderStatus());
            orderOperationLogOut.setOrderStatusName(GovPublicCarOrderStatusEnum.getName(order.getOrderStatus()));
            orderOperationLogOut.setOperatorCode("");
            orderOperationLogOut.setOperatorName("System");
            orderOperationLogOut.setOperatorMobile("");
            govOrderOperationLogService.save(orderOperationLogOut);
            //插入开始行程日志
            GovOrderOperationLog endTripLog = new GovOrderOperationLog();
            endTripLog.setOrderNo(order.getOrderNo());
            endTripLog.setOperationType(GovPublicCarOrderOperationTypeEnum.END_TRIP.getCode());
            endTripLog.setOperationDescription(GovPublicCarOrderOperationTypeEnum.END_TRIP.getName());
            endTripLog.setOperationTime(param.getInFenceTime());
            endTripLog.setOrderStatus(GovPublicCarOrderStatusEnum.COMPLETED.getCode());
            endTripLog.setOrderStatusName(GovPublicCarOrderStatusEnum.COMPLETED.getName());
            endTripLog.setOperatorCode("");
            endTripLog.setOperatorName("System");
            endTripLog.setOperatorMobile("");
            govOrderOperationLogService.save(endTripLog);
            //释放车态
            LambdaUpdateWrapper<GovVehicleBaseInfo> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(GovVehicleBaseInfo::getVehicleNo, govVehicleBaseInfo.getVehicleNo());
            updateWrapper.set(GovVehicleBaseInfo::getWorkStatus, WorkStatusEnum.NO_TASK.getCode());
            govVehicleBaseInfoService.update(updateWrapper);
            //计算费用
            if (ObjectUtil.equals(order.getUseType(), GovPublicCarApplyTypeEnum.SOCIAL_RENT.getCode())) {
                calculateFee(order.getOrderNo());
            }
            //更新节假日报警的订单号
            carWarnFenceHolidayService.updateOrderNo(param.getWarnSn(), order.getOrderNo());
        } else {
            //更新订单信息
            LambdaUpdateWrapper<GovPublicCarOrder> updateOrder = new LambdaUpdateWrapper<>();
            updateOrder.eq(GovPublicCarOrder::getOrderNo, order.getOrderNo())
                    .set(GovPublicCarOrder::getReturnLotEntryTime, param.getInFenceTime());
            CarTravelDistanceReqDTO carLocationTrailReqDTO = new CarTravelDistanceReqDTO();
            carLocationTrailReqDTO.setVehicleNo(order.getVehicleNo());
            carLocationTrailReqDTO.setCarNo(order.getVehicleLicense());
            if (StringUtils.isNotEmpty(param.getSimNo())) {
                carLocationTrailReqDTO.setSimNo(param.getSimNo());
            } else {
                carLocationTrailReqDTO.setSimNo(param.getDeviceNo());
            }
            carLocationTrailReqDTO.setDeviceNoList(Collections.singletonList(param.getDeviceNo()));
            carLocationTrailReqDTO.setBeginCreateDate(DateUtils.format(order.getOrderStartTime(), DateUtils.TIME_FORMAT));
            carLocationTrailReqDTO.setEndCreateDate(DateUtils.format(param.getInFenceTime(), DateUtils.TIME_FORMAT));
            try {
                Double distance = govDeviceMongoService.calculateTravelDistanceForCarTbox(carLocationTrailReqDTO);
                if (ObjectUtil.isNotNull(distance)) {
                    updateOrder
                            .set(GovPublicCarOrder::getOutInFenceTotalMileage, new BigDecimal(distance));
                }
            } catch (Exception e) {
                log.error("公务用车-车辆进入围栏-计算里程失败，车架号：{}", vehicleVin, e);
            }
            govPublicCarOrderService.update(updateOrder);
            LambdaUpdateWrapper<GovPublicCarOrderAddressInfo> addressInfoLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            addressInfoLambdaUpdateWrapper.eq(GovPublicCarOrderAddressInfo::getOrderNo, order.getOrderNo())
                    .set(GovPublicCarOrderAddressInfo::getActualDestinationLatitude, param.getLatitude())
                    .set(GovPublicCarOrderAddressInfo::getActualDestinationLongitude, param.getLongitude())
                    .set(GovPublicCarOrderAddressInfo::getActualDestinationLongLocation, param.getAddress())
                    .set(GovPublicCarOrderAddressInfo::getActualDestinationShortLocation, param.getAddress())
                    .set(GovPublicCarOrderAddressInfo::getReturnFenceId, param.getFenceSnapId())
                    .set(GovPublicCarOrderAddressInfo::getReturnLatitude, param.getLatitude())
                    .set(GovPublicCarOrderAddressInfo::getReturnLongitude, param.getLongitude())
                    .set(GovPublicCarOrderAddressInfo::getReturnLocation, param.getAddress())
                    .set(GovPublicCarOrderAddressInfo::getAlarmCode, param.getWarnSn())
                    .set(ObjectUtil.isNotNull(cityDic), GovPublicCarOrderAddressInfo::getEndCityCode, ObjectUtil.isNotNull(cityDic) ? cityDic.getCityCode().toString() : "")
                    .set(ObjectUtil.isNotNull(cityDic), GovPublicCarOrderAddressInfo::getEndCityName, ObjectUtil.isNotNull(cityDic) ? cityDic.getCityName() : "");
            govPublicCarOrderAddressInfoService.update(addressInfoLambdaUpdateWrapper);
            //更新车辆上的设备信息
            LambdaUpdateWrapper<GovPublicCarOrderVehicleInfo> vehicleInfoLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            vehicleInfoLambdaUpdateWrapper.eq(GovPublicCarOrderVehicleInfo::getOrderNo, order.getOrderNo())
                    .set(GovPublicCarOrderVehicleInfo::getDeviceId, param.getDeviceNo())
                    .set(GovPublicCarOrderVehicleInfo::getDeviceType, param.getDeviceType());
            govPublicCarOrderVehicleInfoService.update(vehicleInfoLambdaUpdateWrapper);
            //插入开始行程日志
            GovOrderOperationLog orderOperationLogOut = new GovOrderOperationLog();
            orderOperationLogOut.setOrderNo(order.getOrderNo());
            orderOperationLogOut.setOperationType(GovPublicCarOrderOperationTypeEnum.IN_FENCE.getCode());
            orderOperationLogOut.setOperationDescription(GovPublicCarOrderOperationTypeEnum.IN_FENCE.getName());
            orderOperationLogOut.setOperationTime(param.getInFenceTime());
            orderOperationLogOut.setOrderStatus(order.getOrderStatus());
            orderOperationLogOut.setOrderStatusName(GovPublicCarOrderStatusEnum.getName(order.getOrderStatus()));
            orderOperationLogOut.setOperatorCode("");
            orderOperationLogOut.setOperatorName("System");
            orderOperationLogOut.setOperatorMobile("");
            govOrderOperationLogService.save(orderOperationLogOut);
        }
    }

    @Override
    public SocVehicleFeeDetailRespDTO getOrderFeeDetail(OrderNoReqDTO param) {
        SocVehicleFeeDetailRespDTO feeDetailRespDTO = new SocVehicleFeeDetailRespDTO();
        //根据订单号获取费用主表
        GovSocVehicleFee govVehicleFee = govSocVehicleFeeService.getOne(new LambdaQueryWrapper<GovSocVehicleFee>().eq(GovSocVehicleFee::getOrderNo, param.getOrderNo()));
        if (ObjectUtil.isNull(govVehicleFee)) {
            return feeDetailRespDTO;
        } else {
            feeDetailRespDTO.setRemark(govVehicleFee.getRemark());
            //查询费用明细,按费用code转化成map
            Map<String, GovSocVehicleFeeDetail> feeDetailMap = govSocVehicleFeeDetailService.list(new LambdaQueryWrapper<GovSocVehicleFeeDetail>().eq(GovSocVehicleFeeDetail::getOrderNo, param.getOrderNo()))
                    .stream()
                    .collect(Collectors.toMap(GovSocVehicleFeeDetail::getFeeCode, govSocVehicleFeeDetail -> govSocVehicleFeeDetail));
            feeDetailRespDTO.setRentFee(
                    feeDetailMap.get(GovPublicSocVehicleFeeItemEnum.RENT_FEE.getCode()) == null ?
                            BigDecimal.ZERO : feeDetailMap.get(GovPublicSocVehicleFeeItemEnum.RENT_FEE.getCode()).getFee());
            feeDetailRespDTO.setParkingFee(
                    feeDetailMap.get(GovPublicSocVehicleFeeItemEnum.PARKING_FEE.getCode()) == null ?
                            BigDecimal.ZERO : feeDetailMap.get(GovPublicSocVehicleFeeItemEnum.PARKING_FEE.getCode()).getFee()
            );
            feeDetailRespDTO.setPassRoadFee(
                    feeDetailMap.get(GovPublicSocVehicleFeeItemEnum.PASS_ROAD_FEE.getCode()) == null ?
                            BigDecimal.ZERO : feeDetailMap.get(GovPublicSocVehicleFeeItemEnum.PASS_ROAD_FEE.getCode()).getFee()
            );
            feeDetailRespDTO.setHotelFee(
                    feeDetailMap.get(GovPublicSocVehicleFeeItemEnum.HOTEL_FEE.getCode()) == null ?
                            BigDecimal.ZERO : feeDetailMap.get(GovPublicSocVehicleFeeItemEnum.HOTEL_FEE.getCode()).getFee()
            );
            feeDetailRespDTO.setMealsFee(
                    feeDetailMap.get(GovPublicSocVehicleFeeItemEnum.MEALS_FEE.getCode()) == null ?
                            BigDecimal.ZERO : feeDetailMap.get(GovPublicSocVehicleFeeItemEnum.MEALS_FEE.getCode()).getFee()
            );
            feeDetailRespDTO.setWashCarFee(
                    feeDetailMap.get(GovPublicSocVehicleFeeItemEnum.WASH_CAR_FEE.getCode()) == null ?
                            BigDecimal.ZERO : feeDetailMap.get(GovPublicSocVehicleFeeItemEnum.WASH_CAR_FEE.getCode()).getFee()
            );
            feeDetailRespDTO.setEtcFee(
                    feeDetailMap.get(GovPublicSocVehicleFeeItemEnum.ETC_FEE.getCode()) == null ?
                            BigDecimal.ZERO : feeDetailMap.get(GovPublicSocVehicleFeeItemEnum.ETC_FEE.getCode()).getFee()
            );
            feeDetailRespDTO.setOtherFee(
                    feeDetailMap.get(GovPublicSocVehicleFeeItemEnum.OTHER_FEE.getCode()) == null ?
                            BigDecimal.ZERO : feeDetailMap.get(GovPublicSocVehicleFeeItemEnum.OTHER_FEE.getCode()).getFee()
            );
            feeDetailRespDTO.setRemark(govVehicleFee.getRemark());
        }
        return feeDetailRespDTO;
    }

    @Override
    public void adjustFee(SocVehicleFeeDetailReqDTO param) {
        //查询费用主体
        GovSocVehicleFee govSocVehicleFee = govSocVehicleFeeService.getOne(new LambdaQueryWrapper<GovSocVehicleFee>().eq(GovSocVehicleFee::getOrderNo, param.getOrderNo()));
        //之前未生成费用主表（没有费用配置不保存）
        if (ObjectUtil.isNull(govSocVehicleFee)) {
            GovSocVehicleFee govSocVehicleFeeNew = new GovSocVehicleFee();
            govSocVehicleFeeNew.setOrderNo(param.getOrderNo());
            govSocVehicleFeeNew.setTotalFee(param.calculateTotalFee());
            //获取订单
            GovPublicCarOrder order = govPublicCarOrderService.getOne(new LambdaQueryWrapper<GovPublicCarOrder>().eq(GovPublicCarOrder::getOrderNo, param.getOrderNo()));
            govSocVehicleFeeNew.setFeeTime(order.getOrderEndTime());
            govSocVehicleFeeNew.setRemark(param.getRemark());
            govSocVehicleFeeNew.setCompanyId(param.getLoginCompanyId());
            govSocVehicleFeeNew.setCompanyName(param.getLoginCompanyName());
            govSocVehicleFeeNew.setCreateCode(param.getLoginUserCode());
            govSocVehicleFeeNew.setCreateName(param.getLoginUserName());
            govSocVehicleFeeNew.setUpdateCode(param.getLoginUserCode());
            govSocVehicleFeeNew.setUpdateName(param.getLoginUserName());
            govSocVehicleFeeService.save(govSocVehicleFeeNew);
        } else {
            //更新费用主表
            LambdaUpdateWrapper<GovSocVehicleFee> updateWrapper = new LambdaUpdateWrapper<GovSocVehicleFee>()
                    .eq(GovSocVehicleFee::getOrderNo, param.getOrderNo())
                    .set(GovSocVehicleFee::getTotalFee, param.calculateTotalFee())
                    .set(GovSocVehicleFee::getUpdateCode, param.getLoginUserCode())
                    .set(GovSocVehicleFee::getUpdateName, param.getLoginUserName())
                    .set(GovSocVehicleFee::getRemark, param.getRemark());
            govSocVehicleFeeService.update(updateWrapper);
            //删除相关明细费用
            govSocVehicleFeeDetailService.remove(new LambdaQueryWrapper<GovSocVehicleFeeDetail>().eq(GovSocVehicleFeeDetail::getOrderNo, param.getOrderNo()));
        }
        //组装并重新保存明细费用
        saveBatchDetails(param);
    }

    @Override
    public void autoCloseOrder(String orderNo) {
        //查询订单
        GovPublicCarOrder order = govPublicCarOrderService.getOne(new LambdaQueryWrapper<GovPublicCarOrder>().eq(GovPublicCarOrder::getOrderNo, orderNo));
        if (ObjectUtil.equal(order.getOrderStatus(), GovPublicCarOrderStatusEnum.IN_USE.getCode())) {
            Boolean isClose = checkOrderNeedClose(order);
            if(isClose){
                closeDuringTrip(order);
            }
        } else if (ObjectUtil.equal(order.getOrderStatus(), GovPublicCarOrderStatusEnum.PENDING_DEPARTURE.getCode())) {
            OrderNoReqDTO param = new OrderNoReqDTO();
            param.setOrderNo(orderNo);
            param.setLoginUserName("system");
            param.setLoginUserCode("0");
            param.setLoginUserMobile("");
            param.setLoginUserBelongDeptCode("");
            param.setLoginUserBelongDeptName("");
            param.setLoginUserBelongStructName("");
            param.setLoginUserBelongStructCode("");
            param.setLoginCompanyId(order.getCompanyId());
            param.setLoginCompanyName(order.getCompanyName());
            cancelOrder(param);
        }
    }

    private Boolean checkOrderNeedClose(GovPublicCarOrder order) {
        Boolean result = Boolean.TRUE;

        //查询该车辆是否支持远程开关锁
        VehicleDeviceRealtimeStatus vehicleDeviceRealtimeStatus = govPublicWarnFenceService.checkVehicleRealtimeStatus(order.getVehicleNo());        //根据车辆编码查询车辆信息
        GovVehicleBaseInfo vehicleBaseInfo = govVehicleBaseInfoService.getOne(new LambdaQueryWrapper<GovVehicleBaseInfo>().eq(GovVehicleBaseInfo::getVehicleNo, order.getVehicleNo()));
        //是否支持开关锁模式
        if (Objects.equals(vehicleBaseInfo.getRemoteLock(), GovPublicCarRemoteLockEnum.OPEN.getCode())) {
            //校验车辆状态
            if(ObjectUtil.isNotNull(vehicleDeviceRealtimeStatus.getStatusInfo())){
                //校验车门是否关闭
                if (vehicleDeviceRealtimeStatus.getStatusInfo().getDoorStatus()==1
                        ||vehicleDeviceRealtimeStatus.getStatusInfo().getLampStatus()==1
                        ||vehicleDeviceRealtimeStatus.getStatusInfo().getEngineStatus()==1) {
                    return Boolean.FALSE;
                }
            }
        }
        //检查是否开启还车围栏校验
        GovPublicCarOrderUserInfo primaryUserInfo = govPublicCarOrderUserInfoService.getOne(new LambdaQueryWrapper<GovPublicCarOrderUserInfo>().eq(GovPublicCarOrderUserInfo::getApplyNo, order.getApplyNo()).eq(GovPublicCarOrderUserInfo::getUserType, GovPublicCarOrderUserTypeEnum.PRIMARY_RIDER.getCode()).last("limit 1"));
        List<GovCompanyConfigItemValueDTO> configItemValueDTOS = govPublicBusinessConfigService.selectByDeptCodeAndBusinessCodeAndItemCode(order.getCompanyId(),primaryUserInfo.getDeptCode() ,
                PublicConfigEnum.BusinessConfigEnum.HOUR_RETURN_FENCE_CHECK.getCode(), PublicConfigEnum.BusinessConfigItemEnum.RETURN_FENCE_CHECK_ITEM.getCode());
        GovCompanyConfigItemValueDTO configItemValueDTO = configItemValueDTOS.get(0);
        // 转化为枚举值
        Integer timeShareCalculateConfig = Integer.parseInt(configItemValueDTO.getConfigValue());
        //开启围栏校验
        if(ObjectUtil.equals(timeShareCalculateConfig, PublicConfigValueEnum.ReturnFenceCheckEnum.INSIDE_THE_FENCE_RETURN_CAR.getValue())){
            if (Objects.nonNull(vehicleDeviceRealtimeStatus) && Objects.nonNull(vehicleDeviceRealtimeStatus.getDeviceStatus())) {
                if (Objects.nonNull(vehicleDeviceRealtimeStatus.getNearestFence()) && !vehicleDeviceRealtimeStatus.getInFence()) {
                    result = Boolean.FALSE;
                    log.info("车辆不在围栏内，不进行关单:{}", JacksonUtils.toJson(vehicleDeviceRealtimeStatus));
                }
            }
        }
        return result;
    }

    @Override
    public void insertTimeShareFee(GovPublicCarOrder order, GovPublicCarOrderVehicleInfo vehicleInfo, Integer drivingType, Integer source) {
        //查询分时计价快照
        GovTimeShareConfigSnapshot timeShareConfigSnapshot = govTimeShareConfigSnapshotService
                .lambdaQuery()
                .eq(GovTimeShareConfigSnapshot::getSnapshotCode, order.getTimeShareSnapshotCode())
                .one();

        List<TimeShareConfigDetailRespDTO> timeShareConfigDetailList = Lists.newArrayList();
        if (Objects.nonNull(timeShareConfigSnapshot) && StringUtils.isNotEmpty(timeShareConfigSnapshot.getSnapshotData())) {
            TimeShareConfigRespDTO timeShareConfigRespDTO = JSON.parseObject(timeShareConfigSnapshot.getSnapshotData(),
                    TimeShareConfigRespDTO.class);
            if (CollectionUtils.isNotEmpty(timeShareConfigRespDTO.getTimeShareConfigDetailRespDTOList())) {
                //筛选出驾驶模式下计费规则明细
                timeShareConfigDetailList =
                        timeShareConfigRespDTO.getTimeShareConfigDetailRespDTOList()
                                .stream()
                                .filter(r -> Objects.equals(r.getDrivingType(), drivingType))
                                .collect(Collectors.toList());
            }
        }
        Date orderStartTime = order.getOrderStartTime();
        Date orderEndTime = order.getOrderEndTime();

        //获取当天日期
        LocalDate today = Objects.equals(source, TimeShareSourceTypeEnum.FINISH_TRIP.getCode()) ? LocalDate.now() : LocalDate.now().minusDays(1);

        //出入栏日志
        ArrayList<Integer> fenceOperateList = Lists.newArrayList(GovPublicCarOrderOperationTypeEnum.IN_FENCE.getCode(), GovPublicCarOrderOperationTypeEnum.OUT_FENCE.getCode());


        //删除当天数据 防止重复
        List<GovSocTimeShareFee> timeShareFeeList = govSocTimeShareFeeService
                .lambdaQuery()
                .eq(GovSocTimeShareFee::getOrderNo, order.getOrderNo())
                .eq(GovSocTimeShareFee::getStatDate, today).list();
        if (CollectionUtils.isNotEmpty(timeShareFeeList)) {
            List<Integer> idList = timeShareFeeList.stream().map(GovSocTimeShareFee::getId).collect(Collectors.toList());
            govSocTimeShareFeeService.removeByIds(idList);
        }

        //配置使用出入栏 并且出入栏时间不为空(兼容强制结束 出入栏时间为空)
        if (Objects.equals(order.getTimeShareCalculateConfig(), 1)) {
            List<GovOrderOperationLog> operationLogList = govOrderOperationLogService
                    .lambdaQuery()
                    .eq(GovOrderOperationLog::getOrderNo, order.getOrderNo())
                    .in(GovOrderOperationLog::getOperationType, fenceOperateList)
                    .orderByAsc(GovOrderOperationLog::getOperationTime)
                    .list();

            if (CollectionUtils.isEmpty(operationLogList)) {
                return;
            }

            //当天日志
            List<GovOrderOperationLog> todayOperationLogList = operationLogList
                    .stream()
                    .filter(r -> Objects.equals(r.getOperationTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate(), today))
                    .collect(Collectors.toList());


            //全部日志最后一条
            GovOrderOperationLog lastLog = operationLogList.get(operationLogList.size() - 1);

            List<List<LocalDateTime>> groupLogs = Lists.newArrayList();


            if (CollectionUtils.isEmpty(todayOperationLogList)) {
                if (Objects.equals(lastLog.getOperationType(), GovPublicCarOrderOperationTypeEnum.IN_FENCE.getCode())) {
                    return;
                }
                if (Objects.equals(source, TimeShareSourceTypeEnum.FINISH_TRIP.getCode())) {
                    List<LocalDateTime> list = Lists.newArrayList();
                    list.add(LocalDateTime.of(today, LocalTime.MIN));
                    list.add(orderEndTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
                    groupLogs.add(list);
                } else {
                    List<LocalDateTime> list = Lists.newArrayList();
                    list.add(LocalDateTime.of(today, LocalTime.MIN));
                    list.add(LocalDateTime.of(today.plusDays(1), LocalTime.MIN));
                    groupLogs.add(list);
                }
                List<TimeShareConfigDetailRespDTO> finalTimeShareConfigs = timeShareConfigDetailList;
                groupLogs.forEach(r -> buildTimeShareFee(order, vehicleInfo, r.get(0), r.get(1), today, finalTimeShareConfigs));
                return;
            }

            GovOrderOperationLog todayFirstLog = todayOperationLogList.get(0);
            GovOrderOperationLog todayLastLog = todayOperationLogList.get(todayOperationLogList.size() - 1);

            int i = 0;
            while (i < todayOperationLogList.size()) {
                // 第一条驶入围栏（如果当前是第一条）
                if (i == 0 && Objects.equals(todayOperationLogList.get(i).getOperationType(), GovPublicCarOrderOperationTypeEnum.IN_FENCE.getCode())) {
                    i++;
                    List<LocalDateTime> list = Lists.newArrayList();
                    list.add(LocalDateTime.of(today, LocalTime.MIN));
                    list.add(todayFirstLog.getOperationTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
                    groupLogs.add(list);
                    continue;
                }

                // 最后一条使出围栏（如果当前是最后一条）
                if (i == todayOperationLogList.size() - 1 && Objects.equals(todayOperationLogList.get(i).getOperationType(), GovPublicCarOrderOperationTypeEnum.OUT_FENCE.getCode())) {
                    i += 2;
                    List<LocalDateTime> list = Lists.newArrayList();
                    list.add(todayLastLog.getOperationTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
                    if (Objects.equals(source, TimeShareSourceTypeEnum.JOB.getCode())) {
                        list.add(LocalDateTime.of(today.plusDays(1), LocalTime.MIN));
                    } else {
                        //强制结束 取实际结束实际
                        list.add(orderEndTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
                    }
                    groupLogs.add(list);
                    continue;
                }

                GovOrderOperationLog log = todayOperationLogList.get(i);
                GovOrderOperationLog nextLog = i + 1 < todayOperationLogList.size() ? todayOperationLogList.get(i + 1) : null;

                // 情况1：连续两个驶出（OUT_FENCE），保留第一个驶出 + 下一个驶入（IN_FENCE）
                if (nextLog != null &&
                        Objects.equals(log.getOperationType(), GovPublicCarOrderOperationTypeEnum.OUT_FENCE.getCode()) &&
                        Objects.equals(nextLog.getOperationType(), GovPublicCarOrderOperationTypeEnum.OUT_FENCE.getCode())) {

                    // 查找下一个驶入操作
                    int j = i + 1;
                    while (j < todayOperationLogList.size() &&
                            Objects.equals(todayOperationLogList.get(j).getOperationType(), GovPublicCarOrderOperationTypeEnum.OUT_FENCE.getCode())) {
                        j++;
                    }

                    // 如果找到驶入操作
                    if (j < todayOperationLogList.size() &&
                            Objects.equals(todayOperationLogList.get(j).getOperationType(), GovPublicCarOrderOperationTypeEnum.IN_FENCE.getCode())) {

                        groupLogs.add(Arrays.asList(log.getOperationTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime(),
                                todayOperationLogList.get(j).getOperationTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime()));
                        i = j + 1;
                    } else {
                        i++;
                    }
                }
                // 情况2：正常情况（OUT_FENCE + IN_FENCE 或 IN_FENCE + OUT_FENCE）
                else if (nextLog != null &&
                        ((Objects.equals(log.getOperationType(), GovPublicCarOrderOperationTypeEnum.OUT_FENCE.getCode()) &&
                                Objects.equals(nextLog.getOperationType(), GovPublicCarOrderOperationTypeEnum.IN_FENCE.getCode())))) {

                    groupLogs.add(Arrays.asList(log.getOperationTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime(),
                            nextLog.getOperationTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime()));
                    i += 2;
                } else {
                    i++;
                }
            }

            //遍历出入栏分组
            List<TimeShareConfigDetailRespDTO> finalTimeShareConfigDetailList = timeShareConfigDetailList;
            groupLogs.forEach(r -> buildTimeShareFee(order, vehicleInfo, r.get(0), r.get(1), today, finalTimeShareConfigDetailList));
        } else {
            //将开始时间转为LocalTime
            LocalDateTime startLocalDateTime = orderStartTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
            //将结束时间转为LocalTime
            LocalDateTime endLocalDateTime = Objects.equals(source, TimeShareSourceTypeEnum.FINISH_TRIP.getCode()) ?
                    orderEndTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime() :
                    LocalDateTime.of(today.plusDays(1), LocalTime.MIN);
            //如果开始时间不是今天，说明定时任务跑过了，所以只需要处理当天时间
            if (!startLocalDateTime.toLocalDate().equals(today)) {
                startLocalDateTime = LocalDateTime.of(today, LocalTime.MIN);
            }
            //分时计费
            buildTimeShareFee(order, vehicleInfo, startLocalDateTime, endLocalDateTime, today, timeShareConfigDetailList);
        }
    }


    private void buildTimeShareFee(GovPublicCarOrder order,
                                   GovPublicCarOrderVehicleInfo vehicleInfo,
                                   LocalDateTime startLocalDateTime,
                                   LocalDateTime endLocalDateTime,
                                   LocalDate today,
                                   List<TimeShareConfigDetailRespDTO> timeShareConfigDetailList) {
        //将实际开始和结束转为range
        Range<LocalDateTime> real = Range.closed(startLocalDateTime,
                endLocalDateTime);

        //计价策略为空 也需要生成分时费用 金额为0
        if (CollectionUtils.isEmpty(timeShareConfigDetailList)) {
            //保存计价费用
            saveTimeShareFee(order, vehicleInfo, startLocalDateTime, endLocalDateTime, today, null);
            return;
        }

        //遍历计价配置
        timeShareConfigDetailList.forEach(o -> {
            Range<LocalDateTime> range;

            //如果结束时间是00:00:00，则结束时间为第二天的00:00:00
            if ("00:00:00".equals(o.getEndTime())) {
                range = Range.closedOpen(LocalDateTime.of(today, LocalTime.parse(o.getStartTime())),
                        LocalDateTime.of(today.plusDays(1), LocalTime.parse(o.getEndTime())));
            } else {
                range = Range.closedOpen(LocalDateTime.of(today, LocalTime.parse(o.getStartTime())),
                        LocalDateTime.of(today, LocalTime.parse(o.getEndTime())));
            }

            //取交集
            if (range.isConnected(real)) {
                Range<LocalDateTime> intersection = real.intersection(range);
                if (!intersection.isEmpty()) {
                    //区间开始
                    LocalDateTime periodStartTime = intersection.lowerEndpoint();
                    //区间结束
                    LocalDateTime periodEndTime = intersection.upperEndpoint();
                    //保存计价费用
                    saveTimeShareFee(order, vehicleInfo, periodStartTime, periodEndTime, today, o);
                }
            }
        });
    }


    private void saveTimeShareFee(GovPublicCarOrder order,
                                  GovPublicCarOrderVehicleInfo vehicleInfo,
                                  LocalDateTime periodStartTime,
                                  LocalDateTime periodEndTime,
                                  LocalDate today,
                                  TimeShareConfigDetailRespDTO timeShareConfig) {

        // 向上取整
        long roundedMinutes = DateUtils.calculateRoundedMinutes(periodStartTime, periodEndTime);

        //格式化区间实际时间
        Date start = Date.from(periodStartTime.atZone(ZoneId.systemDefault()).toInstant());
        Date end = Date.from(periodEndTime.atZone(ZoneId.systemDefault()).toInstant());

        GovSocTimeShareFee timeShareFee = new GovSocTimeShareFee();
        timeShareFee.setOrderNo(order.getOrderNo());
        timeShareFee.setVehicleNo(order.getVehicleNo());
        timeShareFee.setVehicleLicense(order.getVehicleLicense());
        timeShareFee.setVehicleVin(order.getVehicleVin());
        timeShareFee.setSupplierCode(vehicleInfo.getSupplierCode());
        timeShareFee.setSupplierName(vehicleInfo.getSupplierName());
        timeShareFee.setStartTimePeriod(Objects.isNull(timeShareConfig) ? "" : timeShareConfig.getStartTime());
        timeShareFee.setEndTimePeriod(Objects.isNull(timeShareConfig) ? "" : timeShareConfig.getEndTime());
        timeShareFee.setStatDate(Date.from(today.atStartOfDay(ZoneId.systemDefault()).toInstant()));
        timeShareFee.setUseStartTime(start);
        timeShareFee.setUseEndTime(end);
        timeShareFee.setDeptCode(vehicleInfo.getVehicleBelongDeptCode());
        timeShareFee.setDeptName(vehicleInfo.getVehicleBelongDeptName());
        timeShareFee.setCompanyId(order.getCompanyId());
        timeShareFee.setCompanyName(order.getCompanyName());
        timeShareFee.setDuration((int) roundedMinutes);
        timeShareFee.setStatDate(Date.from(today.atStartOfDay(ZoneId.systemDefault()).toInstant()));
        BigDecimal totalMileage = BigDecimal.ZERO;
        try {
            DeviceHistoryTraceDTO deviceHistory = govCarInfoService.queryHistoryTrace(order.getVehicleNo(),
                    order.getVehicleLicense(),
                    order.getVehicleVin(),
                    vehicleInfo.getDeviceId(),
                    DateUtils.format(start, DateUtils.TIME_FORMAT),
                    DateUtils.format(end, DateUtils.TIME_FORMAT));

            if (Objects.nonNull(deviceHistory)) {
                //千米
                totalMileage = deviceHistory.getDistance();
            }
        } catch (Exception e) {
            log.error("计算区间里程失败,订单号:{}", order.getOrderNo(), e);
        }
        timeShareFee.setMileage(totalMileage);
        //里程x里程租金 + 时长x时长租金
        if (Objects.nonNull(timeShareConfig)) {
            timeShareFee.setRent((totalMileage.multiply(timeShareConfig.getMileageRent())).add(new BigDecimal(roundedMinutes).multiply(timeShareConfig.getDurationRent())));
        }
        govSocTimeShareFeeService.save(timeShareFee);
    }


    @Override
    public void timeShareFeeGenerate(TimeShareGenerateReqDTO req) {

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate today = LocalDate.parse(req.getStatDate(), formatter);

        //查询订单
        GovPublicCarOrder order = govPublicCarOrderService.lambdaQuery().eq(GovPublicCarOrder::getOrderNo, req.getOrderNo()).one();
        if (Objects.isNull(order)) {
            return;
        }
        if (StringUtils.isBlank(order.getTimeShareSnapshotCode())) {
            return;
        }

        //查询分时计价快照
        GovTimeShareConfigSnapshot timeShareConfigSnapshot = govTimeShareConfigSnapshotService
                .lambdaQuery()
                .eq(GovTimeShareConfigSnapshot::getSnapshotCode, order.getTimeShareSnapshotCode())
                .one();

        List<TimeShareConfigDetailRespDTO> timeShareConfigDetailList = Lists.newArrayList();

        //无任务用车默认自驾
        Integer drivingType = GovPublicCarDrivingTypeEnum.SELF_DRIVE.getCode();
        GovPublicCarApply apply = null;
        if (StringUtils.isNotBlank(order.getApplyNo())) {
            apply = govPublicCarApplyService.lambdaQuery().eq(GovPublicCarApply::getApplyNo, order.getApplyNo()).one();
        }

        GovPublicCarOrderVehicleInfo vehicleInfo = govPublicCarOrderVehicleInfoService.lambdaQuery().eq(GovPublicCarOrderVehicleInfo::getOrderNo, order.getOrderNo()).one();

        if (Objects.isNull(vehicleInfo)) {
            return;
        }

        if (Objects.nonNull(apply)) {
            if (Objects.equals(apply.getRentType(), GovPublicCarRentTypeEnum.DAILY.getCode())) {
                return;
            }
            drivingType = apply.getDrivingType();
            if(ObjectUtil.equals(drivingType, GovPublicCarDrivingTypeEnum.UNIT_DRIVE.getCode())){
                drivingType = GovPublicCarDrivingTypeEnum.SELF_DRIVE.getCode();
            }
        }

        if (Objects.nonNull(timeShareConfigSnapshot) && StringUtils.isNotEmpty(timeShareConfigSnapshot.getSnapshotData())) {
            TimeShareConfigRespDTO timeShareConfigRespDTO = JSON.parseObject(timeShareConfigSnapshot.getSnapshotData(),
                    TimeShareConfigRespDTO.class);
            if (CollectionUtils.isNotEmpty(timeShareConfigRespDTO.getTimeShareConfigDetailRespDTOList())) {
                //筛选出驾驶模式下计费规则明细
                Integer finalDrivingType = drivingType;
                timeShareConfigDetailList =
                        timeShareConfigRespDTO.getTimeShareConfigDetailRespDTOList()
                                .stream()
                                .filter(r -> Objects.equals(r.getDrivingType(), finalDrivingType))
                                .collect(Collectors.toList());
            }
        }

        Date orderStartTime = order.getOrderStartTime();

        //出入栏日志
        ArrayList<Integer> fenceOperateList = Lists.newArrayList(GovPublicCarOrderOperationTypeEnum.IN_FENCE.getCode(), GovPublicCarOrderOperationTypeEnum.OUT_FENCE.getCode());


        //删除当天数据 防止重复
        List<GovSocTimeShareFee> timeShareFeeList = govSocTimeShareFeeService
                .lambdaQuery()
                .eq(GovSocTimeShareFee::getOrderNo, order.getOrderNo())
                .eq(GovSocTimeShareFee::getStatDate, today).list();
        if (CollectionUtils.isNotEmpty(timeShareFeeList)) {
            List<Integer> idList = timeShareFeeList.stream().map(GovSocTimeShareFee::getId).collect(Collectors.toList());
            govSocTimeShareFeeService.removeByIds(idList);
        }

        //配置使用出入栏 并且出入栏时间不为空(兼容强制结束 出入栏时间为空)
        if (Objects.equals(order.getTimeShareCalculateConfig(), 1)) {
            List<GovOrderOperationLog> operationLogList = govOrderOperationLogService
                    .lambdaQuery()
                    .eq(GovOrderOperationLog::getOrderNo, order.getOrderNo())
                    .in(GovOrderOperationLog::getOperationType, fenceOperateList)
                    .orderByAsc(GovOrderOperationLog::getOperationTime)
                    .list();

            if (CollectionUtils.isEmpty(operationLogList)) {
                return;
            }

            //当天日志
            List<GovOrderOperationLog> todayOperationLogList = operationLogList
                    .stream()
                    .filter(r -> Objects.equals(r.getOperationTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate(), today))
                    .collect(Collectors.toList());


            //全部日志最后一条
            GovOrderOperationLog lastLog = operationLogList.get(operationLogList.size() - 1);

            List<List<LocalDateTime>> groupLogs = Lists.newArrayList();


            if (CollectionUtils.isEmpty(todayOperationLogList)) {
                if (Objects.equals(lastLog.getOperationType(), GovPublicCarOrderOperationTypeEnum.IN_FENCE.getCode())) {
                    return;
                }
                List<LocalDateTime> list = Lists.newArrayList();
                list.add(LocalDateTime.of(today, LocalTime.MIN));
                list.add(LocalDateTime.of(today.plusDays(1), LocalTime.MIN));
                groupLogs.add(list);
                List<TimeShareConfigDetailRespDTO> finalTimeShareConfigs = timeShareConfigDetailList;
                groupLogs.forEach(r -> buildTimeShareFee(order, vehicleInfo, r.get(0), r.get(1), today, finalTimeShareConfigs));
                return;
            }

            GovOrderOperationLog todayFirstLog = todayOperationLogList.get(0);
            GovOrderOperationLog todayLastLog = todayOperationLogList.get(todayOperationLogList.size() - 1);

            int i = 0;
            while (i < todayOperationLogList.size()) {
                // 第一条驶入围栏（如果当前是第一条）
                if (i == 0 && Objects.equals(todayOperationLogList.get(i).getOperationType(), GovPublicCarOrderOperationTypeEnum.IN_FENCE.getCode())) {
                    i++;
                    List<LocalDateTime> list = Lists.newArrayList();
                    list.add(LocalDateTime.of(today, LocalTime.MIN));
                    list.add(todayFirstLog.getOperationTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
                    groupLogs.add(list);
                    continue;
                }

                // 最后一条使出围栏（如果当前是最后一条）
                if (i == todayOperationLogList.size() - 1 && Objects.equals(todayOperationLogList.get(i).getOperationType(), GovPublicCarOrderOperationTypeEnum.OUT_FENCE.getCode())) {
                    i += 2;
                    List<LocalDateTime> list = Lists.newArrayList();
                    list.add(todayLastLog.getOperationTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
                    list.add(LocalDateTime.of(today.plusDays(1), LocalTime.MIN));
                    groupLogs.add(list);
                    continue;
                }

                GovOrderOperationLog log = todayOperationLogList.get(i);
                GovOrderOperationLog secondLog = todayOperationLogList.get(i + 1);

                // 检查是否是 "驶入围栏" 和 "使出围栏" 的组合
                if ((Objects.equals(log.getOperationType(), GovPublicCarOrderOperationTypeEnum.OUT_FENCE.getCode()) && Objects.equals(secondLog.getOperationType(), GovPublicCarOrderOperationTypeEnum.IN_FENCE.getCode()))) {
                    groupLogs.add(Arrays.asList(log.getOperationTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime(),
                            secondLog.getOperationTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime()));
                    i += 2;
                } else {
                    i++;
                }
            }

            //遍历出入栏分组
            List<TimeShareConfigDetailRespDTO> finalTimeShareConfigDetailList = timeShareConfigDetailList;
            groupLogs.forEach(r -> buildTimeShareFee(order, vehicleInfo, r.get(0), r.get(1), today, finalTimeShareConfigDetailList));
        } else {
            //将开始时间转为LocalTime
            LocalDateTime startLocalDateTime = orderStartTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
            //将结束时间转为LocalTime
            LocalDateTime endLocalDateTime = LocalDateTime.of(today.plusDays(1), LocalTime.MIN);
            //如果开始时间不是今天，说明定时任务跑过了，所以只需要处理当天时间
            if (!startLocalDateTime.toLocalDate().equals(today)) {
                startLocalDateTime = LocalDateTime.of(today, LocalTime.MIN);
            }
            //分时计费
            buildTimeShareFee(order, vehicleInfo, startLocalDateTime, endLocalDateTime, today, timeShareConfigDetailList);
        }
    }

    @Override
    public DeviceAndOrderInfoDTO queryBluetoothInfo(OrderNoReqDTO reqDTO) {
        DeviceAndOrderInfoDTO deviceAndOrderInfoDTO = new DeviceAndOrderInfoDTO();
        //查询订单的车辆信息
        GovPublicCarOrderVehicleInfo vehicleInfo = govPublicCarOrderVehicleInfoService.getOne(new LambdaQueryWrapper<GovPublicCarOrderVehicleInfo>()
                .eq(GovPublicCarOrderVehicleInfo::getOrderNo, reqDTO.getOrderNo()).last("limit 1"));
        String vehicleNo = vehicleInfo.getVehicleNo();
        //查询车辆是否支持远程开关锁
        GovVehicleBaseInfo vehicleBaseInfo = govVehicleBaseInfoService.getOne(new LambdaQueryWrapper<GovVehicleBaseInfo>().eq(GovVehicleBaseInfo::getVehicleNo, vehicleNo));
        if (ObjectUtil.isNull(vehicleBaseInfo)||vehicleBaseInfo.getRemoteLock().equals(GovPublicCarRemoteLockEnum.LOCK.getCode())) {
            return deviceAndOrderInfoDTO;
        }
        //查询订单状态展示开关锁按钮
        GovPublicCarOrder orderInfo = govPublicCarOrderService.getOne(new LambdaQueryWrapper<GovPublicCarOrder>().eq(GovPublicCarOrder::getOrderNo, reqDTO.getOrderNo()).last("limit 1"));
        //行程中才展示开关锁按钮
        if(orderInfo.getOrderStatus().equals(GovPublicCarOrderStatusEnum.IN_USE.getCode())){
            deviceAndOrderInfoDTO.setOpenLock(Boolean.TRUE);
            deviceAndOrderInfoDTO.setCloseLock(Boolean.TRUE);
        }
        //查询车辆绑定设备
        GovGpsVehicleRelation deviceRelation = govGpsVehicleRelationService.getOne(new LambdaQueryWrapper<GovGpsVehicleRelation>()
                .eq(GovGpsVehicleRelation::getVehicleNo, vehicleNo)
                .eq(GovGpsVehicleRelation::getBindStatus, GovGpsDeviceEnum.DeviceBindStatusEnum.BIND.getCode())
                .eq(GovGpsVehicleRelation::getDeviceType, GovGpsDeviceEnum.DeviceTypeEnum.TBOX.getCode()).last("limit 1"));
        //未查询到车机设备
        if (ObjectUtil.isNull(deviceRelation)) {
            deviceAndOrderInfoDTO.setOpenLock(Boolean.FALSE);
            deviceAndOrderInfoDTO.setCloseLock(Boolean.FALSE);
            return deviceAndOrderInfoDTO;
        }

        // SIM卡号
        String simNo = deviceRelation.getSimNo();
        //查询设备信息
        LambdaQueryWrapper<GovGpsDevice> deviceWrapper = new LambdaQueryWrapper<GovGpsDevice>()
                .eq(GovGpsDevice::getSimNo, simNo)
                .eq(GovGpsDevice::getDeleteStatus, GovGpsDeviceEnum.DeviceDeleteStatusEnum.NORMAL.getCode())
                .last("limit 1");
        GovGpsDevice device = govGpsDeviceService.getOne(deviceWrapper);
        if (ObjectUtil.isNull(device)) {
            deviceAndOrderInfoDTO.setOpenLock(Boolean.FALSE);
            deviceAndOrderInfoDTO.setCloseLock(Boolean.FALSE);
            return deviceAndOrderInfoDTO;
        }
        deviceAndOrderInfoDTO.setSimNo(simNo);
        //查询该设备的型号是否支持蓝牙
        GovGpsModel govGpsModel = govGpsModelService.getOne(new LambdaQueryWrapper<GovGpsModel>().eq(GovGpsModel::getId, device.getModelId()).last("limit 1"));
        //为空或者不支持蓝牙，则直接返回
        if (ObjectUtil.isNull(govGpsModel)||govGpsModel.getSupportBluetooth().equals(0)) {
            return deviceAndOrderInfoDTO;
        }
        //不支持蓝牙 有蓝牙地址，并且modelId=31即车机类型为gofan车机的
        if(StringUtils.isEmpty(device.getBlueToothMac())){
            return deviceAndOrderInfoDTO;
        }
        deviceAndOrderInfoDTO.setBluetooth(Boolean.TRUE);
        //构建蓝牙信息
        DeviceBluetoothConnectionInfoDTO result = buildBlueToothInfo(device, simNo);
        deviceAndOrderInfoDTO.setDeviceBluetoothConnectionInfoDTO(result);
        return deviceAndOrderInfoDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OpenOrCloseInfoDTO openOrCloseLock(LockAndOrderReq reqDTO) {
        OpenOrCloseInfoDTO openOrCloseInfoDTO = new OpenOrCloseInfoDTO();
        //判断当前操作类型 是网络开关锁还是蓝牙开关锁
        GovPublicCarLockTypeEnum enumByCode = GovPublicCarLockTypeEnum.getEnumByCode(reqDTO.getLockType());
        switch (enumByCode)
                {
                    //网络开关锁
                    case NETWORK_LOCK:
                        doOpenOrCloseLockByNetwork(reqDTO,openOrCloseInfoDTO);
                        break;
                    //蓝牙开关锁(这块不执行调接口，只做日志记录)
                    case BLUE_LOCK:
                        doOpenOrCloseLockByBlue(reqDTO,openOrCloseInfoDTO);
                        break;
                    default:
                        break;
                }
        return openOrCloseInfoDTO;
    }

    private void doOpenOrCloseLockByBlue(LockAndOrderReq reqDTO, OpenOrCloseInfoDTO openOrCloseInfoDTO) {
        //插入订单日志
        GovOrderOperationLog operationLog = new GovOrderOperationLog();
        operationLog.setOrderNo(reqDTO.getOrderNo());
        if(reqDTO.getOperateType().equals(GovPublicCarOperateTypeEnum.OPEN_LOCK.getCode())){
            operationLog.setOperationType(GovPublicCarOrderOperationTypeEnum.OPEN_LOCK.getCode());
            operationLog.setOperationDescription(GovPublicCarOrderOperationTypeEnum.OPEN_LOCK.getName());
        }else if(reqDTO.getOperateType().equals(GovPublicCarOperateTypeEnum.CLOSE_LOCK.getCode())){
            operationLog.setOperationType(GovPublicCarOrderOperationTypeEnum.CLOSE_LOCK.getCode());
            operationLog.setOperationDescription(GovPublicCarOrderOperationTypeEnum.CLOSE_LOCK.getName());
        }
        operationLog.setOrderStatus(GovPublicCarOrderStatusEnum.IN_USE.getCode());
        operationLog.setOrderStatusName(GovPublicCarOrderStatusEnum.IN_USE.getName());
        operationLog.setOperatorCode(reqDTO.getLoginUserCode());
        operationLog.setOperatorName(reqDTO.getLoginUserName());
        operationLog.setOperatorMobile(reqDTO.getLoginUserMobile());
        govOrderOperationLogService.save(operationLog);
        //插入指令下发记录
        //查询订单车辆相关信息
        GovPublicCarOrderVehicleInfo vehicleInfo = govPublicCarOrderVehicleInfoService.getOne(new LambdaQueryWrapper<GovPublicCarOrderVehicleInfo>().eq(GovPublicCarOrderVehicleInfo::getOrderNo, reqDTO.getOrderNo()).last("limit 1"));
        //根据sim查询车机信息
        GovGpsDevice govGpsDevice = govGpsDeviceService.getOne(new LambdaQueryWrapper<GovGpsDevice>()
                .eq(GovGpsDevice::getSimNo, reqDTO.getSimNo())
                .eq(GovGpsDevice::getDeleteStatus, GovGpsDeviceEnum.DeviceDeleteStatusEnum.NORMAL.getCode())
                .last("limit 1"));
        GovPublicCarCommandOperateLog commandOperateLog = new GovPublicCarCommandOperateLog();
        commandOperateLog.setBusinessNo(reqDTO.getOrderNo());
        String commandNo = sequenceGenerator.generate(new Date(), PUBLIC_CAR_COMMAND_PREFIX);
        commandOperateLog.setCommandNo(commandNo);
        commandOperateLog.setVehicleNo(vehicleInfo.getVehicleNo());
        commandOperateLog.setVehicleLicense(vehicleInfo.getVehicleLicense());
        commandOperateLog.setVehicleVin(vehicleInfo.getVehicleVin());
        commandOperateLog.setVehicleSeriesId(vehicleInfo.getVehicleSeriesId());
        commandOperateLog.setVehicleSeriesName(vehicleInfo.getVehicleSeriesName());
        commandOperateLog.setSimNo(govGpsDevice.getSimNo());
        commandOperateLog.setDeviceNo(govGpsDevice.getDeviceNo());
        commandOperateLog.setManufactId(govGpsDevice.getManufactId());
        commandOperateLog.setManufactName(govGpsDevice.getManufactName());
        commandOperateLog.setModelId(govGpsDevice.getModelId());
        commandOperateLog.setModelName(govGpsDevice.getModelName());
        if(GovPublicCarOperateTypeEnum.OPEN_LOCK.getCode().equals(reqDTO.getOperateType())){
            commandOperateLog.setCommandType(GovPublicCarCommandTypeEnum.UNLOCK.getCode());
        } else if (GovPublicCarOperateTypeEnum.CLOSE_LOCK.getCode().equals(reqDTO.getOperateType())) {
            commandOperateLog.setCommandType(GovPublicCarCommandTypeEnum.LOCK.getCode());
        }
        commandOperateLog.setCommandDispatchTime(reqDTO.getCommandDispatchTime());
        commandOperateLog.setCommandCompleteTime(reqDTO.getCommandCompleteTime());
        if(reqDTO.getIsSuccess()){
            commandOperateLog.setCommandResult(GovPublicCarCommandResultEnum.SUCCESS.getCode());
            //构建返回信息
            openOrCloseInfoDTO.setResult(Boolean.TRUE);
            if(GovPublicCarOperateTypeEnum.OPEN_LOCK.getCode().equals(reqDTO.getOperateType())){
                openOrCloseInfoDTO.setMessage("开锁成功");
            } else if (GovPublicCarOperateTypeEnum.CLOSE_LOCK.getCode().equals(reqDTO.getOperateType())) {
                openOrCloseInfoDTO.setMessage("关锁成功");
            }
        }else{
            commandOperateLog.setCommandResult(GovPublicCarCommandResultEnum.FAIL.getCode());
            commandOperateLog.setFailReason(reqDTO.getMessage());
            //构建返回信息
            openOrCloseInfoDTO.setResult(Boolean.FALSE);
            openOrCloseInfoDTO.setMessage(reqDTO.getMessage());
        }
        commandOperateLog.setOperateSource(GovPublicCarCommandSourceEnum.SOCIAL_VEHICLE.getCode());
        commandOperateLog.setCompanyId(reqDTO.getLoginCompanyId());
        commandOperateLog.setCompanyName(reqDTO.getLoginCompanyName());
        commandOperateLog.setOperatorCode(reqDTO.getLoginUserCode());
        commandOperateLog.setOperatorName(reqDTO.getLoginUserName());
        commandOperateLog.setOperatorMobile(reqDTO.getLoginUserMobile());
        commandOperateLog.setDeptCode(reqDTO.getLoginUserBelongDeptCode());
        commandOperateLog.setDeptName(reqDTO.getLoginUserBelongDeptName());
        commandOperateLog.setCreateTime(new Date());
        commandOperateLog.setUpdateTime(new Date());
        commandOperateLog.setRequestType(1);
        govPublicCarCommandOperateLogService.save(commandOperateLog);
    }

    private void doOpenOrCloseLockByNetwork(LockAndOrderReq reqDTO, OpenOrCloseInfoDTO openOrCloseInfoDTO) {
        //指令下发时间
        Date commandDispatchTime =null;
        //指令完成时间
        Date commandCompleteTime =null;
        //下发指令返回结果
        CommandRespDTO commandRespDTO = null;
        String url;
        //调用接口，进行开关锁操作
        GovPublicCarOperateTypeEnum govPublicCarOperateTypeEnum = GovPublicCarOperateTypeEnum.getEnumByCode(reqDTO.getOperateType());
        switch (govPublicCarOperateTypeEnum) {
            //开锁
            case OPEN_LOCK:
                url = transportationUrl+ OPEN_LOCK_URL;
                //记录指令下发时间
                commandDispatchTime = new Date();
                commandRespDTO = executePostUrl(reqDTO, url);
                //记录指令完成时间
                commandCompleteTime = new Date();
                break;
            //关锁
            case CLOSE_LOCK:
                url = transportationUrl+ CLOSE_LOCK_URL;
                //记录指令下发时间
                commandDispatchTime = new Date();
                commandRespDTO = executePostUrl(reqDTO, url);
                //记录指令完成时间
                commandCompleteTime = new Date();
                break;
            default:
                break;
        }
        //构建返回信息
        if (ObjectUtil.isNull(commandRespDTO)||commandRespDTO.getStatus()!=1) {
            openOrCloseInfoDTO.setResult(Boolean.FALSE);
            if(commandRespDTO!=null){
                openOrCloseInfoDTO.setMessage(commandRespDTO.getMsg());
            }
            //如果是关锁，需要有校验toast提示
            if(ObjectUtil.equals(GovPublicCarOperateTypeEnum.CLOSE_LOCK, govPublicCarOperateTypeEnum)){
                JSONObject jsonObject = (JSONObject)commandRespDTO.getValue();
                if(ObjectUtil.isNull(jsonObject)){
                    if(StringUtils.isEmpty(openOrCloseInfoDTO.getMessage())){
                        openOrCloseInfoDTO.setMessage("关锁失败");
                    }
                }else{
                    CommandRespDTO.Rep rep = jsonObject.toJavaObject(CommandRespDTO.Rep.class);
                    StringBuilder msg = new StringBuilder();
                    if(rep.getDoor()==1){
                        msg.append("车门未关闭，关锁失败;");
                    }
                    if(rep.getLamp()==1){
                        msg.append("车灯未关闭，关锁失败;");
                    }
                    if(rep.getStall()==1){
                        msg.append("发动机未熄火，关锁失败;");
                    }
                    if(rep.getDoor()==0&&rep.getLamp()==0&&rep.getStall()==0){
                        msg.append("未知原因,关锁失败");
                    }
                    openOrCloseInfoDTO.setMessage(msg.toString());
                }
            }
        }else{
            openOrCloseInfoDTO.setResult(Boolean.TRUE);
            if(GovPublicCarOperateTypeEnum.OPEN_LOCK.getCode().equals(reqDTO.getOperateType())){
                openOrCloseInfoDTO.setMessage("开锁成功");
            } else if (GovPublicCarOperateTypeEnum.CLOSE_LOCK.getCode().equals(reqDTO.getOperateType())) {
                openOrCloseInfoDTO.setMessage("关锁成功");
            }
        }
        //插入操作日志
        insertOrderOperationLog(reqDTO);
        //插入指令日志
        insertCommandOperationLog(reqDTO,commandRespDTO,commandDispatchTime,commandCompleteTime,openOrCloseInfoDTO);

    }

    private void insertCommandOperationLog(LockAndOrderReq reqDTO, CommandRespDTO commandRespDTO, Date commandDispatchTime, Date commandCompleteTime, OpenOrCloseInfoDTO openOrCloseInfoDTO) {

        //查询订单车辆相关信息
        GovPublicCarOrderVehicleInfo vehicleInfo = govPublicCarOrderVehicleInfoService.getOne(new LambdaQueryWrapper<GovPublicCarOrderVehicleInfo>().eq(GovPublicCarOrderVehicleInfo::getOrderNo, reqDTO.getOrderNo()).last("limit 1"));
        //根据sim查询车机信息
        GovGpsDevice govGpsDevice = govGpsDeviceService.getOne(new LambdaQueryWrapper<GovGpsDevice>()
                .eq(GovGpsDevice::getSimNo, reqDTO.getSimNo())
                .eq(GovGpsDevice::getDeleteStatus, GovGpsDeviceEnum.DeviceDeleteStatusEnum.NORMAL.getCode())
                .last("limit 1"));
        GovPublicCarCommandOperateLog commandOperateLog = new GovPublicCarCommandOperateLog();
        commandOperateLog.setBusinessNo(reqDTO.getOrderNo());
        String commandNo = sequenceGenerator.generate(new Date(), PUBLIC_CAR_COMMAND_PREFIX);
        commandOperateLog.setCommandNo(commandNo);
        commandOperateLog.setVehicleNo(vehicleInfo.getVehicleNo());
        commandOperateLog.setVehicleLicense(vehicleInfo.getVehicleLicense());
        commandOperateLog.setVehicleVin(vehicleInfo.getVehicleVin());
        commandOperateLog.setVehicleSeriesId(vehicleInfo.getVehicleSeriesId());
        commandOperateLog.setVehicleSeriesName(vehicleInfo.getVehicleSeriesName());
        commandOperateLog.setSimNo(govGpsDevice.getSimNo());
        commandOperateLog.setDeviceNo(govGpsDevice.getDeviceNo());
        commandOperateLog.setManufactId(govGpsDevice.getManufactId());
        commandOperateLog.setManufactName(govGpsDevice.getManufactName());
        commandOperateLog.setModelId(govGpsDevice.getModelId());
        commandOperateLog.setModelName(govGpsDevice.getModelName());
        if(GovPublicCarOperateTypeEnum.OPEN_LOCK.getCode().equals(reqDTO.getOperateType())){
            commandOperateLog.setCommandType(GovPublicCarCommandTypeEnum.UNLOCK.getCode());
        } else if (GovPublicCarOperateTypeEnum.CLOSE_LOCK.getCode().equals(reqDTO.getOperateType())) {
            commandOperateLog.setCommandType(GovPublicCarCommandTypeEnum.LOCK.getCode());
        }
        commandOperateLog.setCommandDispatchTime(commandDispatchTime);
        commandOperateLog.setCommandCompleteTime(commandCompleteTime);
        if(commandRespDTO != null&& commandRespDTO.getStatus()==1){
            commandOperateLog.setCommandResult(GovPublicCarCommandResultEnum.SUCCESS.getCode());
        }else if (commandRespDTO != null){
            commandOperateLog.setCommandResult(GovPublicCarCommandResultEnum.FAIL.getCode());
            commandOperateLog.setFailReason(openOrCloseInfoDTO.getMessage());
        }else{
            commandOperateLog.setCommandResult(GovPublicCarCommandResultEnum.FAIL.getCode());
        }
        commandOperateLog.setOperateSource(GovPublicCarCommandSourceEnum.SOCIAL_VEHICLE.getCode());
        commandOperateLog.setCompanyId(reqDTO.getLoginCompanyId());
        commandOperateLog.setCompanyName(reqDTO.getLoginCompanyName());
        commandOperateLog.setOperatorCode(reqDTO.getLoginUserCode());
        commandOperateLog.setOperatorName(reqDTO.getLoginUserName());
        commandOperateLog.setOperatorMobile(reqDTO.getLoginUserMobile());
        commandOperateLog.setDeptCode(reqDTO.getLoginUserBelongDeptCode());
        commandOperateLog.setDeptName(reqDTO.getLoginUserBelongDeptName());
        commandOperateLog.setCreateTime(new Date());
        commandOperateLog.setUpdateTime(new Date());
        commandOperateLog.setRequestType(2);
        govPublicCarCommandOperateLogService.save(commandOperateLog);


    }

    private void insertOrderOperationLog(LockAndOrderReq reqDTO) {
        GovOrderOperationLog operationLog = new GovOrderOperationLog();
        operationLog.setOrderNo(reqDTO.getOrderNo());
        if(reqDTO.getOperateType().equals(GovPublicCarOperateTypeEnum.OPEN_LOCK.getCode())){
            operationLog.setOperationType(GovPublicCarOrderOperationTypeEnum.OPEN_LOCK.getCode());
            operationLog.setOperationDescription(GovPublicCarOrderOperationTypeEnum.OPEN_LOCK.getName());
        }else if(reqDTO.getOperateType().equals(GovPublicCarOperateTypeEnum.CLOSE_LOCK.getCode())){
            operationLog.setOperationType(GovPublicCarOrderOperationTypeEnum.CLOSE_LOCK.getCode());
            operationLog.setOperationDescription(GovPublicCarOrderOperationTypeEnum.CLOSE_LOCK.getName());
        }
        operationLog.setOrderStatus(GovPublicCarOrderStatusEnum.IN_USE.getCode());
        operationLog.setOrderStatusName(GovPublicCarOrderStatusEnum.IN_USE.getName());
        operationLog.setOperatorCode(reqDTO.getLoginUserCode());
        operationLog.setOperatorName(reqDTO.getLoginUserName());
        operationLog.setOperatorMobile(reqDTO.getLoginUserMobile());
        govOrderOperationLogService.save(operationLog);
        
    }

    private static CommandRespDTO executePostUrl(LockAndOrderReq reqDTO, String url) {
        CommandRespDTO commandRespDTO;
        Map<String, Object> params = new HashMap<>();
        CommandReqDTO commandReqDTO = new CommandReqDTO();
        commandReqDTO.setTerminalId(reqDTO.getSimNo());
        params.put(BaseHttpClient.POSTBODY_MAP_KEY, commandReqDTO);
        RestResponse response = RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, url, params, null, RestResponse.class);
        if(response!=null&&response.getCode()==0){
            RestResponse result = (RestResponse)response.getData();
            com.alibaba.fastjson.JSONObject jsonObject = (com.alibaba.fastjson.JSONObject)result.getData();
            if(ObjectUtil.isNotNull(jsonObject)){
                commandRespDTO = jsonObject.toJavaObject(CommandRespDTO.class);
            }else{
                commandRespDTO = new CommandRespDTO();
                commandRespDTO.setStatus(result.getCode());
                commandRespDTO.setMsg(result.getMsg());
            }
        }else {
            commandRespDTO = new CommandRespDTO();
            commandRespDTO.setStatus(-1);
            commandRespDTO.setMsg("开锁失败，请重试");
        }
        return commandRespDTO;
    }

    @NotNull
    private static DeviceBluetoothConnectionInfoDTO buildBlueToothInfo(GovGpsDevice device, String simNo) {
        // 蓝牙密钥
        Random r = new Random();
        Integer bluetoothLoc = r.nextInt(10) + 1;
        Integer bluetoothLength = r.nextInt(10) + 1;
        String encryptionKey  = "";
        try {
            encryptionKey  = BluetoothUtil.encryptionStr(bluetoothLoc, bluetoothLength);
        } catch (Exception e) {
            log.error("蓝牙密钥生成失败", e);
        }
        //组装结果
        DeviceBluetoothConnectionInfoDTO result = new DeviceBluetoothConnectionInfoDTO();
        result.setMac(device.getBlueToothMac());
        if(StringUtils.isNotEmpty(simNo) && simNo.startsWith("15")){
            result.setServiceUUID(BLUE_SERVICEUUID_TERMINAL5);
            result.setCharacterUUID(BLUE_CHARACTERUUID_TERMINAL5);
        }else{
            //车机4.0(含)之前下发
            result.setServiceUUID(BLUE_SERVICEUUID_TERMINAL124);
            result.setCharacterUUID(BLUE_CHARACTERUUID_TERMINAL124);
        }
        result.setBluetoothLoc(bluetoothLoc);
        result.setBluetoothLength(bluetoothLength);
        result.setEncryptionKey(encryptionKey);
        return result;
    }


    private void closeDuringTrip(GovPublicCarOrder order) {
        //修改订单状态&计算总里程
        //查询地址表
        BigDecimal totalMileage = BigDecimal.ZERO;
        //查询车辆表
        GovPublicCarOrderVehicleInfo vehicleInfo = govPublicCarOrderVehicleInfoService.getOne(new LambdaQueryWrapper<GovPublicCarOrderVehicleInfo>()
                .eq(GovPublicCarOrderVehicleInfo::getOrderNo, order.getOrderNo()));
        GovGpsDevice govGpsDevice = govGpsDeviceService.getOne(new LambdaQueryWrapper<GovGpsDevice>()
                .eq(GovGpsDevice::getDeviceNo, vehicleInfo.getDeviceId())
                .eq(GovGpsDevice::getCompanyId, order.getCompanyId()).last("limit 1"));
        if (ObjectUtil.isNotNull(govGpsDevice)) {
            try {
                CarTravelDistanceReqDTO carTravelDistanceReqDTO = new CarTravelDistanceReqDTO();
                carTravelDistanceReqDTO.setVehicleNo(vehicleInfo.getVehicleNo());
                carTravelDistanceReqDTO.setCarNo(vehicleInfo.getVehicleLicense());
                carTravelDistanceReqDTO.setSimNo(govGpsDevice.getSimNo());
                carTravelDistanceReqDTO.setDeviceNoList(Lists.newArrayList(govGpsDevice.getDeviceNo()));
                carTravelDistanceReqDTO.setBeginCreateDate(DateUtils.format(order.getOrderStartTime(), DateUtils.TIME_FORMAT));
                carTravelDistanceReqDTO.setEndCreateDate(DateUtils.format(new Date(), DateUtils.TIME_FORMAT));
                Double distance = govDeviceMongoService.calculateTravelDistanceForCarTbox(carTravelDistanceReqDTO);
                log.info("计算订单里程 请求参数:{} 响应结果：{}", JSON.toJSONString(carTravelDistanceReqDTO), distance);
                if (ObjectUtil.isNotNull(distance)) {
                    totalMileage = new BigDecimal(distance);
                }
            } catch (Exception e) {
                log.info("计算订单里程异常 orderNo:{}", order.getOrderNo(), e);
            }
            //查询地址信息表，关闭报警
            GovPublicCarOrderAddressInfo addressInfo = govPublicCarOrderAddressInfoService.getOne(new LambdaQueryWrapper<GovPublicCarOrderAddressInfo>().eq(GovPublicCarOrderAddressInfo::getOrderNo, order.getOrderNo()));
            if (ObjectUtil.isNotNull(addressInfo) && StringUtils.isNotEmpty(addressInfo.getAlarmCode())) {
                GovVehicleLocation govVehicleLocation = vehicleDeviceService.getOneLatestLocation(govGpsDevice.getDeviceSysNo());
                if (ObjectUtil.isNotNull(govVehicleLocation) && StringUtils.isEmpty(addressInfo.getActualDestinationShortLocation())) {
                    String address = BaiduUtils.gpsTransBaiduAddress(govVehicleLocation.getLatBaidu().doubleValue(), govVehicleLocation.getLngBaidu().doubleValue());
                    LambdaUpdateWrapper<GovPublicCarOrderAddressInfo> updateWrapper = new LambdaUpdateWrapper<>();
                    updateWrapper.eq(GovPublicCarOrderAddressInfo::getOrderNo, order.getOrderNo())
                            .set(GovPublicCarOrderAddressInfo::getActualDestinationShortLocation, StrUtils.getOrDefaultEmpty(address))
                            .set(GovPublicCarOrderAddressInfo::getActualDestinationLongLocation, StrUtils.getOrDefaultEmpty(address))
                            .set(GovPublicCarOrderAddressInfo::getActualDestinationLatitude, govVehicleLocation.getLatBaidu())
                            .set(GovPublicCarOrderAddressInfo::getActualDestinationLongitude, govVehicleLocation.getLngBaidu());
                    govPublicCarOrderAddressInfoService.update(updateWrapper);
                }
                // 报警编号
                String warnSn = addressInfo.getAlarmCode();
                if (StringUtils.isNotEmpty(warnSn)) {
                    // 进行报警关闭
                    govPublicWarnFenceService.forceEndWarnBySn(warnSn, order.getVehicleVin());
                }
            }
        }
        LambdaUpdateWrapper<GovPublicCarOrder> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(GovPublicCarOrder::getOrderNo, order.getOrderNo())
                .set(GovPublicCarOrder::getOrderStatus, GovPublicCarOrderStatusEnum.COMPLETED.getCode())
                .set(GovPublicCarOrder::getOrderEndTime, new Date())
                .set(GovPublicCarOrder::getTotalMileage, totalMileage)
                .set(GovPublicCarOrder::getUpdateCode, "")
                .set(GovPublicCarOrder::getUpdateName, "system");
        govPublicCarOrderService.update(updateWrapper);
        //插入订单开始操作人
        GovPublicCarOrderUserInfo startUser = new GovPublicCarOrderUserInfo();
        startUser.setOrderNo(order.getOrderNo());
        startUser.setUserType(GovPublicCarOrderUserTypeEnum.ORDER_FORCE_END_OPERATOR.getCode());
        startUser.setUserName("system");
        startUser.setCompanyId(order.getCompanyId());
        startUser.setCompanyName(order.getCompanyName());
        startUser.setApplyNo(order.getApplyNo());
        govPublicCarOrderUserInfoService.save(startUser);
        //插入操作日志
        GovOrderOperationLog operationLog = new GovOrderOperationLog();
        operationLog.setOrderNo(order.getOrderNo());
        operationLog.setOperationType(GovPublicCarOrderOperationTypeEnum.AUTO_END_TRIP.getCode());
        operationLog.setOperationDescription(GovPublicCarOrderOperationTypeEnum.AUTO_END_TRIP.getName());
        operationLog.setOrderStatus(GovPublicCarOrderStatusEnum.COMPLETED.getCode());
        operationLog.setOrderStatusName(GovPublicCarOrderStatusEnum.COMPLETED.getName());
        operationLog.setOperatorCode("");
        operationLog.setOperatorName("system");
        operationLog.setOperatorMobile("");
        govOrderOperationLogService.save(operationLog);
        //更新车辆&司机状态
        //车辆状态更新为无任务
        LambdaUpdateWrapper<GovVehicleBaseInfo> baseInfoLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        baseInfoLambdaUpdateWrapper.eq(GovVehicleBaseInfo::getVehicleNo, order.getVehicleNo())
                .set(GovVehicleBaseInfo::getWorkStatus, WorkStatusEnum.NO_TASK.getCode());
        govVehicleBaseInfoService.update(baseInfoLambdaUpdateWrapper);
        //释放司机状态
        releaseDriverStatus(order.getOrderNo());
        //社会用车，需要计算费用
        if (ObjectUtil.equals(order.getUseType(), GovPublicCarApplyTypeEnum.SOCIAL_RENT.getCode())) {
            calculateFee(order.getOrderNo());
        }
    }

    private void saveBatchDetails(SocVehicleFeeDetailReqDTO param) {
        List<GovSocVehicleFeeDetail> govSocVehicleFeeDetailList = new ArrayList<>();
        //保存不为0的明细
        //保存租金明细
        if (param.getRentFee().compareTo(BigDecimal.ZERO) != 0) {
            GovSocVehicleFeeDetail govSocVehicleFeeDetail = new GovSocVehicleFeeDetail();
            govSocVehicleFeeDetail.setOrderNo(param.getOrderNo());
            govSocVehicleFeeDetail.setFeeCode(GovPublicSocVehicleFeeItemEnum.RENT_FEE.getCode());
            govSocVehicleFeeDetail.setFeeName(GovPublicSocVehicleFeeItemEnum.RENT_FEE.getName());
            govSocVehicleFeeDetail.setFee(param.getRentFee());
            govSocVehicleFeeDetailList.add(govSocVehicleFeeDetail);
        }
        // 保存停车费明细
        if (param.getParkingFee().compareTo(BigDecimal.ZERO) != 0) {
            GovSocVehicleFeeDetail govSocVehicleFeeDetail = new GovSocVehicleFeeDetail();
            govSocVehicleFeeDetail.setOrderNo(param.getOrderNo());
            govSocVehicleFeeDetail.setFeeCode(GovPublicSocVehicleFeeItemEnum.PARKING_FEE.getCode());
            govSocVehicleFeeDetail.setFeeName(GovPublicSocVehicleFeeItemEnum.PARKING_FEE.getName());
            govSocVehicleFeeDetail.setFee(param.getParkingFee());
            govSocVehicleFeeDetailList.add(govSocVehicleFeeDetail);
        }

        // 保存过路过桥费明细
        if (param.getPassRoadFee().compareTo(BigDecimal.ZERO) != 0) {
            GovSocVehicleFeeDetail govSocVehicleFeeDetail = new GovSocVehicleFeeDetail();
            govSocVehicleFeeDetail.setOrderNo(param.getOrderNo());
            govSocVehicleFeeDetail.setFeeCode(GovPublicSocVehicleFeeItemEnum.PASS_ROAD_FEE.getCode());
            govSocVehicleFeeDetail.setFeeName(GovPublicSocVehicleFeeItemEnum.PASS_ROAD_FEE.getName());
            govSocVehicleFeeDetail.setFee(param.getPassRoadFee());
            govSocVehicleFeeDetailList.add(govSocVehicleFeeDetail);
        }

        // 保存住宿费明细
        if (param.getHotelFee().compareTo(BigDecimal.ZERO) != 0) {
            GovSocVehicleFeeDetail govSocVehicleFeeDetail = new GovSocVehicleFeeDetail();
            govSocVehicleFeeDetail.setOrderNo(param.getOrderNo());
            govSocVehicleFeeDetail.setFeeCode(GovPublicSocVehicleFeeItemEnum.HOTEL_FEE.getCode());
            govSocVehicleFeeDetail.setFeeName(GovPublicSocVehicleFeeItemEnum.HOTEL_FEE.getName());
            govSocVehicleFeeDetail.setFee(param.getHotelFee());
            govSocVehicleFeeDetailList.add(govSocVehicleFeeDetail);
        }

        // 保存餐饮费明细
        if (param.getMealsFee().compareTo(BigDecimal.ZERO) != 0) {
            GovSocVehicleFeeDetail govSocVehicleFeeDetail = new GovSocVehicleFeeDetail();
            govSocVehicleFeeDetail.setOrderNo(param.getOrderNo());
            govSocVehicleFeeDetail.setFeeCode(GovPublicSocVehicleFeeItemEnum.MEALS_FEE.getCode());
            govSocVehicleFeeDetail.setFeeName(GovPublicSocVehicleFeeItemEnum.MEALS_FEE.getName());
            govSocVehicleFeeDetail.setFee(param.getMealsFee());
            govSocVehicleFeeDetailList.add(govSocVehicleFeeDetail);
        }

        // 保存洗车费明细
        if (param.getWashCarFee().compareTo(BigDecimal.ZERO) != 0) {
            GovSocVehicleFeeDetail govSocVehicleFeeDetail = new GovSocVehicleFeeDetail();
            govSocVehicleFeeDetail.setOrderNo(param.getOrderNo());
            govSocVehicleFeeDetail.setFeeCode(GovPublicSocVehicleFeeItemEnum.WASH_CAR_FEE.getCode());
            govSocVehicleFeeDetail.setFeeName(GovPublicSocVehicleFeeItemEnum.WASH_CAR_FEE.getName());
            govSocVehicleFeeDetail.setFee(param.getWashCarFee());
            govSocVehicleFeeDetailList.add(govSocVehicleFeeDetail);
        }

        // 保存ETC费明细
        if (param.getEtcFee().compareTo(BigDecimal.ZERO) != 0) {
            GovSocVehicleFeeDetail govSocVehicleFeeDetail = new GovSocVehicleFeeDetail();
            govSocVehicleFeeDetail.setOrderNo(param.getOrderNo());
            govSocVehicleFeeDetail.setFeeCode(GovPublicSocVehicleFeeItemEnum.ETC_FEE.getCode());
            govSocVehicleFeeDetail.setFeeName(GovPublicSocVehicleFeeItemEnum.ETC_FEE.getName());
            govSocVehicleFeeDetail.setFee(param.getEtcFee());
            govSocVehicleFeeDetailList.add(govSocVehicleFeeDetail);
        }

        // 保存其他费用明细
        if (param.getOtherFee().compareTo(BigDecimal.ZERO) != 0) {
            GovSocVehicleFeeDetail govSocVehicleFeeDetail = new GovSocVehicleFeeDetail();
            govSocVehicleFeeDetail.setOrderNo(param.getOrderNo());
            govSocVehicleFeeDetail.setFeeCode(GovPublicSocVehicleFeeItemEnum.OTHER_FEE.getCode());
            govSocVehicleFeeDetail.setFeeName(GovPublicSocVehicleFeeItemEnum.OTHER_FEE.getName());
            govSocVehicleFeeDetail.setFee(param.getOtherFee());
            govSocVehicleFeeDetailList.add(govSocVehicleFeeDetail);
        }
        govSocVehicleFeeDetailService.saveBatch(govSocVehicleFeeDetailList);
    }

    private void autoStartOrder(OfficialVehicleOutFenceMsgDTO param, GovPublicCarOrder order, List<String> orderNoList, GovCityDic cityDic) {
        String orderNo = orderNoList.get(0);
        GovPublicCarOrder searchOrder = govPublicCarOrderService.getOne(new LambdaQueryWrapper<GovPublicCarOrder>().eq(GovPublicCarOrder::getOrderNo, orderNo));
        //更新订单状态为用车中
        LambdaUpdateWrapper<GovPublicCarOrder> updateOrder = new LambdaUpdateWrapper<GovPublicCarOrder>()
                .eq(GovPublicCarOrder::getOrderNo, orderNo)
                .set(GovPublicCarOrder::getOrderStatus, GovPublicCarOrderStatusEnum.IN_USE.getCode())
                .set(GovPublicCarOrder::getOrderStartTime, param.getOutFenceTime())
                .set(GovPublicCarOrder::getPickupLotExitTime, param.getOutFenceTime());
        govPublicCarOrderService.update(updateOrder);
        //将地址信息写入订单
        GovPublicCarOrderAddressInfo govPublicCarOrderAddressInfo = new GovPublicCarOrderAddressInfo();
        govPublicCarOrderAddressInfo.setOrderNo(searchOrder.getOrderNo());
        govPublicCarOrderAddressInfo.setCompanyId(searchOrder.getCompanyId());
        govPublicCarOrderAddressInfo.setStartFenceId(param.getFenceSnapId());
        govPublicCarOrderAddressInfo.setStartLatitude(param.getLatitude());
        govPublicCarOrderAddressInfo.setStartLongitude(param.getLongitude());
        govPublicCarOrderAddressInfo.setStartLocation(param.getAddress());
        govPublicCarOrderAddressInfo.setAlarmCode(param.getWarnSn());
        govPublicCarOrderAddressInfo.setActualDepartureLongLocation(param.getAddress());
        govPublicCarOrderAddressInfo.setActualDepartureLatitude(param.getLatitude());
        govPublicCarOrderAddressInfo.setActualDepartureLongitude(param.getLongitude());
        govPublicCarOrderAddressInfo.setActualDepartureShortLocation(param.getAddress());
        govPublicCarOrderAddressInfo.setApplyNo(searchOrder.getApplyNo());
        if (ObjectUtil.isNotNull(cityDic)) {
            govPublicCarOrderAddressInfo.setStartCityCode(cityDic.getCityCode().toString());
            govPublicCarOrderAddressInfo.setStartCityName(cityDic.getCityName());
        }
        govPublicCarOrderAddressInfoService.save(govPublicCarOrderAddressInfo);
        //更新车辆上的设备信息
        LambdaUpdateWrapper<GovPublicCarOrderVehicleInfo> vehicleInfoLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        vehicleInfoLambdaUpdateWrapper.eq(GovPublicCarOrderVehicleInfo::getOrderNo, order.getOrderNo())
                .set(GovPublicCarOrderVehicleInfo::getDeviceId, param.getDeviceNo())
                .set(GovPublicCarOrderVehicleInfo::getDeviceType, param.getDeviceType());
        govPublicCarOrderVehicleInfoService.update(vehicleInfoLambdaUpdateWrapper);
        //插入开始行程日志
        GovOrderOperationLog orderOperationLogOut = new GovOrderOperationLog();
        orderOperationLogOut.setOrderNo(order.getOrderNo());
        orderOperationLogOut.setOperationType(GovPublicCarOrderOperationTypeEnum.START_TRIP.getCode());
        orderOperationLogOut.setOperationDescription(GovPublicCarOrderOperationTypeEnum.START_TRIP.getName());
        orderOperationLogOut.setOperationTime(param.getOutFenceTime());
        orderOperationLogOut.setOrderStatus(order.getOrderStatus());
        orderOperationLogOut.setOrderStatusName(GovPublicCarOrderStatusEnum.getName(order.getOrderStatus()));
        orderOperationLogOut.setOperatorCode("");
        orderOperationLogOut.setOperatorName("System");
        orderOperationLogOut.setOperatorMobile("");
        govOrderOperationLogService.save(orderOperationLogOut);
        //插入使出围栏日志
        GovOrderOperationLog outFenceLog = new GovOrderOperationLog();
        outFenceLog.setOrderNo(order.getOrderNo());
        outFenceLog.setOperationType(GovPublicCarOrderOperationTypeEnum.OUT_FENCE.getCode());
        outFenceLog.setOperationDescription(GovPublicCarOrderOperationTypeEnum.OUT_FENCE.getName());
        outFenceLog.setOperationTime(param.getOutFenceTime());
        outFenceLog.setOrderStatus(order.getOrderStatus());
        outFenceLog.setOrderStatusName(GovPublicCarOrderStatusEnum.getName(order.getOrderStatus()));
        outFenceLog.setOperatorCode("");
        outFenceLog.setOperatorName("System");
        outFenceLog.setOperatorMobile("");
        govOrderOperationLogService.save(outFenceLog);
    }

    private void updateOrderTime(OfficialVehicleOutFenceMsgDTO param, GovPublicCarOrder order) {
        if (Objects.isNull(order.getPickupLotExitTime())) {
            LambdaUpdateWrapper<GovPublicCarOrder> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(GovPublicCarOrder::getOrderNo, order.getOrderNo())
                    .set(GovPublicCarOrder::getPickupLotExitTime, param.getOutFenceTime());
            govPublicCarOrderService.update(updateWrapper);
        }
        //将地址信息写入订单
        LambdaUpdateWrapper<GovPublicCarOrderAddressInfo> govPublicCarOrderAddressInfo = new LambdaUpdateWrapper<>();
        govPublicCarOrderAddressInfo.eq(GovPublicCarOrderAddressInfo::getOrderNo, order.getOrderNo());
        govPublicCarOrderAddressInfo.set(GovPublicCarOrderAddressInfo::getStartFenceId, param.getFenceSnapId());
        govPublicCarOrderAddressInfo.set(GovPublicCarOrderAddressInfo::getStartLatitude, param.getLatitude());
        govPublicCarOrderAddressInfo.set(GovPublicCarOrderAddressInfo::getStartLongitude, param.getLongitude());
        govPublicCarOrderAddressInfo.set(GovPublicCarOrderAddressInfo::getStartLocation, param.getAddress());
        govPublicCarOrderAddressInfo.set(GovPublicCarOrderAddressInfo::getAlarmCode, param.getWarnSn());
        //根据经纬度查询地理位置
        govPublicCarOrderAddressInfoService.update(govPublicCarOrderAddressInfo);
        //更新车辆上的设备信息
        LambdaUpdateWrapper<GovPublicCarOrderVehicleInfo> vehicleInfoLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        vehicleInfoLambdaUpdateWrapper.eq(GovPublicCarOrderVehicleInfo::getOrderNo, order.getOrderNo())
                .set(GovPublicCarOrderVehicleInfo::getDeviceId, param.getDeviceNo())
                .set(GovPublicCarOrderVehicleInfo::getDeviceType, param.getDeviceType());
        govPublicCarOrderVehicleInfoService.update(vehicleInfoLambdaUpdateWrapper);
        //插入日志
        GovOrderOperationLog orderOperationLogOut = new GovOrderOperationLog();
        orderOperationLogOut.setOrderNo(order.getOrderNo());
        orderOperationLogOut.setOperationType(GovPublicCarOrderOperationTypeEnum.OUT_FENCE.getCode());
        orderOperationLogOut.setOperationDescription(GovPublicCarOrderOperationTypeEnum.OUT_FENCE.getName());
        orderOperationLogOut.setOperationTime(param.getOutFenceTime());
        orderOperationLogOut.setOrderStatus(order.getOrderStatus());
        orderOperationLogOut.setOrderStatusName(GovPublicCarOrderStatusEnum.getName(order.getOrderStatus()));
        orderOperationLogOut.setOperatorCode("");
        orderOperationLogOut.setOperatorName("System");
        orderOperationLogOut.setOperatorMobile("");
        govOrderOperationLogService.save(orderOperationLogOut);
    }

    private void generateNoTaskOrder(OfficialVehicleOutFenceMsgDTO param, GovVehicleBaseInfo govVehicleBaseInfo, GovCityDic cityDic) {
        String orderNo;
        Integer useAttribute = govVehicleBaseInfo.getUseAttribute();
        Integer sourceType = govVehicleBaseInfo.getSourceType();
        if (ObjectUtil.equals(sourceType, VehicleSourceTypeEnum.SOCIAL_VEHICLE.getCode())) {
            orderNo = sequenceGenerator.generate(new Date(), PUBLIC_SOC_GOV_CAR_ORDER_PREFIX);
        } else {
            orderNo = sequenceGenerator.generate(new Date(), PUBLIC_GOV_CAR_ORDER_PREFIX);
        }
        //生成订单信息
        GovPublicCarOrder order = new GovPublicCarOrder();
        order.setOrderNo(orderNo);
        order.setOrderType(GovPublicCarOrderTypeEnum.NO_TASK_USE_CAR_TYPE.getCode());
        order.setVehicleNo(govVehicleBaseInfo.getVehicleNo());
        order.setVehicleLicense(govVehicleBaseInfo.getVehicleLicense());
        order.setVehicleVin(govVehicleBaseInfo.getVehicleVin());
        order.setOrderStatus(GovPublicCarOrderStatusEnum.IN_USE.getCode());
        if (ObjectUtil.equals(sourceType, VehicleSourceTypeEnum.SOCIAL_VEHICLE.getCode())) {
            order.setUseType(GovPublicCarApplyTypeEnum.SOCIAL_RENT.getCode());
            //分时
            TimeShareConfigQueryReqDTO req = new TimeShareConfigQueryReqDTO();
            req.setCompanyId(govVehicleBaseInfo.getCompanyId());
            req.setSupplierCode(govVehicleBaseInfo.getSupplierServiceCode());
            req.setVehicleType(govVehicleBaseInfo.getVehicleType());
            req.setVehicleBrandId(govVehicleBaseInfo.getVehicleBrandId());
            req.setVehicleSeriesId(govVehicleBaseInfo.getVehicleSeriesId());
            TimeShareConfigRespDTO detail = govTimeShareConfigService.detail(req);
            if (Objects.nonNull(detail) && StringUtils.isNotBlank(detail.getSnapshotCode())) {
                order.setTimeShareSnapshotCode(detail.getSnapshotCode());
            }
            //计价时间配置  无任务用车取车上的  其余的取主用车人的
            List<GovCompanyConfigItemValueDTO> configItemValueDTOS = govPublicBusinessConfigService.selectByDeptCodeAndBusinessCodeAndItemCode(govVehicleBaseInfo.getCompanyId(),govVehicleBaseInfo.getVehicleBelongDeptCode() ,
                    PublicConfigEnum.BusinessConfigEnum.HOUR_USE_CAR_DURATION_FILED.getCode(), PublicConfigEnum.BusinessConfigItemEnum.USE_CAR_DURATION_FILED_ITEM.getCode());
            GovCompanyConfigItemValueDTO configItemValueDTO = configItemValueDTOS.get(0);
            // 计价时间配置: 实际开始/结束时间
            Integer timeShareCalculateConfig = Integer.parseInt(configItemValueDTO.getConfigValue());
            order.setTimeShareCalculateConfig(timeShareCalculateConfig);
        } else {
            order.setUseType(GovPublicCarApplyTypeEnum.PUBLIC_CAR.getCode());
        }
        //无需审核
        if (noNeedVerifyOrder(useAttribute)) {
            order.setVerifyStatus(GovPublicCarVerifyStatusEnum.NO_VERIFICATION_REQUIRED.getCode());
        } else {
            order.setVerifyStatus(GovPublicCarVerifyStatusEnum.UNVERIFIED.getCode());
        }
        order.setRelatedFeesStatus(GovPublicCarRelatedFeesStatusEnum.UNASSOCIATED.getCode());
        order.setOrderStartTime(param.getOutFenceTime());
        order.setPickupLotExitTime(param.getOutFenceTime());
        order.setCompanyId(govVehicleBaseInfo.getCompanyId());
        //查询企业名称
        LambdaQueryWrapper<GovCompany> companyLambdaQueryWrapper = new LambdaQueryWrapper<>();
        companyLambdaQueryWrapper.eq(GovCompany::getCompanyId, govVehicleBaseInfo.getCompanyId()).last("limit 1");
        GovCompany company = govCompanyService.getOne(companyLambdaQueryWrapper);
        if (ObjectUtil.isNotNull(company)) {
            order.setCompanyName(company.getCompanyName());
        }
        govPublicCarOrderService.save(order);
        //将地址信息写入订单
        GovPublicCarOrderAddressInfo govPublicCarOrderAddressInfo = new GovPublicCarOrderAddressInfo();
        govPublicCarOrderAddressInfo.setOrderNo(order.getOrderNo());
        govPublicCarOrderAddressInfo.setCompanyId(order.getCompanyId());
        govPublicCarOrderAddressInfo.setStartFenceId(param.getFenceSnapId());
        govPublicCarOrderAddressInfo.setStartLatitude(param.getLatitude());
        govPublicCarOrderAddressInfo.setStartLongitude(param.getLongitude());
        govPublicCarOrderAddressInfo.setStartLocation(param.getAddress());
        govPublicCarOrderAddressInfo.setAlarmCode(param.getWarnSn());
        govPublicCarOrderAddressInfo.setApplyNo(order.getApplyNo());
        govPublicCarOrderAddressInfo.setActualDepartureLatitude(param.getLatitude());
        govPublicCarOrderAddressInfo.setActualDepartureLongitude(param.getLongitude());
        govPublicCarOrderAddressInfo.setActualDepartureLongLocation(param.getAddress());
        govPublicCarOrderAddressInfo.setActualDepartureShortLocation(param.getAddress());
        if (ObjectUtil.isNotNull(cityDic)) {
            govPublicCarOrderAddressInfo.setStartCityCode(cityDic.getCityCode().toString());
            govPublicCarOrderAddressInfo.setStartCityName(cityDic.getCityName());
        }
        govPublicCarOrderAddressInfoService.save(govPublicCarOrderAddressInfo);
        //生成车辆信息
        GovPublicCarOrderVehicleInfo govPublicCarOrderVehicleInfo = new GovPublicCarOrderVehicleInfo();
        govPublicCarOrderVehicleInfo.setOrderNo(orderNo);
        govPublicCarOrderVehicleInfo.setCompanyId(order.getCompanyId());
        govPublicCarOrderVehicleInfo.setVehicleNo(govVehicleBaseInfo.getVehicleNo());
        govPublicCarOrderVehicleInfo.setVehicleLicense(govVehicleBaseInfo.getVehicleLicense());
        govPublicCarOrderVehicleInfo.setVehicleVin(govVehicleBaseInfo.getVehicleVin());
        govPublicCarOrderVehicleInfo.setVehicleType(govVehicleBaseInfo.getVehicleType());
        govPublicCarOrderVehicleInfo.setDeviceId(param.getDeviceNo());
        govPublicCarOrderVehicleInfo.setDeviceType(param.getDeviceType());
        govPublicCarOrderVehicleInfo.setVehicleBrandId(govVehicleBaseInfo.getVehicleBrandId());
        govPublicCarOrderVehicleInfo.setVehicleBrandName(govVehicleBaseInfo.getVehicleBrandName());
        govPublicCarOrderVehicleInfo.setVehicleSeriesId(govVehicleBaseInfo.getVehicleSeriesId());
        govPublicCarOrderVehicleInfo.setVehicleSeriesName(govVehicleBaseInfo.getVehicleSeriesName());
        govPublicCarOrderVehicleInfo.setUseAttribute(useAttribute);
        govPublicCarOrderVehicleInfo.setVehicleUseStructCode(govVehicleBaseInfo.getVehicleUseStructCode());
        govPublicCarOrderVehicleInfo.setVehicleUseStructName(govVehicleBaseInfo.getVehicleUseStructName());
        govPublicCarOrderVehicleInfo.setVehicleBelongDeptCode(govVehicleBaseInfo.getVehicleBelongDeptCode());
        govPublicCarOrderVehicleInfo.setVehicleBelongDeptName(govVehicleBaseInfo.getVehicleBelongDeptName());
        govPublicCarOrderVehicleInfo.setVehicleUseDeptCode(govVehicleBaseInfo.getVehicleUseDeptCode());
        govPublicCarOrderVehicleInfo.setVehicleUseDeptName(govVehicleBaseInfo.getVehicleUseDeptName());
        govPublicCarOrderVehicleInfo.setSupplierCode(govVehicleBaseInfo.getSupplierServiceCode());
        govPublicCarOrderVehicleInfo.setSupplierName(govVehicleBaseInfo.getSupplierServiceName());
        govPublicCarOrderVehicleInfo.setManageCarType(govVehicleBaseInfo.getManageCarType());
        govPublicCarOrderVehicleInfoService.save(govPublicCarOrderVehicleInfo);
        //插入开始行程日志
        GovOrderOperationLog orderOperationLogOut = new GovOrderOperationLog();
        orderOperationLogOut.setOrderNo(order.getOrderNo());
        orderOperationLogOut.setOperationType(GovPublicCarOrderOperationTypeEnum.CREATE.getCode());
        orderOperationLogOut.setOperationDescription(GovPublicCarOrderOperationTypeEnum.CREATE.getName());
        orderOperationLogOut.setOperationTime(param.getOutFenceTime());
        orderOperationLogOut.setOrderStatus(order.getOrderStatus());
        orderOperationLogOut.setOrderStatusName(GovPublicCarOrderStatusEnum.getName(order.getOrderStatus()));
        orderOperationLogOut.setOperatorCode("");
        orderOperationLogOut.setOperatorName("System");
        orderOperationLogOut.setOperatorMobile("");
        govOrderOperationLogService.save(orderOperationLogOut);
        //插入使出围栏日志
        GovOrderOperationLog outFenceLog = new GovOrderOperationLog();
        outFenceLog.setOrderNo(order.getOrderNo());
        outFenceLog.setOperationType(GovPublicCarOrderOperationTypeEnum.OUT_FENCE.getCode());
        outFenceLog.setOperationDescription(GovPublicCarOrderOperationTypeEnum.OUT_FENCE.getName());
        outFenceLog.setOperationTime(param.getOutFenceTime());
        outFenceLog.setOrderStatus(order.getOrderStatus());
        outFenceLog.setOrderStatusName(GovPublicCarOrderStatusEnum.getName(order.getOrderStatus()));
        outFenceLog.setOperatorCode("");
        outFenceLog.setOperatorName("System");
        outFenceLog.setOperatorMobile("");
        govOrderOperationLogService.save(outFenceLog);
        //修改车辆任务状态
        LambdaUpdateWrapper<GovVehicleBaseInfo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(GovVehicleBaseInfo::getVehicleVin, param.getVehicleVin())
                .set(GovVehicleBaseInfo::getWorkStatus, WorkStatusEnum.TASK_ING.getCode());
        govVehicleBaseInfoService.update(updateWrapper);
        //更新节假日报警的订单号
        carWarnFenceHolidayService.updateOrderNo(param.getWarnSn(), order.getOrderNo());
    }

    public boolean noNeedVerifyOrder(Integer useAttribute) {
        Boolean result = (UseAttributeEnum.COMMUNICATION_VEHICLE.getCode() == useAttribute
                || UseAttributeEnum.EMERGENCY_VEHICLE.getCode() == useAttribute
                || UseAttributeEnum.POLICE_VEHICLE.getCode() == useAttribute
                || UseAttributeEnum.SPECIAL_VEHICLE.getCode() == useAttribute);
        return result;
    }

    private void buildNoTaskUsers(VerifyOrderReqDTO param) {
        List<GovPublicCarOrderUserInfo> userInfos = new ArrayList<>();
        List<GovOrderUserInfoDTO> passengersInfoList = param.getPassengersInfoList();
        //1. 主乘车人&次乘车人
        for (int i = 0; i < passengersInfoList.size(); i++) {
            GovOrderUserInfoDTO passengersInfo = passengersInfoList.get(i);
            GovPublicCarOrderUserInfo govPublicCarOrderUserInfo = BeanUtil.copyObject(passengersInfo, GovPublicCarOrderUserInfo.class);
            govPublicCarOrderUserInfo.setOrderNo(param.getOrderNo());
            if (i == 0) {
                govPublicCarOrderUserInfo.setUserType(GovPublicCarOrderUserTypeEnum.PRIMARY_RIDER.getCode());
            } else {
                govPublicCarOrderUserInfo.setUserType(GovPublicCarOrderUserTypeEnum.SECONDARY_RIDER.getCode());
            }
            govPublicCarOrderUserInfo.setCompanyId(param.getLoginCompanyId());
            govPublicCarOrderUserInfo.setCompanyName(param.getLoginCompanyName());
            userInfos.add(govPublicCarOrderUserInfo);
        }
        //2.司机
        GovPublicCarOrderUserInfo driverInfo = BeanUtil.copyObject(param.getDriverInfo(), GovPublicCarOrderUserInfo.class);
        driverInfo.setOrderNo(param.getOrderNo());
        driverInfo.setUserType(GovPublicCarOrderUserTypeEnum.DRIVER.getCode());
        driverInfo.setCompanyId(param.getLoginCompanyId());
        driverInfo.setCompanyName(param.getLoginCompanyName());
        userInfos.add(driverInfo);
        //3.订单核实人
        GovPublicCarOrderUserInfo verifyUserInfo = new GovPublicCarOrderUserInfo();
        verifyUserInfo.setOrderNo(param.getOrderNo());
        verifyUserInfo.setUserType(GovPublicCarOrderUserTypeEnum.ORDER_VERIFIER.getCode());
        verifyUserInfo.setUserCode(param.getLoginUserCode());
        verifyUserInfo.setUserName(param.getLoginUserName());
        verifyUserInfo.setUserMobile(param.getLoginUserMobile());
        verifyUserInfo.setStructCode(param.getLoginUserBelongStructCode());
        verifyUserInfo.setStructName(param.getLoginUserBelongStructName());
        verifyUserInfo.setDeptCode(param.getLoginUserBelongDeptCode());
        verifyUserInfo.setDeptName(param.getLoginUserBelongDeptName());
        verifyUserInfo.setCompanyId(param.getLoginCompanyId());
        verifyUserInfo.setCompanyName(param.getLoginCompanyName());
        userInfos.add(verifyUserInfo);
        govPublicCarOrderUserInfoService.saveBatch(userInfos);
    }

    /**
     * 强制结束用车
     */
    private void forceEndOrder(List<GovPublicCarOrder> orderList, BaseDTO param, GovPublicCarOrderOperationTypeEnum operationType) {
        orderList.forEach(order -> {
            if (Objects.equals(order.getOrderStatus(), GovPublicCarOrderStatusEnum.IN_USE.getCode())) {
                //修改订单状态&计算总里程
                //查询地址表
                BigDecimal totalMileage = BigDecimal.ZERO;
                //查询车辆表
                GovPublicCarOrderVehicleInfo vehicleInfo = govPublicCarOrderVehicleInfoService.getOne(new LambdaQueryWrapper<GovPublicCarOrderVehicleInfo>()
                        .eq(GovPublicCarOrderVehicleInfo::getOrderNo, order.getOrderNo()));
                GovGpsDevice govGpsDevice = govGpsDeviceService.getOne(new LambdaQueryWrapper<GovGpsDevice>()
                        .eq(GovGpsDevice::getDeviceNo, vehicleInfo.getDeviceId())
                        .eq(GovGpsDevice::getCompanyId, order.getCompanyId()).last("limit 1"));
                if (ObjectUtil.isNotNull(govGpsDevice)) {
                    try {
                        CarTravelDistanceReqDTO carTravelDistanceReqDTO = new CarTravelDistanceReqDTO();
                        carTravelDistanceReqDTO.setVehicleNo(vehicleInfo.getVehicleNo());
                        carTravelDistanceReqDTO.setCarNo(vehicleInfo.getVehicleLicense());
                        carTravelDistanceReqDTO.setSimNo(govGpsDevice.getSimNo());
                        carTravelDistanceReqDTO.setDeviceNoList(Lists.newArrayList(govGpsDevice.getDeviceNo()));
                        carTravelDistanceReqDTO.setBeginCreateDate(DateUtils.format(order.getOrderStartTime(), DateUtils.TIME_FORMAT));
                        carTravelDistanceReqDTO.setEndCreateDate(DateUtils.format(new Date(), DateUtils.TIME_FORMAT));
                        Double distance = govDeviceMongoService.calculateTravelDistanceForCarTbox(carTravelDistanceReqDTO);
                        log.info("计算订单里程 请求参数:{} 响应结果：{}", JSON.toJSONString(carTravelDistanceReqDTO), distance);
                        if (ObjectUtil.isNotNull(distance)) {
                            totalMileage = new BigDecimal(distance);
                        }
                    } catch (Exception e) {
                        log.info("计算订单里程异常 orderNo:{}", order.getOrderNo(), e);
                    }
                    //查询地址信息表，关闭报警
                    GovPublicCarOrderAddressInfo addressInfo = govPublicCarOrderAddressInfoService.getOne(new LambdaQueryWrapper<GovPublicCarOrderAddressInfo>().eq(GovPublicCarOrderAddressInfo::getOrderNo, order.getOrderNo()));
                    if (ObjectUtil.isNotNull(addressInfo) && StringUtils.isNotEmpty(addressInfo.getAlarmCode())) {
                        GovVehicleLocation govVehicleLocation = vehicleDeviceService.getOneLatestLocation(govGpsDevice.getDeviceSysNo());
                        if (ObjectUtil.isNotNull(govVehicleLocation) && StringUtils.isEmpty(addressInfo.getActualDestinationShortLocation())) {
                            String address = BaiduUtils.gpsTransBaiduAddress(govVehicleLocation.getLatBaidu().doubleValue(), govVehicleLocation.getLngBaidu().doubleValue());
                            LambdaUpdateWrapper<GovPublicCarOrderAddressInfo> updateWrapper = new LambdaUpdateWrapper<>();
                            updateWrapper.eq(GovPublicCarOrderAddressInfo::getOrderNo, order.getOrderNo())
                                    .set(GovPublicCarOrderAddressInfo::getActualDestinationShortLocation, StrUtils.getOrDefaultEmpty(address))
                                    .set(GovPublicCarOrderAddressInfo::getActualDestinationLongLocation, StrUtils.getOrDefaultEmpty(address))
                                    .set(GovPublicCarOrderAddressInfo::getActualDestinationLatitude, govVehicleLocation.getLatBaidu())
                                    .set(GovPublicCarOrderAddressInfo::getActualDestinationLongitude, govVehicleLocation.getLngBaidu());
                            govPublicCarOrderAddressInfoService.update(updateWrapper);
                        }
                        // 报警编号
                        String warnSn = addressInfo.getAlarmCode();
                        if (StringUtils.isNotEmpty(warnSn)) {
                            // 进行报警关闭
                            govPublicWarnFenceService.forceEndWarnBySn(warnSn, order.getVehicleVin());
                        }
                    }
                }
//                if(ObjectUtil.equals(GovPublicCarOrderOperationTypeEnum.FORCE_END_TRIP, operationType)){
                //查询该车辆所绑定的设备信息
                buildAndUpdateAddress(order, vehicleInfo);
//                }
                LambdaUpdateWrapper<GovPublicCarOrder> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(GovPublicCarOrder::getOrderNo, order.getOrderNo())
                        .set(GovPublicCarOrder::getOrderStatus, GovPublicCarOrderStatusEnum.COMPLETED.getCode())
                        .set(GovPublicCarOrder::getOrderEndTime, new Date())
                        .set(GovPublicCarOrder::getTotalMileage, totalMileage)
                        .set(GovPublicCarOrder::getUpdateCode, param.getLoginUserCode())
                        .set(GovPublicCarOrder::getUpdateName, param.getLoginUserName());
                govPublicCarOrderService.update(updateWrapper);
                //插入订单开始操作人
                GovPublicCarOrderUserInfo startUser = new GovPublicCarOrderUserInfo();
                startUser.setOrderNo(order.getOrderNo());
                if (operationType.equals(GovPublicCarOrderOperationTypeEnum.FORCE_END_TRIP)) {
                    startUser.setUserType(GovPublicCarOrderUserTypeEnum.ORDER_FORCE_END_OPERATOR.getCode());
                } else {
                    startUser.setUserType(GovPublicCarOrderUserTypeEnum.ORDER_END_OPERATOR.getCode());
                }
                startUser.setUserCode(param.getLoginUserCode());
                startUser.setUserName(param.getLoginUserName());
                startUser.setUserMobile(param.getLoginUserMobile());
                startUser.setStructCode(param.getLoginUserBelongStructCode());
                startUser.setStructName(param.getLoginUserBelongStructName());
                startUser.setDeptCode(param.getLoginUserBelongDeptCode());
                startUser.setDeptName(param.getLoginUserBelongDeptName());
                startUser.setCompanyId(param.getLoginCompanyId());
                startUser.setCompanyName(param.getLoginCompanyName());
                startUser.setApplyNo(order.getApplyNo());
                govPublicCarOrderUserInfoService.save(startUser);
                //插入操作日志
                GovOrderOperationLog operationLog = new GovOrderOperationLog();
                operationLog.setOrderNo(order.getOrderNo());
                operationLog.setOperationType(operationType.getCode());
                operationLog.setOperationDescription(operationType.getName());
                operationLog.setOrderStatus(GovPublicCarOrderStatusEnum.COMPLETED.getCode());
                operationLog.setOrderStatusName(GovPublicCarOrderStatusEnum.COMPLETED.getName());
                operationLog.setOperatorCode(param.getLoginUserCode());
                operationLog.setOperatorName(param.getLoginUserName());
                operationLog.setOperatorMobile(param.getLoginUserMobile());
                govOrderOperationLogService.save(operationLog);
                //更新车辆&司机状态
                //车辆状态更新为无任务
                LambdaUpdateWrapper<GovVehicleBaseInfo> baseInfoLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
                baseInfoLambdaUpdateWrapper.eq(GovVehicleBaseInfo::getVehicleNo, order.getVehicleNo())
                        .set(GovVehicleBaseInfo::getWorkStatus, WorkStatusEnum.NO_TASK.getCode());
                govVehicleBaseInfoService.update(baseInfoLambdaUpdateWrapper);
                //释放司机状态
                releaseDriverStatus(order.getOrderNo());
                //社会用车，需要计算费用
                if (ObjectUtil.equals(order.getUseType(), GovPublicCarApplyTypeEnum.SOCIAL_RENT.getCode())) {
                    calculateFee(order.getOrderNo());
                }
            }
        });
    }

    private void buildAndUpdateAddress(GovPublicCarOrder order, GovPublicCarOrderVehicleInfo vehicleInfo) {
        List<GovGpsDevice> govGpsDevices = govGpsDeviceService.list(new LambdaQueryWrapper<GovGpsDevice>().eq(GovGpsDevice::getVehicleNo, vehicleInfo.getVehicleNo())
                .eq(GovGpsDevice::getBindStatus, GovGpsDeviceEnum.DeviceBindStatusEnum.BIND.getCode())
                .in(GovGpsDevice::getDeviceType, GovGpsDeviceEnum.DeviceTypeEnum.WIRED.getCode(), GovGpsDeviceEnum.DeviceTypeEnum.TBOX.getCode(), GovGpsDeviceEnum.DeviceTypeEnum.VIDEO.getCode()));
        if (CollectionUtils.isNotEmpty(govGpsDevices)) {
            List<String> deviceSysNos = govGpsDevices.stream().map(GovGpsDevice::getDeviceSysNo).collect(Collectors.toList());
            List<GovVehicleLocation> latestLocations = vehicleDeviceService.getLatestLocations(deviceSysNos);
            //并按时间倒叙
            List<GovVehicleLocation> latestLocationsFilter = latestLocations.stream()
                    .filter(s -> DateUtil.between(s.getTimestamp(), new Date(), DateUnit.HOUR) < 3)
                    .sorted(Comparator.comparing(GovVehicleLocation::getTimestamp).reversed()).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(latestLocationsFilter)) {
                GovVehicleLocation latestLocation = latestLocationsFilter.get(0);
                AddressResult addressResult = new AddressResult();
                try {
                    addressResult = baiduMapApiClient.pointToAddress(latestLocation.getLatBaidu(), latestLocation.getLngBaidu());
                } catch (Exception e) {
                    log.info("百度坐标转换地理位置失败，错误信息：{}", e.getMessage());
                }
                GovCityDic cityDic;
                if (ObjectUtil.isNotNull(addressResult)) {
                    //根据百度的citycode转化为mrcar自己的城市code
                    cityDic = govCityDicService.getOne(new LambdaQueryWrapper<GovCityDic>().eq(GovCityDic::getBaiduCode, addressResult.getCityCode()).last("limit 1"));
                    //查询是否已存在地址信息，如果没有，则新增，否则更新
                    long count = govPublicCarOrderAddressInfoService.count(new LambdaQueryWrapper<GovPublicCarOrderAddressInfo>().eq(GovPublicCarOrderAddressInfo::getOrderNo, order.getOrderNo()));
                    if (count == 0) {
                        //插入地址信息
                        GovPublicCarOrderAddressInfo orderAddress = new GovPublicCarOrderAddressInfo();
                        orderAddress.setOrderNo(order.getOrderNo());
                        orderAddress.setCompanyId(order.getCompanyId());
                        orderAddress.setActualDestinationLongLocation(addressResult.getAddress());
                        orderAddress.setActualDestinationLatitude(latestLocation.getLatBaidu());
                        orderAddress.setActualDestinationLongitude(latestLocation.getLngBaidu());
                        orderAddress.setActualDestinationShortLocation(addressResult.getAddress());
                        orderAddress.setApplyNo(order.getApplyNo());
                        orderAddress.setEndCityCode(cityDic.getCityCode().toString());
                        orderAddress.setEndCityName(cityDic.getCityName());
                        govPublicCarOrderAddressInfoService.save(orderAddress);
                    } else {
                        govPublicCarOrderAddressInfoService.update(new LambdaUpdateWrapper<GovPublicCarOrderAddressInfo>()
                                .eq(GovPublicCarOrderAddressInfo::getOrderNo, order.getOrderNo())
                                .set(GovPublicCarOrderAddressInfo::getActualDestinationLongLocation, addressResult.getAddress())
                                .set(GovPublicCarOrderAddressInfo::getActualDestinationLatitude, latestLocation.getLatBaidu())
                                .set(GovPublicCarOrderAddressInfo::getActualDestinationLongitude, latestLocation.getLngBaidu())
                                .set(GovPublicCarOrderAddressInfo::getActualDestinationShortLocation, addressResult.getAddress())
                                .set(GovPublicCarOrderAddressInfo::getEndCityCode, cityDic.getCityCode().toString())
                                .set(GovPublicCarOrderAddressInfo::getEndCityName, cityDic.getCityName()));
                    }
                }
            }
        }
    }

    private void calculateFee(String orderNo) {
        //获取订单实体
        GovPublicCarOrder order = govPublicCarOrderService.getOne(new LambdaQueryWrapper<GovPublicCarOrder>().eq(GovPublicCarOrder::getOrderNo, orderNo));

        //车辆信息
        GovPublicCarOrderVehicleInfo vehicleInfo = govPublicCarOrderVehicleInfoService.getOne(new LambdaQueryWrapper<GovPublicCarOrderVehicleInfo>().eq(GovPublicCarOrderVehicleInfo::getOrderNo, orderNo));

        //默认为分时租赁规则
        GovPublicCarRentTypeEnum rentTypeEnum = GovPublicCarRentTypeEnum.TIME_SHARE;
        Integer drivingType = GovPublicCarDrivingTypeEnum.SELF_DRIVE.getCode();
        //获取订单的申请单信息
        if (StringUtils.isNotEmpty(order.getApplyNo())) {
            GovPublicCarApply apply = govPublicCarApplyService.getOne(new LambdaQueryWrapper<GovPublicCarApply>().eq(GovPublicCarApply::getApplyNo, order.getApplyNo()));
            if (ObjectUtil.isNotNull(apply)) {
                rentTypeEnum = GovPublicCarRentTypeEnum.getEnumByCode(apply.getRentType());
                drivingType = apply.getDrivingType();
                if(Objects.equals(GovPublicCarDrivingTypeEnum.UNIT_DRIVE.getCode(),drivingType)){
                    drivingType = GovPublicCarDrivingTypeEnum.SELF_DRIVE.getCode();
                }
            }
        }

        Date orderStartTime = order.getOrderStartTime();
        Date orderEndTime = order.getOrderEndTime();
        BigDecimal totalFee = BigDecimal.ZERO;

        if (Objects.equals(rentTypeEnum, GovPublicCarRentTypeEnum.TIME_SHARE)) {
            //插入分时计费记录
            insertTimeShareFee(order, vehicleInfo, drivingType, TimeShareSourceTypeEnum.FINISH_TRIP.getCode());
            //统计费用
            List<GovSocTimeShareFee> list = govSocTimeShareFeeService.lambdaQuery().eq(GovSocTimeShareFee::getOrderNo, orderNo).list();
            if (CollectionUtils.isNotEmpty(list)) {
                totalFee = list.stream().map(GovSocTimeShareFee::getRent).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            }

        } else {
            Integer billingConfigurationVersionId = order.getBillingConfigurationVersionId();
            if (ObjectUtil.isNotNull(billingConfigurationVersionId)) {
                //查询快照费配置
                GovBillingConfigurationSnapshoot snapshoot = govBillingConfigurationSnapshootService.getById(billingConfigurationVersionId);
                if (ObjectUtil.isNotNull(snapshoot)) {
                    int days = calculateDays(orderStartTime, orderEndTime);
                    //计算租金
                    //自驾
                    if (ObjectUtil.equals(drivingType, GovPublicCarDrivingTypeEnum.SELF_DRIVE.getCode())) {
                        totalFee = new BigDecimal(days).multiply(snapshoot.getDailyRentWithSelf()).setScale(2, RoundingMode.HALF_UP);
                    } else {
                        totalFee = new BigDecimal(days).multiply(snapshoot.getDailyRentWithDriver()).setScale(2, RoundingMode.HALF_UP);
                    }
                }
            }
        }
        //插入订单费用
        GovSocVehicleFee fee = new GovSocVehicleFee();
        fee.setOrderNo(orderNo);
        fee.setTotalFee(totalFee);
        fee.setFeeTime(new Date());
        fee.setCompanyId(order.getCompanyId());
        fee.setCompanyName(order.getCompanyName());
        fee.setCreateName("system");
        fee.setUpdateName("system");
        govSocVehicleFeeService.save(fee);
        //插入订单明细
        GovSocVehicleFeeDetail detail = new GovSocVehicleFeeDetail();
        detail.setOrderNo(orderNo);
        detail.setFeeCode(GovPublicSocVehicleFeeItemEnum.RENT_FEE.getCode());
        detail.setFeeName(GovPublicSocVehicleFeeItemEnum.RENT_FEE.getName());
        detail.setFee(totalFee);
        govSocVehicleFeeDetailService.save(detail);
    }


    /**
     * 两个时间的天数
     */
    public static int calculateDays(Date date1, Date date2) {
        // 计算两个时间的毫秒差
        long diffInMillis = date2.getTime() - date1.getTime();

        // 将毫秒转换为小时（浮点数）
        double days = diffInMillis / 1000.0 / 3600.0 / 24;

        // 向上取整到最接近的整数小时
        return (int) Math.ceil(days);
    }

    /**
     * 释放司机状态
     */
    private void releaseDriverStatus(String orderNo) {
        //更新车辆任务状态
        //检查该订单的司机除了本订单是否还有其余订单处于未完成状态，如果无，则释放司机状态为空闲，如果还有，则不释放
        //获取本订单的司机信息
        LambdaQueryWrapper<GovPublicCarOrderUserInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(GovPublicCarOrderUserInfo::getOrderNo, orderNo);
        queryWrapper.eq(GovPublicCarOrderUserInfo::getUserType, GovPublicCarOrderUserTypeEnum.DRIVER.getCode());
        GovPublicCarOrderUserInfo govPublicCarOrderUserInfo = govPublicCarOrderUserInfoService.getOne(queryWrapper);
        if (govPublicCarOrderUserInfo == null) {
            return;
        }
        String driverCode = govPublicCarOrderUserInfo.getUserCode();
        Boolean checkDriverStatus = govPublicCarOrderUserInfoService.checkDriverStatus(driverCode, orderNo);
        //无其他单子占用，则释放司机状态
        if (!checkDriverStatus) {
            LambdaUpdateWrapper<GovDriver> updateWrapperDriver = new LambdaUpdateWrapper<>();
            updateWrapperDriver.eq(GovDriver::getUserCode, driverCode);
            updateWrapperDriver.set(GovDriver::getWorkingStatus, WorkStatusEnum.NO_TASK.getCode());
            govDriverService.update(updateWrapperDriver);
        }
    }

    /**
     * 占用司机状态
     */
    private void occupationDriverStatus(String orderNo) {
        //更新车辆任务状态
        //检查该订单的司机除了本订单是否还有其余订单处于未完成状态，如果无，则释放司机状态为空闲，如果还有，则不释放
        //获取本订单的司机信息
        LambdaQueryWrapper<GovPublicCarOrderUserInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(GovPublicCarOrderUserInfo::getOrderNo, orderNo);
        queryWrapper.eq(GovPublicCarOrderUserInfo::getUserType, GovPublicCarOrderUserTypeEnum.DRIVER.getCode());
        GovPublicCarOrderUserInfo govPublicCarOrderUserInfo = govPublicCarOrderUserInfoService.getOne(queryWrapper);
        if (govPublicCarOrderUserInfo == null) {
            return;
        }
        String driverCode = govPublicCarOrderUserInfo.getUserCode();
        LambdaUpdateWrapper<GovDriver> updateWrapperDriver = new LambdaUpdateWrapper<>();
        updateWrapperDriver.eq(GovDriver::getUserCode, driverCode);
        updateWrapperDriver.set(GovDriver::getWorkingStatus, WorkStatusEnum.TASK_ING.getCode());
        govDriverService.update(updateWrapperDriver);
    }
}
