package com.mrcar.gov.rest.service.asset.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.common.utils.JacksonUtils;
import com.alibaba.nacos.common.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.izu.framework.exception.ApiException;
import com.izu.framework.resp.InfoCode;
import com.mrcar.gov.asset.common.RelationStatusEnum;
import com.mrcar.gov.asset.domain.*;
import com.mrcar.gov.asset.service.*;
import com.mrcar.gov.base.service.SequenceGenerator;
import com.mrcar.gov.common.constant.PushMessageUtil;
import com.mrcar.gov.common.constant.asset.*;
import com.mrcar.gov.common.constant.business.GovMsgSceneEnum;
import com.mrcar.gov.common.constant.user.GovUserTypeEnum;
import com.mrcar.gov.common.dto.BaseDTO;
import com.mrcar.gov.common.dto.asset.maintenance.req.*;
import com.mrcar.gov.common.dto.workflow.enums.BpmProcessInstanceResultEnum;
import com.mrcar.gov.common.dto.workflow.enums.ModelEnum;
import com.mrcar.gov.common.dto.workflow.instance.ApplyStartDTO;
import com.mrcar.gov.common.dto.workflow.instance.ApplyStartSwitchDTO;
import com.mrcar.gov.common.dto.workflow.instance.BpmMessageSendApproveResultDTO;
import com.mrcar.gov.common.dto.workflow.instance.BpmProcessInstanceCancelReqDTO;
import com.mrcar.gov.common.service.api.MessageTrackingSender;
import com.mrcar.gov.common.util.EmojiUtils;
import com.mrcar.gov.rest.service.asset.MaintenanceOPTService;
import com.mrcar.gov.user.domain.*;
import com.mrcar.gov.user.service.*;
import com.mrcar.gov.workflow.service.impl.WorkflowApprovalService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * <AUTHOR>
 * @description:
 * @date 2025/1/10 9:23
 */
@Slf4j
@Service
public class MaintenanceOPTServiceImpl implements MaintenanceOPTService {

    @Resource
    private GovVehicleMaintenanceService govVehicleMaintenanceService;

    @Resource
    private GovVehicleBaseInfoService govVehiclebaseInfoService;

    @Resource
    private SequenceGenerator sequenceGenerator;

    @Resource
    private WorkflowApprovalService workflowApprovalService;

    @Resource
    private GovVehicleDriverRelationService govVehicleDriverRelationService;

    @Resource
    private GovUserService govUserService;

    @Resource
    private GovDriverService govDriverService;

    @Resource
    private GovOrgInfoService govOrgInfoService;

    @Resource
    private GovMaintenanceOperationRecordService govMaintenanceOperationRecordService;

    @Resource
    private GovMaintenanceFileInfoService govMaintenanceFileInfoService;

    @Resource
    private GovRepairQuotesService govRepairQuotesService;

    @Resource
    private GovRepairQuotesDetailService govRepairQuotesDetailService;

    @Resource
    private GovRepairQuotesLogService govRepairQuotesLogService;

    @Resource
    private GovCompanyService govCompanyService;
    @Autowired
    private MessageTrackingSender messageTrackingSender;

    @Autowired
    private GovStructService govStructService;

    /**
     * 维保 单号前缀
     */
    public static final String PUBLIC_MAINTENANCE_NO_PREFIX = "WB";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void driverCreateMaintenance(DriverMaintenanceCreateDTO param) {
        //校验故障主诉是否含有表情
        if (EmojiUtils.containsEmoji(param.getFaultDesc())) {
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "故障主诉中含有特殊字符");
        }
        Date now = new Date();
        //校验该车是否有处于维修状态的维保单，如果有，则直接阻断
        checkVehicle(param, param.getVehicleNo());
        //根据车架号查询车辆信息
        GovVehicleBaseInfo vehicleBaseInfo = govVehiclebaseInfoService.getOne(new LambdaQueryWrapper<GovVehicleBaseInfo>().eq(GovVehicleBaseInfo::getVehicleNo, param.getVehicleNo()));
        if (vehicleBaseInfo == null) {
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "该车不存在");
        }
        Boolean isActive = workFlowIsActive(param.getLoginCompanyId(), ModelEnum.BusinessTypeEnum.MAINTENANCE_APPROVAL.getCode(), param.getLoginUserBelongDeptCode());
        String maintenanceNo = sequenceGenerator.generate(new Date(), PUBLIC_MAINTENANCE_NO_PREFIX);
        //查询车辆绑定的司机
        GovVehicleDriverRelation driverRelation = govVehicleDriverRelationService.getOne(new LambdaQueryWrapper<GovVehicleDriverRelation>().eq(GovVehicleDriverRelation::getVehicleNo, param.getVehicleNo()).eq(GovVehicleDriverRelation::getRelationStatus, RelationStatusEnum.NORMAL.getCode()).last("limit 1"));
        //查询维修厂信息
        GovOrgInfo govOrgInfo = govOrgInfoService.getOne(new LambdaQueryWrapper<GovOrgInfo>().eq(GovOrgInfo::getOrgNo, param.getConstructionGarageNo()));
        if (ObjectUtil.isNull(govOrgInfo)) {
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "维修厂不存在");
        }
        //构造维保单信息并插入数据库
        saveMaintenanceInfo(param, maintenanceNo, isActive, vehicleBaseInfo, driverRelation, govOrgInfo, now);
        //保存维保照片url信息
        List<GovMaintenanceFileInfo> fileInfoList = new ArrayList<>();
        GovMaintenanceFileInfo fileInfo = new GovMaintenanceFileInfo();
        fileInfo.setFileType(MaintenanceFileEnum.PRE_REPAIR_PHOTO_DASHBOARD.getCode());
        fileInfo.setRelationCode(maintenanceNo);
        fileInfo.setFileName(MaintenanceFileEnum.PRE_REPAIR_PHOTO_DASHBOARD.getName());
        fileInfo.setFileUrl(param.getBeforeDashboardPhotoUrl());
        fileInfoList.add(fileInfo);
        GovMaintenanceFileInfo plate = new GovMaintenanceFileInfo();
        plate.setFileType(MaintenanceFileEnum.PRE_REPAIR_PHOTO_LICENSE_PLATE.getCode());
        plate.setRelationCode(maintenanceNo);
        plate.setFileName(MaintenanceFileEnum.PRE_REPAIR_PHOTO_LICENSE_PLATE.getName());
        plate.setFileUrl(param.getBeforeVehicleLicensePhotoUrl());
        fileInfoList.add(plate);
        govMaintenanceFileInfoService.saveBatch(fileInfoList);
        if (isActive) {
            //插入操作日志
            insertLog(param, maintenanceNo, MaintenanceOperationStatusEnum.REPAIR_APPROVAL_PENDING);
            //提交审批
            String approvalId = startApply(param, maintenanceNo, vehicleBaseInfo.getVehicleBelongDeptCode());
            //更新维保单上的审批id
            LambdaUpdateWrapper<GovVehicleMaintenance> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            lambdaUpdateWrapper.eq(GovVehicleMaintenance::getMaintenanceNo, maintenanceNo)
                    .set(GovVehicleMaintenance::getMaintenanceAuditApplyId, approvalId);
            govVehicleMaintenanceService.update(lambdaUpdateWrapper);
        } else {
            //插入操作日志
            insertLog(param, maintenanceNo, MaintenanceOperationStatusEnum.WAITING_FOR_FACTORY);
        }
    }

    private void checkVehicle(BaseDTO param, String vehicleNo) {
        LambdaQueryWrapper<GovVehicleMaintenance> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(GovVehicleMaintenance::getVehicleNo, vehicleNo)
                .eq(GovVehicleMaintenance::getCompanyId, param.getLoginCompanyId())
                .notIn(GovVehicleMaintenance::getMaintenanceStatus,
                        MaintenanceStatusEnum.REPAIR_APPROVAL_REJECTED.getCode(),
                        MaintenanceStatusEnum.VEHICLE_RECEIVED.getCode(),
                        MaintenanceStatusEnum.DISCARDED.getCode(),
                        MaintenanceStatusEnum.APPROVAL_WITHDRAWAL.getCode()
                );
        long count = govVehicleMaintenanceService.count(queryWrapper);
        if (count > 0) {
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "该车有处于维修状态的维保单，请先处理");
        }
    }

    private String startApply(DriverMaintenanceCreateDTO param, String maintenanceNo, String vehicleBelongDeptCode) {
        ApplyStartDTO applyStartDTO = new ApplyStartDTO();
        applyStartDTO.setBusinessType(ModelEnum.BusinessTypeEnum.MAINTENANCE_APPROVAL.getCode());
        applyStartDTO.setBusinessNo(maintenanceNo);
        applyStartDTO.setLoginCompanyId(param.getLoginCompanyId());
        applyStartDTO.setLoginCompanyName(param.getLoginCompanyName());
        applyStartDTO.setLoginUserId(param.getLoginUserId());
        applyStartDTO.setLoginUserName(param.getLoginUserName());
        applyStartDTO.setLoginDeptId(param.getLoginUserBelongDeptId());
        applyStartDTO.setLoginDeptName(param.getLoginUserBelongDeptName());
        Map<String, Object> variables = new HashMap<>();
        variables.put("applyRemark", param.getFaultDesc());
        variables.put("vehicleBelongDeptCode", vehicleBelongDeptCode);
        applyStartDTO.setVariables(JSON.toJSONString(variables));
        String approvalId = workflowApprovalService.startApproval(applyStartDTO);
        return approvalId;
    }

    private void saveMaintenanceInfo(DriverMaintenanceCreateDTO param, String maintenanceNo, Boolean isActive, GovVehicleBaseInfo vehicleBaseInfo, GovVehicleDriverRelation driverRelation, GovOrgInfo govOrgInfo, Date now) {
        GovVehicleMaintenance govVehicleMaintenance = new GovVehicleMaintenance();
        govVehicleMaintenance.setMaintenanceNo(maintenanceNo);
        govVehicleMaintenance.setMaintenanceType(MaintenanceTypeEnum.PRE_ORDER.getCode());
        if (isActive) {
            govVehicleMaintenance.setMaintenanceStatus(MaintenanceStatusEnum.REPAIR_APPROVAL_PENDING.getCode());
        } else {
            govVehicleMaintenance.setMaintenanceStatus(MaintenanceStatusEnum.WAITING_FOR_FACTORY.getCode());
        }
        govVehicleMaintenance.setCompanyId(param.getLoginCompanyId());
        govVehicleMaintenance.setCompanyName(param.getLoginCompanyName());
        govVehicleMaintenance.setCreatedCode(param.getLoginUserCode());
        govVehicleMaintenance.setCreatedName(param.getLoginUserName());
        govVehicleMaintenance.setCreatedPhone(param.getLoginUserMobile());
        govVehicleMaintenance.setVehicleNo(vehicleBaseInfo.getVehicleNo());
        govVehicleMaintenance.setVehicleLicense(vehicleBaseInfo.getVehicleLicense());
        govVehicleMaintenance.setVehicleVin(vehicleBaseInfo.getVehicleVin());
        govVehicleMaintenance.setVehicleBrandId(vehicleBaseInfo.getVehicleBrandId());
        govVehicleMaintenance.setVehicleBrandName(vehicleBaseInfo.getVehicleBrandName());
        govVehicleMaintenance.setVehicleSeriesId(vehicleBaseInfo.getVehicleSeriesId());
        govVehicleMaintenance.setVehicleSeriesName(vehicleBaseInfo.getVehicleSeriesName());
        govVehicleMaintenance.setVehicleBelongDeptCode(vehicleBaseInfo.getVehicleBelongDeptCode());
        govVehicleMaintenance.setVehicleBelongDeptName(vehicleBaseInfo.getVehicleBelongDeptName());
        govVehicleMaintenance.setVehicleUseDeptCode(vehicleBaseInfo.getVehicleUseDeptCode());
        govVehicleMaintenance.setVehicleUseDeptName(vehicleBaseInfo.getVehicleUseDeptName());
        govVehicleMaintenance.setVehicleUseStructCode(vehicleBaseInfo.getVehicleUseStructCode());
        govVehicleMaintenance.setVehicleUseStructName(vehicleBaseInfo.getVehicleUseStructName());
        govVehicleMaintenance.setManageCarType(vehicleBaseInfo.getManageCarType());
        if (ObjectUtil.isNotNull(driverRelation)) {
            //查询司机信息
            GovUser driverInfo = govUserService.getOne(new LambdaQueryWrapper<GovUser>().eq(GovUser::getUserCode, driverRelation.getUserCode()));
            if (ObjectUtil.isNotNull(driverInfo)) {
                govVehicleMaintenance.setDriverCode(driverInfo.getUserCode());
                govVehicleMaintenance.setDriverName(driverInfo.getUserName());
                govVehicleMaintenance.setDriverMobile(driverInfo.getMobile());
                //查询司机
                GovDriver driverLicense = govDriverService.getOne(new LambdaQueryWrapper<GovDriver>().eq(GovDriver::getUserCode, driverRelation.getUserCode()));
                if (ObjectUtil.isNotNull(driverLicense)) {
                    govVehicleMaintenance.setRegisterDate(driverLicense.getFirstPickupTime());
                }
            }
        }
        govVehicleMaintenance.setConstructionGarageType(ConstructionGarageTypeEnum.FIXED_POINT.getCode());
        govVehicleMaintenance.setConstructionGarageNo(govOrgInfo.getOrgNo());
        govVehicleMaintenance.setConstructionGarageName(govOrgInfo.getOrgName());
        govVehicleMaintenance.setConstructionGaragePhone(govOrgInfo.getServiceTelephone());
        govVehicleMaintenance.setConstructionGarageAddress(govOrgInfo.getOrgAddressDetail());
        govVehicleMaintenance.setConstructionGarageCityCode(govOrgInfo.getOrgCityCode());
        govVehicleMaintenance.setConstructionGarageCityName(govOrgInfo.getOrgCityName());
        govVehicleMaintenance.setInvoiceType(govOrgInfo.getInvoiceType());
        govVehicleMaintenance.setInvoiceOpenType(govOrgInfo.getInvoiceOpenType());
        govVehicleMaintenance.setInvoiceRate(govOrgInfo.getInvoiceRate());
        govVehicleMaintenance.setFaultDesc(param.getFaultDesc());
        govVehicleMaintenance.setSubmitterName(param.getSubmitterName());
        govVehicleMaintenance.setSubmitterPhone(param.getSubmitterPhone());
        govVehicleMaintenance.setArrivalOdometerReading(param.getArrivalOdometerReading());
        govVehicleMaintenance.setUpdatedCode(param.getLoginUserCode());
        govVehicleMaintenance.setUpdatedName(param.getLoginUserName());
        govVehicleMaintenanceService.save(govVehicleMaintenance);
    }

    private void insertLog(BaseDTO param, String maintenanceNo, MaintenanceOperationStatusEnum operationStatusEnum) {
        GovMaintenanceOperationRecord operationRecord = new GovMaintenanceOperationRecord();
        operationRecord.setMaintenanceNo(maintenanceNo);
        operationRecord.setOperationStatus(operationStatusEnum.getCode());
        operationRecord.setOperationUserCode(param.getLoginUserCode());
        operationRecord.setOperationUserName(param.getLoginUserName());
        operationRecord.setOperationUserPhone(param.getLoginUserMobile());
        govMaintenanceOperationRecordService.save(operationRecord);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void repairCreateMaintenance(RepairMaintenanceCreateDTO param) {
        if (!ObjectUtil.equal(param.getLoginUserType(), GovUserTypeEnum.REPAIR_STATION_EMPLOYEE.getCode())) {
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "当前登录用户不是维修厂员工，不能进行此操作");
        }
        Date now = new Date();
        //有维保单号（证明匹配到了有待到厂的维保单子）
        if (StringUtils.isNotBlank(param.getMaintenanceNo())) {
            //查询维保单实体
            GovVehicleMaintenance govVehicleMaintenance = govVehicleMaintenanceService.getOne(new LambdaQueryWrapper<GovVehicleMaintenance>().eq(GovVehicleMaintenance::getMaintenanceNo, param.getMaintenanceNo()));
            //如果当前维保单状态不属于待到厂状态，则报错
            if (!ObjectUtil.equal(MaintenanceOperationStatusEnum.WAITING_FOR_FACTORY.getCode(), govVehicleMaintenance.getMaintenanceStatus())) {
                throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "当前维保单状态不是待到厂状态，请勿进行确认到厂操作");
            }
            //更新维保单上的故障主诉&到厂信息&维修前
            LambdaUpdateWrapper<GovVehicleMaintenance> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(GovVehicleMaintenance::getMaintenanceNo, param.getMaintenanceNo())
                    .set(GovVehicleMaintenance::getFaultDesc, param.getFaultDesc())
                    .set(GovVehicleMaintenance::getArrivalOdometerReading, param.getArrivalOdometerReading())
                    .set(GovVehicleMaintenance::getMaintenanceStatus, MaintenanceOperationStatusEnum.ARRIVED_AT_FACTORY.getCode())
                    .set(GovVehicleMaintenance::getSubmitterName, param.getSubmitterName())
                    .set(GovVehicleMaintenance::getSubmitterPhone, param.getSubmitterPhone())
                    .set(GovVehicleMaintenance::getArrivalContactCode, param.getLoginUserCode())
                    .set(GovVehicleMaintenance::getArrivalContactName, param.getLoginUserName())
                    .set(GovVehicleMaintenance::getArrivalContactPhone, param.getLoginUserMobile())
                    .set(GovVehicleMaintenance::getArrivalContactTime, now)
                    .set(GovVehicleMaintenance::getSubmissionTime, now)
                    .set(GovVehicleMaintenance::getUpdatedCode, param.getLoginUserCode())
                    .set(GovVehicleMaintenance::getUpdatedName, param.getLoginUserName());
            govVehicleMaintenanceService.update(updateWrapper);
            //先清空照片内容
            govMaintenanceFileInfoService.remove(new LambdaQueryWrapper<GovMaintenanceFileInfo>()
                    .eq(GovMaintenanceFileInfo::getRelationCode, param.getMaintenanceNo())
                    .in(GovMaintenanceFileInfo::getFileType
                            , MaintenanceFileEnum.PRE_REPAIR_PHOTO_DASHBOARD.getCode()
                            , MaintenanceFileEnum.PRE_REPAIR_PHOTO_LICENSE_PLATE.getCode()));
        } else {
            //没有维保单号，则直接新增维保单
            //校验该车是否有处于维修状态的维保单，如果有，则直接阻断
            checkVehicle(param, param.getVehicleNo());
            //根据车架号查询车辆信息
            GovVehicleBaseInfo vehicleBaseInfo = govVehiclebaseInfoService.getOne(new LambdaQueryWrapper<GovVehicleBaseInfo>().eq(GovVehicleBaseInfo::getVehicleNo, param.getVehicleNo()));
            if (vehicleBaseInfo == null) {
                throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "该车不存在");
            }
            String maintenanceNo = sequenceGenerator.generate(new Date(), PUBLIC_MAINTENANCE_NO_PREFIX);
            //查询车辆绑定的司机
            GovVehicleDriverRelation driverRelation = govVehicleDriverRelationService.getOne(new LambdaQueryWrapper<GovVehicleDriverRelation>().eq(GovVehicleDriverRelation::getVehicleNo, param.getVehicleNo()).eq(GovVehicleDriverRelation::getRelationStatus, RelationStatusEnum.NORMAL.getCode()).last("limit 1"));
            //查询当前登录人的信息
            GovUser userInfo = govUserService.getOne(new LambdaQueryWrapper<GovUser>().eq(GovUser::getUserCode, param.getLoginUserCode()));
            String supplierServiceCode = userInfo.getSupplierServiceCode();
            //查询维修厂信息
            GovOrgInfo govOrgInfo = govOrgInfoService.getOne(new LambdaQueryWrapper<GovOrgInfo>().eq(GovOrgInfo::getOrgNo, supplierServiceCode));
            if (ObjectUtil.isNull(govOrgInfo)) {
                throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "维修厂不存在");
            }
            GovVehicleMaintenance govVehicleMaintenance = new GovVehicleMaintenance();
            govVehicleMaintenance.setMaintenanceNo(maintenanceNo);
            govVehicleMaintenance.setMaintenanceType(MaintenanceTypeEnum.PRE_ORDER.getCode());
            govVehicleMaintenance.setMaintenanceStatus(MaintenanceStatusEnum.ARRIVED_AT_FACTORY.getCode());
            govVehicleMaintenance.setCompanyId(param.getLoginCompanyId());
            govVehicleMaintenance.setCompanyName(param.getLoginCompanyName());
            govVehicleMaintenance.setCreatedCode(param.getLoginUserCode());
            govVehicleMaintenance.setCreatedName(param.getLoginUserName());
            govVehicleMaintenance.setCreatedPhone(param.getLoginUserMobile());
            govVehicleMaintenance.setVehicleNo(vehicleBaseInfo.getVehicleNo());
            govVehicleMaintenance.setVehicleLicense(vehicleBaseInfo.getVehicleLicense());
            govVehicleMaintenance.setVehicleVin(vehicleBaseInfo.getVehicleVin());
            govVehicleMaintenance.setVehicleBrandId(vehicleBaseInfo.getVehicleBrandId());
            govVehicleMaintenance.setVehicleBrandName(vehicleBaseInfo.getVehicleBrandName());
            govVehicleMaintenance.setVehicleSeriesId(vehicleBaseInfo.getVehicleSeriesId());
            govVehicleMaintenance.setVehicleSeriesName(vehicleBaseInfo.getVehicleSeriesName());
            govVehicleMaintenance.setVehicleBelongDeptCode(vehicleBaseInfo.getVehicleBelongDeptCode());
            govVehicleMaintenance.setVehicleBelongDeptName(vehicleBaseInfo.getVehicleBelongDeptName());
            govVehicleMaintenance.setVehicleUseDeptCode(vehicleBaseInfo.getVehicleUseDeptCode());
            govVehicleMaintenance.setVehicleUseDeptName(vehicleBaseInfo.getVehicleUseDeptName());
            govVehicleMaintenance.setVehicleUseStructCode(vehicleBaseInfo.getVehicleUseStructCode());
            govVehicleMaintenance.setVehicleUseStructName(vehicleBaseInfo.getVehicleUseStructName());
            govVehicleMaintenance.setManageCarType(vehicleBaseInfo.getManageCarType());
            if (ObjectUtil.isNotNull(driverRelation)) {
                //查询司机信息
                GovUser driverInfo = govUserService.getOne(new LambdaQueryWrapper<GovUser>().eq(GovUser::getUserCode, driverRelation.getUserCode()));
                if (ObjectUtil.isNotNull(driverInfo)) {
                    govVehicleMaintenance.setDriverCode(driverInfo.getUserCode());
                    govVehicleMaintenance.setDriverName(driverInfo.getUserName());
                    govVehicleMaintenance.setDriverMobile(driverInfo.getMobile());
                    //查询司机
                    GovDriver driverLicense = govDriverService.getOne(new LambdaQueryWrapper<GovDriver>().eq(GovDriver::getUserCode, driverRelation.getUserCode()));
                    if (ObjectUtil.isNotNull(driverLicense)) {
                        govVehicleMaintenance.setRegisterDate(driverLicense.getFirstPickupTime());
                    }
                }
            }
            govVehicleMaintenance.setConstructionGarageType(ConstructionGarageTypeEnum.FIXED_POINT.getCode());
            govVehicleMaintenance.setConstructionGarageNo(govOrgInfo.getOrgNo());
            govVehicleMaintenance.setConstructionGarageName(govOrgInfo.getOrgName());
            govVehicleMaintenance.setConstructionGaragePhone(govOrgInfo.getServiceTelephone());
            govVehicleMaintenance.setConstructionGarageAddress(govOrgInfo.getOrgAddressDetail());
            govVehicleMaintenance.setConstructionGarageCityCode(govOrgInfo.getOrgCityCode());
            govVehicleMaintenance.setConstructionGarageCityName(govOrgInfo.getOrgCityName());
            govVehicleMaintenance.setInvoiceType(govOrgInfo.getInvoiceType());
            govVehicleMaintenance.setInvoiceOpenType(govOrgInfo.getInvoiceOpenType());
            govVehicleMaintenance.setInvoiceRate(govOrgInfo.getInvoiceRate());
            govVehicleMaintenance.setFaultDesc(param.getFaultDesc());
            govVehicleMaintenance.setSubmitterName(param.getSubmitterName());
            govVehicleMaintenance.setSubmitterPhone(param.getSubmitterPhone());
            govVehicleMaintenance.setSubmissionTime(now);
            govVehicleMaintenance.setArrivalContactTime(now);
            govVehicleMaintenance.setArrivalOdometerReading(param.getArrivalOdometerReading());
            govVehicleMaintenance.setUpdatedCode(param.getLoginUserCode());
            govVehicleMaintenance.setUpdatedName(param.getLoginUserName());
            govVehicleMaintenance.setArrivalContactCode(param.getLoginUserCode());
            govVehicleMaintenance.setArrivalContactName(param.getLoginUserName());
            govVehicleMaintenance.setArrivalContactPhone(param.getLoginUserMobile());
            govVehicleMaintenanceService.save(govVehicleMaintenance);
            param.setMaintenanceNo(maintenanceNo);

        }
        //插入日志已到厂
        insertLog(param, param.getMaintenanceNo(), MaintenanceOperationStatusEnum.ARRIVED_AT_FACTORY);
        //保存维保照片url信息
        List<GovMaintenanceFileInfo> fileInfoList = new ArrayList<>();
        GovMaintenanceFileInfo fileInfo = new GovMaintenanceFileInfo();
        fileInfo.setFileType(MaintenanceFileEnum.PRE_REPAIR_PHOTO_DASHBOARD.getCode());
        fileInfo.setRelationCode(param.getMaintenanceNo());
        fileInfo.setFileName(MaintenanceFileEnum.PRE_REPAIR_PHOTO_DASHBOARD.getName());
        fileInfo.setFileUrl(param.getBeforeDashboardPhotoUrl());
        fileInfoList.add(fileInfo);
        GovMaintenanceFileInfo plate = new GovMaintenanceFileInfo();
        plate.setFileType(MaintenanceFileEnum.PRE_REPAIR_PHOTO_LICENSE_PLATE.getCode());
        plate.setRelationCode(param.getMaintenanceNo());
        plate.setFileName(MaintenanceFileEnum.PRE_REPAIR_PHOTO_LICENSE_PLATE.getName());
        plate.setFileUrl(param.getBeforeVehicleLicensePhotoUrl());
        fileInfoList.add(plate);
        GovMaintenanceFileInfo signature = new GovMaintenanceFileInfo();
        signature.setFileType(MaintenanceFileEnum.HANDWRITTEN_SIGNATURE_BY_DELIVERY_PERSON.getCode());
        signature.setRelationCode(param.getMaintenanceNo());
        signature.setFileName(MaintenanceFileEnum.HANDWRITTEN_SIGNATURE_BY_DELIVERY_PERSON.getName());
        signature.setFileUrl(param.getSubmitterSignature());
        fileInfoList.add(signature);
        govMaintenanceFileInfoService.saveBatch(fileInfoList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void abandonedMaintenance(MaintenanceNoDTO param) {
        Date now = new Date();
        //查询维保单
        GovVehicleMaintenance govVehicleMaintenance = govVehicleMaintenanceService.getOne(new LambdaQueryWrapper<GovVehicleMaintenance>().eq(GovVehicleMaintenance::getMaintenanceNo, param.getMaintenanceNo()));
        //只有单子为已到厂或者维修审批驳回或者维修中或者增项审批驳回这几种状态才可以进行废除
        if (!ObjectUtil.equals(MaintenanceStatusEnum.ARRIVED_AT_FACTORY.getCode(), govVehicleMaintenance.getMaintenanceStatus())
                && !ObjectUtil.equals(MaintenanceStatusEnum.QUOTE_APPROVAL_REJECTED.getCode(), govVehicleMaintenance.getMaintenanceStatus())
                && !ObjectUtil.equals(MaintenanceStatusEnum.UNDER_REPAIR.getCode(), govVehicleMaintenance.getMaintenanceStatus())
                && !ObjectUtil.equals(MaintenanceStatusEnum.ADDITIONAL_ITEM_APPROVAL_REJECTED.getCode(), govVehicleMaintenance.getMaintenanceStatus())) {
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "当前维保单状态不支持废弃操作");
        }
        //更新订单为废弃
        LambdaUpdateWrapper<GovVehicleMaintenance> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(GovVehicleMaintenance::getMaintenanceNo, param.getMaintenanceNo())
                .set(GovVehicleMaintenance::getMaintenanceStatus, MaintenanceStatusEnum.DISCARDED.getCode())
                .set(GovVehicleMaintenance::getUpdateTime, now)
                .set(GovVehicleMaintenance::getUpdatedCode, param.getLoginUserCode())
                .set(GovVehicleMaintenance::getUpdatedName, param.getLoginUserName());
        govVehicleMaintenanceService.update(updateWrapper);
        //插入操作日志
        insertLog(param, param.getMaintenanceNo(), MaintenanceOperationStatusEnum.DISCARDED);
    }

    @Override
    public void withdrawMaintenance(MaintenanceNoDTO param) {
        //查询维保单
        GovVehicleMaintenance govVehicleMaintenance = govVehicleMaintenanceService.getOne(new LambdaQueryWrapper<GovVehicleMaintenance>().eq(GovVehicleMaintenance::getMaintenanceNo, param.getMaintenanceNo()));
        //判度维保单状态
        if (!ObjectUtil.equals(MaintenanceStatusEnum.REPAIR_APPROVAL_PENDING.getCode(), govVehicleMaintenance.getMaintenanceStatus())) {
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "当前维保单状态不支持撤回操作");
        }
        //判度点击撤回审批得人是不是发起人
        if (!ObjectUtil.equals(param.getLoginUserCode(), govVehicleMaintenance.getCreatedCode())) {
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "仅发起人可操作撤回");
        }
        Date now = new Date();
        //更新订单为废弃
        LambdaUpdateWrapper<GovVehicleMaintenance> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(GovVehicleMaintenance::getMaintenanceNo, param.getMaintenanceNo())
                .set(GovVehicleMaintenance::getMaintenanceStatus, MaintenanceStatusEnum.APPROVAL_WITHDRAWAL.getCode())
                .set(GovVehicleMaintenance::getUpdateTime, now)
                .set(GovVehicleMaintenance::getUpdatedCode, param.getLoginUserCode())
                .set(GovVehicleMaintenance::getUpdatedName, param.getLoginUserName());
        govVehicleMaintenanceService.update(updateWrapper);
        //插入操作日志
        insertLog(param, param.getMaintenanceNo(), MaintenanceOperationStatusEnum.APPROVAL_WITHDRAWAL);
        //撤回审批流
        BpmProcessInstanceCancelReqDTO reqDTO = new BpmProcessInstanceCancelReqDTO();
        reqDTO.setId(govVehicleMaintenance.getMaintenanceAuditApplyId());
        reqDTO.setReason("发起人撤回审批");
        reqDTO.setIsBusiness(Boolean.TRUE);
        reqDTO.setLoginUserId(param.getLoginUserId());
        reqDTO.setLoginUserCode(param.getLoginUserCode());
        reqDTO.setLoginUserName(param.getLoginUserName());
        reqDTO.setLoginCompanyId(param.getLoginCompanyId());
        reqDTO.setLoginCompanyName(param.getLoginCompanyName());
        workflowApprovalService.cancelApproval(reqDTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitQuote(SubmitQuoteDTO param) {
        //查询维保单
        GovVehicleMaintenance govVehicleMaintenance = govVehicleMaintenanceService.getOne(new LambdaQueryWrapper<GovVehicleMaintenance>().eq(GovVehicleMaintenance::getMaintenanceNo, param.getMaintenanceNo()));
        Integer maintenanceStatus = govVehicleMaintenance.getMaintenanceStatus();
        MaintenanceStatusEnum maintenanceStatusEnum = MaintenanceStatusEnum.getEnum(maintenanceStatus);
        //查看审批开关是否打开
        Boolean isActive = workFlowIsActive(param.getLoginCompanyId(), ModelEnum.BusinessTypeEnum.QUOTATION_APPROVAL.getCode(), govVehicleMaintenance.getVehicleBelongDeptCode());
        switch (maintenanceStatusEnum) {
            //当前维保状态为已到厂->提交报价操作->更新维保单状态为报价审批中
            case ARRIVED_AT_FACTORY:
                dealSubmitAtFactory(param, govVehicleMaintenance, isActive);
                //插入日志
                insertLog(param, param.getMaintenanceNo(), MaintenanceOperationStatusEnum.QUOTE_APPROVAL_PENDING);
                break;
            //当前维保状态为报价审批驳回->提交报价操作->更新维保单状态为报价审批中
            case QUOTE_APPROVAL_REJECTED:
                dealAllSubmit(param, govVehicleMaintenance, isActive, MaintenanceStatusEnum.QUOTE_APPROVAL_PENDING, 0);
                //插入日志
                insertLog(param, param.getMaintenanceNo(), MaintenanceOperationStatusEnum.QUOTE_APPROVAL_PENDING);
                break;
            //当前审批状态为维修中->提交报价操作->更新维保单状态为增项审批中
            case UNDER_REPAIR:
                dealAllSubmit(param, govVehicleMaintenance, isActive, MaintenanceStatusEnum.ADDITIONAL_ITEM_APPROVAL_PENDING, 1);
                //插入日志
                insertLog(param, param.getMaintenanceNo(), MaintenanceOperationStatusEnum.ADDITIONAL_ITEM_APPROVAL_PENDING);
                break;
            //当前审批状态为增项审批驳回->提交报价操作->更新维保单状态为增项审批中
            case ADDITIONAL_ITEM_APPROVAL_REJECTED:
                dealAllSubmit(param, govVehicleMaintenance, isActive, MaintenanceStatusEnum.ADDITIONAL_ITEM_APPROVAL_PENDING, 1);
                //插入日志
                insertLog(param, param.getMaintenanceNo(), MaintenanceOperationStatusEnum.ADDITIONAL_ITEM_APPROVAL_PENDING);
                break;
            default:
                throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "当前维保单状态不支持提交报价操作");
        }
        //保存报价依据照片和车辆视频
        //先删除之前的相关照片和视频
        govMaintenanceFileInfoService.remove(new LambdaQueryWrapper<GovMaintenanceFileInfo>()
                .eq(GovMaintenanceFileInfo::getRelationCode, param.getMaintenanceNo())
                .in(GovMaintenanceFileInfo::getFileType, Arrays.asList(MaintenanceFileEnum.QUOTATION_BASIS_PHOTO.getCode(), MaintenanceFileEnum.QUOTATION_BASIS_VIDEO.getCode())));
        List<GovMaintenanceFileInfo> fileInfoList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(param.getQuoteBaseImgUrl())) {
            param.getQuoteBaseImgUrl().forEach(url -> {
                GovMaintenanceFileInfo fileInfo = new GovMaintenanceFileInfo();
                fileInfo.setFileType(MaintenanceFileEnum.QUOTATION_BASIS_PHOTO.getCode());
                fileInfo.setRelationCode(param.getMaintenanceNo());
                fileInfo.setFileName(MaintenanceFileEnum.QUOTATION_BASIS_PHOTO.getName());
                fileInfo.setFileUrl(url);
                fileInfoList.add(fileInfo);
            });
        }
        if (CollectionUtil.isNotEmpty(param.getVehicleVideoUrl())) {
            param.getVehicleVideoUrl().forEach(url -> {
                GovMaintenanceFileInfo fileInfo = new GovMaintenanceFileInfo();
                fileInfo.setFileType(MaintenanceFileEnum.QUOTATION_BASIS_VIDEO.getCode());
                fileInfo.setRelationCode(param.getMaintenanceNo());
                fileInfo.setFileName(MaintenanceFileEnum.QUOTATION_BASIS_VIDEO.getName());
                fileInfo.setFileUrl(url);
                fileInfoList.add(fileInfo);
            });
        }
        if (CollectionUtil.isNotEmpty(fileInfoList)) {
            govMaintenanceFileInfoService.saveBatch(fileInfoList);
        }
    }

    private void dealAllSubmit(SubmitQuoteDTO param, GovVehicleMaintenance govVehicleMaintenance, Boolean isActive, MaintenanceStatusEnum maintenanceStatusEnum, int approveType) {
        //先删除报价单
        govRepairQuotesService.remove(new LambdaQueryWrapper<GovRepairQuotes>().eq(GovRepairQuotes::getMaintenanceNo, govVehicleMaintenance.getMaintenanceNo()));
        GovRepairQuotes govRepairQuotes = new GovRepairQuotes();
        govRepairQuotes.setMaintenanceNo(govVehicleMaintenance.getMaintenanceNo());
        govRepairQuotes.setShopFaultDesc(param.getShopFaultDesc());
        govRepairQuotes.setQuotedByCode(param.getLoginUserCode());
        govRepairQuotes.setQuotedByName(param.getLoginUserName());
        govRepairQuotes.setQuotedByPhone(param.getLoginUserMobile());
        govRepairQuotesService.save(govRepairQuotes);
        //查询保存的报价单id
        Long quotedId = govRepairQuotesService.getOne(new LambdaQueryWrapper<GovRepairQuotes>().eq(GovRepairQuotes::getMaintenanceNo, govVehicleMaintenance.getMaintenanceNo())).getQuotedId();
        //删除明细
        govRepairQuotesDetailService.remove(new LambdaQueryWrapper<GovRepairQuotesDetail>().eq(GovRepairQuotesDetail::getQuotedId, quotedId));
        List<QuoteDetail> quoteDetailList = param.getQuoteDetailList();
        List<GovRepairQuotesDetail> govRepairQuotesDetailList = new ArrayList<>();
        for (QuoteDetail quoteDetail : quoteDetailList) {
            GovRepairQuotesDetail govRepairQuotesDetail = new GovRepairQuotesDetail();
            govRepairQuotesDetail.setQuotedId(quotedId);
            govRepairQuotesDetail.setChargeCategory(quoteDetail.getChargeCategory().intValue());
            govRepairQuotesDetail.setRepairName(quoteDetail.getRepairName());
            govRepairQuotesDetail.setUnitPrice(quoteDetail.getUnitPrice());
            govRepairQuotesDetail.setQuantity(quoteDetail.getQuantity());
            govRepairQuotesDetail.setTotalPrice(quoteDetail.getTotalPrice());
            // 计算不含税价
            BigDecimal taxInclusiveRate = BigDecimal.ONE.add(govVehicleMaintenance.getInvoiceRate()); // 1 + 税率
            BigDecimal taxExclusivePrice = quoteDetail.getTotalPrice().divide(taxInclusiveRate, 2, RoundingMode.HALF_UP); // 保留两位小数，四舍五入
            govRepairQuotesDetail.setActualCost(taxExclusivePrice);
            govRepairQuotesDetailList.add(govRepairQuotesDetail);
        }
        govRepairQuotesDetailService.saveBatch(govRepairQuotesDetailList);
        //更新维保单状态
        LambdaUpdateWrapper<GovVehicleMaintenance> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(GovVehicleMaintenance::getMaintenanceNo, param.getMaintenanceNo())
                .set(GovVehicleMaintenance::getUpdateTime, new Date())
                .set(GovVehicleMaintenance::getUpdatedCode, param.getLoginUserCode())
                .set(GovVehicleMaintenance::getUpdatedName, param.getLoginUserName());
        //保存维修报价日志
        GovRepairQuotesLog govRepairQuotesLog = new GovRepairQuotesLog();
        govRepairQuotesLog.setMaintenanceNo(govVehicleMaintenance.getMaintenanceNo());
        govRepairQuotesLog.setQuotedByCode(param.getLoginUserCode());
        govRepairQuotesLog.setQuotedByName(param.getLoginUserName());
        govRepairQuotesLog.setQuotedByPhone(param.getLoginUserMobile());
        govRepairQuotesLog.setQuotedContent(JacksonUtils.toJson(govRepairQuotesDetailList));
        govRepairQuotesLog.setApproveType(approveType);
        if (isActive) {
            //提交审批
            ApplyStartDTO applyStartDTO = new ApplyStartDTO();
            applyStartDTO.setBusinessType(ModelEnum.BusinessTypeEnum.QUOTATION_APPROVAL.getCode());
            applyStartDTO.setBusinessNo(govVehicleMaintenance.getMaintenanceNo());
            applyStartDTO.setLoginCompanyId(param.getLoginCompanyId());
            applyStartDTO.setLoginCompanyName(param.getLoginCompanyName());
            applyStartDTO.setLoginUserId(param.getLoginUserId());
            applyStartDTO.setLoginUserName(param.getLoginUserName() + "（服务机构人员）");
            //根据部门code查询部门id
            LambdaQueryWrapper<GovStruct> govStructLambdaQueryWrapper = new LambdaQueryWrapper<>();
            govStructLambdaQueryWrapper.eq(GovStruct::getStructCode, govVehicleMaintenance.getVehicleBelongDeptCode());
            GovStruct govStruct = govStructService.getOne(govStructLambdaQueryWrapper);
            if (govStruct != null) {
                applyStartDTO.setLoginDeptId(govStruct.getId());
            }
            applyStartDTO.setLoginDeptName(govVehicleMaintenance.getVehicleBelongDeptName());
            Map<String, Object> variables = new HashMap<>();
            variables.put("applyRemark", param.getShopFaultDesc());
            variables.put("vehicleBelongDeptCode", govVehicleMaintenance.getVehicleBelongDeptCode());
            applyStartDTO.setVariables(JSON.toJSONString(variables));
            String approvalId = workflowApprovalService.startApproval(applyStartDTO);
            govRepairQuotesLog.setAuditApplyId(approvalId);
            updateWrapper.set(GovVehicleMaintenance::getMaintenanceStatus, maintenanceStatusEnum.getCode());
        } else {
            updateWrapper.set(GovVehicleMaintenance::getMaintenanceStatus, MaintenanceStatusEnum.UNDER_REPAIR.getCode());
        }
        govVehicleMaintenanceService.update(updateWrapper);
        govRepairQuotesLogService.save(govRepairQuotesLog);
    }

    private void dealSubmitAtFactory(SubmitQuoteDTO param, GovVehicleMaintenance govVehicleMaintenance, Boolean isActive) {
        GovRepairQuotes govRepairQuotes = new GovRepairQuotes();
        govRepairQuotes.setMaintenanceNo(govVehicleMaintenance.getMaintenanceNo());
        govRepairQuotes.setShopFaultDesc(param.getShopFaultDesc());
        govRepairQuotes.setQuotedByCode(param.getLoginUserCode());
        govRepairQuotes.setQuotedByName(param.getLoginUserName());
        govRepairQuotes.setQuotedByPhone(param.getLoginUserMobile());
        govRepairQuotesService.save(govRepairQuotes);
        //查询保存的报价单id
        Long quotedId = govRepairQuotesService.getOne(new LambdaQueryWrapper<GovRepairQuotes>().eq(GovRepairQuotes::getMaintenanceNo, govVehicleMaintenance.getMaintenanceNo())).getQuotedId();
        List<QuoteDetail> quoteDetailList = param.getQuoteDetailList();
        List<GovRepairQuotesDetail> govRepairQuotesDetailList = new ArrayList<>();
        for (QuoteDetail quoteDetail : quoteDetailList) {
            GovRepairQuotesDetail govRepairQuotesDetail = new GovRepairQuotesDetail();
            govRepairQuotesDetail.setQuotedId(quotedId);
            govRepairQuotesDetail.setChargeCategory(quoteDetail.getChargeCategory().intValue());
            govRepairQuotesDetail.setRepairName(quoteDetail.getRepairName());
            govRepairQuotesDetail.setUnitPrice(quoteDetail.getUnitPrice());
            govRepairQuotesDetail.setQuantity(quoteDetail.getQuantity());
            govRepairQuotesDetail.setTotalPrice(quoteDetail.getTotalPrice());
            // 计算不含税价
            BigDecimal taxInclusiveRate = BigDecimal.ONE.add(govVehicleMaintenance.getInvoiceRate()); // 1 + 税率
            BigDecimal taxExclusivePrice = quoteDetail.getTotalPrice().divide(taxInclusiveRate, 2, RoundingMode.HALF_UP); // 保留两位小数，四舍五入
            govRepairQuotesDetail.setActualCost(taxExclusivePrice);
            govRepairQuotesDetailList.add(govRepairQuotesDetail);
        }
        govRepairQuotesDetailService.saveBatch(govRepairQuotesDetailList);
        //更新维保单状态
        LambdaUpdateWrapper<GovVehicleMaintenance> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(GovVehicleMaintenance::getMaintenanceNo, param.getMaintenanceNo())
                .set(GovVehicleMaintenance::getUpdateTime, new Date())
                .set(GovVehicleMaintenance::getUpdatedCode, param.getLoginUserCode())
                .set(GovVehicleMaintenance::getUpdatedName, param.getLoginUserName());
        //保存维修报价日志
        GovRepairQuotesLog govRepairQuotesLog = new GovRepairQuotesLog();
        govRepairQuotesLog.setMaintenanceNo(govVehicleMaintenance.getMaintenanceNo());
        govRepairQuotesLog.setQuotedByCode(param.getLoginUserCode());
        govRepairQuotesLog.setQuotedByName(param.getLoginUserName());
        govRepairQuotesLog.setQuotedByPhone(param.getLoginUserMobile());
        govRepairQuotesLog.setQuotedContent(JacksonUtils.toJson(govRepairQuotesDetailList));
        govRepairQuotesLog.setApproveType(0);
        if (isActive) {
            //提交审批
            ApplyStartDTO applyStartDTO = new ApplyStartDTO();
            applyStartDTO.setBusinessType(ModelEnum.BusinessTypeEnum.QUOTATION_APPROVAL.getCode());
            applyStartDTO.setBusinessNo(govVehicleMaintenance.getMaintenanceNo());
            applyStartDTO.setLoginCompanyId(param.getLoginCompanyId());
            applyStartDTO.setLoginCompanyName(param.getLoginCompanyName());
            applyStartDTO.setLoginUserId(param.getLoginUserId());
            applyStartDTO.setLoginUserName(param.getLoginUserName() + "（服务机构人员）");
            //根据部门code查询部门id
            LambdaQueryWrapper<GovStruct> govStructLambdaQueryWrapper = new LambdaQueryWrapper<>();
            govStructLambdaQueryWrapper.eq(GovStruct::getStructCode, govVehicleMaintenance.getVehicleBelongDeptCode());
            GovStruct govStruct = govStructService.getOne(govStructLambdaQueryWrapper);
            if (govStruct != null) {
                applyStartDTO.setLoginDeptId(govStruct.getId());
            }
            applyStartDTO.setLoginDeptName(govVehicleMaintenance.getVehicleBelongDeptName());
            Map<String, Object> variables = new HashMap<>();
            variables.put("applyRemark", param.getShopFaultDesc());
            variables.put("vehicleBelongDeptCode", govVehicleMaintenance.getVehicleBelongDeptCode());
            applyStartDTO.setVariables(JSON.toJSONString(variables));
            String approvalId = workflowApprovalService.startApproval(applyStartDTO);
            govRepairQuotesLog.setAuditApplyId(approvalId);
            updateWrapper.set(GovVehicleMaintenance::getMaintenanceStatus, MaintenanceStatusEnum.QUOTE_APPROVAL_PENDING.getCode());
        } else {
            updateWrapper.set(GovVehicleMaintenance::getMaintenanceStatus, MaintenanceStatusEnum.UNDER_REPAIR.getCode());
        }
        govVehicleMaintenanceService.update(updateWrapper);
        govRepairQuotesLogService.save(govRepairQuotesLog);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void finishMaintenance(MaintenanceNoDTO param) {
        GovVehicleMaintenance govVehicleMaintenance = govVehicleMaintenanceService.getOne(new LambdaQueryWrapper<GovVehicleMaintenance>().eq(GovVehicleMaintenance::getMaintenanceNo, param.getMaintenanceNo()));

        if (!MaintenanceStatusEnum.UNDER_REPAIR.getCode().equals(govVehicleMaintenance.getMaintenanceStatus())) {
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "当前维保单不是维修中状态，不可点击完成维修");
        }
        //修改维保单状态为完成维修
        LambdaUpdateWrapper<GovVehicleMaintenance> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(GovVehicleMaintenance::getMaintenanceNo, param.getMaintenanceNo())
                .set(GovVehicleMaintenance::getMaintenanceStatus, MaintenanceStatusEnum.COMPLETED.getCode())
                .set(GovVehicleMaintenance::getUpdateTime, new Date())
                .set(GovVehicleMaintenance::getUpdatedCode, param.getLoginUserCode())
                .set(GovVehicleMaintenance::getUpdatedName, param.getLoginUserName());
        govVehicleMaintenanceService.update(updateWrapper);
        //插入日志
        insertLog(param, param.getMaintenanceNo(), MaintenanceOperationStatusEnum.COMPLETED);
        //发送站内信
        try {
            pushMessageForDriver(govVehicleMaintenance);
        } catch (Exception e) {
            log.error("发送消息异常", e);
        }
    }

    private void pushMessageForDriver(GovVehicleMaintenance govVehicleMaintenance) {
        String driverCode = govVehicleMaintenance.getDriverCode();
        if (StringUtils.isNotEmpty(driverCode)) {
            MessageTrackingSender.MessageTrackingParam pushParam = new MessageTrackingSender.MessageTrackingParam();
            pushParam.setCompanyId(govVehicleMaintenance.getCompanyId());
            pushParam.setScene(GovMsgSceneEnum.MAINTENANCE_FINISH);
            pushParam.setReceiverCodeSet(new HashSet<>(Collections.singletonList(driverCode)));
            Map<String, String> extendParamMap = new HashMap<>();
            extendParamMap.put(PushMessageUtil.MSG_PARAM_APPLY_VEHICLE_LICENSE, govVehicleMaintenance.getVehicleLicense());
            extendParamMap.put(PushMessageUtil.MSG_PARAM_ORG_NAME, govVehicleMaintenance.getConstructionGarageName());
            pushParam.setExtendParamMap(extendParamMap);
            messageTrackingSender.sendMessageTracking(pushParam);
        }
        String submitterPhone = govVehicleMaintenance.getSubmitterPhone();
        if (StringUtils.isNotEmpty(submitterPhone)) {
            MessageTrackingSender.MessageTrackingParam pushParam = new MessageTrackingSender.MessageTrackingParam();
            pushParam.setCompanyId(govVehicleMaintenance.getCompanyId());
            pushParam.setScene(GovMsgSceneEnum.MAINTENANCE_FINISH);
            pushParam.setMobile(new HashSet<>(Collections.singletonList(submitterPhone)));
            Map<String, String> extendParamMap = new HashMap<>();
            extendParamMap.put(PushMessageUtil.MSG_PARAM_APPLY_VEHICLE_LICENSE, govVehicleMaintenance.getVehicleLicense());
            extendParamMap.put(PushMessageUtil.MSG_PARAM_ORG_NAME, govVehicleMaintenance.getConstructionGarageName());
            pushParam.setExtendParamMap(extendParamMap);
            messageTrackingSender.sendMessageTracking(pushParam);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void outFactory(OutFactoryDTO param) {
        GovVehicleMaintenance govVehicleMaintenance = govVehicleMaintenanceService.getOne(new LambdaQueryWrapper<GovVehicleMaintenance>().eq(GovVehicleMaintenance::getMaintenanceNo, param.getMaintenanceNo()));

        if (!MaintenanceStatusEnum.COMPLETED.getCode().equals(govVehicleMaintenance.getMaintenanceStatus())) {
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "当前维保单不是已竣工状态，不可点击确认出厂");
        }
        //更新维保单状态
        LambdaUpdateWrapper<GovVehicleMaintenance> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(GovVehicleMaintenance::getMaintenanceNo, param.getMaintenanceNo())
                .set(GovVehicleMaintenance::getMaintenanceStatus, MaintenanceStatusEnum.VEHICLE_RECEIVED.getCode())
                .set(GovVehicleMaintenance::getUpdateTime, new Date())
                .set(GovVehicleMaintenance::getDepartureContactName, param.getDepartureContactName())
                .set(GovVehicleMaintenance::getDepartureContactPhone, param.getDepartureContactPhone())
                .set(GovVehicleMaintenance::getDepartureOdometerReading, param.getDepartureOdometerReading())
                .set(GovVehicleMaintenance::getPickupTime, new Date())
                .set(GovVehicleMaintenance::getUpdatedCode, param.getLoginUserCode())
                .set(GovVehicleMaintenance::getUpdatedName, param.getLoginUserName());
        govVehicleMaintenanceService.update(updateWrapper);
        //插入日志
        insertLog(param, param.getMaintenanceNo(), MaintenanceOperationStatusEnum.VEHICLE_RECEIVED);
        //插入文件
        List<GovMaintenanceFileInfo> fileInfoList = new ArrayList<>();
        if (StringUtils.isNotEmpty(param.getDepartureContactSignatureUrl())) {
            GovMaintenanceFileInfo fileInfo = new GovMaintenanceFileInfo();
            fileInfo.setFileType(MaintenanceFileEnum.HANDWRITTEN_SIGNATURE_BY_PICKUP_PERSON.getCode());
            fileInfo.setRelationCode(param.getMaintenanceNo());
            fileInfo.setFileName(MaintenanceFileEnum.HANDWRITTEN_SIGNATURE_BY_PICKUP_PERSON.getName());
            fileInfo.setFileUrl(param.getDepartureContactSignatureUrl());
            fileInfoList.add(fileInfo);
        }
        if (StringUtils.isNotEmpty(param.getAfterDashboardPhotoUrl())) {
            GovMaintenanceFileInfo fileInfo2 = new GovMaintenanceFileInfo();
            fileInfo2.setFileType(MaintenanceFileEnum.POST_REPAIR_ODOMETER_PHOTO.getCode());
            fileInfo2.setRelationCode(param.getMaintenanceNo());
            fileInfo2.setFileName(MaintenanceFileEnum.POST_REPAIR_ODOMETER_PHOTO.getName());
            fileInfo2.setFileUrl(param.getAfterDashboardPhotoUrl());
            fileInfoList.add(fileInfo2);
        }
        if (StringUtils.isNotEmpty(param.getBillPhotoUrl())) {
            GovMaintenanceFileInfo fileInfo3 = new GovMaintenanceFileInfo();
            fileInfo3.setFileType(MaintenanceFileEnum.INVOICE.getCode());
            fileInfo3.setRelationCode(param.getMaintenanceNo());
            fileInfo3.setFileName(MaintenanceFileEnum.INVOICE.getName());
            fileInfo3.setFileUrl(param.getBillPhotoUrl());
            fileInfoList.add(fileInfo3);
        }
        if (StringUtils.isNotEmpty(param.getRepairDetailsPhotoUrl())) {
            GovMaintenanceFileInfo fileInfo4 = new GovMaintenanceFileInfo();
            fileInfo4.setFileType(MaintenanceFileEnum.REPAIR_DETAILS.getCode());
            fileInfo4.setRelationCode(param.getMaintenanceNo());
            fileInfo4.setFileName(MaintenanceFileEnum.REPAIR_DETAILS.getName());
            fileInfo4.setFileUrl(param.getRepairDetailsPhotoUrl());
            fileInfoList.add(fileInfo4);
        }
        if (CollectionUtil.isNotEmpty(fileInfoList)) {
            govMaintenanceFileInfoService.saveBatch(fileInfoList);
        }
        //发送站内信
        try {
            pushMessageForDriverByReceived(govVehicleMaintenance);
        } catch (Exception e) {
            log.error("发送消息异常", e);
        }
    }

    private void pushMessageForDriverByReceived(GovVehicleMaintenance govVehicleMaintenance) {
        String driverCode = govVehicleMaintenance.getDriverCode();
        if (StringUtils.isNotEmpty(driverCode)) {
            MessageTrackingSender.MessageTrackingParam pushParam = new MessageTrackingSender.MessageTrackingParam();
            pushParam.setCompanyId(govVehicleMaintenance.getCompanyId());
            pushParam.setScene(GovMsgSceneEnum.MAINTENANCE_RECEIVED);
            pushParam.setReceiverCodeSet(new HashSet<>(Collections.singletonList(driverCode)));
            Map<String, String> extendParamMap = new HashMap<>();
            extendParamMap.put(PushMessageUtil.MSG_PARAM_APPLY_VEHICLE_LICENSE, govVehicleMaintenance.getVehicleLicense());
            pushParam.setExtendParamMap(extendParamMap);
            messageTrackingSender.sendMessageTracking(pushParam);
        }
        String submitterPhone = govVehicleMaintenance.getSubmitterPhone();
        if (StringUtils.isNotEmpty(submitterPhone)) {
            MessageTrackingSender.MessageTrackingParam pushParam = new MessageTrackingSender.MessageTrackingParam();
            pushParam.setCompanyId(govVehicleMaintenance.getCompanyId());
            pushParam.setScene(GovMsgSceneEnum.MAINTENANCE_RECEIVED);
            pushParam.setMobile(new HashSet<>(Collections.singletonList(submitterPhone)));
            Map<String, String> extendParamMap = new HashMap<>();
            extendParamMap.put(PushMessageUtil.MSG_PARAM_APPLY_VEHICLE_LICENSE, govVehicleMaintenance.getVehicleLicense());
            pushParam.setExtendParamMap(extendParamMap);
            messageTrackingSender.sendMessageTracking(pushParam);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createMaintenance(PCMaintenanceCreateDTO param) {
        String maintenanceNo = sequenceGenerator.generate(new Date(), PUBLIC_MAINTENANCE_NO_PREFIX);
        //根据车辆编码查询车辆信息
        GovVehicleBaseInfo vehicleBaseInfo = govVehiclebaseInfoService.getOne(new LambdaQueryWrapper<GovVehicleBaseInfo>().eq(GovVehicleBaseInfo::getVehicleNo, param.getVehicleNo()));
        if (vehicleBaseInfo == null) {
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "该车不存在");
        }
        if (param.getSubmissionTime().getTime() > param.getPickupTime().getTime()) {
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "接车时间不能早于送修时间");
        }
        //查询车辆绑定的司机
        GovVehicleDriverRelation driverRelation = govVehicleDriverRelationService.getOne(new LambdaQueryWrapper<GovVehicleDriverRelation>().eq(GovVehicleDriverRelation::getVehicleNo, param.getVehicleNo()).eq(GovVehicleDriverRelation::getRelationStatus, RelationStatusEnum.NORMAL.getCode()).last("limit 1"));
        Integer constructionGarageType = param.getConstructionGarageType();
        GovVehicleMaintenance govVehicleMaintenance = new GovVehicleMaintenance();
        if (ObjectUtil.equals(constructionGarageType, ConstructionGarageTypeEnum.FIXED_POINT.getCode())) {
            if (StringUtils.isEmpty(param.getConstructionGarageNo())) {
                throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "维修机构编码不能为空");
            }
            //查询维修厂信息
            GovOrgInfo govOrgInfo = govOrgInfoService.getOne(new LambdaQueryWrapper<GovOrgInfo>().eq(GovOrgInfo::getOrgNo, param.getConstructionGarageNo()));
            if (ObjectUtil.isNull(govOrgInfo)) {
                throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "维修厂不存在");
            }
            govVehicleMaintenance.setConstructionGarageNo(govOrgInfo.getOrgNo());
            govVehicleMaintenance.setConstructionGarageName(govOrgInfo.getOrgName());
            govVehicleMaintenance.setConstructionGaragePhone(govOrgInfo.getServiceTelephone());
            govVehicleMaintenance.setConstructionGarageAddress(govOrgInfo.getOrgAddressDetail());
            govVehicleMaintenance.setConstructionGarageCityCode(govOrgInfo.getOrgCityCode());
            govVehicleMaintenance.setConstructionGarageCityName(govOrgInfo.getOrgCityName());
            govVehicleMaintenance.setInvoiceType(govOrgInfo.getInvoiceType());
            govVehicleMaintenance.setInvoiceOpenType(govOrgInfo.getInvoiceOpenType());
            govVehicleMaintenance.setInvoiceRate(govOrgInfo.getInvoiceRate());
        } else {
            govVehicleMaintenance.setConstructionGarageName(param.getConstructionGarageName());
            govVehicleMaintenance.setConstructionGaragePhone(param.getConstructionGaragePhone());
            govVehicleMaintenance.setConstructionGarageAddress(param.getConstructionGarageAddress());
            govVehicleMaintenance.setInvoiceType(param.getInvoiceType());
            govVehicleMaintenance.setInvoiceRate(param.getInvoiceRate());
        }
        govVehicleMaintenance.setMaintenanceNo(maintenanceNo);
        govVehicleMaintenance.setMaintenanceType(MaintenanceTypeEnum.POST_RECORD.getCode());
        govVehicleMaintenance.setMaintenanceStatus(MaintenanceStatusEnum.VEHICLE_RECEIVED.getCode());
        govVehicleMaintenance.setCompanyId(vehicleBaseInfo.getCompanyId());
        //查询公司名称
        GovCompany company = govCompanyService.getById(vehicleBaseInfo.getCompanyId());
        if (ObjectUtil.isNotNull(company)) {
            govVehicleMaintenance.setCompanyName(company.getCompanyName());
        }
        govVehicleMaintenance.setCreatedCode(param.getLoginUserCode());
        govVehicleMaintenance.setCreatedName(param.getLoginUserName());
        govVehicleMaintenance.setCreatedPhone(param.getLoginUserMobile());
        govVehicleMaintenance.setVehicleNo(vehicleBaseInfo.getVehicleNo());
        govVehicleMaintenance.setVehicleLicense(vehicleBaseInfo.getVehicleLicense());
        govVehicleMaintenance.setVehicleVin(vehicleBaseInfo.getVehicleVin());
        govVehicleMaintenance.setVehicleBrandId(vehicleBaseInfo.getVehicleBrandId());
        govVehicleMaintenance.setVehicleBrandName(vehicleBaseInfo.getVehicleBrandName());
        govVehicleMaintenance.setVehicleSeriesId(vehicleBaseInfo.getVehicleSeriesId());
        govVehicleMaintenance.setVehicleSeriesName(vehicleBaseInfo.getVehicleSeriesName());
        govVehicleMaintenance.setVehicleBelongDeptCode(vehicleBaseInfo.getVehicleBelongDeptCode());
        govVehicleMaintenance.setVehicleBelongDeptName(vehicleBaseInfo.getVehicleBelongDeptName());
        govVehicleMaintenance.setVehicleUseDeptCode(vehicleBaseInfo.getVehicleUseDeptCode());
        govVehicleMaintenance.setVehicleUseDeptName(vehicleBaseInfo.getVehicleUseDeptName());
        govVehicleMaintenance.setVehicleUseStructCode(vehicleBaseInfo.getVehicleUseStructCode());
        govVehicleMaintenance.setVehicleUseStructName(vehicleBaseInfo.getVehicleUseStructName());
        govVehicleMaintenance.setManageCarType(vehicleBaseInfo.getManageCarType());
        if (ObjectUtil.isNotNull(driverRelation)) {
            //查询司机信息
            GovUser driverInfo = govUserService.getOne(new LambdaQueryWrapper<GovUser>().eq(GovUser::getUserCode, driverRelation.getUserCode()));
            if (ObjectUtil.isNotNull(driverInfo)) {
                govVehicleMaintenance.setDriverCode(driverInfo.getUserCode());
                govVehicleMaintenance.setDriverName(driverInfo.getUserName());
                govVehicleMaintenance.setDriverMobile(driverInfo.getMobile());
                //查询司机
                GovDriver driverLicense = govDriverService.getOne(new LambdaQueryWrapper<GovDriver>().eq(GovDriver::getUserCode, driverRelation.getUserCode()));
                if (ObjectUtil.isNotNull(driverLicense)) {
                    govVehicleMaintenance.setRegisterDate(driverLicense.getFirstPickupTime());
                }
            }
        }
        govVehicleMaintenance.setConstructionGarageType(param.getConstructionGarageType());
        govVehicleMaintenance.setFaultDesc(param.getFaultDesc());
        govVehicleMaintenance.setSubmitterName(param.getSubmitterName());
        govVehicleMaintenance.setSubmitterPhone(param.getSubmitterPhone());
        govVehicleMaintenance.setSubmissionTime(param.getSubmissionTime());
        govVehicleMaintenance.setArrivalOdometerReading(param.getArrivalOdometerReading());
        govVehicleMaintenance.setUpdatedCode(param.getLoginUserCode());
        govVehicleMaintenance.setUpdatedName(param.getLoginUserName());
        govVehicleMaintenance.setSubmitterCode(param.getSubmitterCode());
        govVehicleMaintenance.setArrivalContactTime(param.getSubmissionTime());
        govVehicleMaintenance.setDepartureContactCode(param.getDepartureContactCode());
        govVehicleMaintenance.setDepartureContactName(param.getDepartureContactName());
        govVehicleMaintenance.setDepartureContactPhone(param.getDepartureContactPhone());
        govVehicleMaintenance.setPickupTime(param.getPickupTime());
        govVehicleMaintenance.setDepartureOdometerReading(param.getDepartureOdometerReading());
        govVehicleMaintenanceService.save(govVehicleMaintenance);
        //插入日志
        insertLog(param, maintenanceNo, MaintenanceOperationStatusEnum.VEHICLE_RECEIVED);
        //插入文件
        insertMaintenanceFile(param, maintenanceNo);
        //插入报价相关
        insertQuotes(param, govVehicleMaintenance);

    }

    private void insertQuotes(PCMaintenanceCreateDTO param, GovVehicleMaintenance govVehicleMaintenance) {
        GovRepairQuotes govRepairQuotes = new GovRepairQuotes();
        govRepairQuotes.setMaintenanceNo(govVehicleMaintenance.getMaintenanceNo());
        govRepairQuotes.setShopFaultDesc(param.getShopFaultDesc());
        govRepairQuotes.setQuotedByCode(param.getLoginUserCode());
        govRepairQuotes.setQuotedByName(param.getLoginUserName());
        govRepairQuotes.setQuotedByPhone(param.getLoginUserMobile());
        govRepairQuotesService.save(govRepairQuotes);
        //查询保存的报价单id
        Long quotedId = govRepairQuotesService.getOne(new LambdaQueryWrapper<GovRepairQuotes>().eq(GovRepairQuotes::getMaintenanceNo, govVehicleMaintenance.getMaintenanceNo())).getQuotedId();
        List<QuoteDetail> quoteDetailList = param.getQuoteDetailList();
        List<GovRepairQuotesDetail> govRepairQuotesDetailList = new ArrayList<>();
        for (QuoteDetail quoteDetail : quoteDetailList) {
            GovRepairQuotesDetail govRepairQuotesDetail = new GovRepairQuotesDetail();
            govRepairQuotesDetail.setQuotedId(quotedId);
            govRepairQuotesDetail.setChargeCategory(quoteDetail.getChargeCategory().intValue());
            govRepairQuotesDetail.setRepairName(quoteDetail.getRepairName());
            govRepairQuotesDetail.setUnitPrice(quoteDetail.getUnitPrice());
            govRepairQuotesDetail.setQuantity(quoteDetail.getQuantity());
            govRepairQuotesDetail.setTotalPrice(quoteDetail.getTotalPrice());
            // 计算不含税价
            BigDecimal taxInclusiveRate = BigDecimal.ONE.add(ObjectUtil.isNull(govVehicleMaintenance.getInvoiceRate()) ? BigDecimal.ZERO : govVehicleMaintenance.getInvoiceRate()); // 1 + 税率
            BigDecimal taxExclusivePrice = quoteDetail.getTotalPrice().divide(taxInclusiveRate, 2, RoundingMode.HALF_UP); // 保留两位小数，四舍五入
            govRepairQuotesDetail.setActualCost(taxExclusivePrice);
            govRepairQuotesDetailList.add(govRepairQuotesDetail);
        }
        govRepairQuotesDetailService.saveBatch(govRepairQuotesDetailList);
        //保存维修报价日志
        GovRepairQuotesLog govRepairQuotesLog = new GovRepairQuotesLog();
        govRepairQuotesLog.setMaintenanceNo(govVehicleMaintenance.getMaintenanceNo());
        govRepairQuotesLog.setQuotedByCode(param.getLoginUserCode());
        govRepairQuotesLog.setQuotedByName(param.getLoginUserName());
        govRepairQuotesLog.setQuotedByPhone(param.getLoginUserMobile());
        govRepairQuotesLog.setQuotedContent(JacksonUtils.toJson(govRepairQuotesDetailList));
        govRepairQuotesLogService.save(govRepairQuotesLog);
    }

    private void insertMaintenanceFile(PCMaintenanceCreateDTO param, String maintenanceNo) {
        List<GovMaintenanceFileInfo> fileInfoList = new ArrayList<>();
        if (StringUtils.isNotEmpty(param.getBeforeDashboardPhotoUrl())) {
            GovMaintenanceFileInfo fileInfo = new GovMaintenanceFileInfo();
            fileInfo.setFileType(MaintenanceFileEnum.PRE_REPAIR_PHOTO_DASHBOARD.getCode());
            fileInfo.setRelationCode(maintenanceNo);
            fileInfo.setFileName(MaintenanceFileEnum.PRE_REPAIR_PHOTO_DASHBOARD.getName());
            fileInfo.setFileUrl(param.getBeforeDashboardPhotoUrl());
            fileInfoList.add(fileInfo);
        }
        if (StringUtils.isNotEmpty(param.getAfterDashboardPhotoUrl())) {
            GovMaintenanceFileInfo fileInfo2 = new GovMaintenanceFileInfo();
            fileInfo2.setFileType(MaintenanceFileEnum.POST_REPAIR_ODOMETER_PHOTO.getCode());
            fileInfo2.setRelationCode(maintenanceNo);
            fileInfo2.setFileName(MaintenanceFileEnum.POST_REPAIR_ODOMETER_PHOTO.getName());
            fileInfo2.setFileUrl(param.getAfterDashboardPhotoUrl());
            fileInfoList.add(fileInfo2);
        }
        if (StringUtils.isNotEmpty(param.getBillPhotoUrl())) {
            GovMaintenanceFileInfo fileInfo3 = new GovMaintenanceFileInfo();
            fileInfo3.setFileType(MaintenanceFileEnum.INVOICE.getCode());
            fileInfo3.setRelationCode(maintenanceNo);
            fileInfo3.setFileName(MaintenanceFileEnum.INVOICE.getName());
            fileInfo3.setFileUrl(param.getBillPhotoUrl());
            fileInfoList.add(fileInfo3);
        }
        if (StringUtils.isNotEmpty(param.getRepairDetailsPhotoUrl())) {
            GovMaintenanceFileInfo fileInfo4 = new GovMaintenanceFileInfo();
            fileInfo4.setFileType(MaintenanceFileEnum.REPAIR_DETAILS.getCode());
            fileInfo4.setRelationCode(maintenanceNo);
            fileInfo4.setFileName(MaintenanceFileEnum.REPAIR_DETAILS.getName());
            fileInfo4.setFileUrl(param.getRepairDetailsPhotoUrl());
            fileInfoList.add(fileInfo4);
        }
        if (StringUtils.isNotEmpty(param.getBeforeVehicleLicensePhotoUrl())) {
            GovMaintenanceFileInfo fileInfo5 = new GovMaintenanceFileInfo();
            fileInfo5.setFileType(MaintenanceFileEnum.PRE_REPAIR_PHOTO_LICENSE_PLATE.getCode());
            fileInfo5.setRelationCode(maintenanceNo);
            fileInfo5.setFileName(MaintenanceFileEnum.PRE_REPAIR_PHOTO_LICENSE_PLATE.getName());
            fileInfo5.setFileUrl(param.getBeforeVehicleLicensePhotoUrl());
            fileInfoList.add(fileInfo5);
        }
        if (CollectionUtil.isNotEmpty(param.getQuoteBaseImgUrl())) {
            param.getQuoteBaseImgUrl().forEach(url -> {
                GovMaintenanceFileInfo fileInfo6 = new GovMaintenanceFileInfo();
                fileInfo6.setFileType(MaintenanceFileEnum.QUOTATION_BASIS_PHOTO.getCode());
                fileInfo6.setRelationCode(maintenanceNo);
                fileInfo6.setFileName(MaintenanceFileEnum.QUOTATION_BASIS_PHOTO.getName());
                fileInfo6.setFileUrl(url);
                fileInfoList.add(fileInfo6);
            });
        }
        if (CollectionUtil.isNotEmpty(param.getVehicleVideoUrl())) {
            param.getVehicleVideoUrl().forEach(url -> {
                GovMaintenanceFileInfo fileInfo7 = new GovMaintenanceFileInfo();
                fileInfo7.setFileType(MaintenanceFileEnum.QUOTATION_BASIS_VIDEO.getCode());
                fileInfo7.setRelationCode(maintenanceNo);
                fileInfo7.setFileName(MaintenanceFileEnum.QUOTATION_BASIS_VIDEO.getName());
                fileInfo7.setFileUrl(url);
                fileInfoList.add(fileInfo7);
            });
        }
        if (CollectionUtil.isNotEmpty(fileInfoList)) {
            govMaintenanceFileInfoService.saveBatch(fileInfoList);
        }
    }

    @Override
    public void approveResult(BpmMessageSendApproveResultDTO bpmMessageSendApproveResultDTO) {
        BpmProcessInstanceResultEnum bpmProcessInstanceResultEnum = BpmProcessInstanceResultEnum.getEnum(bpmMessageSendApproveResultDTO.getResult());
        //获取维保单
        GovVehicleMaintenance govVehicleMaintenance = govVehicleMaintenanceService.getOne(new LambdaQueryWrapper<GovVehicleMaintenance>().eq(GovVehicleMaintenance::getMaintenanceNo, bpmMessageSendApproveResultDTO.getBusinessNo()));
        if (ObjectUtil.isNull(govVehicleMaintenance)) {
            log.info("审批消息：维保单不存在{}", JacksonUtils.toJson(bpmMessageSendApproveResultDTO));
            return;
        }
        switch (bpmProcessInstanceResultEnum) {
            //审批通过
            case APPROVE:
                //修改订单状态为通过
                //修改维保单的状态为待到厂
                LambdaUpdateWrapper<GovVehicleMaintenance> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(GovVehicleMaintenance::getMaintenanceNo, bpmMessageSendApproveResultDTO.getBusinessNo())
                        .set(GovVehicleMaintenance::getMaintenanceStatus, MaintenanceStatusEnum.WAITING_FOR_FACTORY.getCode())
                        .set(GovVehicleMaintenance::getUpdatedCode, bpmMessageSendApproveResultDTO.getApproverCode())
                        .set(GovVehicleMaintenance::getUpdateTime, bpmMessageSendApproveResultDTO.getApproverTime())
                        .set(GovVehicleMaintenance::getUpdatedName, bpmMessageSendApproveResultDTO.getApproverName());
                govVehicleMaintenanceService.update(updateWrapper);
                insertApproveLog(bpmMessageSendApproveResultDTO, bpmMessageSendApproveResultDTO.getBusinessNo(), MaintenanceOperationStatusEnum.WAITING_FOR_FACTORY);
                break;
            //驳回
            case REJECT:
                //插入审核日志
                LambdaUpdateWrapper<GovVehicleMaintenance> reject = new LambdaUpdateWrapper<>();
                reject.eq(GovVehicleMaintenance::getMaintenanceNo, bpmMessageSendApproveResultDTO.getBusinessNo())
                        .set(GovVehicleMaintenance::getMaintenanceStatus, MaintenanceStatusEnum.REPAIR_APPROVAL_REJECTED.getCode())
                        .set(GovVehicleMaintenance::getUpdatedCode, bpmMessageSendApproveResultDTO.getApproverCode())
                        .set(GovVehicleMaintenance::getUpdateTime, bpmMessageSendApproveResultDTO.getApproverTime())
                        .set(GovVehicleMaintenance::getUpdatedName, bpmMessageSendApproveResultDTO.getApproverName());
                govVehicleMaintenanceService.update(reject);
                insertApproveLog(bpmMessageSendApproveResultDTO, bpmMessageSendApproveResultDTO.getBusinessNo(), MaintenanceOperationStatusEnum.REPAIR_APPROVAL_REJECTED);
                break;
            case CANCEL:
                //更新订单为废弃
                LambdaUpdateWrapper<GovVehicleMaintenance> cancel = new LambdaUpdateWrapper<>();
                cancel.eq(GovVehicleMaintenance::getMaintenanceNo, bpmMessageSendApproveResultDTO.getBusinessNo())
                        .set(GovVehicleMaintenance::getMaintenanceStatus, MaintenanceStatusEnum.APPROVAL_WITHDRAWAL.getCode())
                        .set(GovVehicleMaintenance::getUpdatedCode, bpmMessageSendApproveResultDTO.getApproverCode())
                        .set(GovVehicleMaintenance::getUpdateTime, bpmMessageSendApproveResultDTO.getApproverTime())
                        .set(GovVehicleMaintenance::getUpdatedName, bpmMessageSendApproveResultDTO.getApproverName());
                govVehicleMaintenanceService.update(cancel);
                //插入操作日志
                insertApproveLog(bpmMessageSendApproveResultDTO, bpmMessageSendApproveResultDTO.getBusinessNo(), MaintenanceOperationStatusEnum.APPROVAL_WITHDRAWAL);
                break;
            case BACK:
                break;
            default:
                log.info("公务用车订单接收审批流消息，审批结果未知：{}", bpmMessageSendApproveResultDTO);
        }
    }

    @Override
    public void approveResultByQuotation(BpmMessageSendApproveResultDTO bpmMessageSendApproveResultDTO) {
        BpmProcessInstanceResultEnum bpmProcessInstanceResultEnum = BpmProcessInstanceResultEnum.getEnum(bpmMessageSendApproveResultDTO.getResult());
        //获取维保单
        GovVehicleMaintenance govVehicleMaintenance = govVehicleMaintenanceService.getOne(new LambdaQueryWrapper<GovVehicleMaintenance>().eq(GovVehicleMaintenance::getMaintenanceNo, bpmMessageSendApproveResultDTO.getBusinessNo()));
        if (ObjectUtil.isNull(govVehicleMaintenance)) {
            log.info("增项审批审批消息：维保单不存在{}", JacksonUtils.toJson(bpmMessageSendApproveResultDTO));
            return;
        }
        switch (bpmProcessInstanceResultEnum) {
            //审批通过
            case APPROVE:
                //修改维保单的状态为维修中
                LambdaUpdateWrapper<GovVehicleMaintenance> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(GovVehicleMaintenance::getMaintenanceNo, bpmMessageSendApproveResultDTO.getBusinessNo())
                        .set(GovVehicleMaintenance::getMaintenanceStatus, MaintenanceStatusEnum.UNDER_REPAIR.getCode())
                        .set(GovVehicleMaintenance::getUpdatedCode, bpmMessageSendApproveResultDTO.getApproverCode())
                        .set(GovVehicleMaintenance::getUpdateTime, bpmMessageSendApproveResultDTO.getApproverTime())
                        .set(GovVehicleMaintenance::getUpdatedName, bpmMessageSendApproveResultDTO.getApproverName());
                govVehicleMaintenanceService.update(updateWrapper);
                insertApproveLog(bpmMessageSendApproveResultDTO, bpmMessageSendApproveResultDTO.getBusinessNo(), MaintenanceOperationStatusEnum.UNDER_REPAIR);
                break;
            //驳回
            case REJECT:
                //修改订单状态为通过
                if (ObjectUtil.equals(govVehicleMaintenance.getMaintenanceStatus(), MaintenanceStatusEnum.QUOTE_APPROVAL_PENDING.getCode())) {
                    //插入审核日志
                    LambdaUpdateWrapper<GovVehicleMaintenance> reject = new LambdaUpdateWrapper<>();
                    reject.eq(GovVehicleMaintenance::getMaintenanceNo, bpmMessageSendApproveResultDTO.getBusinessNo())
                            .set(GovVehicleMaintenance::getMaintenanceStatus, MaintenanceStatusEnum.QUOTE_APPROVAL_REJECTED.getCode())
                            .set(GovVehicleMaintenance::getUpdatedCode, bpmMessageSendApproveResultDTO.getApproverCode())
                            .set(GovVehicleMaintenance::getUpdateTime, bpmMessageSendApproveResultDTO.getApproverTime())
                            .set(GovVehicleMaintenance::getUpdatedName, bpmMessageSendApproveResultDTO.getApproverName());
                    govVehicleMaintenanceService.update(reject);
                    insertApproveLog(bpmMessageSendApproveResultDTO, bpmMessageSendApproveResultDTO.getBusinessNo(), MaintenanceOperationStatusEnum.QUOTE_APPROVAL_REJECTED);
                } else if (ObjectUtil.equals(govVehicleMaintenance.getMaintenanceStatus(), MaintenanceStatusEnum.ADDITIONAL_ITEM_APPROVAL_PENDING.getCode())) {
                    //插入审核日志
                    LambdaUpdateWrapper<GovVehicleMaintenance> reject = new LambdaUpdateWrapper<>();
                    reject.eq(GovVehicleMaintenance::getMaintenanceNo, bpmMessageSendApproveResultDTO.getBusinessNo())
                            .set(GovVehicleMaintenance::getMaintenanceStatus, MaintenanceStatusEnum.ADDITIONAL_ITEM_APPROVAL_REJECTED.getCode())
                            .set(GovVehicleMaintenance::getUpdatedCode, bpmMessageSendApproveResultDTO.getApproverCode())
                            .set(GovVehicleMaintenance::getUpdateTime, bpmMessageSendApproveResultDTO.getApproverTime())
                            .set(GovVehicleMaintenance::getUpdatedName, bpmMessageSendApproveResultDTO.getApproverName());
                    govVehicleMaintenanceService.update(reject);
                    insertApproveLog(bpmMessageSendApproveResultDTO, bpmMessageSendApproveResultDTO.getBusinessNo(), MaintenanceOperationStatusEnum.ADDITIONAL_ITEM_APPROVAL_REJECTED);
                }
                break;
            case CANCEL:
                //更新订单为废弃
                LambdaUpdateWrapper<GovVehicleMaintenance> cancel = new LambdaUpdateWrapper<>();
                cancel.eq(GovVehicleMaintenance::getMaintenanceNo, bpmMessageSendApproveResultDTO.getBusinessNo())
                        .set(GovVehicleMaintenance::getMaintenanceStatus, MaintenanceStatusEnum.APPROVAL_WITHDRAWAL.getCode())
                        .set(GovVehicleMaintenance::getUpdatedCode, bpmMessageSendApproveResultDTO.getApproverCode())
                        .set(GovVehicleMaintenance::getUpdateTime, bpmMessageSendApproveResultDTO.getApproverTime())
                        .set(GovVehicleMaintenance::getUpdatedName, bpmMessageSendApproveResultDTO.getApproverName());
                govVehicleMaintenanceService.update(cancel);
                //插入操作日志
                insertApproveLog(bpmMessageSendApproveResultDTO, bpmMessageSendApproveResultDTO.getBusinessNo(), MaintenanceOperationStatusEnum.APPROVAL_WITHDRAWAL);
                break;
            case BACK:
                break;
            default:
                log.info("公务用车订单接收审批流消息，审批结果未知：{}", bpmMessageSendApproveResultDTO);
        }
    }

    private void insertApproveLog(BpmMessageSendApproveResultDTO bpmMessageSendApproveResultDTO, String businessNo, MaintenanceOperationStatusEnum maintenanceOperationStatusEnum) {
        //获取审批人电话
        GovUser govUser = govUserService.getOne(new LambdaQueryWrapper<GovUser>().eq(GovUser::getUserCode, bpmMessageSendApproveResultDTO.getApproverCode()));
        GovMaintenanceOperationRecord operationRecord = new GovMaintenanceOperationRecord();
        operationRecord.setMaintenanceNo(businessNo);
        operationRecord.setOperationStatus(maintenanceOperationStatusEnum.getCode());
        operationRecord.setOperationUserCode(bpmMessageSendApproveResultDTO.getApproverCode());
        operationRecord.setOperationUserName(bpmMessageSendApproveResultDTO.getApproverName());
        if (ObjectUtil.isNull(govUser)) {
            operationRecord.setOperationUserPhone(govUser.getMobile());
        }
        govMaintenanceOperationRecordService.save(operationRecord);
    }

    /**
     * 审批是否激活
     *
     * @param loginCompanyId
     * @param code
     * @return
     */
    private Boolean workFlowIsActive(Integer loginCompanyId, Byte code, String loginDeptCode) {
        ApplyStartSwitchDTO applyStartSwitchDTO = new ApplyStartSwitchDTO();
        applyStartSwitchDTO.setBusinessType(code);
        applyStartSwitchDTO.setLoginCompanyId(loginCompanyId);
        //根据部门code查询部门id
        LambdaQueryWrapper<GovStruct> govStructLambdaQueryWrapper = new LambdaQueryWrapper<>();
        govStructLambdaQueryWrapper.eq(GovStruct::getStructCode, loginDeptCode);
        GovStruct govStruct = govStructService.getOne(govStructLambdaQueryWrapper);
        if (govStruct != null) {
            applyStartSwitchDTO.setLoginDeptId(govStruct.getId());
        }
        return workflowApprovalService.isApprovalActive(applyStartSwitchDTO);
    }

    private Boolean workFlowIsActive(Integer loginCompanyId, Byte code, Integer loginUserDeptCode) {
        ApplyStartSwitchDTO applyStartSwitchDTO = new ApplyStartSwitchDTO();
        applyStartSwitchDTO.setBusinessType(code);
        applyStartSwitchDTO.setLoginCompanyId(loginCompanyId);
        applyStartSwitchDTO.setLoginDeptId(loginUserDeptCode);
        return workflowApprovalService.isApprovalActive(applyStartSwitchDTO);
    }
}
