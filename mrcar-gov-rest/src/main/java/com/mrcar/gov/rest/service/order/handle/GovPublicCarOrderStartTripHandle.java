//package com.mrcar.gov.rest.service.order.handle;
//import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
//import com.izu.framework.exception.ApiException;
//import com.mrcar.gov.common.constant.RestErrorCode;
//import com.mrcar.gov.common.constant.order.GovPublicCarOrderOperationTypeEnum;
//import com.mrcar.gov.common.constant.order.GovPublicCarOrderOptCodeEnum;
//import com.mrcar.gov.common.constant.order.GovPublicCarOrderStatusEnum;
//import com.mrcar.gov.common.constant.order.GovPublicCarOrderUserTypeEnum;
//import com.mrcar.gov.order.domain.GovPublicCarOrder;
//import com.mrcar.gov.order.domain.GovPublicCarOrderUserInfo;
//import com.mrcar.gov.rest.service.order.handle.model.GovPublicOrderHandleContext;
//import com.mrcar.gov.rest.service.order.handle.model.GovPublicOrderHandleDTO;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Service;
//
//import java.util.Date;
//import java.util.Objects;
//
///**
// * <AUTHOR>
// * @date 2024/8/18 10:39
// */
//@Service
//@Slf4j
//public class GovPublicCarOrderStartTripHandle extends AbstractGovPublicCarOrderHandle{
//
//    public int getOrderOptCode() {
//        return GovPublicCarOrderOptCodeEnum.START_TRIP.getOptCode();
//    }
//
//
//    @Override
//    public void beforeHandle(GovPublicOrderHandleDTO handleDto, GovPublicOrderHandleContext context) {
//
//        GovPublicCarOrder govPublicCarOrder =
//                getOrder(handleDto.getOrderNo(), handleDto.getCompanyId());
//        if(Objects.isNull(govPublicCarOrder)){
//            throw new ApiException(RestErrorCode.GOV_PUBLIC_CAR_ORDER_NOT_EXIST);
//        }
//        // 当前订单状态不允许操作开始
//        if(!Objects.equals(govPublicCarOrder.getOrderStatus(), GovPublicCarOrderStatusEnum.PENDING_DEPARTURE.getCode())){
//            throw new ApiException(RestErrorCode.ORDER_STATUS_ERROR_NOT_SUPPORT_OPT);
//        }
//        context.setGovPublicCarOrder(govPublicCarOrder);
//    }
//
//    @Override
//    public void doHandle(GovPublicOrderHandleDTO handleDto, GovPublicOrderHandleContext context) {
//        GovPublicCarOrder updateOrder = new GovPublicCarOrder();
//        Date updateDate = new Date();
//        context.setUpdateDate(updateDate);
//        updateOrder.setOrderStatus(GovPublicCarOrderStatusEnum.IN_USE.getCode());
//        updateOrder.setOrderStartTime(updateDate);
//        updateOrder.setUpdateCode(handleDto.getOptUserCode());
//        updateOrder.setUpdateName(handleDto.getOptUserName());
//        updateOrder.setUpdateTime(updateDate);
//
//        transactionTemplate.execute((object) -> {
//            boolean result = govPublicCarOrderService.getBaseMapper().update(updateOrder,
//                    new LambdaQueryWrapper<GovPublicCarOrder>().eq(GovPublicCarOrder::getOrderNo, handleDto.getOrderNo())
//                            .eq(GovPublicCarOrder::getCompanyId, handleDto.getCompanyId())
//                            .eq(GovPublicCarOrder::getOrderStatus, context.getGovPublicCarOrder().getOrderStatus())) > 0;
//            if(!result){
//                throw new ApiException(RestErrorCode.ORDER_STATUS_HAS_CHANGE);
//            }
//            GovPublicCarOrderUserInfo optUserInfo = buildOptUserInfo(GovPublicCarOrderUserTypeEnum.ORDER_START_OPERATOR, handleDto, context);
//            govPublicCarOrderUserInfoService.save(optUserInfo);
//            boolean addLogRst = addOrderOperationLog(handleDto, context, updateDate,
//                    GovPublicCarOrderStatusEnum.IN_USE.getCode(), GovPublicCarOrderOperationTypeEnum.START_TRIP);
//            return true;
//        });
//    }
//
//    @Override
//    public void afterHandle(GovPublicOrderHandleDTO handleDto, GovPublicOrderHandleContext context) {
//
//    }
//}
