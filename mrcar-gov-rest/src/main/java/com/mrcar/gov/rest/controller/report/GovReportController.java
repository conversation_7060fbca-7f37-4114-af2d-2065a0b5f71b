package com.mrcar.gov.rest.controller.report;

import com.google.common.collect.Lists;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.framework.resp.RestResp;
import com.izu.framework.response.PageDTO;
import com.mrcar.gov.rest.service.asset.GovReportVehicleOrderAggService;
import com.mrcar.gov.bi.service.GovReportDeptAnalyseService;
import com.mrcar.gov.bi.service.GovReportVehicleService;
import com.mrcar.gov.bi.service.ReportTableConfigService;
import com.mrcar.gov.common.constant.asset.VehicleReportUseAttributeEnum;
import com.mrcar.gov.common.constant.asset.VehicleReportVehicleTypeEnum;
import com.mrcar.gov.common.constant.bi.BiSwitchUtil;
import com.mrcar.gov.common.dto.DictionaryEnumDTO;
import com.mrcar.gov.common.dto.asset.request.DictReqDTO;
import com.mrcar.gov.common.dto.bi.TableColumnDTO;
import com.mrcar.gov.common.dto.bi.req.VehicleDicMappingReqDTO;
import com.mrcar.gov.common.dto.bi.req.VehicleReportTableConfigReqDTO;
import com.mrcar.gov.common.dto.bi.req.VehicleReportTableConfigUpdateReqDTO;
import com.mrcar.gov.common.dto.bi.resp.VehicleDicMappingRespDTO;
import com.mrcar.gov.common.dto.report.GovSingleCar;
import com.mrcar.gov.common.dto.report.GovSingleCarParam;
import com.mrcar.gov.common.dto.report.GovUseCarDetail;
import com.mrcar.gov.common.dto.report.GovUseCarParam;
import com.mrcar.gov.common.dto.session.AccountDataPerm;
import com.mrcar.gov.common.dto.session.GovUserSessionInfoDTO;
import com.mrcar.gov.rest.service.asset.VehicleReportDictService;
import com.mrcar.gov.rest.util.SessionUtil;
import com.mrcar.gov.user.domain.GovStruct;
import com.mrcar.gov.user.service.GovStructService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 公务用车报表相关接口
 */
@Slf4j
@RestController
@RequestMapping("/gov/report")
public class GovReportController {
    @Resource
    private GovReportVehicleOrderAggService govReportVehicleOrderService;

    @Autowired
    private GovStructService govStructService;
    @Resource
    private VehicleReportDictService vehicleReportDictService;

    @Resource
    private GovReportVehicleService govReportVehicleService;

    @Resource
    private GovReportDeptAnalyseService govReportDeptAnalyseService;


    /**
     * 单位用车分析-明细
     */
    @PostMapping("/useCarDetail")
    public RestResp<List<GovUseCarDetail>> useCarDetail(@RequestBody @Valid GovUseCarParam govUseCarParam) {
        GovUserSessionInfoDTO govUserSessionInfoDTO = SessionUtil.currentUser();
        Integer companyId = govUserSessionInfoDTO.getCompanyInfo().getCompanyId();
        AccountDataPerm dataPerm = govUserSessionInfoDTO.getDataPerm();
        String structCode = govUseCarParam.getStructCode();
        Integer structType = govUseCarParam.getStructType();
        Integer enableStatus = govUseCarParam.getEnableStatus();
        Map<String, Pair<GovStruct, GovStruct>> unitParentStructMap = govStructService.getAllUnitByConditionV2(companyId, structCode, structType, enableStatus, dataPerm);
        // 这块没有机构名称，需要从这个方法里面补下 TODO
        if(BiSwitchUtil.oldLogicSwitch(govUseCarParam.getLoginCompanyId())){
//            Map<String, Pair<String, String>> unitParentStructMap = govStructService.getAllUnitByCondition(companyId, structCode, structType, enableStatus, dataPerm);
            return RestResp.ok(govReportVehicleOrderService.useCarDetail(govUseCarParam, unitParentStructMap));
        }else{
            return RestResp.ok(govReportDeptAnalyseService.useCarDetail(govUseCarParam, unitParentStructMap));
        }
    }

    /**
     * 单位用车分析-明细-导出
     */
    @PostMapping("/useCarDetail/export")
    public void useCarDetailExport(@RequestBody @Valid GovUseCarParam govUseCarParam,
                                   HttpServletRequest request,
                                   HttpServletResponse response,
                                   IzuEasyExcelSession izuEasyExcelSession) {
        GovUserSessionInfoDTO govUserSessionInfoDTO = SessionUtil.currentUser();
        AccountDataPerm dataPerm = govUserSessionInfoDTO.getDataPerm();
        Integer companyId = govUserSessionInfoDTO.getCompanyInfo().getCompanyId();
        String structCode = govUseCarParam.getStructCode();
        Integer structType = govUseCarParam.getStructType();
        Integer enableStatus = govUseCarParam.getEnableStatus();
        Map<String, Pair<GovStruct, GovStruct>> unitParentStructMap = govStructService.getAllUnitByConditionV2(companyId, structCode, structType, enableStatus, dataPerm);

        if(BiSwitchUtil.oldLogicSwitch(govUseCarParam.getLoginCompanyId())){
            govReportVehicleOrderService.useCarDetailExport(govUseCarParam, unitParentStructMap, request, response, izuEasyExcelSession);
        }else{
            govReportDeptAnalyseService.useCarDetailExport(govUseCarParam, unitParentStructMap, request, response, izuEasyExcelSession);
        }
    }

    /**
     * 单车分析
     */
    @PostMapping("/singleCarAnalysis")
    public RestResp<PageDTO<GovSingleCar>> singleCarAnalysis(@RequestBody GovSingleCarParam singleCarParam) {
        GovUserSessionInfoDTO govUserSessionInfoDTO = SessionUtil.currentUser();
        AccountDataPerm dataPerm = govUserSessionInfoDTO.getDataPerm();
        Integer companyId = govUserSessionInfoDTO.getCompanyInfo().getCompanyId();
        List<String> allUnitByCondition = govStructService.getAllUnitByCondition(companyId, singleCarParam.getStructCode(), singleCarParam.getManageDeptCode(), singleCarParam.getStructType(), dataPerm);

        if(BiSwitchUtil.oldLogicSwitch(singleCarParam.getLoginCompanyId())){
            return RestResp.ok(govReportVehicleOrderService.singleCarAnalysis(singleCarParam, allUnitByCondition));
        }else{
            return RestResp.ok(govReportVehicleService.singleCarAnalysis(singleCarParam, allUnitByCondition));
        }
    }

    /**
     * 单车分析-导出
     */
    @PostMapping("/singleCarAnalysis/export")
    public void singleCarAnalysisExport(@RequestBody GovSingleCarParam singleCarParam,HttpServletResponse response) {
        GovUserSessionInfoDTO govUserSessionInfoDTO = SessionUtil.currentUser();
        AccountDataPerm dataPerm = govUserSessionInfoDTO.getDataPerm();
        Integer companyId = govUserSessionInfoDTO.getCompanyInfo().getCompanyId();
        List<String> allUnitByCondition = govStructService.getAllUnitByCondition(companyId, singleCarParam.getStructCode(), singleCarParam.getManageDeptCode(), singleCarParam.getStructType(), dataPerm);
         // 5 期代码
        if(BiSwitchUtil.oldLogicSwitch(singleCarParam.getLoginCompanyId())){
            govReportVehicleOrderService.singleCarAnalysisExport(singleCarParam, allUnitByCondition, response);
        }else{
            govReportVehicleService.singleCarAnalysisExport(singleCarParam, allUnitByCondition, response);
        }
    }

    /**
     * 用车报表字典
     */
    @RequestMapping(value = "/useVehicleDict",method = {RequestMethod.POST, RequestMethod.GET})
    public Map<Integer,List<DictionaryEnumDTO>> getVehicleReportDictionaryEnum(@RequestBody DictReqDTO dictReqDTO) {
        return vehicleReportDictService.listDictionaryEnumByType(dictReqDTO);
    }

    /**
     * 用车报表字典映射
     */
    @PostMapping(value = "/useVehicleDictMapping")
    public RestResp<VehicleDicMappingRespDTO> useVehicleDicMapping(@RequestBody VehicleDicMappingReqDTO reqDTO) {
        VehicleDicMappingRespDTO respDTO = new VehicleDicMappingRespDTO();
        respDTO.setVehicleTypeList(Lists.newArrayList());
        respDTO.setVehicleUseTypeList(Lists.newArrayList());
        if(CollectionUtils.isNotEmpty(reqDTO.getVehicleTypeList())){
            reqDTO.getVehicleTypeList().forEach(type -> {
                respDTO.getVehicleTypeList().addAll(VehicleReportVehicleTypeEnum.listMappingCode(type));
            });
        }
        if(CollectionUtils.isNotEmpty(reqDTO.getVehicleUseTypeList())){
            reqDTO.getVehicleUseTypeList().forEach(type -> {
                respDTO.getVehicleUseTypeList().addAll(VehicleReportUseAttributeEnum.listMappingCode(type));
            });
        }
        return RestResp.ok(respDTO);
    }

    @Resource
    private ReportTableConfigService reportTableConfigService;

    /**
     * 获取车辆报表表格配置
     * @param reqDTO
     * @return
     */
    @PostMapping(value = "/vehicle/getTableConfig")
    public RestResp<List<TableColumnDTO>> getVehicleReportTableConfig(@RequestBody VehicleReportTableConfigReqDTO reqDTO) {
        return RestResp.ok(reportTableConfigService.getVehicleReportTableConfig(reqDTO));
    }



    /**
     * 更新车辆报表表格配置
     * @param reqDTO
     * @return
     */
    @PostMapping(value = "/vehicle/updateTableConfig")
    public RestResp<List<TableColumnDTO>> updateVehicleReportTableConfig(@RequestBody VehicleReportTableConfigUpdateReqDTO reqDTO) {
        reportTableConfigService.updateVehicleReportTableConfig(reqDTO);
        return RestResp.ok(reqDTO.getTableConfig());
    }
}
