package com.mrcar.gov.rest.service.asset.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.izu.file.storage.FileInfo;
import com.izu.file.storage.FileStorageService;
import com.izu.file.storage.UploadPretreatment;
import com.izu.framework.exception.ApiException;
import com.izu.framework.web.util.BeanUtil;
import com.mrcar.gov.asset.domain.GovVehicleBaseInfo;
import com.mrcar.gov.asset.domain.GovVehicleBrands;
import com.mrcar.gov.asset.domain.GovVehicleConfigurations;
import com.mrcar.gov.asset.domain.GovVehicleSeries;
import com.mrcar.gov.asset.service.GovVehicleBaseInfoService;
import com.mrcar.gov.asset.service.GovVehicleBrandsService;
import com.mrcar.gov.asset.service.GovVehicleConfigurationsService;
import com.mrcar.gov.asset.service.GovVehicleSeriesService;
import com.mrcar.gov.common.constant.RestErrorCode;
import com.mrcar.gov.common.constant.asset.*;
import com.mrcar.gov.common.constant.order.GovPublicCarRemoteLockEnum;
import com.mrcar.gov.common.constant.order.GovPublicCarRentTypeEnum;
import com.mrcar.gov.common.constant.order.GovVehicleServiceTypeEnum;
import com.mrcar.gov.common.constant.user.GovStructTypeEnum;
import com.mrcar.gov.common.constant.user.GovUserTypeEnum;
import com.mrcar.gov.common.dto.BaseDTO;
import com.mrcar.gov.common.dto.BatchImportReqDTO;
import com.mrcar.gov.common.dto.BatchImportResultDTO;
import com.mrcar.gov.common.dto.asset.GovSupplierVehicleImportDTO;
import com.mrcar.gov.common.dto.asset.GovVehicleImportDTO;
import com.mrcar.gov.common.dto.asset.request.VehicleConfigurationRequestDTO;
import com.mrcar.gov.common.dto.asset.request.VehicleSaveRequestDTO;
import com.mrcar.gov.common.dto.asset.request.VehicleSupplierRequestDTO;
import com.mrcar.gov.common.dto.user.GovStructImportDTO;
import com.mrcar.gov.common.dto.user.req.GovStructRequestDTO;
import com.mrcar.gov.common.dto.user.resp.GovOrgNameDTO;
import com.mrcar.gov.common.dto.user.resp.GovStructRespondDTO;
import com.mrcar.gov.common.util.*;
import com.mrcar.gov.config.domain.GovCityDic;
import com.mrcar.gov.config.service.GovCityDicService;
import com.mrcar.gov.rest.service.asset.GovVehicleImportAggService;
import com.mrcar.gov.rest.service.asset.VehicleInfoService;
import com.mrcar.gov.user.domain.GovOrgDeptRelation;
import com.mrcar.gov.user.domain.GovOrgInfo;
import com.mrcar.gov.user.domain.GovStruct;
import com.mrcar.gov.user.domain.GovUser;
import com.mrcar.gov.user.mapper.GovStructMapper;
import com.mrcar.gov.user.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.RandomStringUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.DataValidation;
import org.apache.poi.ss.usermodel.DataValidationConstraint;
import org.apache.poi.ss.usermodel.DataValidationHelper;
import org.apache.poi.ss.usermodel.Name;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2024/11/14 14:56
 * 车辆导入聚合服务实现
 */
@Slf4j
@Service
public class GovVehicleImportAggServiceImpl implements GovVehicleImportAggService {

    public static final String TEMPLATE_EXCEL_PATH = "template/vehicle_import_template.xlsx";


    public static final String SUPPLIER_TEMPLATE_EXCEL_PATH = "template/supplier_vehicle_import_template.xlsx";


    private static final String VEHICLE_IMPORT_ERROR_FILE_PRE_FIX = "车辆导入错误信息";

    @Resource
    private GovVehicleBaseInfoService govVehicleBaseInfoService;
    @Resource
    private FileStorageService fileStorageService;
    @Resource
    private GovStructService govStructService;
    @Resource
    private GovVehicleBrandsService govVehicleBrandsService;
    @Resource
    private GovVehicleSeriesService govVehicleSeriesService;
    @Resource
    private GovVehicleConfigurationsService govVehicleConfigurationsService;
    @Resource
    private VehicleInfoService vehicleInfoService;
    @Resource
    private GovStructCommonHandle4ImportService govStructCommonHandle4ImportService;
    @Resource
    private GovOrgDeptRelationService govOrgDeptRelationService;
    @Resource
    private GovOrgInfoService govOrgInfoService;

    @Resource
    private GovCityDicService govCityDicService;

    @Resource
    private GovUserService govUserService;


    @Override
    public void downloadTemplate(BaseDTO reqDto, HttpServletResponse response) {
        // key 列索引，value:字典
        Map<Integer, List<String>> columnDicMap = obtainColumnDicMap(reqDto);
        org.springframework.core.io.Resource excelResource = new ClassPathResource(TEMPLATE_EXCEL_PATH);
        try {
            // 读取excel
            try (InputStream inStream = excelResource.getInputStream(); Workbook workbook = new XSSFWorkbook(inStream)) {
                Sheet sheet = workbook.getSheetAt(0);
                // 创建隐藏sheet
                Sheet hiddenSheet = workbook.createSheet(RandomStringUtils.randomAlphabetic(32));
                workbook.setSheetHidden(workbook.getSheetIndex(hiddenSheet), true);
                columnDicMap.forEach((columnIndex, dic) -> {
                    ExcelUtil.createDropdownSelectionItems(workbook, hiddenSheet, sheet, dic, 2, columnIndex);
                });
                branchSeriesSecondCascade(workbook, sheet);
//                 需要保存在本地测试下 TODO
//                OutputStream outputStream = new FileOutputStream(new File("/Users/<USER>/Downloads/mrcar-gov/"  + System.currentTimeMillis() +"车辆导入模板.xlsx"));
//                workbook.write(outputStream);
//                outputStream.close();

                OutputStream outStream = response.getOutputStream();
                workbook.write(outStream);
                String fileName = URLEncoder.encode("车辆导入模板", "UTF-8").replaceAll("\\+", "%20");
                response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ExcelTypeEnum.XLSX.getValue());
                outStream.flush();
                outStream.close();
            }
        } catch (Exception e) {
            log.error("车辆导入模板下载异常", e);

        }
    }


    private Map<Integer, List<String>> obtainColumnDicMap(BaseDTO reqDto) {
        Map<Integer, List<String>> columnMap = Maps.newLinkedHashMap();
        // 使用性质
        List<String> useAttrList = UseAttributeEnum.getDescList();
        columnMap.put(2, useAttrList);
        //车辆类型
        List<String> vehicleTypeList = VehicleTypeEnum.getDescList();
        columnMap.put(3, vehicleTypeList);
//        // 车辆品牌
//        LambdaQueryWrapper<GovVehicleBrands> brandsQryWrapper = new LambdaQueryWrapper<>();
//        brandsQryWrapper.select(GovVehicleBrands::getBrandName, GovVehicleBrands::getBrandId);
//        List<GovVehicleBrands> branchList = govVehicleBrandsService.list(brandsQryWrapper);
//        columnMap.put(4, branchList.stream().map(GovVehicleBrands::getBrandName).collect(Collectors.toList()));
//        // 车系
//        LambdaQueryWrapper<GovVehicleSeries> seriesQryWrapper = new LambdaQueryWrapper<>();
//        seriesQryWrapper.select(GovVehicleSeries::getBrandId, GovVehicleSeries::getSeriesName);
//        List<GovVehicleSeries> seriesList = govVehicleSeriesService.list(seriesQryWrapper);
//        columnMap.put(5, seriesList.stream().map(GovVehicleSeries::getSeriesName).collect(Collectors.toList()));
        List<GovStructImportDTO> allStInfo = govStructCommonHandle4ImportService.listStructInfo(reqDto, null, Boolean.TRUE);
        List<String> deptNames = allStInfo.stream().filter(s -> Objects.equals(s.getStructType(), GovStructTypeEnum.UNIT.getCode())).map(GovStructImportDTO::getStructLevelName).collect(Collectors.toList());
        deptNames.sort(Comparator.comparingInt(String::length));
        List<String> structNames = allStInfo.stream().filter(s -> Objects.equals(s.getStructType(), GovStructTypeEnum.DEPARTMENT.getCode())).map(GovStructImportDTO::getStructLevelName).collect(Collectors.toList());
        structNames.sort(Comparator.comparingInt(String::length));
        // 车辆所有人|车辆使用人-- 单位列表
//        List<String> deptNameList = govStructCommonHandle4ImportService.listStructNames(
//                reqDto, GovStructTypeEnum.UNIT, Boolean.TRUE);
        columnMap.put(6, deptNames);
        columnMap.put(7, deptNames);
        // 车辆状态
        List<String> vehicleStatusList = VehicleStatusEnum.getDescList();
        columnMap.put(9, vehicleStatusList);
        // 车辆来源
        List<String> vehicleSourceList = VehicleSourceEnum.getDescList();
        columnMap.put(10, vehicleSourceList);
        //编制类型
        List<String> preparationTypeList = PreparationTypeEnum.getDescList();
        columnMap.put(11, preparationTypeList);
        // 购买服务供应商
        List<GovOrgDeptRelation> deptRelationList = govOrgDeptRelationService.list(new LambdaQueryWrapper<GovOrgDeptRelation>()
                .eq(GovOrgDeptRelation::getCompanyId, reqDto.getLoginCompanyId()).select(GovOrgDeptRelation::getOrgNo, GovOrgDeptRelation::getDeptCode));
        if (CollectionUtils.isNotEmpty(deptRelationList)) {
            List<GovOrgInfo> orgInfoList = govOrgInfoService.list(new LambdaQueryWrapper<GovOrgInfo>()
                    .eq(GovOrgInfo::getCompanyId, reqDto.getLoginCompanyId())
                    .in(GovOrgInfo::getOrgNo, deptRelationList.stream().map(GovOrgDeptRelation::getOrgNo).distinct().collect(Collectors.toList()))
                    .select(GovOrgInfo::getOrgName, GovOrgInfo::getOrgNo));
            if (CollectionUtils.isEmpty(orgInfoList)) {
                orgInfoList = Lists.newArrayList();
            }
            columnMap.put(12, orgInfoList.stream().map(GovOrgInfo::getOrgName).collect(Collectors.toList()));
        }

        // 车辆性质
        List<String> vehicleAttributeList = VehicleAttributeEnum.getDescList();
        columnMap.put(13, vehicleAttributeList);
        // 车辆使用部门-部门
//        List<String> structNameList = govStructCommonHandle4ImportService.listStructNames(
//                reqDto, GovStructTypeEnum.DEPARTMENT, Boolean.TRUE);
        columnMap.put(14, structNames);
        // 喷涂标识
        List<String> vehicleSprayMarkList = SprayLogoFlagEnum.getDescList();
        columnMap.put(16, vehicleSprayMarkList);
        // 配置款
//        List<String> configNameList = govVehicleConfigurationsService.listConfigNameList();
//        columnMap.put(18, configNameList);
        // 发动机排量
        columnMap.put(19, Arrays.stream(OutputVolumeConstant.OUTPUT_VOLUME.split(",")).collect(Collectors.toList()));

        //动力类型
//        List<String> fuelTypeNameList = govVehicleConfigurationsService.listFuelTypeName();
        List<String> fuelTypeNameList = FuelTypeEnum.getDescList();
        columnMap.put(20, fuelTypeNameList);
        //燃油标号
//        List<String> fuelGradeList = govVehicleConfigurationsService.listFuelGrade();
        List<String> fuelGradeList = FuelNoEnum.getDescList();
        columnMap.put(21, fuelGradeList);
        // 变速箱类型
//        List<String> gearNameList = govVehicleConfigurationsService.listGearName();
        List<String> gearNameList = GearboxTypeEnum.getDescList();
        columnMap.put(22, gearNameList);

        List<String> colorNameList = VehicleColorEnum.getDescList();
        columnMap.put(25, colorNameList);
        return columnMap;
    }


    @Override
    public BatchImportResultDTO importVehicle(BatchImportReqDTO reqDTO) {
        InputStream file = ExcelDownloadUtil.downloadFile(reqDTO.getExcelUrl());
//        InputStream file = null;
//        try {
//            file = new FileInputStream(new File("/Users/<USER>/Downloads/mrcar-gov/vehicle-import.xlsx"));
//        } catch (FileNotFoundException e) {
//            throw new RuntimeException(e);
//        }
        XSSFWorkbook workbook = null;
        try {
            workbook = new XSSFWorkbook(file);
        } catch (Exception e) {
            log.error("车辆信息导入，文件转换错误", e);
            throw new ApiException(RestErrorCode.EXCEL_CONVERT_ERROR);
        }
        XSSFSheet sheet = workbook.getSheetAt(0);
        final int lastRowNum = sheet.getLastRowNum();
        if (lastRowNum < 2) {
            throw new ApiException(RestErrorCode.EXCEL_DATA_EMPTY);
//            final BatchImportResultDTO batchDTO = new BatchImportResultDTO();
//            batchDTO.setTotalNum(BigDecimal.ZERO.intValue());
//            batchDTO.setSuccessNum(BigDecimal.ZERO.intValue());
//            batchDTO.setErrorNum(BigDecimal.ZERO.intValue());
//            batchDTO.setErrorFileUrl(StringUtils.EMPTY);
//            return batchDTO;
        }
        List<GovVehicleImportDTO> dataList = new LinkedList<>();
        for (int i = 2; i <= sheet.getLastRowNum(); i++) {
            final XSSFRow row = sheet.getRow(i);
            dataList.add(buildGovVehicleImportList(row));
        }
        // 进行插入 TODO
        return saveGovVehicleImportList(dataList, reqDTO);
    }

    /**
     * 供应商导入模版下载
     *
     * @param baseDTO
     * @param response
     */
    @Override
    public void downloadSupplierTemplate(BaseDTO baseDTO, HttpServletResponse response) {
        // key 列索引，value:字典
        Map<Integer, List<String>> columnDicMap = getSupplierColumnDicMap(baseDTO);
        org.springframework.core.io.Resource excelResource = new ClassPathResource(SUPPLIER_TEMPLATE_EXCEL_PATH);
        try {
            // 读取excel
            try (InputStream inStream = excelResource.getInputStream(); Workbook workbook = new XSSFWorkbook(inStream)) {
                Sheet sheet = workbook.getSheetAt(0);
                // 创建隐藏sheet
                Sheet hiddenSheet = workbook.createSheet(RandomStringUtils.randomAlphabetic(32));
                workbook.setSheetHidden(workbook.getSheetIndex(hiddenSheet), true);
                columnDicMap.forEach((columnIndex, dic) -> {
                    ExcelUtil.createDropdownSelectionItems(workbook, hiddenSheet, sheet, dic, 2, columnIndex);
                });
                //车辆品牌,车系联动
                supplierBranchSeriesSecondCascade(workbook, sheet);
                OutputStream outStream = response.getOutputStream();
                workbook.write(outStream);
                String fileName = URLEncoder.encode("供应商车辆导入模板", "UTF-8").replaceAll("\\+", "%20");
                response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ExcelTypeEnum.XLSX.getValue());
                outStream.flush();
                outStream.close();
            }
        } catch (Exception e) {
            log.error("供应商车辆导入模板下载异常", e);

        }
    }

    @Override
    public BatchImportResultDTO importSupplierVehicle(BatchImportReqDTO reqDTO) {
        InputStream file = ExcelDownloadUtil.downloadFile(reqDTO.getExcelUrl());
        XSSFWorkbook workbook = null;
        try {
            workbook = new XSSFWorkbook(file);
        } catch (Exception e) {
            log.error("供应商车辆信息导入，文件转换错误", e);
            throw new ApiException(RestErrorCode.EXCEL_CONVERT_ERROR);
        }
        XSSFSheet sheet = workbook.getSheetAt(0);
        final int lastRowNum = sheet.getLastRowNum();
        if (lastRowNum < 2) {
            throw new ApiException(RestErrorCode.EXCEL_DATA_EMPTY);
        }
        List<GovSupplierVehicleImportDTO> dataList = new LinkedList<>();
        for (int i = 2; i <= sheet.getLastRowNum(); i++) {
            final XSSFRow row = sheet.getRow(i);
            dataList.add(buildSupplierGovVehicleImportList(row));
        }
        return saveGovSupplierVehicleImportList(dataList, reqDTO);
    }

    /**
     * 供应商车辆校验
     *
     * @param dataList
     * @param baseDTO
     * @return
     */
    private BatchImportResultDTO saveGovSupplierVehicleImportList(List<GovSupplierVehicleImportDTO> dataList, BaseDTO baseDTO) {
        BatchImportResultDTO batchDTO = new BatchImportResultDTO();
        batchDTO.setTotal(dataList.size());
        for (GovSupplierVehicleImportDTO data : dataList) {
            checkSupplierGovVehicleImportDTO(data, baseDTO);
            if (StringUtils.isNotBlank(data.getErrorMsg())) {
                continue;
            }

            VehicleConfigurationRequestDTO vehicleConfiguration = checkAndMakeUpSupplierVehicleConfiguration(data);
            if (StringUtils.isNotBlank(data.getErrorMsg())) {
                continue;
            }
            try {
                // 添加车辆数据
                addSupplierVehicleInfo(data, baseDTO, vehicleConfiguration);
            } catch (ApiException e) {
                log.info("供应商车辆数据：{} 导入异常", JSON.toJSONString(data), e);
                data.setErrorMsg(e.getMsg());
            } catch (Exception e) {
                log.info("供应商车辆数据：{} 导入异常", JSON.toJSONString(data), e);
                data.setErrorMsg("未知异常");
            }
        }
        List<GovSupplierVehicleImportDTO> errorDataList =
                dataList.stream().filter(x -> StringUtils.isNotBlank(x.getErrorMsg())).collect(Collectors.toList());
        batchDTO.setError(errorDataList.size());
        batchDTO.setSuccess(dataList.size() - batchDTO.getError());
        if (CollectionUtils.isNotEmpty(errorDataList)) {
            dataList.forEach(d -> {
                if (StringUtils.isBlank(d.getErrorMsg())) {
                    d.setErrorMsg("导入成功");
                }
            });
            ExcelWriter excelWriter = null;
            ByteArrayOutputStream byteOut = null;
            try {
                byteOut = new ByteArrayOutputStream();
                excelWriter = EasyExcel.write(byteOut, GovSupplierVehicleImportDTO.class).build();
                WriteSheet writeSheet = EasyExcel.writerSheet(0, VEHICLE_IMPORT_ERROR_FILE_PRE_FIX).build();
                excelWriter.write(errorDataList, writeSheet);
                excelWriter.finish();
                UploadPretreatment uploadPretreatment = fileStorageService.of(byteOut.toByteArray());
                uploadPretreatment.setSaveFilename(VEHICLE_IMPORT_ERROR_FILE_PRE_FIX + System.currentTimeMillis() + ExcelTypeEnum.XLSX.getValue());
                FileInfo fileInfo = uploadPretreatment.upload();
                if (Objects.nonNull(fileInfo)) {
                    batchDTO.setErrorFileUrl(fileInfo.getUrl());
                }
            } catch (Exception e) {
                log.info("车辆数据：{} 导入，错误数据上传oss异常", e);
            } finally {
                if (Objects.nonNull(excelWriter)) {
                    excelWriter.finish();
                }
                if (Objects.nonNull(byteOut)) {
                    try {
                        byteOut.close();
                    } catch (IOException e) {
                    }
                }
            }
        }
        return batchDTO;


    }

    private Boolean addSupplierVehicleInfo(GovSupplierVehicleImportDTO data, BaseDTO baseDTO, VehicleConfigurationRequestDTO vehicleConfiguration) {
        VehicleSaveRequestDTO requestDTO = BeanUtil.copyObject(baseDTO, VehicleSaveRequestDTO.class);
        // 拷贝属性
        BeanUtils.copyProperties(data, requestDTO);
        if (Objects.nonNull(vehicleConfiguration)) {
            requestDTO.setVehicleConfiguration(vehicleConfiguration);
        }
        if (StringUtils.isNotBlank(requestDTO.getVehicleUseDeptName())) {
            requestDTO.setVehicleUseDeptName(getStructName(requestDTO.getVehicleUseDeptName()));
        }
        if (StringUtils.isNotBlank(requestDTO.getVehicleUseStructName())) {
            requestDTO.setVehicleUseStructName(getStructName(requestDTO.getVehicleUseStructName()));
        }
        if (StringUtils.isNotBlank(requestDTO.getVehicleBelongDeptName())) {
            requestDTO.setVehicleBelongDeptName(getStructName(requestDTO.getVehicleBelongDeptName()));
        }
        if (StringUtils.isNotBlank(requestDTO.getLoginUserBelongStructName())) {
            requestDTO.setLoginUserBelongStructName(getStructName(requestDTO.getLoginUserBelongStructName()));
        }

        requestDTO.setSourceType(VehicleSourceTypeEnum.SOCIAL_VEHICLE.getCode());
        vehicleInfoService.checkVehicleParam(requestDTO);
        vehicleInfoService.setVehicleParam(requestDTO);
        return govVehicleBaseInfoService.saveVehicle(requestDTO);
    }

    private VehicleConfigurationRequestDTO checkAndMakeUpSupplierVehicleConfiguration(GovSupplierVehicleImportDTO data) {
        VehicleConfigurationRequestDTO vehicleConfiguration = new VehicleConfigurationRequestDTO();
        if (StringUtils.isNotBlank(data.getOutputVolume())) {
            List<String> outputVolumeList = Arrays.stream(OutputVolumeConstant.OUTPUT_VOLUME.split(",")).collect(Collectors.toList());
            if (!outputVolumeList.contains(data.getOutputVolume())) {
                setErrorMsg(data, "请检查发动机排量是否与模板中的选项一致");
                return null;
            }
            vehicleConfiguration.setOutputVolume(data.getOutputVolume());

        }
        if (StringUtils.isNotBlank(data.getFuelTypeStr())) {
            Integer fuelType = FuelTypeEnum.getCodeByDesc(data.getFuelTypeStr());
            if (Objects.isNull(fuelType)) {
                setErrorMsg(data, "请检查动力类型是否与模板中的选项一致");
                return null;
            }
            vehicleConfiguration.setFuelType(fuelType);
        }
        if (StringUtils.isNotBlank(data.getFuelNo())) {
            String fuelNo = FuelNoEnum.getCodeByDesc(data.getFuelNo());
            if (Objects.isNull(fuelNo)) {
                setErrorMsg(data, "请检查燃油标号是否与模板中的选项一致");
                return null;
            }
            vehicleConfiguration.setFuelNo(data.getFuelNo());
        }
        if (StringUtils.isNotBlank(data.getGearboxTypeStr())) {
            Integer gearboxType = GearboxTypeEnum.getCodeByDesc(data.getGearboxTypeStr());
            if (Objects.isNull(gearboxType)) {
                setErrorMsg(data, "请检查变速箱类型是否与模板中的选项一致");
                return null;
            }
            vehicleConfiguration.setGearboxType(gearboxType);
        }

        if (StringUtils.isNotBlank(data.getApprovedPassengerSum())) {
            vehicleConfiguration.setApprovedPassengerSum(data.getApprovedPassengerSum());
        }
        if (StringUtils.isNotBlank(data.getVehicleBodyColor())) {
            vehicleConfiguration.setVehicleBodyColor(data.getVehicleBodyColor());
        }

        // 先不做颜色校验
        if (StringUtils.isNotBlank(data.getVehicleBodyColor())) {
            vehicleConfiguration.setVehicleBodyColor(data.getVehicleBodyColor());
        }
        return vehicleConfiguration;
    }

    private void checkSupplierGovVehicleImportDTO(GovSupplierVehicleImportDTO data, BaseDTO baseDTO) {
        if (StringUtils.isBlank(data.getVehicleLicense())) {
            setErrorMsg(data, "车牌号不能为空");
            return;
        } else if (!AssetConstant.vehicleLicensePattern.matcher(data.getVehicleLicense()).matches()) {
            setErrorMsg(data, "车牌号格式错误，请重新确认");
            return;
        }
        if (StringUtils.isBlank(data.getVehicleVin())) {
            setErrorMsg(data, "车架号不能为空");
            return;
        } else if (!VerifyUtil.checkVehicleVin(data.getVehicleVin())) {
            setErrorMsg(data, "车架号格式错误，请重新确认");
            return;
        }
        if (StringUtils.isBlank(data.getVehicleTypeStr())) {
            setErrorMsg(data, "车辆类型不能为空");
            return;
        }
        Integer vehicleType = VehicleTypeEnum.getCodeByDesc(data.getVehicleTypeStr());
        if (Objects.isNull(vehicleType)) {
            setErrorMsg(data, "请检查车辆类型是否与模板中的选项一致");
            return;
        }
        data.setVehicleType(vehicleType);

        if (StringUtils.isBlank(data.getVehicleBrandName())) {
            setErrorMsg(data, "车辆品牌不能为空");
            return;
        }
        GovVehicleBrands govVehicleBrands = govVehicleBrandsService.getBaseMapper().selectOne(
                new LambdaQueryWrapper<GovVehicleBrands>().eq(GovVehicleBrands::getBrandName, data.getVehicleBrandName()));
        if (Objects.isNull(govVehicleBrands)) {
            setErrorMsg(data, "请检查车辆品牌是否与模板中的选项一致");
            return;
        }
        data.setVehicleBrandId(govVehicleBrands.getBrandId());
        if (StringUtils.isBlank(data.getVehicleSeriesName())) {
            setErrorMsg(data, "车系不能为空");
            return;
        }
        GovVehicleSeries govVehicleSeries = govVehicleSeriesService.getBaseMapper().selectOne(
                new LambdaQueryWrapper<GovVehicleSeries>()
                        .eq(GovVehicleSeries::getSeriesName, data.getVehicleSeriesName())
                        .last("limit 1"));
        if (Objects.isNull(govVehicleSeries)) {
            setErrorMsg(data, "请检查车系是否与模板中的选项一致");
            return;
        }
        if (!Objects.equals(govVehicleSeries.getBrandId(), govVehicleBrands.getBrandId())) {
            setErrorMsg(data, "品牌和车系不匹配");
            return;
        }
        data.setVehicleSeriesId(govVehicleSeries.getSeriesId());
        if (StringUtils.isBlank(data.getParkCityName())) {
            setErrorMsg(data, "停放城市不能为空");
            return;
        }
        GovCityDic govCityDic = govCityDicService.getBaseMapper().selectOne(new LambdaQueryWrapper<GovCityDic>().
                eq(GovCityDic::getCityName, data.getParkCityName())
                .last("limit 1"));

        if (govCityDic == null) {
            setErrorMsg(data, "停放城市不存在");
            return;
        }
        data.setParkCityCode(govCityDic.getCityCode());

        if (StringUtils.isBlank(data.getRegisterDateStr())) {
            setErrorMsg(data, "登记日期不能为空");
            return;
        }
        if (StringUtils.isBlank(data.getVehicleStatusStr())) {
            setErrorMsg(data, "车辆状态不能为空");
            return;
        }

        Integer vehicleStatus = VehicleStatusEnum.getCodeByDesc(data.getVehicleStatusStr());
        if (Objects.isNull(vehicleStatus)) {
            setErrorMsg(data, "请检查车辆状态是否与模板中的选项一致");
            return;
        }
        data.setVehicleStatus(vehicleStatus);

        if (StringUtils.isBlank(data.getRentalPurposeStr())) {
            setErrorMsg(data, "租赁用途不能为空");
        }
        Integer rentType = GovPublicCarRentTypeEnum.getCodeByDesc(data.getRentalPurposeStr());
        if (Objects.isNull(rentType)) {
            setErrorMsg(data, "请检查租赁用途是否与模板中的选项一致");
            return;
        }
        data.setRentalPurpose(rentType);

        if (StringUtils.isBlank(data.getRemoteLockStr())) {
            setErrorMsg(data, "远程开关锁不能为空");
        }
        Integer remoteLock = GovPublicCarRemoteLockEnum.getCodeByDesc(data.getRemoteLockStr());
        if (Objects.isNull(remoteLock)) {
            setErrorMsg(data, "请检查远程开关锁是否与模板中的选项一致");
            return;
        }
        data.setRemoteLock(remoteLock);
        if (Objects.equals(data.getRentalPurpose(), GovPublicCarRentTypeEnum.TIME_SHARE.getCode())) {//分时必传车辆服务类型 跟车辆管理单位
            if (StringUtils.isBlank(data.getVehicleServiceTypeStr())) {
                setErrorMsg(data, "车辆服务类型不能为空");
            }
            Integer vehicleServiceType = GovVehicleServiceTypeEnum.getCodeByDesc(data.getVehicleServiceTypeStr());
            if (Objects.isNull(vehicleServiceType)) {
                setErrorMsg(data, "请检车辆服务类型是否与模板中的选项一致");
                return;
            }
            data.setVehicleServiceType(vehicleServiceType);
            if (StringUtils.isBlank(data.getVehicleBelongDeptName())) {
                setErrorMsg(data, "车辆管理单位不能为空");
                return;
            }
            GovStructRequestDTO govStructRequestDTO = assembleGovStructRequestDTO(data, baseDTO);
            List<GovStructRespondDTO> govStructRespondDTOS = govStructService.queryVehicleServiceUnitList(govStructRequestDTO);
            if (CollectionUtils.isNotEmpty(govStructRespondDTOS)) {
                String structName = getStructName(data.getVehicleBelongDeptName());
                Optional<GovStructRespondDTO> first = govStructRespondDTOS.stream().filter(govStructRespondDTO -> Objects.equals(govStructRespondDTO.getStructName(), structName)).findFirst();
                if (!first.isPresent()) {
                    String collect = govStructRespondDTOS.stream().map(GovStructRespondDTO::getStructName).collect(Collectors.joining(","));
                    setErrorMsg(data, "车辆管理单位不存在或与车辆服务类型管理单位不匹配!当前可选的车辆管理单位为" + collect + ",请检查是否选择正确");
                    return;
                }
                data.setVehicleBelongDeptCode(first.get().getStructCode());
            } else {
                setErrorMsg(data, "当前公司无可选车辆管理单位，请检查导入单位是否正确");
                return;
            }

        } else {
            data.setVehicleBelongDeptName("");
            data.setVehicleBelongDeptCode("");
        }
        if (StringUtils.isBlank(data.getSupplierServiceName())) {
            setErrorMsg(data, "供应商不能为空");
            return;
        }
        GovOrgInfo govOrgInfo = govOrgInfoService.getBaseMapper().selectOne(new LambdaQueryWrapper<GovOrgInfo>().eq(GovOrgInfo::getOrgStatus, 1)
                .eq(GovOrgInfo::getOrgType, 4).eq(GovOrgInfo::getOrgName, data.getSupplierServiceName()));
        if (govOrgInfo == null) {
            setErrorMsg(data, "供应商不存在,请检查是否填写正确");
            return;

        }
        data.setSupplierServiceCode(govOrgInfo.getOrgNo());

        if (StringUtils.isNotBlank(data.getDriverMobile())) {
            if (ValidationUtils.isMobile(data.getDriverMobile())) {
                setErrorMsg(data, "司机手机号格式不正确");
                return;
            }
            GovUser govUser = govUserService.getBaseMapper().
                    selectOne(new LambdaQueryWrapper<GovUser>().eq(GovUser::getUserType, GovUserTypeEnum.SOCIAL_DRIVER.getCode()).
                            eq(GovUser::getMobile, data.getDriverMobile()));
            if (govUser == null) {
                setErrorMsg(data, "司机不存在,请检查手机号是否填写正确");
                return;
            }
            if (StringUtils.isNotBlank(govUser.getSupplierServiceCode()) && !govUser.getSupplierServiceCode().equals(data.getSupplierServiceCode())) {
                setErrorMsg(data, "司机与车辆供应商不一致，请检查填写是否正确");
                return;
            }
            data.setDriverName(govUser.getUserName());
            data.setDriverCode(govUser.getUserCode());
        }
        if (StringUtils.isNotBlank(data.getSupplierOutletsName()) && data.getSupplierOutletsName().length() > 50) {
            setErrorMsg(data, "供应商网点名称长度不能超过50个字符");
            return;

        }
        if (StringUtils.isNotBlank(data.getRemark()) && data.getRemark().length() > 500) {
            setErrorMsg(data, "备注长度不能超过500个字符");
        }

    }

    private GovStructRequestDTO assembleGovStructRequestDTO(GovSupplierVehicleImportDTO data, BaseDTO baseDTO) {
        GovStructRequestDTO govStructRequestDTO = new GovStructRequestDTO();
        if (Objects.equals(data.getVehicleServiceType(), GovVehicleServiceTypeEnum.STAGE_CAR.getCode())) {//平台车
            govStructRequestDTO.setManageCar(1);
            govStructRequestDTO.setManageCarType(1);
        }
        govStructRequestDTO.setLoginCompanyId(baseDTO.getLoginCompanyId());
        govStructRequestDTO.setStructType(2);
        return govStructRequestDTO;
    }


    private Map<Integer, List<String>> getSupplierColumnDicMap(BaseDTO reqDto) {
        Map<Integer, List<String>> columnMap = Maps.newLinkedHashMap();
        //车辆类型
        List<String> vehicleTypeList = VehicleTypeEnum.getDescList();
        columnMap.put(2, vehicleTypeList);
        //城市列表
        List<GovCityDic> govCityDics = govCityDicService.listCityDic();
        columnMap.put(5, govCityDics.stream().map(GovCityDic::getCityName).collect(Collectors.toList()));
        // 车辆状态
        List<String> vehicleStatusList = VehicleStatusEnum.getDescList();
        columnMap.put(7, vehicleStatusList);

        //租赁用途
        columnMap.put(8, GovPublicCarRentTypeEnum.getDescList());

        //远程开关锁
        columnMap.put(9, GovPublicCarRemoteLockEnum.getDescList());

        //车辆服务类型
        columnMap.put(10, GovVehicleServiceTypeEnum.getDescList());

        //车辆管理单位
        List<GovStructImportDTO> allStInfo = govStructCommonHandle4ImportService.listStructInfo(reqDto, null, Boolean.TRUE);
        List<String> deptNames = allStInfo.stream().filter(s -> Objects.equals(s.getStructType(), GovStructTypeEnum.UNIT.getCode())).map(GovStructImportDTO::getStructLevelName).collect(Collectors.toList());
        deptNames.sort(Comparator.comparingInt(String::length));
        columnMap.put(11, deptNames);


        //供应商下来选逻辑：当前用户=政府员工||司机 可选所有供应商， 当前用户=供应商员工||供应商司机  只能选自己供应商
        Integer loginUserType = reqDto.getLoginUserType();
        String loginOrgNo = null;
        if (GovUserTypeEnum.RENT_COMPANY_EMPLOYEE.getCode().equals(loginUserType) || GovUserTypeEnum.SOCIAL_DRIVER.getCode().equals(loginUserType)) {
            loginOrgNo = reqDto.getLoginOrgNo();
        }
        List<GovOrgInfo> orgInfoList = govOrgInfoService.list(new LambdaQueryWrapper<GovOrgInfo>()
                .eq(Objects.nonNull(loginOrgNo), GovOrgInfo::getOrgNo, loginOrgNo)
                .eq(GovOrgInfo::getOrgStatus, 1)
                .eq(GovOrgInfo::getOrgType, 4)
                .select(GovOrgInfo::getOrgName));
        if (CollectionUtils.isNotEmpty(orgInfoList)) {
            columnMap.put(12, orgInfoList.stream().map(GovOrgInfo::getOrgName).collect(Collectors.toList()));
        }
        // 发动机排量
        columnMap.put(17, Arrays.stream(OutputVolumeConstant.OUTPUT_VOLUME.split(",")).collect(Collectors.toList()));

        //动力类型
        List<String> fuelTypeNameList = FuelTypeEnum.getDescList();
        columnMap.put(18, fuelTypeNameList);
        //燃油标号
        List<String> fuelGradeList = FuelNoEnum.getDescList();
        columnMap.put(19, fuelGradeList);
        // 变速箱类型
        List<String> gearNameList = GearboxTypeEnum.getDescList();
        columnMap.put(20, gearNameList);
        //车身颜色
        List<String> colorNameList = VehicleColorEnum.getDescList();
        columnMap.put(22, colorNameList);
        return columnMap;
    }


    private BatchImportResultDTO saveGovVehicleImportList(List<GovVehicleImportDTO> dataList,
                                                          BaseDTO baseDTO) {
        BatchImportResultDTO batchDTO = new BatchImportResultDTO();
        batchDTO.setTotal(dataList.size());

        List<GovStructImportDTO> allStInfo = govStructCommonHandle4ImportService.listStructInfo(baseDTO, null, Boolean.TRUE);
        List<GovStructImportDTO> deptList = allStInfo.stream().filter(s -> Objects.equals(s.getStructType(), GovStructTypeEnum.UNIT.getCode())).collect(Collectors.toList());
        List<GovStructImportDTO> structList = allStInfo.stream().filter(s -> Objects.equals(s.getStructType(), GovStructTypeEnum.DEPARTMENT.getCode())).collect(Collectors.toList());
        Map<String, GovStructImportDTO> deptMap = deptList.stream().collect(Collectors.toMap(GovStructImportDTO::getStructLevelName, Function.identity()));
        Map<String, GovStructImportDTO> structMap = structList.stream().collect(Collectors.toMap(GovStructImportDTO::getStructLevelName, Function.identity()));
        // 解析数据 后续使用多线程处理 TODO
        for (GovVehicleImportDTO data : dataList) {
            checkGovVehicleImportDTO(data, baseDTO);
            if (StringUtils.isNotBlank(data.getErrorMsg())) {
                continue;
            }
            // 查询部门或单位信息
            checkStructInfo(data, deptMap, structMap);
            if (StringUtils.isNotBlank(data.getErrorMsg())) {
                continue;
            }
            VehicleConfigurationRequestDTO vehicleConfiguration = checkAndMakeUpVehicleConfiguration(data);
            if (StringUtils.isNotBlank(data.getErrorMsg())) {
                continue;
            }
            try {
//                GovVehicleBaseInfo vehicleInfo = getVehicle(baseDTO.getLoginCompanyId(), data.getVehicleVin());
                // 添加车辆数据
                addVehicleInfo(data, baseDTO, vehicleConfiguration);
            } catch (ApiException e) {
                log.info("车辆数据：{} 导入异常", JSON.toJSONString(data), e);
                data.setErrorMsg(e.getMsg());
            } catch (Exception e) {
                log.info("车辆数据：{} 导入异常", JSON.toJSONString(data), e);
                data.setErrorMsg("未知异常");
            }
        }
        List<GovVehicleImportDTO> errorDataList =
                dataList.stream().filter(x -> StringUtils.isNotBlank(x.getErrorMsg())).collect(Collectors.toList());
        batchDTO.setError(errorDataList.size());
        batchDTO.setSuccess(dataList.size() - batchDTO.getError());
        if (CollectionUtils.isNotEmpty(errorDataList)) {
            dataList.forEach(d -> {
                if (StringUtils.isBlank(d.getErrorMsg())) {
                    d.setErrorMsg("导入成功");
                }
            });
            ExcelWriter excelWriter = null;
            ByteArrayOutputStream byteOut = null;
            try {
                byteOut = new ByteArrayOutputStream();
                excelWriter = EasyExcel.write(byteOut, GovVehicleImportDTO.class).build();
                WriteSheet writeSheet = EasyExcel.writerSheet(0, VEHICLE_IMPORT_ERROR_FILE_PRE_FIX).build();
                excelWriter.write(errorDataList, writeSheet);
                excelWriter.finish();
                UploadPretreatment uploadPretreatment = fileStorageService.of(byteOut.toByteArray());
                uploadPretreatment.setSaveFilename(VEHICLE_IMPORT_ERROR_FILE_PRE_FIX + System.currentTimeMillis() + ExcelTypeEnum.XLSX.getValue());
                FileInfo fileInfo = uploadPretreatment.upload();
                if (Objects.nonNull(fileInfo)) {
                    batchDTO.setErrorFileUrl(fileInfo.getUrl());
                }
            } catch (Exception e) {
                log.info("车辆数据：{} 导入，错误数据上传oss异常", e);
            } finally {
                if (Objects.nonNull(excelWriter)) {
                    excelWriter.finish();
                }
                if (Objects.nonNull(byteOut)) {
                    try {
                        byteOut.close();
                    } catch (IOException e) {
                    }
                }
            }
        }
        return batchDTO;
    }

    private GovVehicleBaseInfo getVehicle(Integer companyId, String vehicleVin) {
        return govVehicleBaseInfoService.getBaseMapper().selectOne(
                new LambdaQueryWrapper<GovVehicleBaseInfo>().eq(GovVehicleBaseInfo::getVehicleVin, vehicleVin).eq(GovVehicleBaseInfo::getCompanyId, companyId));

    }

    private Boolean addVehicleInfo(GovVehicleImportDTO data,
                                   BaseDTO baseDTO,
                                   VehicleConfigurationRequestDTO vehicleConfiguration) {
        VehicleSaveRequestDTO requestDTO = BeanUtil.copyObject(baseDTO, VehicleSaveRequestDTO.class);
//        if(Objects.nonNull(vehicleInfo)){
//            requestDTO.setVehicleNo(vehicleInfo.getVehicleNo());
//        }
        // 拷贝属性
        BeanUtils.copyProperties(data, requestDTO);
        if (Objects.nonNull(vehicleConfiguration)) {
            requestDTO.setVehicleConfiguration(vehicleConfiguration);
        }
        if (StringUtils.isNotBlank(requestDTO.getVehicleUseDeptName())) {
            requestDTO.setVehicleUseDeptName(getStructName(requestDTO.getVehicleUseDeptName()));
        }
        if (StringUtils.isNotBlank(requestDTO.getVehicleUseStructName())) {
            requestDTO.setVehicleUseStructName(getStructName(requestDTO.getVehicleUseStructName()));
        }
        if (StringUtils.isNotBlank(requestDTO.getVehicleBelongDeptName())) {
            requestDTO.setVehicleBelongDeptName(getStructName(requestDTO.getVehicleBelongDeptName()));
        }
        if (StringUtils.isNotBlank(requestDTO.getLoginUserBelongStructName())) {
            requestDTO.setLoginUserBelongStructName(getStructName(requestDTO.getLoginUserBelongStructName()));
        }
        GovStructRespondDTO govStructResp = govStructService.getGovStructByCarOwner(data.getVehicleBelongDeptCode(), data.getUseAttribute());
        if (Objects.nonNull(govStructResp)) {
            requestDTO.setManageCarType(govStructResp.getManageCarType());
            requestDTO.setVehicleManageDeptCode(govStructResp.getStructCode());
            requestDTO.setVehicleManageDeptName(govStructResp.getStructName());
        }
        requestDTO.setSourceType(VehicleSourceTypeEnum.GOV_VEHICLE.getCode());
        vehicleInfoService.checkVehicleParam(requestDTO);
        vehicleInfoService.setVehicleParam(requestDTO);
        return govVehicleBaseInfoService.saveVehicle(requestDTO);
    }

    private void checkStructInfo(GovVehicleImportDTO data,
                                 Map<String, GovStructImportDTO> deptMap,
                                 Map<String, GovStructImportDTO> structMap) {
        String belongDeptName = data.getVehicleBelongDeptLevelName().trim();
        GovStructImportDTO belongDept = deptMap.get(belongDeptName);
        if (Objects.isNull(belongDept)) {
            setErrorMsg(data, "请检查车辆所有人是否与模板中的选项一致");
            return;
        }
        data.setVehicleBelongDeptName(belongDept.getStructName());
        data.setVehicleBelongDeptCode(belongDept.getStructCode());
        data.setVehicleBelongDeptId(belongDept.getId());
        String useDeptName = data.getVehicleUseDeptLevelName().trim();
        GovStructImportDTO useDept = deptMap.get(useDeptName);
        if (Objects.isNull(useDept)) {
            setErrorMsg(data, "请检查车辆使用人是否与模板中的选项一致");
            return;
        }
        data.setVehicleUseDeptName(useDept.getStructName());
        data.setVehicleUseDeptCode(useDept.getStructCode());
        data.setVehicleUseDeptId(useDept.getId());
        if (StringUtils.isNotBlank(data.getVehicleUseStructLevelName())) {
            String useStructName = data.getVehicleUseStructLevelName().trim();
            GovStructImportDTO useStruct = structMap.get(useStructName);
            if (Objects.isNull(useStruct)) {
                setErrorMsg(data, "请检查车辆使用部门是否与模板中的选项一致");
                return;
            }
            data.setVehicleUseStructName(useStruct.getStructName());
            data.setVehicleUseStructCode(useStruct.getStructCode());
            data.setVehicleUseStructId(useStruct.getId());
            // 校验使用单位和使用部门是否匹配
            String split = "->";
            String[] useDeptNames = useDeptName.split(split);
            String[] useStructNames = useStructName.split(split);
            for (int idx = useDeptNames.length - 1; idx >= 0; idx--) {
                if (useStructNames.length <= idx) {
                    setErrorMsg(data, "使用单单位和使用部门不匹配");
                    return;
                }
                if (!useStructNames[idx].equals(useDeptNames[idx])) {
                    setErrorMsg(data, "使用单位和使用部门不匹配");
                    return;
                }
            }
        }
        if (Objects.equals(data.getPreparationType(), PreparationTypeEnum.PREPARATION_VEHICLE.getCode())) {
            if (StringUtils.isBlank(data.getSupplierServiceName())) {
                setErrorMsg(data, "购买服务供应商不能为空");
                return;
            }
            VehicleSupplierRequestDTO vehicleSupplierRequestDTO = new VehicleSupplierRequestDTO();
            vehicleSupplierRequestDTO.setVehicleBelongDeptCode(data.getVehicleBelongDeptCode());
            List<GovOrgNameDTO> orgNameDTOS = govOrgDeptRelationService.listVehicleSupplier(vehicleSupplierRequestDTO);
            if (CollectionUtils.isEmpty(orgNameDTOS)) {
                setErrorMsg(data, "车辆所有人无购买服务供应商");
                return;
            }
            List<GovOrgNameDTO> filterList = orgNameDTOS.stream().filter(org -> Objects.equals(org.getOrgName(), data.getSupplierServiceName())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(filterList)) {
                setErrorMsg(data, "车辆所有人购买服务供应商列表不包含所选购买服务供应商");
                return;
            }
            data.setSupplierServiceCode(filterList.get(0).getOrgNo());
        }
    }

    private VehicleConfigurationRequestDTO checkAndMakeUpVehicleConfiguration(GovVehicleImportDTO data) {
        VehicleConfigurationRequestDTO vehicleConfiguration = new VehicleConfigurationRequestDTO();
        if (StringUtils.isNotBlank(data.getOutputVolume())) {
            vehicleConfiguration.setOutputVolume(data.getOutputVolume());
        }
        if (StringUtils.isNotBlank(data.getConfigurationVersionName())) {
            vehicleConfiguration.setConfigurationVersionName(data.getConfigurationVersionName());

            GovVehicleConfigurations govVehicleConfigurations = govVehicleConfigurationsService.getBaseMapper().selectOne(
                    new LambdaQueryWrapper<GovVehicleConfigurations>().eq(GovVehicleConfigurations::getConfigName, data.getConfigurationVersionName()));
            if (Objects.nonNull(govVehicleConfigurations)) {
                vehicleConfiguration.setConfigurationVersionId(govVehicleConfigurations.getConfigId());
//                vehicleConfiguration.setOutputVolume(govVehicleConfigurations.getLiter());
//                vehicleConfiguration.setFuelNo(govVehicleConfigurations.getFuelGrade());
            }
        }
        if (StringUtils.isNotBlank(data.getOutputVolume())) {
            List<String> outputVolumeList = Arrays.stream(OutputVolumeConstant.OUTPUT_VOLUME.split(",")).collect(Collectors.toList());
            if (!outputVolumeList.contains(data.getOutputVolume())) {
                setErrorMsg(data, "请检查发动机排量是否与模板中的选项一致");
                return null;
            }
        }
        if (StringUtils.isNotBlank(data.getFuelTypeStr())) {
            Integer fuelType = FuelTypeEnum.getCodeByDesc(data.getFuelTypeStr());
            if (Objects.isNull(fuelType)) {
                setErrorMsg(data, "请检查动力类型是否与模板中的选项一致");
                return null;
            }
            vehicleConfiguration.setFuelType(fuelType);
        }
        if (StringUtils.isNotBlank(data.getFuelNo())) {
            String fuelNo = FuelNoEnum.getCodeByDesc(data.getFuelNo());
            if (Objects.isNull(fuelNo)) {
                setErrorMsg(data, "请检查燃油标号是否与模板中的选项一致");
                return null;
            }
            vehicleConfiguration.setFuelNo(data.getFuelNo());
        }
        if (StringUtils.isNotBlank(data.getGearboxTypeStr())) {
            Integer gearboxType = GearboxTypeEnum.getCodeByDesc(data.getGearboxTypeStr());
            if (Objects.isNull(gearboxType)) {
                setErrorMsg(data, "请检查变速箱类型是否与模板中的选项一致");
                return null;
            }
            vehicleConfiguration.setGearboxType(gearboxType);
        }
        if (StringUtils.isNotBlank(data.getOilTankVolume())) {
            vehicleConfiguration.setOilTankVolume(data.getOilTankVolume());
        }
        if (StringUtils.isNotBlank(data.getApprovedPassengerSum())) {
            vehicleConfiguration.setApprovedPassengerSum(data.getApprovedPassengerSum());
        }
        if (StringUtils.isNotBlank(data.getVehicleBodyColor())) {
            vehicleConfiguration.setVehicleBodyColor(data.getVehicleBodyColor());
        }
        if (StringUtils.isNotBlank(data.getVehicleBarePrice())) {
            vehicleConfiguration.setVehicleBarePrice(new BigDecimal(data.getVehicleBarePrice()));
        }
        // 先不做颜色校验
        if (StringUtils.isNotBlank(data.getVehicleBodyColor())) {
            vehicleConfiguration.setVehicleBodyColor(data.getVehicleBodyColor());
        }
        return vehicleConfiguration;
    }


    private void checkGovVehicleImportDTO(GovVehicleImportDTO data, BaseDTO baseDTO) {
        if (StringUtils.isBlank(data.getVehicleLicense())) {
            setErrorMsg(data, "车牌号不能为空");
            return;
        }
        if (StringUtils.isBlank(data.getVehicleVin())) {
            setErrorMsg(data, "车架号不能为空");
            return;
        }
        if (StringUtils.isBlank(data.getUseAttributeName())) {
            setErrorMsg(data, "使用性质不能为空");
            return;
        }
        if (StringUtils.isEmpty(data.getConfigurationDateStr())) {
            setErrorMsg(data, "配置日期不能为空");
            return;
        }

        Integer userAttr = UseAttributeEnum.getCodeByDesc(data.getUseAttributeName());
        if (Objects.isNull(userAttr)) {
            setErrorMsg(data, "请检查使用性质是否与模板中的选项一致");
            return;
        }
        data.setUseAttribute(userAttr);
        if (StringUtils.isBlank(data.getVehicleTypeStr())) {
            setErrorMsg(data, "车辆类型不能为空");
            return;
        }
        Integer vehicleType = VehicleTypeEnum.getCodeByDesc(data.getVehicleTypeStr());
        if (Objects.isNull(vehicleType)) {
            setErrorMsg(data, "请检查车辆类型是否与模板中的选项一致");
            return;
        }
        data.setVehicleType(vehicleType);
        if (StringUtils.isBlank(data.getVehicleBrandName())) {
            setErrorMsg(data, "车辆品牌不能为空");
            return;
        }
        GovVehicleBrands govVehicleBrands = govVehicleBrandsService.getBaseMapper().selectOne(
                new LambdaQueryWrapper<GovVehicleBrands>().eq(GovVehicleBrands::getBrandName, data.getVehicleBrandName()));
        if (Objects.isNull(govVehicleBrands)) {
            setErrorMsg(data, "请检查车辆品牌是否与模板中的选项一致");
            return;
        }
        data.setVehicleBrandId(govVehicleBrands.getBrandId());
        if (StringUtils.isBlank(data.getVehicleSeriesName())) {
            setErrorMsg(data, "车系不能为空");
            return;
        }
        GovVehicleSeries govVehicleSeries = govVehicleSeriesService.getBaseMapper().selectOne(
                new LambdaQueryWrapper<GovVehicleSeries>()
                        .eq(GovVehicleSeries::getSeriesName, data.getVehicleSeriesName())
                        .last("limit 1"));
        if (Objects.isNull(govVehicleSeries)) {
            setErrorMsg(data, "请检查车系是否与模板中的选项一致");
            return;
        }
        if (!Objects.equals(govVehicleSeries.getBrandId(), govVehicleBrands.getBrandId())) {
            setErrorMsg(data, "品牌和车系不匹配");
            return;
        }
        data.setVehicleSeriesId(govVehicleSeries.getSeriesId());
        if (StringUtils.isBlank(data.getVehicleBelongDeptLevelName())) {
            setErrorMsg(data, "车辆所有人不能为空");
            return;
        }
        if (StringUtils.isBlank(data.getVehicleUseDeptLevelName())) {
            setErrorMsg(data, "车辆使用人不能为空");
            return;
        }
        if (StringUtils.isBlank(data.getRegisterDateStr())) {
            setErrorMsg(data, "注册日期不能为空");
            return;
        }
        if (StringUtils.isBlank(data.getVehicleStatusStr())) {
            setErrorMsg(data, "车辆状态不能为空");
            return;
        }
        Integer vehicleStatus = VehicleStatusEnum.getCodeByDesc(data.getVehicleStatusStr());
        if (Objects.isNull(vehicleStatus)) {
            setErrorMsg(data, "请检查车辆状态是否与模板中的选项一致");
            return;
        }
        data.setVehicleStatus(vehicleStatus);
        if (StringUtils.isBlank(data.getVehicleSourceStr())) {
            setErrorMsg(data, "车辆来源不能为空");
            return;
        }
        Integer vehicleSource = VehicleSourceEnum.getCodeByDesc(data.getVehicleSourceStr());
        if (Objects.isNull(vehicleSource)) {
            setErrorMsg(data, "请检查车辆状态是否与模板中的选项一致");
            return;
        }
        data.setVehicleSource(vehicleSource);

        if (StringUtils.isBlank(data.getPreparationTypeStr())) {
            setErrorMsg(data, "编制类型不能为空");
            return;
        }
        Integer preparationType = PreparationTypeEnum.getCodeByDesc(data.getPreparationTypeStr());
        if (Objects.isNull(preparationType)) {
            setErrorMsg(data, "请检查编制类型是否与模板中的选项一致");
            return;
        }
        data.setPreparationType(preparationType);
        if (StringUtils.isBlank(data.getVehicleAttributeStr())) {
            setErrorMsg(data, "车辆性质不能为空");
            return;
        }
        Integer vehicleAttribute = VehicleAttributeEnum.getCodeByDesc(data.getVehicleAttributeStr());
        if (Objects.isNull(vehicleAttribute)) {
            setErrorMsg(data, "请检查车辆性质是否与模板中的选项一致");
            return;
        }
        data.setVehicleAttribute(vehicleAttribute);

        if (StringUtils.isNotBlank(data.getSprayLogoFlagStr())) {
            Integer sprayLogoFlag = SprayLogoFlagEnum.getCodeByDesc(data.getSprayLogoFlagStr());
            if (Objects.isNull(sprayLogoFlag)) {
                setErrorMsg(data, "请检查喷涂标识是否与模板中的选项一致");
                return;
            }
            data.setSprayLogoFlag(sprayLogoFlag);
        }
    }

    private void setErrorMsg(GovVehicleImportDTO data, String errorMsg) {
        if (StringUtils.isBlank(data.getErrorMsg())) {
            data.setErrorMsg(errorMsg);
        } else {
            data.setErrorMsg(data.getErrorMsg() + "," + errorMsg);
        }
    }

    private void setErrorMsg(GovSupplierVehicleImportDTO data, String errorMsg) {
        if (StringUtils.isBlank(data.getErrorMsg())) {
            data.setErrorMsg(errorMsg);
        } else {
            data.setErrorMsg(data.getErrorMsg() + "," + errorMsg);
        }
    }

    private GovVehicleImportDTO buildGovVehicleImportList(XSSFRow row) {
        final GovVehicleImportDTO importDto = new GovVehicleImportDTO();
        int idx = 0;
        importDto.setVehicleLicense(ExcelReadUtil.getCellValue(row.getCell(idx++)));
        importDto.setVehicleVin(ExcelReadUtil.getCellValue(row.getCell(idx++)));
        importDto.setUseAttributeName(ExcelReadUtil.getCellValue(row.getCell(idx++)));
        // 车辆类型
        importDto.setVehicleTypeStr(ExcelReadUtil.getCellValue(row.getCell(idx++)));
        // 车辆品牌
        importDto.setVehicleBrandName(ExcelReadUtil.getCellValue(row.getCell(idx++)));
        // 车系
        importDto.setVehicleSeriesName(ExcelReadUtil.getCellValue(row.getCell(idx++)));
        // 车辆所有人
        importDto.setVehicleBelongDeptLevelName(ExcelReadUtil.getCellValue(row.getCell(idx++)));
        // 车辆使用人
        importDto.setVehicleUseDeptLevelName(ExcelReadUtil.getCellValue(row.getCell(idx++)));
        // 注册日期
        importDto.setRegisterDateStr(ExcelReadUtil.getCellValue(row.getCell(idx++)));
        if (StringUtils.isNotBlank(importDto.getRegisterDateStr())) {
            importDto.setRegisterDate(DateUtils.getDate(importDto.getRegisterDateStr(), DateUtils.DATE_FORMAT));
        }
        // 车辆状态
        importDto.setVehicleStatusStr(ExcelReadUtil.getCellValue(row.getCell(idx++)));
        // 车辆来源
        importDto.setVehicleSourceStr(ExcelReadUtil.getCellValue(row.getCell(idx++)));
        // 编制类型
        importDto.setPreparationTypeStr(ExcelReadUtil.getCellValue(row.getCell(idx++)));
        //  购买服务供应商
        importDto.setSupplierServiceName(ExcelReadUtil.getCellValue(row.getCell(idx++)));
        // 车辆性质
        importDto.setVehicleAttributeStr(ExcelReadUtil.getCellValue(row.getCell(idx++)));
        //车辆使用部门
        importDto.setVehicleUseStructLevelName(ExcelReadUtil.getCellValue(row.getCell(idx++)));
        // 发送机号
        importDto.setEngineNum(ExcelReadUtil.getCellValue(row.getCell(idx++)));
        // 是否喷涂标识
        importDto.setSprayLogoFlagStr(ExcelReadUtil.getCellValue(row.getCell(idx++)));
        // 配置日期
        importDto.setConfigurationDateStr(ExcelReadUtil.getCellValue(row.getCell(idx++)));
        if (StringUtils.isNotBlank(importDto.getConfigurationDateStr())) {
            importDto.setConfigurationDate(DateUtils.getDate(importDto.getConfigurationDateStr(), DateUtils.DATE_FORMAT));
        }
        // 配置款
        importDto.setConfigurationVersionName(ExcelReadUtil.getCellValue(row.getCell(idx++)));
        // 发送机排量
        importDto.setOutputVolume(ExcelReadUtil.getCellValue(row.getCell(idx++)));
        // 动力类型
        importDto.setFuelTypeStr(ExcelReadUtil.getCellValue(row.getCell(idx++)));
        // 燃油标号
        importDto.setFuelNo(ExcelReadUtil.getCellValue(row.getCell(idx++)));
        // 变速箱类型
        importDto.setGearboxTypeStr(ExcelReadUtil.getCellValue(row.getCell(idx++)));
        // 邮箱容积
        importDto.setOilTankVolume(ExcelReadUtil.getCellValue(row.getCell(idx++)));
        // 核定载人数
        importDto.setApprovedPassengerSum(ExcelReadUtil.getCellValue(row.getCell(idx++)));
        // 车身颜色
        importDto.setVehicleBodyColor(ExcelReadUtil.getCellValue(row.getCell(idx++)));
        // 车辆裸价
        importDto.setVehicleBarePrice(ExcelReadUtil.getCellValue(row.getCell(idx++)));
        // 备注
        importDto.setRemark(ExcelReadUtil.getCellValue(row.getCell(idx++)));
        return importDto;
    }


    private GovStruct getImportStructInfo(String importStructName, Integer companyId) {
        String split = "->";
        String[] structNames = importStructName.split(split);
        int level = structNames.length - 1;
        return govStructService.getBaseMapper().selectOne(new LambdaQueryWrapper<GovStruct>()
                .eq(GovStruct::getCompanyId, companyId)
                .eq(GovStruct::getStructName, structNames[level])
//                .eq(GovStruct::getStatus, GovStructStatusEnum.NORMAL.getCode())
                .eq(GovStruct::getStructLevel, structNames.length).last("limit 1"));
    }

    private String getStructName(String name) {
        String split = "->";
        String[] structNames = name.split(split);
        return structNames[structNames.length - 1];
    }


    /**
     * 二级级联
     */
    private void branchSeriesSecondCascade(Workbook workbook,
                                           Sheet mainSheet) {
        Sheet hiddenSheet = workbook.createSheet("branchSeries");
        workbook.setSheetHidden(workbook.getSheetIndex(hiddenSheet), true);
        // 车辆品牌
        LambdaQueryWrapper<GovVehicleBrands> brandsQryWrapper = new LambdaQueryWrapper<>();
        brandsQryWrapper.select(GovVehicleBrands::getBrandName, GovVehicleBrands::getBrandId).orderByAsc(GovVehicleBrands::getInitial);
        List<GovVehicleBrands> branchList = govVehicleBrandsService.list(brandsQryWrapper);

        Map<Integer, String> branchNameMap = branchList.stream().collect(Collectors.toMap(GovVehicleBrands::getBrandId, GovVehicleBrands::getBrandName));
        String branchNameStr = JSON.toJSONString(branchNameMap);
        branchNameMap = JSONObject.parseObject(branchNameStr, new TypeReference<Map<Integer, String>>() {
        });
        // 车系
        LambdaQueryWrapper<GovVehicleSeries> seriesQryWrapper = new LambdaQueryWrapper<>();
        seriesQryWrapper.select(GovVehicleSeries::getBrandId, GovVehicleSeries::getSeriesName);
        List<GovVehicleSeries> seriesList = govVehicleSeriesService.list(seriesQryWrapper);
        // 根据 GovVehicleSeries 中的 brandId 分组，每个分组list中放 SeriesName
        Map<Integer, List<GovVehicleSeries>> seriesNameMap = seriesList.stream().collect(Collectors.groupingBy(GovVehicleSeries::getBrandId));
        // 填充隐藏工作表的数据
        int rowIndex = 0;
        for (Map.Entry<Integer, String> branchNameEntry : branchNameMap.entrySet()) {
            Integer branchId = branchNameEntry.getKey();
            String branchName = branchNameEntry.getValue();
            try {
                validateName(branchName);
            } catch (Exception e) {
                log.info("车辆模板导出，构建级联异常", e);
                continue;
            }
            Row row = hiddenSheet.createRow(rowIndex++);
            Cell cell = row.createCell(0);
            cell.setCellValue(branchName);
            if (seriesNameMap.containsKey(branchId)) {
                List<GovVehicleSeries> seriesNames = seriesNameMap.get(branchId);
                for (int j = 0; j < seriesNames.size(); j++) {
                    Cell subCell = row.createCell(j + 1);
                    subCell.setCellValue(seriesNames.get(j).getSeriesName());
                }
                // 添加名称管理器
                String range = ExcelUtil.getRange(1, rowIndex, seriesNames.size());
                Name name = workbook.createName();
                //key不可重复
                name.setNameName(branchName);
                String formula = hiddenSheet.getSheetName() + "!" + range;
                name.setRefersToFormula(formula);
            }
        }
        // 创建名称管理器引用
        Name name = workbook.createName();
        name.setNameName(RandomStringUtils.randomAlphabetic(32));
        String reference = hiddenSheet.getSheetName() + "!$A$1:$A$" + branchNameMap.size();
        name.setRefersToFormula(reference);

        // 数据验证辅助对象
        DataValidationHelper helper = mainSheet.getDataValidationHelper();
        DataValidationConstraint constraint = helper.createFormulaListConstraint(name.getNameName());
        CellRangeAddressList cellRangeAddressList = new CellRangeAddressList(2, 65535, 4, 4); // A2:A65535
        DataValidation validation = helper.createValidation(constraint, cellRangeAddressList);
        validation.setSuppressDropDownArrow(true);
        validation.setShowErrorBox(false);
        mainSheet.addValidationData(validation);


        // 数据验证辅助对象
        DataValidationHelper subHelper = mainSheet.getDataValidationHelper();
        DataValidationConstraint subConstraint = subHelper.createFormulaListConstraint("INDIRECT($E3)");
        CellRangeAddressList subCellRangeAddressList = new CellRangeAddressList(2, 65535, 5, 5); // B2:B65535
        DataValidation subValidation = subHelper.createValidation(subConstraint, subCellRangeAddressList);
        subValidation.setSuppressDropDownArrow(true);
        subValidation.setShowErrorBox(false);
        mainSheet.addValidationData(subValidation);
    }


    /**
     * 供应商二级级联
     */
    private void supplierBranchSeriesSecondCascade(Workbook workbook,
                                                   Sheet mainSheet) {
        Sheet hiddenSheet = workbook.createSheet("branchSeries");
        workbook.setSheetHidden(workbook.getSheetIndex(hiddenSheet), true);
        // 车辆品牌
        LambdaQueryWrapper<GovVehicleBrands> brandsQryWrapper = new LambdaQueryWrapper<>();
        brandsQryWrapper.select(GovVehicleBrands::getBrandName, GovVehicleBrands::getBrandId).orderByAsc(GovVehicleBrands::getInitial);
        List<GovVehicleBrands> branchList = govVehicleBrandsService.list(brandsQryWrapper);

        Map<Integer, String> branchNameMap = branchList.stream().collect(Collectors.toMap(GovVehicleBrands::getBrandId, GovVehicleBrands::getBrandName));
        String branchNameStr = JSON.toJSONString(branchNameMap);
        branchNameMap = JSONObject.parseObject(branchNameStr, new TypeReference<Map<Integer, String>>() {
        });
        // 车系
        LambdaQueryWrapper<GovVehicleSeries> seriesQryWrapper = new LambdaQueryWrapper<>();
        seriesQryWrapper.select(GovVehicleSeries::getBrandId, GovVehicleSeries::getSeriesName);
        List<GovVehicleSeries> seriesList = govVehicleSeriesService.list(seriesQryWrapper);
        // 根据 GovVehicleSeries 中的 brandId 分组，每个分组list中放 SeriesName
        Map<Integer, List<GovVehicleSeries>> seriesNameMap = seriesList.stream().collect(Collectors.groupingBy(GovVehicleSeries::getBrandId));
        // 填充隐藏工作表的数据
        int rowIndex = 0;
        for (Map.Entry<Integer, String> branchNameEntry : branchNameMap.entrySet()) {
            Integer branchId = branchNameEntry.getKey();
            String branchName = branchNameEntry.getValue();
            try {
                validateName(branchName);
            } catch (Exception e) {
                log.info("车辆模板导出，构建级联异常", e);
                continue;
            }
            Row row = hiddenSheet.createRow(rowIndex++);
            Cell cell = row.createCell(0);
            cell.setCellValue(branchName);
            if (seriesNameMap.containsKey(branchId)) {
                List<GovVehicleSeries> seriesNames = seriesNameMap.get(branchId);
                for (int j = 0; j < seriesNames.size(); j++) {
                    Cell subCell = row.createCell(j + 1);
                    subCell.setCellValue(seriesNames.get(j).getSeriesName());
                }
                // 添加名称管理器
                String range = ExcelUtil.getRange(1, rowIndex, seriesNames.size());
                Name name = workbook.createName();
                //key不可重复
                name.setNameName(branchName);
                String formula = hiddenSheet.getSheetName() + "!" + range;
                name.setRefersToFormula(formula);
            }
        }
        // 创建名称管理器引用
        Name name = workbook.createName();
        name.setNameName(RandomStringUtils.randomAlphabetic(32));
        String reference = hiddenSheet.getSheetName() + "!$A$1:$A$" + branchNameMap.size();
        name.setRefersToFormula(reference);

        // 数据验证辅助对象
        DataValidationHelper helper = mainSheet.getDataValidationHelper();
        DataValidationConstraint constraint = helper.createFormulaListConstraint(name.getNameName());
        CellRangeAddressList cellRangeAddressList = new CellRangeAddressList(2, 65535, 3, 3); // A2:A65535
        DataValidation validation = helper.createValidation(constraint, cellRangeAddressList);
        validation.setSuppressDropDownArrow(true);
        validation.setShowErrorBox(false);
        mainSheet.addValidationData(validation);


        // 数据验证辅助对象
        DataValidationHelper subHelper = mainSheet.getDataValidationHelper();
        DataValidationConstraint subConstraint = subHelper.createFormulaListConstraint("INDIRECT($D3)");
        CellRangeAddressList subCellRangeAddressList = new CellRangeAddressList(2, 65535, 4, 4); // B2:B65535
        DataValidation subValidation = subHelper.createValidation(subConstraint, subCellRangeAddressList);
        subValidation.setSuppressDropDownArrow(true);
        subValidation.setShowErrorBox(false);
        mainSheet.addValidationData(subValidation);
    }


    private static void validateName(String name) {
        if (name.length() == 0) {
            throw new IllegalArgumentException("Name cannot be blank");
        }

        // is first character valid?
        char c = name.charAt(0);
        String allowedSymbols = "_";
        boolean characterIsValid = (Character.isLetter(c) || allowedSymbols.indexOf(c) != -1);
        if (!characterIsValid) {
            throw new IllegalArgumentException("Invalid name: '" + name + "': first character must be underscore or a letter");
        }

        // are all other characters valid?
        allowedSymbols = "_\\"; //backslashes needed for unicode escape
        for (final char ch : name.toCharArray()) {
            characterIsValid = (Character.isLetterOrDigit(ch) || allowedSymbols.indexOf(ch) != -1);
            if (!characterIsValid) {
                throw new IllegalArgumentException("Invalid name: '" + name + "'");
            }
        }
    }

    private void excelDropDown(Workbook workbook, Sheet hiddenSheet, Sheet mainSheet, Integer columnIndex, Integer rowIndex, Integer itemSize) {
        // 创建名称管理器引用
        Name name = workbook.createName();
        name.setNameName(RandomStringUtils.randomAlphabetic(32));
        String reference = hiddenSheet.getSheetName() + "!$" + ((char) ('A' + columnIndex)) + "$1:$" + ((char) ('A' + columnIndex)) + "$" + itemSize;
        name.setRefersToFormula(reference);

        // 数据验证辅助对象
        DataValidationHelper helper = mainSheet.getDataValidationHelper();
        DataValidationConstraint constraint = helper.createFormulaListConstraint(name.getNameName());
        DataValidation validation = helper.createValidation(constraint, new CellRangeAddressList(rowIndex, 65535, columnIndex, columnIndex));

        validation.setSuppressDropDownArrow(true);
        validation.setShowErrorBox(false);
        mainSheet.addValidationData(validation);

    }


    private String buildRange(int offset, int startRow, int rowCount) {
        char start = (char) ('A' + offset);
        return "$" + start + "$" + startRow + ":$" + start + "$" + (startRow + rowCount - 1);
    }


    private String buildFormulaString(String offset, int rowNum) {
        return "INDIRECT($" + offset + (rowNum) + ")";
    }

    private GovSupplierVehicleImportDTO buildSupplierGovVehicleImportList(XSSFRow row) {
        final GovSupplierVehicleImportDTO importDto = new GovSupplierVehicleImportDTO();
        int idx = 0;
        importDto.setVehicleLicense(ExcelReadUtil.getCellValue(row.getCell(idx++)));//车牌号
        importDto.setVehicleVin(ExcelReadUtil.getCellValue(row.getCell(idx++)));//车架号
        importDto.setVehicleTypeStr(ExcelReadUtil.getCellValue(row.getCell(idx++)));//车辆类型
        importDto.setVehicleBrandName(ExcelReadUtil.getCellValue(row.getCell(idx++)));// 车辆品牌
        importDto.setVehicleSeriesName(ExcelReadUtil.getCellValue(row.getCell(idx++)));//车系
        importDto.setParkCityName(ExcelReadUtil.getCellValue(row.getCell(idx++))); //停放城市
        // 注册日期
        importDto.setRegisterDateStr(ExcelReadUtil.getCellValue(row.getCell(idx++)));
        if (StringUtils.isNotBlank(importDto.getRegisterDateStr())) {
            importDto.setRegisterDate(DateUtils.getDate(importDto.getRegisterDateStr(), DateUtils.DATE_FORMAT));
        }
        // 车辆状态
        importDto.setVehicleStatusStr(ExcelReadUtil.getCellValue(row.getCell(idx++)));
        // 租赁用途
        importDto.setRentalPurposeStr(ExcelReadUtil.getCellValue(row.getCell(idx++)));
        // 远程开关锁
        importDto.setRemoteLockStr(ExcelReadUtil.getCellValue(row.getCell(idx++)));
        // 车辆服务类型
        importDto.setVehicleServiceTypeStr(ExcelReadUtil.getCellValue(row.getCell(idx++)));
        //车辆管理单位
        importDto.setVehicleBelongDeptName(ExcelReadUtil.getCellValue(row.getCell(idx++)));
        // 供应商
        importDto.setSupplierServiceName(ExcelReadUtil.getCellValue(row.getCell(idx++)));
        // 供应商网点
        importDto.setSupplierOutletsName(ExcelReadUtil.getCellValue(row.getCell(idx++)));
        //司机姓名
        importDto.setDriverName(ExcelReadUtil.getCellValue(row.getCell(idx++)));
        //司机手机号
        importDto.setDriverMobile(ExcelReadUtil.getCellValue(row.getCell(idx++)));
        // 发动机号
        importDto.setEngineNum(ExcelReadUtil.getCellValue(row.getCell(idx++)));
        // 发送机排量
        importDto.setOutputVolume(ExcelReadUtil.getCellValue(row.getCell(idx++)));
        // 动力类型
        importDto.setFuelTypeStr(ExcelReadUtil.getCellValue(row.getCell(idx++)));
        // 燃油标号
        importDto.setFuelNo(ExcelReadUtil.getCellValue(row.getCell(idx++)));
        // 变速箱类型
        importDto.setGearboxTypeStr(ExcelReadUtil.getCellValue(row.getCell(idx++)));
        // 核定载人数
        importDto.setApprovedPassengerSum(ExcelReadUtil.getCellValue(row.getCell(idx++)));
        // 车身颜色
        importDto.setVehicleBodyColor(ExcelReadUtil.getCellValue(row.getCell(idx++)));
        // 备注
        importDto.setRemark(ExcelReadUtil.getCellValue(row.getCell(idx)));
        return importDto;
    }

}
