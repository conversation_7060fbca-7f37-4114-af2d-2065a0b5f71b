package com.mrcar.gov.rest.service.asset.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.metadata.CellExtra;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.row.SimpleRowHeightStyleStrategy;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.izu.file.storage.FileInfo;
import com.izu.file.storage.FileStorageService;
import com.izu.file.storage.UploadPretreatment;
import com.izu.framework.exception.ApiException;
import com.izu.framework.resp.InfoCode;
import com.izu.framework.web.util.BeanUtil;
import com.mrcar.gov.asset.domain.GovVehicleBaseInfo;
import com.mrcar.gov.asset.domain.GovWarranty;
import com.mrcar.gov.asset.domain.GovWarrantyInsurance;
import com.mrcar.gov.asset.service.GovVehicleBaseInfoService;
import com.mrcar.gov.asset.service.GovWarrantyInsuranceService;
import com.mrcar.gov.asset.service.GovWarrantyService;
import com.mrcar.gov.base.common.SeqPrefix;
import com.mrcar.gov.base.service.SequenceGenerator;
import com.mrcar.gov.common.constant.RestErrorCode;
import com.mrcar.gov.common.dto.BatchImportReqDTO;
import com.mrcar.gov.common.dto.asset.request.WarrantyImportDTO;
import com.mrcar.gov.common.dto.asset.request.WarrantyInsuranceReqDTO;
import com.mrcar.gov.common.dto.asset.request.WarrantyReqDTO;
import com.mrcar.gov.common.dto.session.GovUserSessionInfoDTO;
import com.mrcar.gov.common.dto.user.resp.BatchDTO;
import com.mrcar.gov.common.enums.warranty.WarrantyEnum;
import com.mrcar.gov.common.util.DateUtils;
import com.mrcar.gov.common.util.ExcelDownloadUtil;
import com.mrcar.gov.rest.service.asset.WarrantyImportService;
import com.mrcar.gov.rest.util.LicensePlateValidator;
import com.mrcar.gov.rest.util.SessionUtil;
import com.mrcar.gov.rest.util.excel.SelectDataSheetMergeWriteHandler;
import com.mrcar.gov.user.domain.GovOrgInfo;
import com.mrcar.gov.user.mapper.GovOrgInfoMapper;
import com.mrcar.gov.user.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
public class WarrantyImportServiceImpl implements WarrantyImportService {

    @Autowired
    private GovOrgInfoMapper govOrgInfoMapper;

    @Autowired
    private GovVehicleBaseInfoService govVehicleBaseInfoService;

    @Autowired
    private GovWarrantyInsuranceService govWarrantyInsuranceService;

    @Autowired
    private GovWarrantyService govWarrantyService;

    @Autowired
    private FileStorageService fileStorageService;

    @Resource
    private SequenceGenerator sequenceGenerator;

    /**
     * 下载保单导入模板
     */
    @Override
    public void importWarrantyTemplate(HttpServletRequest request, HttpServletResponse response) {
        try {
            // 导出模板名称
            String fileName = "保单录入模板";
            response.setContentType("application/msexcel");
            response.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码
            String fileNameEncode = URLEncoder.encode(fileName, "UTF-8");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileNameEncode + ExcelTypeEnum.XLSX.getValue());
            // 设置下拉框内容
            Map<Integer, List<String>> selectMap = buildSelectMap();
            //表头
            List<?> list = new ArrayList<>();
            EasyExcelFactory.write(response.getOutputStream())
                    // 设置字典
                    .registerWriteHandler(new SelectDataSheetMergeWriteHandler(selectMap))
                    // 设置行高度
                    .registerWriteHandler(new SimpleRowHeightStyleStrategy((short) 35, (short) 22))
                    // 此处对应的是实体类
                    .head(WarrantyImportDTO.class)
                    // 设置导出格式为xls后缀
                    .excelType(ExcelTypeEnum.XLSX)
                    .sheet("保单录入模板")
                    .doWrite(list);
        } catch (IOException e) {
            log.error("下载下载保单导入模板异常", e);
            throw new ApiException(RestErrorCode.EXCEL_DOWNLOAD_ERROR);
        }

    }

    private Map<Integer, List<String>> buildSelectMap() {
        GovUserSessionInfoDTO govUserSessionInfoDTO = SessionUtil.currentUser();
        Map<Integer, List<String>> selectMap = new HashMap<>();
        //  保单类型
        List<String> typeList = Arrays.stream(WarrantyEnum.WarrantyTypeEnum.values()).map(WarrantyEnum.WarrantyTypeEnum::getName).collect(Collectors.toList());
        selectMap.put(1, typeList);
        //  保险服务机构
        List<GovOrgInfo> govOrgInfos = govOrgInfoMapper.selectList(new LambdaQueryWrapper<GovOrgInfo>().eq(GovOrgInfo::getOrgType, 2)
                .eq(GovOrgInfo::getCompanyId,govUserSessionInfoDTO.getCompanyInfo().getCompanyId())
                .eq(GovOrgInfo::getOrgStatus, 1));
        List<String> orgNameList = govOrgInfos.stream().map(GovOrgInfo::getOrgName).distinct().collect(Collectors.toList());
        selectMap.put(3, orgNameList);
        return selectMap;
    }

    @Override
    @Transactional
    public BatchDTO importWarrantyBatch(BatchImportReqDTO reqDTO) {
        InputStream file = ExcelDownloadUtil.downloadFile(reqDTO.getExcelUrl());
//        InputStream file = null;
//        try {
//            file = new FileInputStream("/Users/<USER>/Downloads/车辆导入模板.xlsx");
//        } catch (FileNotFoundException e) {
//            throw new RuntimeException(e);
//        }
        //1. 读取excel 封装数据
        List<WarrantyReqDTO> dataList = readExcelToDataList(file);
        // 2. 校验并保存合法数据，返回失败数据
        List<WarrantyReqDTO> errorData = checkAndSaveDataReturnErrorData(dataList, reqDTO);
        //3.  写出失败的
        String errorFileUrl = writeFailData2Exel(errorData);

        // 4.返回结果
        BatchDTO batchDTO = new BatchDTO();
        batchDTO.setError(errorData.size());
        batchDTO.setTotal(dataList.size());
        batchDTO.setSuccess(dataList.size() - errorData.size());
        batchDTO.setDownloadUrl(errorFileUrl);
        return batchDTO;

    }

    /**
     * 校验并保存合法数据，返回失败数据
     */
    private List<WarrantyReqDTO> checkAndSaveDataReturnErrorData(List<WarrantyReqDTO> dataList, BatchImportReqDTO reqDTO) {
        Set<String> vehicleLicenseSet = dataList.stream().map(WarrantyReqDTO::getVehicleLicense).filter(x -> StringUtils.isNotBlank(x)).collect(Collectors.toSet());
        //存在车辆的 车牌号/车架号
        Map<String, GovVehicleBaseInfo> vehicleLicenseKeyMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(vehicleLicenseSet)) {
            vehicleLicenseKeyMap = govVehicleBaseInfoService.list(new LambdaQueryWrapper<GovVehicleBaseInfo>()
                            .eq(GovVehicleBaseInfo::getCompanyId, reqDTO.getLoginCompanyId())
                            .in(GovVehicleBaseInfo::getVehicleLicense, vehicleLicenseSet))
                    .stream().collect(Collectors.toMap(GovVehicleBaseInfo::getVehicleLicense,Function.identity(), (x, y) -> x));
        }
        Set<String> vehicleVinSet = dataList.stream().map(WarrantyReqDTO::getVehicleVin).filter(x -> StringUtils.isNotBlank(x)).collect(Collectors.toSet());
        Map<String, GovVehicleBaseInfo> vehicleVinKeyMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(vehicleVinSet)) {
            vehicleVinKeyMap = govVehicleBaseInfoService.list(new LambdaQueryWrapper<GovVehicleBaseInfo>()
                            .eq(GovVehicleBaseInfo::getCompanyId, reqDTO.getLoginCompanyId())
                            .in(GovVehicleBaseInfo::getVehicleVin, vehicleVinSet))
                    .stream().collect(Collectors.toMap(GovVehicleBaseInfo::getVehicleVin,Function.identity(), (x, y) -> x));

        }
        //查询已存在的保单号码
        Set<String> set = dataList.stream().map(WarrantyReqDTO::getInsuranceCompanyNumber).filter(x -> StringUtils.isNotBlank(x)).collect(Collectors.toSet());
        Set<String> insuranceCompanyNumberSet = new HashSet<>();
        if (CollectionUtils.isNotEmpty(set)) {
            insuranceCompanyNumberSet = govWarrantyService.list(new LambdaQueryWrapper<GovWarranty>()
                            .eq(GovWarranty::getCompanyId, reqDTO.getLoginCompanyId())
                            .in(GovWarranty::getInsuranceCompanyNumber, set))
                    .stream().map(GovWarranty::getInsuranceCompanyNumber).collect(Collectors.toSet());
        }
        // 查询 同一车辆 的 保单 <车牌+车架+保单类型,保单集合>
        Map<String, List<GovWarranty>> govWarrantyMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(vehicleLicenseKeyMap.keySet())) {
            govWarrantyMap = govWarrantyService.list(new LambdaQueryWrapper<GovWarranty>()
                            .eq(GovWarranty::getCompanyId, reqDTO.getLoginCompanyId())
                            .in(GovWarranty::getVehicleLicense, vehicleLicenseKeyMap.keySet()))
                    .stream().collect(Collectors.groupingBy(x -> x.getVehicleLicense() + x.getVehicleVin() + x.getWarrantyType()));
        }
        if (CollectionUtils.isNotEmpty(vehicleVinKeyMap.keySet())) {
            Map<String, List<GovWarranty>> collect = govWarrantyService.list(new LambdaQueryWrapper<GovWarranty>()
                            .eq(GovWarranty::getCompanyId, reqDTO.getLoginCompanyId())
                            .in(GovWarranty::getVehicleVin, vehicleVinKeyMap.keySet()))
                    .stream().collect(Collectors.groupingBy(x -> x.getVehicleLicense() + x.getVehicleVin() + x.getWarrantyType()));
            govWarrantyMap.putAll(collect);
        }

        List<WarrantyReqDTO> errorData = new ArrayList<>();
        List<WarrantyReqDTO> successData = new ArrayList<>();
        //用老记录 已经校验成功的保单号
        Set<String> successInsuranceCompanyNumberSet = Sets.newHashSet();
        for (WarrantyReqDTO warrantyReqDTO : dataList) {
            String vehicleVin = warrantyReqDTO.getVehicleVin();
            String vehicleLicense = warrantyReqDTO.getVehicleLicense();
            Integer warrantyType = warrantyReqDTO.getWarrantyType();
            //校验 车架号/车牌号
            StringBuilder builder = new StringBuilder();
            if (StringUtils.isBlank(vehicleVin) && StringUtils.isBlank(vehicleLicense)) {
                builder.append("A.车架号/车牌号不能为空\n");
            } else if (!vehicleVinKeyMap.containsKey(vehicleVin) && !vehicleLicenseKeyMap.containsKey(vehicleLicense)) {
                builder.append("A.系统中不存在该车架号/车牌号\n");
            } else if (vehicleVinKeyMap.containsKey(vehicleVin)) {
                // 赋值车牌号
                warrantyReqDTO.setVehicleLicense(vehicleVinKeyMap.get(vehicleVin).getVehicleLicense());
            } else if (vehicleLicenseKeyMap.containsKey(vehicleLicense)) {
                // 赋值车架号
                warrantyReqDTO.setVehicleVin(vehicleLicenseKeyMap.get(vehicleLicense).getVehicleVin());
            }
            //校验 保单类型
            if (warrantyType == null || warrantyType == NumberUtils.INTEGER_MINUS_ONE) {
                builder.append("C. 保单类型不能为空或者不存在\n");
            }
            //校验 保单号不能为空
            if (StringUtils.isBlank(warrantyReqDTO.getInsuranceCompanyNumber())) {
                builder.append("C. 保单不能为空\n");
            } else if (insuranceCompanyNumberSet.contains(warrantyReqDTO.getInsuranceCompanyNumber())||
                successInsuranceCompanyNumberSet.contains(warrantyReqDTO.getInsuranceCompanyNumber())) {
                builder.append("C. 保单号与已有保单号重复\n");
            }
            //校验 保险服务机构 不能为空
            if (StringUtils.isBlank(warrantyReqDTO.getOrgName())) {
                builder.append("D. 保险服务机构不能为空\n");
            } else if (StringUtils.isBlank(warrantyReqDTO.getOrgNo())) {
                builder.append("D. 保险服务机构不存在\n");
            }
            //校验 总保费
            if (warrantyReqDTO.getTotalPremium() == null) {
                builder.append("E. 总保费不能为空 或 不为数字\n");
            }
            String key = vehicleLicense + vehicleVin + warrantyType;
            if (warrantyReqDTO.getWarrantyStartTime() == null || warrantyReqDTO.getWarrantyEndTime() == null) {
                builder.append("F、G. 保险开始日期和结束日期都不能为空或者格式不正确");
            } else if (warrantyReqDTO.getWarrantyStartTime().compareTo(warrantyReqDTO.getWarrantyEndTime()) > 0) {
                builder.append("F、G. 保险结束日期不早于开始日期");
            } else if (govWarrantyMap.containsKey(key)) {
                List<GovWarranty> warrantyTime = govWarrantyMap.get(key).stream().filter(entity ->
                        ((entity.getWarrantyStartTime().compareTo(warrantyReqDTO.getWarrantyStartTime()) <= 0 && entity.getWarrantyEndTime().compareTo(warrantyReqDTO.getWarrantyEndTime()) >= 0)
                                || (entity.getWarrantyStartTime().compareTo(warrantyReqDTO.getWarrantyStartTime()) >= 0 && entity.getWarrantyEndTime().compareTo(warrantyReqDTO.getWarrantyEndTime()) <= 0)
                                || (entity.getWarrantyStartTime().compareTo(warrantyReqDTO.getWarrantyStartTime()) <= 0 && entity.getWarrantyEndTime().compareTo(warrantyReqDTO.getWarrantyEndTime()) <= 0 && entity.getWarrantyEndTime().compareTo(warrantyReqDTO.getWarrantyStartTime()) >= 0)
                                || (entity.getWarrantyStartTime().compareTo(warrantyReqDTO.getWarrantyEndTime()) <= 0 && entity.getWarrantyStartTime().compareTo(warrantyReqDTO.getWarrantyStartTime()) >= 0 && entity.getWarrantyEndTime().compareTo(warrantyReqDTO.getWarrantyEndTime()) >= 0))
                ).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(warrantyTime)) {
                    builder.append("F、G. 保险时间段重复，已有生效的此类保险");
                }
            }
            // 有错误信息
            if (builder.length() > 0) {
                warrantyReqDTO.setErrMsg(builder.toString());
                errorData.add(warrantyReqDTO);
            } else {
                successInsuranceCompanyNumberSet.add(warrantyReqDTO.getInsuranceCompanyNumber());
                successData.add(warrantyReqDTO);
            }
        }

        log.info("保存校验成功的保单数据");
        // 进行插入 成功的
        if (CollectionUtils.isNotEmpty(successData)) {
            // 保存 保单
            for (WarrantyReqDTO successDatum : successData) {
                successDatum.setWarrantyCode(sequenceGenerator.generate(new Date(), SeqPrefix.GOV_WARRANTY_CODE));
                successDatum.setCreateCode(reqDTO.getLoginUserCode());
                successDatum.setCreateName(reqDTO.getLoginUserName());
                successDatum.setCompanyId(reqDTO.getLoginCompanyId());
                successDatum.setCompanyName(reqDTO.getLoginCompanyName());

                // 赋值各种使用人
                String vehicleVin = successDatum.getVehicleVin();
                String vehicleLicense = successDatum.getVehicleLicense();
                GovVehicleBaseInfo govVehicleBaseInfo = vehicleVinKeyMap.containsKey(vehicleVin) ? vehicleVinKeyMap.get(vehicleVin) : vehicleLicenseKeyMap.get(vehicleLicense);
                if (Objects.nonNull(govVehicleBaseInfo)){
                    successDatum.setVehicleBelongDeptCode(govVehicleBaseInfo.getVehicleBelongDeptCode());
                    successDatum.setVehicleBelongDeptName(govVehicleBaseInfo.getVehicleBelongDeptName());
                    successDatum.setVehicleUseDeptCode(govVehicleBaseInfo.getVehicleUseDeptCode());
                    successDatum.setVehicleUseDeptName(govVehicleBaseInfo.getVehicleUseDeptName());
                    successDatum.setVehicleUseStructCode(govVehicleBaseInfo.getVehicleUseStructCode());
                    successDatum.setVehicleUseStructName(govVehicleBaseInfo.getVehicleUseStructName());
                }
            }
            List<GovWarranty> saveList = BeanUtil.copyList(successData, GovWarranty.class);
            govWarrantyService.saveBatch(saveList);

            // 保存险种，
            Set<String> codeSet = saveList.stream().map(GovWarranty::getWarrantyCode).collect(Collectors.toSet());
            Map<String, GovWarranty> warrantyCodeMap = govWarrantyService.list(new LambdaQueryWrapper<GovWarranty>().in(GovWarranty::getWarrantyCode, codeSet))
                    .stream().collect(Collectors.toMap(GovWarranty::getWarrantyCode, Function.identity(), (v1, v2) -> v1));

            List<GovWarrantyInsurance> saveWarrantyInsuranceList = Lists.newArrayList();
            for (WarrantyReqDTO successDatum : successData) {
                GovWarranty govWarranty = warrantyCodeMap.get(successDatum.getWarrantyCode());
                for (WarrantyInsuranceReqDTO warrantyInsuranceReqDTO : successDatum.getWarrantyInsuranceList()) {
                    GovWarrantyInsurance govWarrantyInsurance = BeanUtil.copyObject(warrantyInsuranceReqDTO, GovWarrantyInsurance.class);
                    govWarrantyInsurance.setWarrantyId(govWarranty.getWarrantyId());
                    govWarrantyInsurance.setWarrantyCode(govWarranty.getWarrantyCode());
                    govWarrantyInsurance.setCompanyId(govWarranty.getCompanyId());
                    govWarrantyInsurance.setCompanyName(govWarranty.getCompanyName());
                    saveWarrantyInsuranceList.add(govWarrantyInsurance);
                }
            }
            govWarrantyInsuranceService.saveBatch(saveWarrantyInsuranceList);
        }
        log.info("保存校验成功的保单数据完毕");
        return errorData;
    }


    private String writeFailData2Exel(List<WarrantyReqDTO> errorData) {
        if (CollectionUtils.isEmpty(errorData)) {
            return "";
        }
        log.info("开始写失败数据到Excel");
        // 1.整理数据
        List<List<String>> errorList = Lists.newArrayList();
        int maxSize = errorData.stream().max(Comparator.comparingInt(a -> a.getWarrantyInsuranceList().size())).get().getWarrantyInsuranceList().size();
        for (WarrantyReqDTO errorDatum : errorData) {
            List<String> error = Lists.newArrayList();
            String vehicleVinOrLicense = Objects.nonNull(errorDatum.getVehicleVinOrLicense()) ? errorDatum.getVehicleVinOrLicense() : "";
            error.add(vehicleVinOrLicense);
            String warrantyTypeStr = Objects.nonNull(errorDatum.getWarrantyTypeStr()) ? errorDatum.getWarrantyTypeStr() : "";
            error.add(warrantyTypeStr);
            String insuranceCompanyNumber = Objects.nonNull(errorDatum.getInsuranceCompanyNumber()) ? errorDatum.getInsuranceCompanyNumber() : "";
            error.add(insuranceCompanyNumber);
            String orgName = Objects.nonNull(errorDatum.getOrgName()) ? errorDatum.getOrgName() : "";
            error.add(orgName);
            String totalPremium = Objects.nonNull(errorDatum.getTotalPremium()) ? errorDatum.getTotalPremium().toString() : "";
            error.add(totalPremium);
            String warrantyStartTime = Objects.nonNull(errorDatum.getWarrantyStartTime()) ? DateUtils.format(errorDatum.getWarrantyStartTime(), DateUtils.DATE_FORMAT) : "";
            error.add(warrantyStartTime);
            String warrantyEndTime = Objects.nonNull(errorDatum.getWarrantyEndTime()) ? DateUtils.format(errorDatum.getWarrantyEndTime(), DateUtils.DATE_FORMAT).toString() : "";
            error.add(warrantyEndTime);
            String recognizeeName = Objects.nonNull(errorDatum.getRecognizeeName()) ? errorDatum.getRecognizeeName().toString() : "";
            error.add(recognizeeName);
            String insuranceCompanyName = Objects.nonNull(errorDatum.getInsuranceCompanyName()) ? errorDatum.getInsuranceCompanyName().toString() : "";
            error.add(insuranceCompanyName);
            String recognizeeTelephone = Objects.nonNull(errorDatum.getRecognizeeTelephone()) ? errorDatum.getRecognizeeTelephone().toString() : "";
            error.add(recognizeeTelephone);
            String recognizeeContactAddress = Objects.nonNull(errorDatum.getRecognizeeContactAddress()) ? errorDatum.getRecognizeeContactAddress().toString() : "";
            error.add(recognizeeContactAddress);

            List<WarrantyInsuranceReqDTO> warrantyInsuranceList = errorDatum.getWarrantyInsuranceList();
            for (int i = 0; i < maxSize; i++) {
                String insuranceTypeName = "";
                String limitOfLiability = "";
                String insurancePremium = "";
                if (i < warrantyInsuranceList.size()) {
                    WarrantyInsuranceReqDTO x = warrantyInsuranceList.get(i);
                    insuranceTypeName = Objects.nonNull(x.getInsuranceName()) ? x.getInsuranceName().toString() : "";
                    limitOfLiability = Objects.nonNull(x.getLimitOfLiability()) ? x.getLimitOfLiability().toString() : "";
                    insurancePremium = Objects.nonNull(x.getInsurancePremium()) ? x.getInsurancePremium().toString() : "";
                }
                error.add(insuranceTypeName);
                error.add(limitOfLiability);
                error.add(insurancePremium);
            }

            error.add(errorDatum.getErrMsg());
            errorList.add(error);
        }
        // 2.整理表头
        List<List<String>> headerList = Lists.newArrayList();
        headerList.add(Lists.newArrayList("车架号/车牌号"));
        headerList.add(Lists.newArrayList("*保单类型\n交强险/商业险"));
        headerList.add(Lists.newArrayList("*保单号"));
        headerList.add(Lists.newArrayList("*保险服务机构"));
        headerList.add(Lists.newArrayList("*总保费"));
        headerList.add(Lists.newArrayList("*保险开始日期\nYYYY-MM-DD"));
        headerList.add(Lists.newArrayList("*保险结束日期\nYYYY-MM-DD"));
        headerList.add(Lists.newArrayList("被保人姓名"));
        headerList.add(Lists.newArrayList("保险公司名称"));
        headerList.add(Lists.newArrayList("被保人电话"));
        headerList.add(Lists.newArrayList("被保人联系地址"));
        for (int i = 1; i <= maxSize; i++) {
            headerList.add(Lists.newArrayList("险种" + i + "\n险种名称"));
            headerList.add(Lists.newArrayList("险种" + i + "\n责任限额"));
            headerList.add(Lists.newArrayList("险种" + i + "\n保费合计（元）"));
        }
        headerList.add(Lists.newArrayList("错误原因"));

        String url = "";
        try (ByteArrayOutputStream byteOut = new ByteArrayOutputStream()) {
            ExcelWriter excelWriter = EasyExcel.write(byteOut).build();
            WriteSheet writeSheet = EasyExcel.writerSheet(0, "保单录入模板").build();
            writeSheet.setHead(headerList);
            excelWriter.write(errorList, writeSheet);
            excelWriter.finish();
            byteOut.close();
            String fileName = "保单录入失败数据" + System.currentTimeMillis() + ExcelTypeEnum.XLSX.getValue();
            UploadPretreatment uploadPretreatment = fileStorageService.of(byteOut.toByteArray());
            uploadPretreatment.setSaveFilename(fileName);
            FileInfo upload = uploadPretreatment.upload();
            if (Objects.nonNull(upload)) {
                url = upload.getUrl();
            }

//            FileOutputStream outputStream = new FileOutputStream("/Users/<USER>/Downloads/" + fileName);
//            outputStream.write(byteOut.toByteArray());
//            outputStream.flush();
//            outputStream.close();
//            url = fileName;

        } catch (Exception e) {
            log.error("保单导入失败数据文件生产异常", e);
            throw new ApiException(InfoCode.SYSTEM_EXCEPTION, "保单导入失败数据文件生产异常");
        }
        return url;
    }

    /**
     * 读取excel 封装数据
     */
    private List<WarrantyReqDTO> readExcelToDataList(InputStream file) {
        List<WarrantyReqDTO> dataList = new ArrayList<>();
        //保险服务机构
        List<GovOrgInfo> govOrgInfos = govOrgInfoMapper.selectList(new LambdaQueryWrapper<GovOrgInfo>().eq(GovOrgInfo::getOrgType, 2)
                .eq(GovOrgInfo::getOrgStatus, 1));
        Map<String, String> govOrgInfoMap = govOrgInfos.stream().collect(Collectors.toMap(GovOrgInfo::getOrgName, GovOrgInfo::getOrgNo, (key1, key2) -> key1));
        EasyExcel.read(file, new ReadListener() {
            @Override
            public void onException(Exception e, AnalysisContext analysisContext) throws Exception {
            }

            @Override
            public void invoke(Object o, AnalysisContext analysisContext) {
                LinkedHashMap<Integer, Object> cellMap = (LinkedHashMap<Integer, Object>) o;
                WarrantyReqDTO warrantyReq = new WarrantyReqDTO();
                int cellNum = 0;
                // 车架号/车牌号
                Object cell = cellMap.get(cellNum++);
                String vehicleVinOrLicense = cell == null ? "" : cell.toString();
                warrantyReq.setVehicleVinOrLicense(vehicleVinOrLicense);
                if (LicensePlateValidator.isLicensePlate(vehicleVinOrLicense)) {
                    // 车牌号
                    warrantyReq.setVehicleLicense(vehicleVinOrLicense);
                } else {
                    // 车架号
                    warrantyReq.setVehicleVin(vehicleVinOrLicense);
                }

                // 保单类型 交强险/商业险
                cell = cellMap.get(cellNum++);
                String warrantyTypeStr = cell == null ? "" : cell.toString();
                warrantyReq.setWarrantyTypeStr(warrantyTypeStr);
                warrantyReq.setWarrantyType(WarrantyEnum.WarrantyTypeEnum.getCodeByName(warrantyTypeStr));
                // 保单号
                cell = cellMap.get(cellNum++);
                String insuranceCompanyNumber = cell == null ? "" : cell.toString();
                warrantyReq.setInsuranceCompanyNumber(insuranceCompanyNumber);
                //  保险服务机构
                cell = cellMap.get(cellNum++);
                String orgName = cell == null ? "" : cell.toString();
                warrantyReq.setOrgNo(govOrgInfoMap.get(orgName));
                warrantyReq.setOrgName(orgName);
                //  总保费
                cell = cellMap.get(cellNum++);
                try {
                    BigDecimal totalPremium = cell == null ? null : new BigDecimal(cell.toString());
                    warrantyReq.setTotalPremium(totalPremium);
                } catch (Exception e) {
                    warrantyReq.setTotalPremium(null);
                }
                // 保险开始日期
                cell = cellMap.get(cellNum++);
                try {
                    String warrantyStartTime = cell == null ? null : cell.toString();
                    warrantyReq.setWarrantyStartTime(DateUtil.format2Date(warrantyStartTime, DateUtil.DATE_FORMAT));
                } catch (Exception e) {
                    warrantyReq.setWarrantyStartTime(null);
                }
                // 保险结束日期
                cell = cellMap.get(cellNum++);
                try {
                    String warrantyEndTime = cell == null ? null : cell.toString();
                    warrantyReq.setWarrantyEndTime(DateUtil.format2Date(warrantyEndTime, DateUtil.DATE_FORMAT));
                } catch (Exception e) {
                    warrantyReq.setWarrantyEndTime(null);
                }
                //被保人姓名
                cell = cellMap.get(cellNum++);
                String recognizeeName = cell == null ? "" : cell.toString();
                warrantyReq.setRecognizeeName(recognizeeName);
                //保险公司名称
                cell = cellMap.get(cellNum++);
                String insuranceCompanyName = cell == null ? "" : cell.toString();
                warrantyReq.setInsuranceCompanyName(insuranceCompanyName);
                //被保人电话
                cell = cellMap.get(cellNum++);
                String recognizeeTelephone = cell == null ? "" : cell.toString();
                warrantyReq.setRecognizeeTelephone(recognizeeTelephone);
                //被保人联系地址
                cell = cellMap.get(cellNum++);
                String recognizeeContactAddress = cell == null ? "" : cell.toString();
                warrantyReq.setRecognizeeContactAddress(recognizeeContactAddress);
                // 险种信息
                List<WarrantyInsuranceReqDTO> warrantyInsuranceList = Lists.newArrayList();
                Integer rows = cellMap.keySet().stream().max(Integer::compareTo).get();
                int soc = rows - cellNum;
                int count = (soc + 3 - 1) / 3;
                for (int j = 0; j < count; j++) {
                    //险种名称
                    cell = cellMap.get(cellNum++);
                    String insuranceName = cell == null ? "" : cell.toString();

                    //责任限额
                    cell = cellMap.get(cellNum++);
                    String limitOfLiability = cell == null ? "" : cell.toString();

                    //保费
                    BigDecimal insurancePremium = null;
                    try {
                        cell = cellMap.get(cellNum++);
                        insurancePremium = cell == null ? null : new BigDecimal(cell.toString());
                    } catch (Exception e) {
                    }
                    if (StringUtils.isNotBlank(insuranceName) || StringUtils.isNotBlank(limitOfLiability) || insurancePremium != null) {
                        WarrantyInsuranceReqDTO warrantyInsuranceReqDTO = new WarrantyInsuranceReqDTO();
                        warrantyInsuranceReqDTO.setInsuranceName(insuranceName);
                        warrantyInsuranceReqDTO.setLimitOfLiability(limitOfLiability);
                        warrantyInsuranceReqDTO.setInsurancePremium(insurancePremium);
                        warrantyInsuranceList.add(warrantyInsuranceReqDTO);
                    }
                }
                warrantyReq.setWarrantyInsuranceList(warrantyInsuranceList);
                dataList.add(warrantyReq);
            }

            @Override
            public void extra(CellExtra cellExtra, AnalysisContext analysisContext) {
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            }

            @Override
            public boolean hasNext(AnalysisContext analysisContext) {
                return true;
            }

            @Override
            public void invokeHead(Map map, AnalysisContext analysisContext) {
            }
        }).sheet(0).doRead();

        return dataList;
    }


}