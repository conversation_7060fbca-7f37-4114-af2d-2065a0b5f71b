package com.mrcar.gov.rest;

import com.mrcar.thirdparty.ocr.OcrProperties;
import org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 应用程序的启动入口类。
 *
 * <p>该类负责启动 Spring Boot 应用，加载必要的配置和依赖，初始化服务环境。</p>
 *
 * <p>主要配置：</p>
 * <ul>
 *     <li>{@link MapperScan}：配置 MyBatis 的 Mapper 扫描路径。指定包路径 "com.mrcar.**.mapper"，确保 MyBatis 能找到并注册数据访问层接口。</li>
 *     <li>{@link SpringBootApplication}：标注为 Spring Boot 应用的入口类，启用组件扫描，并排除默认的 {@link RocketMQAutoConfiguration} 自动配置，避免未使用 RocketMQ 时的启动冲突。</li>
 * </ul>
 *
 * <p>相关文档：</p>
 * <p>有关各组件的使用说明和详细配置指南，请访问项目的 Wiki 文档：</p>
 * <a href="https://iwiki.izuche.com/pages/viewpage.action?pageId=21846171">https://iwiki.izuche.com/pages/viewpage.action?pageId=21846171</a>
 *
 */
@MapperScan(basePackages = "com.mrcar.**.mapper")
@EnableScheduling
@EnableConfigurationProperties(OcrProperties.class)
@SpringBootApplication(scanBasePackages = {"com.mrcar", "com.izu.excel.aspect"},
        exclude = {RocketMQAutoConfiguration.class})
public class Application {

    /**
     * 应用程序的主方法，用于启动 Spring Boot 应用。
     *
     * @param args 启动参数
     */
    public static void main(String[] args) {
        SpringApplication.run(Application.class);
        System.out.println("启动完成");
    }
}
