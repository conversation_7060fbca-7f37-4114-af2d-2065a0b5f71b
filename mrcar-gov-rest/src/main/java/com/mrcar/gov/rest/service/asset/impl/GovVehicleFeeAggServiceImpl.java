package com.mrcar.gov.rest.service.asset.impl;


import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.google.common.collect.Lists;
import com.izu.framework.exception.ApiException;
import com.izu.framework.resp.InfoCode;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.util.BeanUtil;
import com.mrcar.gov.asset.domain.*;
import com.mrcar.gov.asset.service.*;
import com.mrcar.gov.common.constant.asset.GovVehicleDriverRelationEnum;
import com.mrcar.gov.common.constant.asset.VehicleFeeApprovalStatusEnum;
import com.mrcar.gov.common.constant.asset.VehicleFeeDetailItemEnum;
import com.mrcar.gov.common.constant.asset.VehicleFeeRelationOrderFlagEnum;
import com.mrcar.gov.common.constant.order.GovPublicCarOrderUserTypeEnum;
import com.mrcar.gov.common.constant.order.GovPublicCarRelatedFeesStatusEnum;
import com.mrcar.gov.common.constant.user.GovDataPermTypeEnum;
import com.mrcar.gov.common.dto.FileDTO;
import com.mrcar.gov.common.dto.ImageDTO;
import com.mrcar.gov.common.dto.asset.request.GovVehicleFeeEditRequestDTO;
import com.mrcar.gov.common.dto.asset.request.GovVehicleFeeListQryRequestDTO;
import com.mrcar.gov.common.dto.asset.request.GovVehicleFeeWithDrawRequestDTO;
import com.mrcar.gov.common.dto.asset.response.GovVehicleFeeDetailItemResponseDTO;
import com.mrcar.gov.common.dto.asset.response.GovVehicleFeeDetailResponseDTO;
import com.mrcar.gov.common.dto.order.resp.GovPublicApplyCarOrderDetailRespDTO;
import com.mrcar.gov.common.dto.workflow.enums.BpmProcessInstanceResultEnum;
import com.mrcar.gov.common.dto.workflow.enums.ModelEnum;
import com.mrcar.gov.common.dto.workflow.instance.ApplyStartDTO;
import com.mrcar.gov.common.dto.workflow.instance.ApplyStartSwitchDTO;
import com.mrcar.gov.common.dto.workflow.instance.BpmMessageSendApproveResultDTO;
import com.mrcar.gov.common.dto.workflow.instance.BpmProcessInstanceCancelReqDTO;
import com.mrcar.gov.order.domain.GovOrderOperationLog;
import com.mrcar.gov.order.domain.GovPublicCarOrder;
import com.mrcar.gov.order.domain.GovPublicCarOrderUserInfo;
import com.mrcar.gov.order.domain.GovPublicCarOrderVehicleInfo;
import com.mrcar.gov.order.service.GovOrderOperationLogService;
import com.mrcar.gov.order.service.GovPublicCarOrderService;
import com.mrcar.gov.order.service.GovPublicCarOrderUserInfoService;
import com.mrcar.gov.order.service.GovPublicCarOrderVehicleInfoService;
import com.mrcar.gov.rest.service.asset.GovVehicleFeeAggService;
import com.mrcar.gov.user.domain.GovUser;
import com.mrcar.gov.user.service.GovDataPermService;
import com.mrcar.gov.user.service.GovStructService;
import com.mrcar.gov.user.service.GovUserService;
import com.mrcar.gov.workflow.service.impl.WorkflowApprovalService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import com.mrcar.gov.common.constant.user.ManageCarTypeEnum;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 车杂费聚合service
 *
 * <AUTHOR>
 * @date 2024-11-20
 */
@Slf4j
@Service
public class GovVehicleFeeAggServiceImpl implements GovVehicleFeeAggService {


    @Resource
    private GovVehicleBaseInfoService govVehicleBaseInfoService;


    @Resource
    private GovVehicleFeeService govVehicleFeeService;

    @Resource
    private GovVehicleFeeDetailService govVehicleFeeDetailService;

    @Resource
    private GovVehicleFeeVoucherImgService govVehicleFeeVoucherImgService;
    @Resource
    private GovVehicleDriverRelationService govVehicleDriverRelationService;

    @Resource
    private GovPublicCarOrderService govPublicCarOrderService;

    @Resource
    private GovUserService govUserService;

    @Resource
    private GovPublicCarOrderVehicleInfoService govPublicCarOrderVehicleInfoService;


    @Autowired
    private GovPublicCarOrderUserInfoService govPublicCarOrderUserInfoService;

    @Autowired
    private WorkflowApprovalService workflowApprovalService;

    @Resource
    private GovOrderOperationLogService govOrderOperationLogService;

    @Resource
    private GovStructService govStructService;

    @Override
    public boolean saveGovVehicleFee(GovVehicleFeeEditRequestDTO govVehicleFeeEditRequestDTO) {
        log.info("saveGovVehicleFee govVehicleFeeEditRequestDTO is{}", JSON.toJSONString(govVehicleFeeEditRequestDTO));
        GovVehicleBaseInfo govVehicleBaseInfo = govVehicleBaseInfoService.getOne(
                new LambdaQueryWrapper<GovVehicleBaseInfo>()
                        .eq(GovVehicleBaseInfo::getVehicleLicense, govVehicleFeeEditRequestDTO.getVehicleLicense()));
        if (null == govVehicleBaseInfo) {
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "车辆信息不存在");
        }

        String orderNo = govVehicleFeeEditRequestDTO.getVehicleRecordCode();

        if (null == govVehicleFeeEditRequestDTO.getVehicleRecordRelationFlag()) {
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "是否关联行程单选项不能为空");
        }

        if (0 == govVehicleFeeEditRequestDTO.getVehicleRecordRelationFlag()) {
            GovVehicleDriverRelation govVehicleDriverRelation = govVehicleDriverRelationService.getOne(new LambdaQueryWrapper<GovVehicleDriverRelation>()
                    .eq(GovVehicleDriverRelation::getVehicleNo, govVehicleBaseInfo.getVehicleNo())
                    .eq(GovVehicleDriverRelation::getRelationStatus, GovVehicleDriverRelationEnum.NORMAL.getCode()));
            if (null != govVehicleDriverRelation) {
                govVehicleFeeEditRequestDTO.setDriverUserCode(govVehicleDriverRelation.getUserCode());
            }
        } else {
            if (StringUtils.isEmpty(orderNo)) {
                throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "关联行程单号时，行程单号不能为空");
            }
            boolean existRelationOrder = govVehicleFeeService.exists(new LambdaQueryWrapper<GovVehicleFee>()
                    .eq(GovVehicleFee::getVehicleRecordCode, orderNo)
                    .in(GovVehicleFee::getApprovalStatus, Arrays.asList(VehicleFeeApprovalStatusEnum.WAIT_AUTIT.getCode(), VehicleFeeApprovalStatusEnum.AUTIT_PASS.getCode())));
            if (existRelationOrder) {
                throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "该行程单号已存在关联关系");
            }
            //查询订单主表
            GovPublicCarOrder govPublicCarOrder = govPublicCarOrderService.lambdaQuery().eq(GovPublicCarOrder::getOrderNo, orderNo).one();
            if (Objects.isNull(govPublicCarOrder)) {
                throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "订单不存在");

            }
            GovPublicApplyCarOrderDetailRespDTO orderDetailRespDTO = new GovPublicApplyCarOrderDetailRespDTO();
            BeanUtils.copyProperties(govPublicCarOrder, orderDetailRespDTO);

            //查询人员信息
            List<GovPublicCarOrderUserInfo> userInfoList = govPublicCarOrderUserInfoService.lambdaQuery().eq(GovPublicCarOrderUserInfo::getOrderNo, orderNo).list();
            if (!CollectionUtils.isEmpty(userInfoList)) {
                Optional<GovPublicCarOrderUserInfo> carOrderUserInfoOption = userInfoList.stream().filter(e -> e.getUserType().equals(GovPublicCarOrderUserTypeEnum.DRIVER.getCode())).findFirst();
                if (carOrderUserInfoOption.isPresent()) {
                    GovPublicCarOrderUserInfo govPublicCarOrderUserInfo = carOrderUserInfoOption.get();
                    if (null != govPublicCarOrderUserInfo) {
                        govVehicleFeeEditRequestDTO.setDriverUserCode(govPublicCarOrderUserInfo.getUserCode());
                    }
                }
            }

            //查询车辆信息
            GovPublicCarOrderVehicleInfo vehicleInfo = govPublicCarOrderVehicleInfoService.lambdaQuery().eq(GovPublicCarOrderVehicleInfo::getOrderNo, orderNo).one();
            if (null != vehicleInfo) {
                govVehicleBaseInfo.setVehicleNo(vehicleInfo.getVehicleNo());
                govVehicleBaseInfo.setVehicleLicense(vehicleInfo.getVehicleLicense());
                govVehicleBaseInfo.setVehicleVin(vehicleInfo.getVehicleVin());
                govVehicleBaseInfo.setVehicleUseDeptCode(govVehicleBaseInfo.getVehicleUseDeptCode());
                govVehicleBaseInfo.setVehicleUseDeptName(govVehicleBaseInfo.getVehicleUseDeptName());
                govVehicleBaseInfo.setVehicleBelongDeptCode(govVehicleBaseInfo.getVehicleBelongDeptCode());
                govVehicleBaseInfo.setVehicleBelongDeptName(govVehicleBaseInfo.getVehicleBelongDeptName());
                govVehicleBaseInfo.setVehicleUseStructCode(govVehicleBaseInfo.getVehicleUseStructCode());
                govVehicleBaseInfo.setVehicleUseStructName(govVehicleBaseInfo.getVehicleUseStructName());
            }
        }

        String vehicleFeeCode = govVehicleFeeService.saveGovVehicleFee(govVehicleFeeEditRequestDTO, govVehicleBaseInfo);

        //更新订单关联状态为已关联
        LambdaUpdateWrapper<GovPublicCarOrder> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(GovPublicCarOrder::getOrderNo, orderNo);
        lambdaUpdateWrapper.set(GovPublicCarOrder::getRelatedFeesStatus, GovPublicCarRelatedFeesStatusEnum.ASSOCIATED.getCode());
        lambdaUpdateWrapper.set(GovPublicCarOrder::getUpdateCode, govVehicleFeeEditRequestDTO.getLoginUserCode());
        lambdaUpdateWrapper.set(GovPublicCarOrder::getUpdateTime, new Date());
        govPublicCarOrderService.update(lambdaUpdateWrapper);

        Boolean isActive = workFlowIsActive(govVehicleFeeEditRequestDTO.getLoginCompanyId(), ModelEnum.BusinessTypeEnum.DRIVER_FEE_APPROVAL.getCode(), govVehicleFeeEditRequestDTO.getLoginUserBelongDeptId());
        log.info("doSaveGovVehicleFee workFlowIsActive is{}", isActive);
        if (isActive) {
            this.doApproval(govVehicleFeeEditRequestDTO, vehicleFeeCode);
        } else {
            GovVehicleFee updateGovVehicleFee = govVehicleFeeService.lambdaQuery().eq(GovVehicleFee::getVehicleFeeCode, vehicleFeeCode).one();
            updateGovVehicleFee.setApprovalStatus(VehicleFeeApprovalStatusEnum.AUTIT_PASS.getCode());
            updateGovVehicleFee.setApprovalTime(new Date());
            govVehicleFeeService.updateById(updateGovVehicleFee);
        }

        return Boolean.TRUE;
    }

    /**
     * 审批是否激活
     *
     * @param loginCompanyId
     * @param code
     * @return
     */
    private Boolean workFlowIsActive(Integer loginCompanyId, Byte code, Integer loginDeptId) {
        ApplyStartSwitchDTO applyStartSwitchDTO = new ApplyStartSwitchDTO();
        applyStartSwitchDTO.setBusinessType(code);
        applyStartSwitchDTO.setLoginCompanyId(loginCompanyId);
        applyStartSwitchDTO.setLoginDeptId(loginDeptId);
        return workflowApprovalService.isApprovalActive(applyStartSwitchDTO);
    }


    /**
     * 调用审批流
     *
     * @param param
     * @param vehicleFeeCode
     */
    private void doApproval(GovVehicleFeeEditRequestDTO param, String vehicleFeeCode) {
        ApplyStartDTO applyStartDTO = new ApplyStartDTO();
        applyStartDTO.setBusinessType(ModelEnum.BusinessTypeEnum.DRIVER_FEE_APPROVAL.getCode());
        applyStartDTO.setBusinessNo(vehicleFeeCode);
        applyStartDTO.setLoginCompanyId(param.getLoginCompanyId());
        applyStartDTO.setLoginCompanyName(param.getLoginCompanyName());
        applyStartDTO.setLoginUserId(param.getLoginUserId());
        applyStartDTO.setLoginUserName(param.getLoginUserName());
        applyStartDTO.setLoginDeptId(param.getLoginUserBelongDeptId());
        applyStartDTO.setLoginDeptName(param.getLoginUserBelongDeptName());
        String approveId = workflowApprovalService.startApproval(applyStartDTO);
        //更新订单上的审批id
        LambdaUpdateWrapper<GovVehicleFee> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(GovVehicleFee::getVehicleFeeCode, vehicleFeeCode);
        lambdaUpdateWrapper.eq(GovVehicleFee::getCompanyId, param.getLoginCompanyId());
        lambdaUpdateWrapper.set(GovVehicleFee::getApprovalId, approveId);
        govVehicleFeeService.update(lambdaUpdateWrapper);
    }


    @Resource
    private GovDataPermService govDataPermService;

    @Override
    public PageDTO<GovVehicleFeeDetailResponseDTO> getVehicleFeeList(GovVehicleFeeListQryRequestDTO vehicleFeeRequestDTO) {
        log.info("doGetVehicleFeeList vehicleFeeRequestDTO is{}", JSON.toJSONString(vehicleFeeRequestDTO));
        if (Objects.isNull(vehicleFeeRequestDTO.getLoginDataPermType())
                || Objects.equals(vehicleFeeRequestDTO.getLoginDataPermType(), GovDataPermTypeEnum.SELF.getCode())) {
            return new PageDTO<>(vehicleFeeRequestDTO.getPage(), vehicleFeeRequestDTO.getPageSize(), 0, Lists.newArrayList());
        }
        Pair<List<String>, List<String>> unitAndDeptUnderCode = Pair.of(Lists.newArrayList(), Lists.newArrayList());
        if (StringUtils.isNotEmpty(vehicleFeeRequestDTO.getSelectedBelongDeptCode())) {
            unitAndDeptUnderCode = govDataPermService.getUnitAndDeptUnderCode(
                    vehicleFeeRequestDTO.getSelectedBelongDeptCode(), vehicleFeeRequestDTO.getLoginCompanyId());
            // 选中的没有单位
            if (CollectionUtils.isEmpty(unitAndDeptUnderCode.getLeft())) {
                return new PageDTO<>(vehicleFeeRequestDTO.getPage(), vehicleFeeRequestDTO.getPageSize(), 0, Lists.newArrayList());
            }
            vehicleFeeRequestDTO.setVehicleBelongDeptCodeList(unitAndDeptUnderCode.getLeft());
        }
        if (Objects.equals(vehicleFeeRequestDTO.getLoginDataPermType(), GovDataPermTypeEnum.ASSIGN_STRUCT.getCode())) {
            Pair<List<String>, List<String>> assignStructUnitAndDept = govDataPermService.getUnitAndDeptByAssignStructCode(vehicleFeeRequestDTO.getDataCodeSet());
            unitAndDeptUnderCode = govDataPermService.unitAndDeptIntersection(unitAndDeptUnderCode, assignStructUnitAndDept);
            if (CollectionUtils.isEmpty(unitAndDeptUnderCode.getLeft())) {
                return new PageDTO<>(vehicleFeeRequestDTO.getPage(), vehicleFeeRequestDTO.getPageSize(), 0, Lists.newArrayList());
            }
            vehicleFeeRequestDTO.setVehicleBelongDeptCodeList(unitAndDeptUnderCode.getLeft());
        }
        PageDTO<GovVehicleFeeDetailResponseDTO> pageDTO = govVehicleFeeService.getVehicleFeeList(vehicleFeeRequestDTO);
        List<GovVehicleFeeDetailResponseDTO> vehicleFeeDetailResponseDTOList = pageDTO.getResult();

        Map<String, GovUser> userMap = null;
        if (!CollectionUtils.isEmpty(vehicleFeeDetailResponseDTOList)) {
            List<String> userCodeList = vehicleFeeDetailResponseDTOList.stream().map(GovVehicleFeeDetailResponseDTO::getDriverUserCode).collect(Collectors.toList());
            List<GovUser> userList = govUserService.list(new LambdaQueryWrapper<GovUser>()
                    .in(GovUser::getUserCode, userCodeList));
            if (!CollectionUtils.isEmpty(userList)) {
                userMap = userList.stream().collect(Collectors.toMap(GovUser::getUserCode, user -> user));
            }
            if (null != userMap) {
                for (GovVehicleFeeDetailResponseDTO govVehicleFeeDetailResponseDTO : vehicleFeeDetailResponseDTOList) {
                    GovUser govUser = userMap.get(govVehicleFeeDetailResponseDTO.getDriverUserCode());
                    if (null != govUser) {
                        govVehicleFeeDetailResponseDTO.setDriverName(govUser.getUserName() + "/" + govUser.getMobile());
                    }
                }
            }
            for (GovVehicleFeeDetailResponseDTO govVehicleFeeDetailResponseDTO : vehicleFeeDetailResponseDTOList) {
                if (null != govVehicleFeeDetailResponseDTO.getApprovalStatus() && VehicleFeeApprovalStatusEnum.WAIT_AUTIT.getCode() == govVehicleFeeDetailResponseDTO.getApprovalStatus().intValue()) {
                    govVehicleFeeDetailResponseDTO.setSupportWithdrawFlag(true);
                } else {
                    govVehicleFeeDetailResponseDTO.setSupportWithdrawFlag(false);
                }
                if (null != govVehicleFeeDetailResponseDTO.getVehicleRecordRelationFlag()) {
                    govVehicleFeeDetailResponseDTO.setVehicleRecordRelationFlagDesc(VehicleFeeRelationOrderFlagEnum.getDesc(govVehicleFeeDetailResponseDTO.getVehicleRecordRelationFlag()));
                }
                govVehicleFeeDetailResponseDTO.setManageCarTypeName(ManageCarTypeEnum.getDesByCode(govVehicleFeeDetailResponseDTO.getManageCarType()));
            }

        }

        return pageDTO;
    }

    @Override
    public PageDTO<GovVehicleFeeDetailResponseDTO> getVehicleFeeListForApp(GovVehicleFeeListQryRequestDTO vehicleFeeRequestDTO) {
        log.info("doGetVehicleFeeListForApp vehicleFeeRequestDTO is{}", JSON.toJSONString(vehicleFeeRequestDTO));

        PageDTO<GovVehicleFeeDetailResponseDTO> pageDTO = govVehicleFeeService.getVehicleFeeListForApp(vehicleFeeRequestDTO);
        List<GovVehicleFeeDetailResponseDTO> vehicleFeeDetailResponseDTOList = pageDTO.getResult();

        Map<String, GovUser> userMap = null;
        if (!CollectionUtils.isEmpty(vehicleFeeDetailResponseDTOList)) {
            List<String> userCodeList = vehicleFeeDetailResponseDTOList.stream().map(GovVehicleFeeDetailResponseDTO::getDriverUserCode).collect(Collectors.toList());
            List<GovUser> userList = govUserService.list(new LambdaQueryWrapper<GovUser>()
                    .in(GovUser::getUserCode, userCodeList));
            if (!CollectionUtils.isEmpty(userList)) {
                userMap = userList.stream().collect(Collectors.toMap(GovUser::getUserCode, user -> user));
            }
            if (null != userMap) {
                for (GovVehicleFeeDetailResponseDTO govVehicleFeeDetailResponseDTO : vehicleFeeDetailResponseDTOList) {
                    GovUser govUser = userMap.get(govVehicleFeeDetailResponseDTO.getDriverUserCode());
                    if (null != govUser) {
                        govVehicleFeeDetailResponseDTO.setDriverName(govUser.getUserName() + "/" + govUser.getMobile());
                    }
                }
            }
            for (GovVehicleFeeDetailResponseDTO govVehicleFeeDetailResponseDTO : vehicleFeeDetailResponseDTOList) {
                if (null != govVehicleFeeDetailResponseDTO.getApprovalStatus() && VehicleFeeApprovalStatusEnum.WAIT_AUTIT.getCode() == govVehicleFeeDetailResponseDTO.getApprovalStatus().intValue()) {
                    govVehicleFeeDetailResponseDTO.setSupportWithdrawFlag(true);
                } else {
                    govVehicleFeeDetailResponseDTO.setSupportWithdrawFlag(false);
                }
                if (null != govVehicleFeeDetailResponseDTO.getVehicleRecordRelationFlag()) {
                    govVehicleFeeDetailResponseDTO.setVehicleRecordRelationFlagDesc(VehicleFeeRelationOrderFlagEnum.getDesc(govVehicleFeeDetailResponseDTO.getVehicleRecordRelationFlag()));
                }
            }

        }

        return pageDTO;
    }

    @Override
    public GovVehicleFeeDetailResponseDTO getGovVehicleFeeDetail(String vehicleFeeCode) {
        log.info("doGetGovVehicleFeeDetail vehicleFeeCode is{}", vehicleFeeCode);
        GovVehicleFeeDetailResponseDTO govVehicleFeeDetailResponseDTO = new GovVehicleFeeDetailResponseDTO();
        GovVehicleFee govVehicleFee = govVehicleFeeService.getOne(
                new LambdaQueryWrapper<GovVehicleFee>()
                        .eq(GovVehicleFee::getVehicleFeeCode, vehicleFeeCode));
        if (null == govVehicleFee) {
            return null;
        }
        BeanUtils.copyProperties(govVehicleFee, govVehicleFeeDetailResponseDTO);
        if (null != govVehicleFeeDetailResponseDTO.getApprovalStatus()) {
            govVehicleFeeDetailResponseDTO.setApprovalStatusDesc(VehicleFeeApprovalStatusEnum.getDesc(govVehicleFeeDetailResponseDTO.getApprovalStatus()));
        }
        if (StringUtils.isNotEmpty(govVehicleFeeDetailResponseDTO.getDriverUserCode())) {
            GovUser govUser = govUserService.getOne(new LambdaQueryWrapper<GovUser>()
                    .in(GovUser::getUserCode, govVehicleFeeDetailResponseDTO.getDriverUserCode()));
            if (null != govUser) {
                govVehicleFeeDetailResponseDTO.setDriverName(govUser.getUserName() + "/" + govUser.getMobile());
            }
        }
        List<GovVehicleFeeDetail> govVehicleFeeDetailList = govVehicleFeeDetailService.list(new LambdaQueryWrapper<GovVehicleFeeDetail>()
                .eq(GovVehicleFeeDetail::getVehicleFeeCode, vehicleFeeCode));
        if (!CollectionUtils.isEmpty(govVehicleFeeDetailList)) {
            List<GovVehicleFeeDetailItemResponseDTO> govVehicleFeeDetailItemResponseDTOList = BeanUtil.copyList(govVehicleFeeDetailList, GovVehicleFeeDetailItemResponseDTO.class);
            govVehicleFeeDetailItemResponseDTOList.stream().forEach(e -> {
                e.setSort(VehicleFeeDetailItemEnum.getEnumByCode(e.getFeeCode()).getSort());
            });
            govVehicleFeeDetailItemResponseDTOList = govVehicleFeeDetailItemResponseDTOList.stream().sorted(Comparator.comparing(GovVehicleFeeDetailItemResponseDTO::getSort)).collect(Collectors.toList());
            govVehicleFeeDetailResponseDTO.setFeeDetailList(govVehicleFeeDetailItemResponseDTOList);
        }

        List<GovVehicleFeeVoucherImg> govVehicleFeeVoucherImgList = govVehicleFeeVoucherImgService.list(new LambdaQueryWrapper<GovVehicleFeeVoucherImg>()
                .eq(GovVehicleFeeVoucherImg::getVehicleFeeCode, vehicleFeeCode));

        govVehicleFeeDetailResponseDTO.setVoucherImgList(BeanUtil.copyList(govVehicleFeeVoucherImgList, ImageDTO.class));

        List<FileDTO> approvalVoucherImgList = govVehicleFeeVoucherImgList.stream()
                .map(image -> {
                    FileDTO fileDTO = new FileDTO();
                    fileDTO.setFileName(image.getImgName());
                    fileDTO.setFileUrl(image.getImgUrl());
                    return fileDTO;
                }).collect(Collectors.toList());
        govVehicleFeeDetailResponseDTO.setApprovalVoucherImgList(approvalVoucherImgList);

        if (null != govVehicleFeeDetailResponseDTO.getVehicleRecordRelationFlag()) {
            govVehicleFeeDetailResponseDTO.setVehicleRecordRelationFlagDesc(VehicleFeeRelationOrderFlagEnum.getDesc(govVehicleFeeDetailResponseDTO.getVehicleRecordRelationFlag()));
        }
        return govVehicleFeeDetailResponseDTO;

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void approveResult(BpmMessageSendApproveResultDTO bpmMessageSendApproveResultDTO) {
        log.info("doApproveResult bpmMessageSendApproveResultDTO is{}", JSON.toJSONString(bpmMessageSendApproveResultDTO));
        String vehicleFeeCode = bpmMessageSendApproveResultDTO.getBusinessNo();
        //根据车杂费单号查询车杂费
        GovVehicleFee govVehicleFee = govVehicleFeeService.getOne(new LambdaQueryWrapper<GovVehicleFee>().eq(GovVehicleFee::getVehicleFeeCode, vehicleFeeCode));
        if (govVehicleFee == null) {
            log.info("车杂费工单接收审批流消息，找不到相关车杂费记录：{}", vehicleFeeCode);
            return;
        }
        //老单子已经是撤回状态则消息丢弃
        if (VehicleFeeApprovalStatusEnum.AUTIT_CANCEL.getCode() == govVehicleFee.getApprovalStatus()) {
            log.info("车杂费工单接收审批流消息，车杂费已经被撤销：{}", vehicleFeeCode);
            return;
        }
        BpmProcessInstanceResultEnum bpmProcessInstanceResultEnum = BpmProcessInstanceResultEnum.getEnum(bpmMessageSendApproveResultDTO.getResult());

        GovVehicleFee updateGovVehicleFee = new GovVehicleFee();
        updateGovVehicleFee.setId(govVehicleFee.getId());
        updateGovVehicleFee.setVehicleFeeCode(vehicleFeeCode);
        updateGovVehicleFee.setUpdateTime(bpmMessageSendApproveResultDTO.getApproverTime());
        updateGovVehicleFee.setApprovalTime(bpmMessageSendApproveResultDTO.getApproverTime());
        switch (bpmProcessInstanceResultEnum) {
            //审批通过
            case APPROVE:
                //修改车杂费工单状态为审核通过
                updateGovVehicleFee.setApprovalStatus(VehicleFeeApprovalStatusEnum.AUTIT_PASS.getCode());
                outOperateLogApprove(bpmMessageSendApproveResultDTO.getApproverTime(), govVehicleFee, VehicleFeeApprovalStatusEnum.AUTIT_PASS, VehicleFeeApprovalStatusEnum.AUTIT_PASS);
                break;
            //驳回
            case REJECT:
                //修改车杂费工单状态为驳回
                updateGovVehicleFee.setApprovalStatus(VehicleFeeApprovalStatusEnum.AUTIT_REFUSE.getCode());
                outOperateLogApprove(bpmMessageSendApproveResultDTO.getApproverTime(), govVehicleFee, VehicleFeeApprovalStatusEnum.AUTIT_REFUSE, VehicleFeeApprovalStatusEnum.AUTIT_REFUSE);

                //更新订单关联状态为未关联
                if (StringUtils.isNotEmpty(govVehicleFee.getVehicleRecordCode())) {
                    this.unBindVehicleRelatedFees(govVehicleFee.getVehicleRecordCode(), bpmMessageSendApproveResultDTO.getApproverCode());
                }
                break;
            case CANCEL:
                //修改订单审批状态为取消，订单状态为已取消，并释放车态
                updateGovVehicleFee.setApprovalStatus(VehicleFeeApprovalStatusEnum.AUTIT_CANCEL.getCode());
                outOperateLogApprove(bpmMessageSendApproveResultDTO.getApproverTime(), govVehicleFee, VehicleFeeApprovalStatusEnum.AUTIT_CANCEL, VehicleFeeApprovalStatusEnum.AUTIT_CANCEL);
                //更新订单关联状态为未关联
                if (StringUtils.isNotEmpty(govVehicleFee.getVehicleRecordCode())) {
                    this.unBindVehicleRelatedFees(govVehicleFee.getVehicleRecordCode(), bpmMessageSendApproveResultDTO.getApproverCode());
                }
                break;
            case BACK:
                //修改订单审批状态为，订单状态为已取消，并释放车态
                updateGovVehicleFee.setApprovalStatus(VehicleFeeApprovalStatusEnum.AUTIT_CANCEL.getCode());
                outOperateLogApprove(bpmMessageSendApproveResultDTO.getApproverTime(), govVehicleFee, VehicleFeeApprovalStatusEnum.AUTIT_CANCEL, VehicleFeeApprovalStatusEnum.AUTIT_CANCEL);
                //更新订单关联状态为未关联
                if (StringUtils.isNotEmpty(govVehicleFee.getVehicleRecordCode())) {
                    this.unBindVehicleRelatedFees(govVehicleFee.getVehicleRecordCode(), bpmMessageSendApproveResultDTO.getApproverCode());
                }
                break;
            default:
                log.info("车杂费接收审批流消息，审批结果未知：{}", bpmMessageSendApproveResultDTO);
        }
        //更新车杂费单表
        govVehicleFeeService.updateById(updateGovVehicleFee);
    }

    private void outOperateLogApprove(Date out, GovVehicleFee govVehicleFee, VehicleFeeApprovalStatusEnum operationTypeEnum, VehicleFeeApprovalStatusEnum statusEnum) {
        GovOrderOperationLog orderOperationLogOut = new GovOrderOperationLog();
        orderOperationLogOut.setOrderNo(govVehicleFee.getVehicleFeeCode());
        orderOperationLogOut.setOperationType(operationTypeEnum.getCode());
        orderOperationLogOut.setOperationDescription(operationTypeEnum.getDesc());
        orderOperationLogOut.setOperationTime(out);
        orderOperationLogOut.setOrderStatus(statusEnum.getCode());
        orderOperationLogOut.setOrderStatusName(statusEnum.getDesc());
        orderOperationLogOut.setOperatorCode("");
        orderOperationLogOut.setOperatorName("系统");
        orderOperationLogOut.setOperatorMobile("");
        govOrderOperationLogService.save(orderOperationLogOut);
    }

    @Override
    public boolean withdrawGovVehicleFee(GovVehicleFeeWithDrawRequestDTO requestDTO) {
        log.info("doWithdrawGovVehicleFee requestDTO is{}", JSON.toJSONString(requestDTO));
        //根据车杂费单号查询车杂费
        GovVehicleFee govVehicleFee = govVehicleFeeService.getOne(new LambdaQueryWrapper<GovVehicleFee>().eq(GovVehicleFee::getVehicleFeeCode, requestDTO.getVehicleFeeCode()));
        if (govVehicleFee == null) {
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "车杂费信息不存在");
        }

        if (VehicleFeeApprovalStatusEnum.AUTIT_CANCEL.getCode() == govVehicleFee.getApprovalStatus()) {
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "车杂费已撤回");
        }

        this.doWithdraw(govVehicleFee, requestDTO);

        return Boolean.TRUE;
    }


    /**
     * 撤回
     *
     * @param govVehicleFee
     * @param param
     */
    private void doWithdraw(GovVehicleFee govVehicleFee, GovVehicleFeeWithDrawRequestDTO param) {
        BpmProcessInstanceCancelReqDTO applyWithdrawDTO = new BpmProcessInstanceCancelReqDTO();
        applyWithdrawDTO.setLoginCompanyId(param.getLoginCompanyId());
        applyWithdrawDTO.setLoginCompanyName(param.getLoginCompanyName());
        applyWithdrawDTO.setLoginUserId(param.getLoginUserId());
        applyWithdrawDTO.setLoginUserCode(param.getLoginUserCode());
        applyWithdrawDTO.setLoginUserName(param.getLoginUserName());
        applyWithdrawDTO.setId(govVehicleFee.getApprovalId());
        applyWithdrawDTO.setReason(param.getReason());
        applyWithdrawDTO.setIsBusiness(true);
        applyWithdrawDTO.setLoginDeptId(param.getLoginUserBelongDeptId());
        applyWithdrawDTO.setLoginDeptName(param.getLoginUserBelongDeptName());
        workflowApprovalService.cancelApproval(applyWithdrawDTO);
        //更新订单上的审批id
        LambdaUpdateWrapper<GovVehicleFee> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(GovVehicleFee::getVehicleFeeCode, govVehicleFee.getVehicleFeeCode());
        lambdaUpdateWrapper.eq(GovVehicleFee::getCompanyId, param.getLoginCompanyId());
        lambdaUpdateWrapper.set(GovVehicleFee::getApprovalStatus, VehicleFeeApprovalStatusEnum.AUTIT_CANCEL.getCode());
        govVehicleFeeService.update(lambdaUpdateWrapper);

        //更新订单关联状态为未关联
        if (StringUtils.isNotEmpty(govVehicleFee.getVehicleRecordCode())) {
            this.unBindVehicleRelatedFees(govVehicleFee.getVehicleRecordCode(), param.getLoginUserCode());
        }

    }

    /**
     * 解除订单与车杂费关系
     *
     * @param orderNO
     * @param loginUserCode
     */
    private void unBindVehicleRelatedFees(String orderNO, String loginUserCode) {
        LambdaUpdateWrapper<GovPublicCarOrder> lambdaCarOrderUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaCarOrderUpdateWrapper.eq(GovPublicCarOrder::getOrderNo, orderNO);
        lambdaCarOrderUpdateWrapper.set(GovPublicCarOrder::getRelatedFeesStatus, GovPublicCarRelatedFeesStatusEnum.UNASSOCIATED.getCode());
        lambdaCarOrderUpdateWrapper.set(GovPublicCarOrder::getUpdateCode, loginUserCode);
        lambdaCarOrderUpdateWrapper.set(GovPublicCarOrder::getUpdateTime, new Date());
        govPublicCarOrderService.update(lambdaCarOrderUpdateWrapper);
    }

}
