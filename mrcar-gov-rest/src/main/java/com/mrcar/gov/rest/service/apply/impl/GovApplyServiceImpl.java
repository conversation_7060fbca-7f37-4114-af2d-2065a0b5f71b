package com.mrcar.gov.rest.service.apply.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.izu.framework.exception.ApiException;
import com.izu.framework.resp.InfoCode;
import com.izu.framework.web.util.BeanUtil;
import com.mrcar.gov.asset.domain.GovGpsDevice;
import com.mrcar.gov.asset.domain.GovVehicleBaseInfo;
import com.mrcar.gov.asset.service.GovGpsDeviceService;
import com.mrcar.gov.asset.service.GovVehicleBaseInfoService;
import com.mrcar.gov.base.service.SequenceGenerator;
import com.mrcar.gov.common.constant.PushMessageUtil;
import com.mrcar.gov.common.constant.business.GovMsgSceneEnum;
import com.mrcar.gov.common.constant.order.*;
import com.mrcar.gov.common.constant.user.GovUserTypeEnum;
import com.mrcar.gov.common.dto.apply.req.*;
import com.mrcar.gov.common.dto.apply.resp.GovCarCreateApplyRespDTO;
import com.mrcar.gov.common.dto.config.GovCompanyConfigItemValueDTO;
import com.mrcar.gov.common.dto.order.req.GovOrderUserInfoDTO;
import com.mrcar.gov.common.dto.order.req.TimeShareConfigQueryReqDTO;
import com.mrcar.gov.common.dto.order.resp.TimeShareConfigRespDTO;
import com.mrcar.gov.common.dto.workflow.enums.BpmProcessInstanceResultEnum;
import com.mrcar.gov.common.dto.workflow.enums.ModelEnum;
import com.mrcar.gov.common.dto.workflow.instance.ApplyStartDTO;
import com.mrcar.gov.common.dto.workflow.instance.ApplyStartSwitchDTO;
import com.mrcar.gov.common.dto.workflow.instance.BpmMessageSendApproveResultDTO;
import com.mrcar.gov.common.dto.workflow.instance.BpmProcessInstanceCancelReqDTO;
import com.mrcar.gov.common.enums.config.PublicConfigEnum;
import com.mrcar.gov.common.enums.device.GovGpsDeviceEnum;
import com.mrcar.gov.common.service.api.MessageTrackingSender;
import com.mrcar.gov.common.util.DateUtils;
import com.mrcar.gov.common.util.EmojiUtils;
import com.mrcar.gov.config.domain.GovCityDic;
import com.mrcar.gov.config.service.GovCityDicService;
import com.mrcar.gov.rest.service.config.GovPublicBusinessConfigService;
import com.mrcar.gov.order.domain.*;
import com.mrcar.gov.order.service.*;
import com.mrcar.gov.rest.service.apply.GovApplyService;
import com.mrcar.gov.user.common.SpecialRoleEnum;
import com.mrcar.gov.user.domain.GovStruct;
import com.mrcar.gov.user.domain.GovUser;
import com.mrcar.gov.user.mapper.GovUserRoleRelationMapper;
import com.mrcar.gov.user.service.GovStructService;
import com.mrcar.gov.user.service.GovUserService;
import com.mrcar.gov.workflow.service.impl.WorkflowApprovalService;
import com.mrcar.thirdparty.baidu.AddressResult;
import com.mrcar.thirdparty.baidu.BaiduMapApiClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description:
 * @date 2024/12/27 9:46
 */
@Service
@Slf4j
public class GovApplyServiceImpl implements GovApplyService {

    @Resource
    private WorkflowApprovalService workflowApprovalService;

    @Resource
    private SequenceGenerator sequenceGenerator;

    @Autowired
    private GovPublicCarApplyService govPublicCarApplyService;

    @Autowired
    private GovPublicApplyVehicleDriverInfoService govPublicApplyVehicleDriverInfoService;

    @Autowired
    private GovPublicCarOrderUserInfoService govPublicCarOrderUserInfoService;

    @Autowired
    private GovApplyOperationLogService govApplyOperationLogService;

    @Autowired
    private GovUserService govUserService;

    @Autowired
    private GovVehicleBaseInfoService govVehicleBaseInfoService;

    @Autowired
    private GovPublicCarOrderService govPublicCarOrderService;

    @Autowired
    private GovStructService govStructService;
    @Resource
    private GovGpsDeviceService govGpsDeviceService;

    @Autowired
    private GovPublicCarOrderVehicleInfoService govPublicCarOrderVehicleInfoService;

    @Autowired
    private GovOrderOperationLogService govOrderOperationLogService;

    @Autowired
    private GovBillingConfigurationService govBillingConfigurationService;

    @Resource
    private GovTimeShareConfigService govTimeShareConfigService;

    private final static int MAX_VEHICLE_SIZE = 10;
    private final static int MAX_PASSENGER_SIZE = 10;

    /**
     * 公务用车对公 订单前缀
     */
    public static final String PUBLIC_GOV_CAR_APPLY_PREFIX = "GWYC";
    /**
     * 公务用车对公 订单前缀
     */
    public static final String PUBLIC_SOC_GOV_CAR_APPLY_PREFIX = "SHZL";
    /**
     * 公务用车对公 子订单前缀
     */
    public static final String PUBLIC_GOV_CAR_ORDER_PREFIX = "GWYCZDD";
    /**
     * 公务用车对公 子订单前缀
     */
    public static final String PUBLIC_SOC_GOV_CAR_ORDER_PREFIX = "SHZLZDD";
    @Autowired
    private MessageTrackingSender messageTrackingSender;

    @Autowired
    private GovUserRoleRelationMapper govUserRoleRelationMapper;
    @Resource
    private GovCityDicService govCityDicService;
    @Resource
    private BaiduMapApiClient baiduMapApiClient;


    @Resource
    private GovPublicBusinessConfigService govPublicBusinessConfigService;
    @Resource
    private GovPublicCarOrderUserInfoService userInfoService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public GovCarCreateApplyRespDTO createApply(GovCarCreateApplyReqDTO param) {
        GovCarCreateApplyRespDTO applyNoResp = new GovCarCreateApplyRespDTO();
        List<GovApplyUserVehicleDTO> vehicleUserList = param.getVehicleUserList();
        List<GovOrderUserInfoDTO> passengersInfoList = param.getPassengersInfoList();
        if (CollectionUtils.isEmpty(passengersInfoList)) {
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "请选择乘车人");
        }
        if (passengersInfoList.size() > MAX_PASSENGER_SIZE) {
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "最多选择" + MAX_PASSENGER_SIZE + "人");
        }
        if (CollectionUtils.isEmpty(vehicleUserList)) {
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "请选择车辆司机信息");
        }
        if (vehicleUserList.size() > MAX_VEHICLE_SIZE) {
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "车辆最多支持" + MAX_VEHICLE_SIZE + "辆");
        }
        if (EmojiUtils.containsEmoji(param.getOrderUserMemo())) {
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "备注中不允许包含表情");
        }
        //校验当前下单人的人员类型
        if (!GovUserTypeEnum.isCompanyUserType(param.getLoginUserType()) &&
                !ObjectUtil.equals(GovUserTypeEnum.TECHNICAL_SUPPORT.getCode(), param.getLoginUserType())) {
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "当前登录人不支持下单操作");
        }
        Date now = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(now);
        // 将秒数设置为 0
        calendar.set(Calendar.SECOND, 0);
        // 将毫秒数设置为 0
        calendar.set(Calendar.MILLISECOND, 0);
        Date dateTimeNow = calendar.getTime();
        if (param.getExpectedPickupTime().before(dateTimeNow)) {
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "开始时间不能小于当前时间");
        }
        if (param.getExpectedReturnTime().before(dateTimeNow)) {
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "结束时间不能小于当前时间");
        }
        if (param.getExpectedReturnTime().getTime() <= param.getExpectedPickupTime().getTime()) {
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "结束时间要晚于当前时间");
        }
        //判断审批开关是否打开
        Boolean isActive;
        if (ObjectUtil.equals(param.getApplyType(), GovPublicCarApplyTypeEnum.PUBLIC_CAR.getCode())) {
            isActive = workFlowIsActive(param.getLoginCompanyId(), ModelEnum.BusinessTypeEnum.CAR_USE_APPROVAL.getCode(), param.getLoginUserBelongDeptId());
        } else {
            isActive = workFlowIsActive(param.getLoginCompanyId(), ModelEnum.BusinessTypeEnum.CAR_SOCIAL_RENT_APPROVAL.getCode(), param.getLoginUserBelongDeptId());
        }
        //构建申请单实体类
        String applyNo = buildOrderInfo(param, isActive);
        //2.保存临时表
        buildReserveInfo(param, vehicleUserList, applyNo);
        //3. 人员信息
        buildUsersInfo(param, passengersInfoList, applyNo);
        //5.操作日志
        buildOperationInfo(param, applyNo);
        //审批
        if (isActive) {
            doApproval(param, applyNo);
        } else {
            Integer scheduleType = param.getScheduleType();
            //只有无需调度的才会直接生成订单变成待出发
            if (ObjectUtil.equals(scheduleType, GovPublicCarScheduleTypeEnum.NO_SCHEDULE.getCode())) {
                //处理生成子行程
                List<GovApplyUserVehicleDTO> dispatchVehicleDriverInfos = param.getVehicleUserList();
                //车辆编码
                List<String> vehicleNoList = dispatchVehicleDriverInfos.stream().map(GovApplyUserVehicleDTO::getVehicleNo).collect(Collectors.toList());
                //司机编码
                List<String> userCodeList = dispatchVehicleDriverInfos.stream().map(GovApplyUserVehicleDTO::getUserCode).collect(Collectors.toList());
                //查询车辆详细信息和司机详细信息并转map
                Map<String, GovVehicleBaseInfo> vehicleMap = govVehicleBaseInfoService.list(new LambdaQueryWrapper<GovVehicleBaseInfo>().in(GovVehicleBaseInfo::getVehicleNo, vehicleNoList)).stream().collect(Collectors.toMap(GovVehicleBaseInfo::getVehicleNo, Function.identity()));
                Map<String, GovUser> userInfoMap = govUserService.list(new LambdaQueryWrapper<GovUser>().in(GovUser::getUserCode, userCodeList)).stream().collect(Collectors.toMap(GovUser::getUserCode, Function.identity()));
                //生成子行程
                List<GovPublicCarOrderUserInfo> list = govPublicCarOrderUserInfoService.list(new LambdaQueryWrapper<GovPublicCarOrderUserInfo>().eq(GovPublicCarOrderUserInfo::getApplyNo, applyNo));
                for (GovApplyUserVehicleDTO dispatchVehicleDriverInfo : dispatchVehicleDriverInfos) {
                    generateOrder(param, applyNo, vehicleMap, userInfoMap, dispatchVehicleDriverInfo, list);
                }
                //调度完成，发调度完成通知
                try {
                    pushMessageForCreatorAndPassenger(applyNo);
                } catch (Exception e) {
                    log.info("发送消息异常，{}", e.getMessage());
                }
            } else {
                //查询申请单信息
                GovPublicCarApply apply = govPublicCarApplyService.getOne(new LambdaQueryWrapper<GovPublicCarApply>().eq(GovPublicCarApply::getApplyNo, applyNo));
                //发送调度信息
                try {
                    pushDispatchMessage(apply, applyNo);
                } catch (Exception e) {
                    log.info("发送消息异常，{}", e.getMessage());
                }
            }
        }
        applyNoResp.setApplyNo(applyNo);
        return applyNoResp;
    }

    private void generateOrder(GovCarCreateApplyReqDTO param, String applyNo, Map<String, GovVehicleBaseInfo> vehicleMap, Map<String, GovUser> userInfoMap, GovApplyUserVehicleDTO dispatchVehicleDriverInfo, List<GovPublicCarOrderUserInfo> list) {
        String orderNo;
        if (ObjectUtil.equal(param.getApplyType(), GovPublicCarApplyTypeEnum.PUBLIC_CAR.getCode())) {
            orderNo = sequenceGenerator.generate(new Date(), PUBLIC_GOV_CAR_ORDER_PREFIX);
        } else {
            orderNo = sequenceGenerator.generate(new Date(), PUBLIC_SOC_GOV_CAR_ORDER_PREFIX);
        }
        GovVehicleBaseInfo govVehicleBaseInfo = vehicleMap.get(dispatchVehicleDriverInfo.getVehicleNo());
        GovUser govUser = userInfoMap.get(dispatchVehicleDriverInfo.getUserCode());
        //生成订单并插入数据库
        GovPublicCarOrder order = new GovPublicCarOrder();
        order.setOrderNo(orderNo);
        order.setOrderType(GovPublicCarOrderTypeEnum.PUBLIC_USE_CAR_TYPE.getCode());
        order.setUseType(param.getApplyType());
        order.setVehicleNo(govVehicleBaseInfo.getVehicleNo());
        order.setVehicleLicense(govVehicleBaseInfo.getVehicleLicense());
        order.setVehicleVin(govVehicleBaseInfo.getVehicleVin());
        order.setOrderStatus(GovPublicCarOrderStatusEnum.PENDING_DEPARTURE.getCode());
        order.setVerifyStatus(GovPublicCarVerifyStatusEnum.NO_VERIFICATION_REQUIRED.getCode());
        order.setRelatedFeesStatus(GovPublicCarRelatedFeesStatusEnum.UNASSOCIATED.getCode());
        order.setCompanyId(param.getLoginCompanyId());
        order.setCompanyName(param.getLoginCompanyName());
        order.setApplyNo(applyNo);
        order.setUpdateCode(param.getLoginUserCode());
        order.setUpdateName(param.getLoginUserName());
        //查询主用车人信息
        GovPublicCarOrderUserInfo primaryUserInfo = govPublicCarOrderUserInfoService.getOne(new LambdaQueryWrapper<GovPublicCarOrderUserInfo>().eq(GovPublicCarOrderUserInfo::getApplyNo, applyNo).eq(GovPublicCarOrderUserInfo::getUserType, GovPublicCarOrderUserTypeEnum.PRIMARY_RIDER.getCode()).last("limit 1"));
        if (!primaryUserInfo.getDeptCode().equals(param.getUseVehicleDeptCode())) {
            order.setCrossType(GovPublicCarCrossTypeEnum.CROSS.getCode());
        } else {
            order.setCrossType(GovPublicCarCrossTypeEnum.NO_CROSS.getCode());
        }
        //社会用车需要查询计费快照id
        if (ObjectUtil.equals(param.getApplyType(), GovPublicCarApplyTypeEnum.SOCIAL_RENT.getCode())) {

            if (Objects.equals(param.getRentType(), GovPublicCarRentTypeEnum.DAILY.getCode())) {
                LambdaQueryWrapper<GovBillingConfiguration> queryWrapper = new LambdaQueryWrapper<GovBillingConfiguration>()
                        .eq(GovBillingConfiguration::getCompanyId, param.getLoginCompanyId())
                        .eq(GovBillingConfiguration::getSupplierCode, govVehicleBaseInfo.getSupplierServiceCode())
                        .eq(GovBillingConfiguration::getVehicleType, govVehicleBaseInfo.getVehicleType())
                        .last("limit 1");
                GovBillingConfiguration govBillingConfiguration = govBillingConfigurationService.getOne(queryWrapper);
                if (ObjectUtil.isNotNull(govBillingConfiguration)) {
                    order.setBillingConfigurationVersionId(govBillingConfiguration.getBillingConfigurationVersionId());
                }
            } else if (ObjectUtil.equals(param.getRentType(), GovPublicCarRentTypeEnum.TIME_SHARE.getCode())) {
                //分时
                TimeShareConfigQueryReqDTO req = new TimeShareConfigQueryReqDTO();
                req.setCompanyId(govVehicleBaseInfo.getCompanyId());
                req.setSupplierCode(govVehicleBaseInfo.getSupplierServiceCode());
                req.setVehicleType(govVehicleBaseInfo.getVehicleType());
                req.setVehicleBrandId(govVehicleBaseInfo.getVehicleBrandId());
                req.setVehicleSeriesId(govVehicleBaseInfo.getVehicleSeriesId());
                TimeShareConfigRespDTO detail = govTimeShareConfigService.detail(req);
                if (Objects.nonNull(detail) && StringUtils.isNotBlank(detail.getSnapshotCode())) {
                    order.setTimeShareSnapshotCode(detail.getSnapshotCode());
                }
                //计价时间配置
                List<GovCompanyConfigItemValueDTO> configItemValueDTOS = govPublicBusinessConfigService.selectByDeptCodeAndBusinessCodeAndItemCode(govVehicleBaseInfo.getCompanyId(), primaryUserInfo.getDeptCode(),
                        PublicConfigEnum.BusinessConfigEnum.HOUR_USE_CAR_DURATION_FILED.getCode(), PublicConfigEnum.BusinessConfigItemEnum.USE_CAR_DURATION_FILED_ITEM.getCode());
                GovCompanyConfigItemValueDTO configItemValueDTO = configItemValueDTOS.get(0);
                // 计价时间配置: 实际开始/结束时间
                Integer timeShareCalculateConfig = Integer.parseInt(configItemValueDTO.getConfigValue());
                order.setTimeShareCalculateConfig(timeShareCalculateConfig);
            }

        }
        //插入订单
        govPublicCarOrderService.save(order);
        if (ObjectUtil.equal(param.getApplyType(), GovPublicCarApplyTypeEnum.SOCIAL_RENT.getCode())
                && ObjectUtil.equal(param.getRentType(), GovPublicCarRentTypeEnum.TIME_SHARE.getCode())
                && StringUtils.isEmpty(govVehicleBaseInfo.getVehicleBelongDeptCode())) {
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "所选车辆信息未维护分时租赁单位");
        }
        //插入人员信息
        //根据申请单号查询所有人员信息
        if (CollectionUtils.isNotEmpty(list)) {
            for (GovPublicCarOrderUserInfo govPublicCarOrderUserInfo : list) {
                govPublicCarOrderUserInfo.setId(null);
                govPublicCarOrderUserInfo.setOrderNo(orderNo);
            }
            govPublicCarOrderUserInfoService.saveBatch(list);
        }
        //插入司机信息
        GovPublicCarOrderUserInfo driverInfo = new GovPublicCarOrderUserInfo();
        driverInfo.setUserType(GovPublicCarOrderUserTypeEnum.DRIVER.getCode());
        driverInfo.setUserCode(govUser.getUserCode());
        driverInfo.setUserName(govUser.getUserName());
        driverInfo.setUserMobile(govUser.getMobile());
        driverInfo.setStructCode(govUser.getBelongStructCode());
        driverInfo.setDeptCode(govUser.getBelongDeptCode());
        Map<String, GovStruct> govStructMap = govStructService.list(new LambdaQueryWrapper<GovStruct>().in(GovStruct::getStructCode, Arrays.asList(govUser.getBelongStructCode(), govUser.getBelongDeptCode()))).stream().collect(Collectors.toMap(GovStruct::getStructCode, Function.identity()));
        GovStruct struct = govStructMap.get(govUser.getBelongStructCode());
        if (Objects.nonNull(struct)) {
            driverInfo.setStructName(struct.getStructName());
        }
        GovStruct dept = govStructMap.get(govUser.getBelongDeptCode());
        if (Objects.nonNull(dept)) {
            driverInfo.setDeptName(dept.getStructName());
        }
        driverInfo.setCompanyId(param.getLoginCompanyId());
        driverInfo.setCompanyName(param.getLoginCompanyName());
        driverInfo.setApplyNo(applyNo);
        driverInfo.setOrderNo(orderNo);
        govPublicCarOrderUserInfoService.save(driverInfo);
        //插入车辆信息
        GovPublicCarOrderVehicleInfo govPublicCarOrderVehicleInfo = new GovPublicCarOrderVehicleInfo();
        govPublicCarOrderVehicleInfo.setOrderNo(orderNo);
        govPublicCarOrderVehicleInfo.setCompanyId(param.getLoginCompanyId());
        govPublicCarOrderVehicleInfo.setVehicleNo(govVehicleBaseInfo.getVehicleNo());
        govPublicCarOrderVehicleInfo.setVehicleLicense(govVehicleBaseInfo.getVehicleLicense());
        govPublicCarOrderVehicleInfo.setVehicleVin(govVehicleBaseInfo.getVehicleVin());
        govPublicCarOrderVehicleInfo.setVehicleType(dispatchVehicleDriverInfo.getVehicleType());
        //查询设备信息
        GovGpsDevice govGpsDevice = govGpsDeviceService.getOne(new LambdaQueryWrapper<GovGpsDevice>().eq(GovGpsDevice::getVehicleNo, govVehicleBaseInfo.getVehicleNo()).eq(GovGpsDevice::getDeleteStatus, GovGpsDeviceEnum.DeviceDeleteStatusEnum.NORMAL.getCode()).last("LIMIT 1"));
        if (ObjectUtil.isNotNull(govGpsDevice)) {
            govPublicCarOrderVehicleInfo.setDeviceId(govGpsDevice.getDeviceNo());
            govPublicCarOrderVehicleInfo.setDeviceType(govGpsDevice.getDeviceType());
        }
        govPublicCarOrderVehicleInfo.setVehicleBrandId(govVehicleBaseInfo.getVehicleBrandId());
        govPublicCarOrderVehicleInfo.setVehicleBrandName(govVehicleBaseInfo.getVehicleBrandName());
        govPublicCarOrderVehicleInfo.setVehicleSeriesId(govVehicleBaseInfo.getVehicleSeriesId());
        govPublicCarOrderVehicleInfo.setVehicleSeriesName(govVehicleBaseInfo.getVehicleSeriesName());
        govPublicCarOrderVehicleInfo.setUseAttribute(govVehicleBaseInfo.getUseAttribute());
        govPublicCarOrderVehicleInfo.setVehicleBelongDeptCode(govVehicleBaseInfo.getVehicleBelongDeptCode());
        govPublicCarOrderVehicleInfo.setVehicleBelongDeptName(govVehicleBaseInfo.getVehicleBelongDeptName());
        govPublicCarOrderVehicleInfo.setVehicleUseDeptCode(govVehicleBaseInfo.getVehicleUseDeptCode());
        govPublicCarOrderVehicleInfo.setVehicleUseDeptName(govVehicleBaseInfo.getVehicleUseDeptName());
        govPublicCarOrderVehicleInfo.setVehicleUseStructCode(govVehicleBaseInfo.getVehicleUseStructCode());
        govPublicCarOrderVehicleInfo.setVehicleUseStructName(govVehicleBaseInfo.getVehicleUseStructName());
        govPublicCarOrderVehicleInfo.setManageCarType(govVehicleBaseInfo.getManageCarType());
        govPublicCarOrderVehicleInfo.setApplyNo(applyNo);
        govPublicCarOrderVehicleInfo.setSupplierCode(govVehicleBaseInfo.getSupplierServiceCode());
        govPublicCarOrderVehicleInfo.setSupplierName(govVehicleBaseInfo.getSupplierServiceName());
        govPublicCarOrderVehicleInfo.setVehicleServiceType(govVehicleBaseInfo.getVehicleServiceType());
        govPublicCarOrderVehicleInfoService.save(govPublicCarOrderVehicleInfo);
        //插入日志
        GovOrderOperationLog operationLog = new GovOrderOperationLog();
        operationLog.setOrderNo(orderNo);
        operationLog.setOperationType(GovPublicCarOrderOperationTypeEnum.CREATE.getCode());
        operationLog.setOperationDescription(GovPublicCarOrderOperationTypeEnum.CREATE.getName());
        operationLog.setOrderStatus(GovPublicCarOrderStatusEnum.PENDING_DEPARTURE.getCode());
        operationLog.setOrderStatusName(GovPublicCarOrderStatusEnum.PENDING_DEPARTURE.getName());
        operationLog.setOperatorCode(param.getLoginUserCode());
        operationLog.setOperatorName(param.getLoginUserName());
        operationLog.setOperatorMobile(param.getLoginUserMobile());
        govOrderOperationLogService.save(operationLog);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelApply(GovCarApplyReqDTO param, String reason) {
        String applyNo = param.getApplyNo();
        //获取申请单
        GovPublicCarApply apply = govPublicCarApplyService.getOne(new LambdaQueryWrapper<GovPublicCarApply>().eq(GovPublicCarApply::getApplyNo, applyNo));
        if (ObjectUtil.isNull(apply)) {
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "申请单不存在");
        }
        //过滤状态，待审核的单子才能进行撤回申请单
        if (!Objects.equals(apply.getApprovalStatus(), ApplyApprovalStatusEnum.PENDING.getCode()) && !Objects.equals(apply.getScheduleStatus(), ApplySchedulingStatusEnum.PENDING.getCode())) {
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "该状态不支持取消申请单");
        }
        //撤回申请单逻辑
        if (Objects.equals(apply.getApprovalStatus(), ApplyApprovalStatusEnum.PENDING.getCode())) {
            //待审核状态，申请单审核状态变为审核撤回
            LambdaUpdateWrapper<GovPublicCarApply> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(GovPublicCarApply::getApplyNo, applyNo).set(GovPublicCarApply::getApprovalStatus, ApplyApprovalStatusEnum.WITHDRAWN.getCode());
            govPublicCarApplyService.update(updateWrapper);
            //插入日志
            GovApplyOperationLog operationLog = new GovApplyOperationLog();
            operationLog.setApplyNo(applyNo);
            operationLog.setOperationType(ApplyOperationTypeEnum.WITHDRAW_APPROVAL.getCode());
            operationLog.setOperationRemark(ApplyOperationTypeEnum.WITHDRAW_APPROVAL.getName());
            operationLog.setOperatorCode(param.getLoginUserCode());
            operationLog.setOperatorName(param.getLoginUserName());
            operationLog.setOperatorMobile(param.getLoginUserMobile());
            govApplyOperationLogService.save(operationLog);
            //撤回审批流
            BpmProcessInstanceCancelReqDTO reqDTO = new BpmProcessInstanceCancelReqDTO();
            reqDTO.setId(apply.getApprovalId());
            reqDTO.setReason(reason);
            reqDTO.setIsBusiness(Boolean.TRUE);
            reqDTO.setLoginUserId(param.getLoginUserId());
            reqDTO.setLoginUserCode(param.getLoginUserCode());
            reqDTO.setLoginUserName(param.getLoginUserName());
            reqDTO.setLoginCompanyId(param.getLoginCompanyId());
            reqDTO.setLoginCompanyName(param.getLoginCompanyName());
            workflowApprovalService.cancelApproval(reqDTO);
        }
        //取消申请单逻辑
        if (Objects.equals(apply.getScheduleStatus(), ApplySchedulingStatusEnum.PENDING.getCode())) {
            //已调度状态，申请单调度状态变为调度取消
            LambdaUpdateWrapper<GovPublicCarApply> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(GovPublicCarApply::getApplyNo, applyNo).set(GovPublicCarApply::getScheduleStatus, ApplySchedulingStatusEnum.CANCELLED.getCode());
            govPublicCarApplyService.update(updateWrapper);
            //插入日志
            GovApplyOperationLog operationLog = new GovApplyOperationLog();
            operationLog.setApplyNo(applyNo);
            operationLog.setOperationType(ApplyOperationTypeEnum.CANCEL_SCHEDULE.getCode());
            operationLog.setOperationRemark(ApplyOperationTypeEnum.CANCEL_SCHEDULE.getName());
            operationLog.setOperatorCode(param.getLoginUserCode());
            operationLog.setOperatorName(param.getLoginUserName());
            operationLog.setOperatorMobile(param.getLoginUserMobile());
            govApplyOperationLogService.save(operationLog);

        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void transferApply(TransferApplyReqDTO param) {
        //查询申请单
        GovPublicCarApply apply = govPublicCarApplyService.getOne(new LambdaQueryWrapper<GovPublicCarApply>().eq(GovPublicCarApply::getApplyNo, param.getApplyNo()));
        if (ObjectUtil.isNull(apply)) {
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "申请单不存在");
        }
        //只有待调度状态支持转派
        if (!Objects.equals(apply.getScheduleStatus(), ApplySchedulingStatusEnum.PENDING.getCode())) {
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "该状态不支持转派");
        }
        //修改申请单上的车辆所属单位
        LambdaUpdateWrapper<GovPublicCarApply> updateWrapper = new LambdaUpdateWrapper<>();
        if (ObjectUtil.equals(apply.getApplyType(), GovPublicCarApplyTypeEnum.PUBLIC_CAR.getCode())) {
            updateWrapper.eq(GovPublicCarApply::getApplyNo, param.getApplyNo())
                    .set(GovPublicCarApply::getVehicleBelongDeptCode, param.getUseVehicleDeptCode())
                    .set(GovPublicCarApply::getVehicleBelongDeptName, param.getUseVehicleDeptName());
            //发生变更需要发送调度消息
            if (!ObjectUtil.equal(apply.getVehicleBelongDeptCode(), param.getUseVehicleDeptCode())) {
                apply.setVehicleBelongDeptCode(param.getUseVehicleDeptCode());
                try {
                    pushDispatchMessage(apply, apply.getApplyNo());
                } catch (Exception e) {
                    log.info("发送消息异常，{}", e.getMessage());
                }
            }
        } else {
            updateWrapper.eq(GovPublicCarApply::getApplyNo, param.getApplyNo())
                    .set(GovPublicCarApply::getSupplierCode, param.getUseVehicleDeptCode())
                    .set(GovPublicCarApply::getSupplierName, param.getUseVehicleDeptName());
            //发生变更需要发送调度消息
            if (!ObjectUtil.equal(apply.getSupplierCode(), param.getUseVehicleDeptCode())) {
                apply.setSupplierCode(param.getUseVehicleDeptCode());
                try {
                    pushDispatchMessage(apply, apply.getApplyNo());
                } catch (Exception e) {
                    log.info("发送消息异常，{}", e.getMessage());
                }
            }
        }
        govPublicCarApplyService.update(updateWrapper);
        //插入操作日志
        GovApplyOperationLog operationLog = new GovApplyOperationLog();
        operationLog.setApplyNo(param.getApplyNo());
        operationLog.setOperationType(ApplyOperationTypeEnum.REASSIGN_SCHEDULE.getCode());
        if (StringUtils.isNotEmpty(param.getRemark())) {
            operationLog.setOperationRemark("转派至" + param.getUseVehicleDeptName() + "，原因" + param.getRemark());
        } else {
            operationLog.setOperationRemark("转派至" + param.getUseVehicleDeptName());
        }
        operationLog.setOperatorCode(param.getLoginUserCode());
        operationLog.setOperatorName(param.getLoginUserName());
        operationLog.setOperatorMobile(param.getLoginUserMobile());
        govApplyOperationLogService.save(operationLog);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void dispatch(DispatchApplyReqDTO param) {
        //获取申请单
        GovPublicCarApply apply = govPublicCarApplyService.getOne(new LambdaQueryWrapper<GovPublicCarApply>().eq(GovPublicCarApply::getApplyNo, param.getApplyNo()));
        if (ObjectUtil.isNull(apply)) {
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "申请单不存在");
        }
        //校验申请单状态
        if (!Objects.equals(apply.getScheduleStatus(), ApplySchedulingStatusEnum.PENDING.getCode())) {
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "该申请单状态不允许调度");
        }
//        Date now = new Date();
//        Calendar calendar = Calendar.getInstance();
//        calendar.setTime(now);
//        calendar.set(Calendar.SECOND, 0); // 将秒数设置为 0
//        calendar.set(Calendar.MILLISECOND, 0); // 将毫秒数设置为 0
//        Date dateTimeNow = calendar.getTime();
//        if(param.getExpectedPickupTime().before(dateTimeNow)){
//            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "开始时间不能小于当前时间");
//        }
//        if(param.getExpectedReturnTime().before(dateTimeNow)){
//            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "结束时间不能小于当前时间");
//        }
//        if(param.getExpectedReturnTime().getTime()<=param.getExpectedPickupTime().getTime()){
//            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "结束时间要晚于当前时间");
//        }
        //修改申请单
        LambdaUpdateWrapper<GovPublicCarApply> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(GovPublicCarApply::getApplyNo, param.getApplyNo())
                .set(GovPublicCarApply::getScheduleStatus, ApplySchedulingStatusEnum.SCHEDULED.getCode())
                .set(GovPublicCarApply::getExpectedPickupTime, param.getExpectedPickupTime())
                .set(GovPublicCarApply::getExpectedReturnTime, param.getExpectedReturnTime())
                .set(GovPublicCarApply::getVehicleBelongDeptCode, param.getUseVehicleDeptCode())
                .set(GovPublicCarApply::getVehicleBelongDeptName, param.getUseVehicleDeptName())
                .set(GovPublicCarApply::getOrderRetentionDateValue, param.getVehicleRetentionDuration())
                .set(GovPublicCarApply::getOrderRetentionDate, DateUtils.addHoursToDate(param.getExpectedReturnTime(), param.getVehicleRetentionDuration()))
                .set(GovPublicCarApply::getDrivingType, param.getDrivingType())
                .set(GovPublicCarApply::getUpdateCode, param.getLoginUserCode())
                .set(GovPublicCarApply::getSupplierCode, param.getSupplierCode())
                .set(GovPublicCarApply::getSupplierName, param.getSupplierName())
                .set(GovPublicCarApply::getUpdateName, param.getLoginUserName());
        govPublicCarApplyService.update(updateWrapper);
        //插入调度日志
        GovApplyOperationLog operationLog = new GovApplyOperationLog();
        operationLog.setApplyNo(param.getApplyNo());
        operationLog.setOperationType(ApplyOperationTypeEnum.SCHEDULE_COMPLETE.getCode());
        operationLog.setOperationRemark(ApplyOperationTypeEnum.SCHEDULE_COMPLETE.getName());
        operationLog.setOperatorCode(param.getLoginUserCode());
        operationLog.setOperatorName(param.getLoginUserName());
        operationLog.setOperatorMobile(param.getLoginUserMobile());
        govApplyOperationLogService.save(operationLog);
        //处理生成子行程
        List<DispatchVehicleDriverInfo> dispatchVehicleDriverInfos = param.getDispatchVehicleDriverInfos();
        //车辆编码
        List<String> vehicleNoList = dispatchVehicleDriverInfos.stream().map(DispatchVehicleDriverInfo::getVehicleNo).collect(Collectors.toList());
        //司机编码
        List<String> userCodeList = dispatchVehicleDriverInfos.stream().map(DispatchVehicleDriverInfo::getUserCode).collect(Collectors.toList());
        //查询车辆详细信息和司机详细信息并转map
        Map<String, GovVehicleBaseInfo> vehicleMap = govVehicleBaseInfoService.list(new LambdaQueryWrapper<GovVehicleBaseInfo>().in(GovVehicleBaseInfo::getVehicleNo, vehicleNoList)).stream().collect(Collectors.toMap(GovVehicleBaseInfo::getVehicleNo, Function.identity()));
        Map<String, GovUser> userInfoMap = govUserService.list(new LambdaQueryWrapper<GovUser>().in(GovUser::getUserCode, userCodeList)).stream().collect(Collectors.toMap(GovUser::getUserCode, Function.identity()));
        //生成子行程
        List<GovPublicCarOrderUserInfo> list = govPublicCarOrderUserInfoService.list(new LambdaQueryWrapper<GovPublicCarOrderUserInfo>().eq(GovPublicCarOrderUserInfo::getApplyNo, apply.getApplyNo()));
        for (DispatchVehicleDriverInfo dispatchVehicleDriverInfo : dispatchVehicleDriverInfos) {
            buildSubOrderInfo(apply, vehicleMap, userInfoMap, dispatchVehicleDriverInfo, param, list);
        }
        //调度完成，发调度完成通知
        try {
            pushMessageForCreatorAndPassenger(apply.getApplyNo());
        } catch (Exception e) {
            log.info("发送消息异常，{}", e.getMessage());
        }
    }

    private void pushMessageForCreatorAndPassenger(String applyNo) {
        //查询获取最新的申请单
        GovPublicCarApply apply = govPublicCarApplyService.getOne(new LambdaQueryWrapper<GovPublicCarApply>().eq(GovPublicCarApply::getApplyNo, applyNo));
        //查询下单人以及主乘车人
        List<GovPublicCarOrderUserInfo> govPublicCarOrderUserInfos = govPublicCarOrderUserInfoService.list(new LambdaQueryWrapper<GovPublicCarOrderUserInfo>()
                .eq(GovPublicCarOrderUserInfo::getApplyNo, applyNo)
                .in(GovPublicCarOrderUserInfo::getUserType, Arrays.asList(GovPublicCarOrderUserTypeEnum.ORDER_PLACER.getCode()
                        , GovPublicCarOrderUserTypeEnum.PRIMARY_RIDER.getCode())));
        //按照userType分组
        Map<Integer, GovPublicCarOrderUserInfo> govPublicCarOrderUserInfoMap = govPublicCarOrderUserInfos.stream().collect(Collectors.toMap(GovPublicCarOrderUserInfo::getUserType, Function.identity(), (key1, key2) -> key1));

        //查询司机
        List<GovPublicCarOrderUserInfo> govPublicCarOrderDriverInfos = govPublicCarOrderUserInfoService.list(new LambdaQueryWrapper<GovPublicCarOrderUserInfo>()
                .eq(GovPublicCarOrderUserInfo::getApplyNo, applyNo)
                .eq(GovPublicCarOrderUserInfo::getUserType, GovPublicCarOrderUserTypeEnum.DRIVER.getCode()));
        //查询车辆信息
        List<GovPublicCarOrderVehicleInfo> govPublicCarOrderVehicleInfos = govPublicCarOrderVehicleInfoService.list(new LambdaQueryWrapper<GovPublicCarOrderVehicleInfo>()
                .eq(GovPublicCarOrderVehicleInfo::getApplyNo, applyNo));
        //按照订单号分组
        Map<String, GovPublicCarOrderVehicleInfo> govPublicCarOrderVehicleInfoMap = govPublicCarOrderVehicleInfos.stream().collect(Collectors.toMap(GovPublicCarOrderVehicleInfo::getOrderNo, Function.identity(), (key1, key2) -> key1));
        Map<String, String> extendParamMap = new HashMap<>();
        extendParamMap.put(PushMessageUtil.MSG_PARAM_APPLY_DRIVER_NAME, govPublicCarOrderDriverInfos.stream()
                .map(item -> item.getUserName() + "(联系电话：" + item.getUserMobile() + "，车牌号：" + govPublicCarOrderVehicleInfoMap.get(item.getOrderNo()).getVehicleLicense() + ")")
                .collect(Collectors.joining("、")));
        extendParamMap.put(PushMessageUtil.MSG_PARAM_APPLY_START_TIME, DateUtils.format(apply.getExpectedPickupTime(), DateUtils.TIME_FORMAT_V3));
        extendParamMap.put(PushMessageUtil.MSG_PARAM_APPLY_END_TIME, DateUtils.format(apply.getExpectedReturnTime(), DateUtils.TIME_FORMAT_V3));
        Map<String, String> exsitParamMap = new HashMap<>();
        for (GovPublicCarOrderUserInfo govPublicCarOrderUserInfo : govPublicCarOrderUserInfos) {
            //去重
            if (!exsitParamMap.containsKey(govPublicCarOrderUserInfo.getUserCode())) {
                MessageTrackingSender.MessageTrackingParam pushParam = new MessageTrackingSender.MessageTrackingParam();
                pushParam.setCompanyId(govPublicCarOrderUserInfo.getCompanyId());
                pushParam.setScene(GovMsgSceneEnum.SCHEDULE_FINISH_NOTICE_ORDER_PERSON_AND_MAIN_PASSENGER);
                pushParam.setReceiverCodeSet(new HashSet<>(Collections.singletonList(govPublicCarOrderUserInfo.getUserCode())));
                extendParamMap.put(PushMessageUtil.MSG_PARAM_APPLY_USER_NAME, govPublicCarOrderUserInfo.getUserName());
                pushParam.setExtendParamMap(extendParamMap);
                pushParam.setMobile(new HashSet<>());
                messageTrackingSender.sendMessageTracking(pushParam);
                exsitParamMap.put(govPublicCarOrderUserInfo.getUserCode(), govPublicCarOrderUserInfo.getUserName());
            }
        }
        exsitParamMap.clear();
        extendParamMap.clear();
        //自驾不发送司机消息
        if (ObjectUtil.equals(apply.getDrivingType(), GovPublicCarDrivingTypeEnum.DRIVER_DRIVE.getCode())) {
            extendParamMap.put(PushMessageUtil.MSG_PARAM_APPLY_START_TIME, DateUtils.format(apply.getExpectedPickupTime(), DateUtils.TIME_FORMAT_V3));
            extendParamMap.put(PushMessageUtil.MSG_PARAM_APPLY_END_TIME, DateUtils.format(apply.getExpectedReturnTime(), DateUtils.TIME_FORMAT_V3));
            extendParamMap.put(PushMessageUtil.MSG_PARAM_APPLY_START_ADDRESS, apply.getEstimatedDepartureShortLocation());
            extendParamMap.put(PushMessageUtil.MSG_PARAM_APPLY_END_ADDRESS, apply.getEstimatedDestinationShortLocation());
            for (GovPublicCarOrderUserInfo govPublicCarOrderDriverInfo : govPublicCarOrderDriverInfos) {
                //去重
                if (!exsitParamMap.containsKey(govPublicCarOrderDriverInfo.getUserCode())) {
                    MessageTrackingSender.MessageTrackingParam pushParam = new MessageTrackingSender.MessageTrackingParam();
                    pushParam.setCompanyId(govPublicCarOrderDriverInfo.getCompanyId());
                    pushParam.setScene(GovMsgSceneEnum.SCHEDULE_FINISH_NOTICE_DRIVER);
                    pushParam.setReceiverCodeSet(new HashSet<>(Collections.singletonList(govPublicCarOrderDriverInfo.getUserCode())));
                    extendParamMap.put(PushMessageUtil.MSG_PARAM_APPLY_PASSENGER_NAME, govPublicCarOrderUserInfoMap.get(GovPublicCarOrderUserTypeEnum.PRIMARY_RIDER.getCode()).getUserName());
                    extendParamMap.put(PushMessageUtil.MSG_PARAM_APPLY_PASSENGER_PHONE, govPublicCarOrderUserInfoMap.get(GovPublicCarOrderUserTypeEnum.PRIMARY_RIDER.getCode()).getUserMobile());
                    extendParamMap.put(PushMessageUtil.MSG_PARAM_APPLY_VEHICLE_LICENSE, govPublicCarOrderVehicleInfoMap.get(govPublicCarOrderDriverInfo.getOrderNo()).getVehicleLicense());
                    pushParam.setExtendParamMap(extendParamMap);
                    pushParam.setMobile(new HashSet<>());
                    messageTrackingSender.sendMessageTracking(pushParam);
                    exsitParamMap.put(govPublicCarOrderDriverInfo.getUserCode(), govPublicCarOrderDriverInfo.getUserName());
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void reDispatch(ReDispatchApplyReqDTO param) {
        //获取申请单
        GovPublicCarApply apply = govPublicCarApplyService.getOne(new LambdaQueryWrapper<GovPublicCarApply>().eq(GovPublicCarApply::getApplyNo, param.getApplyNo()));
        if (ObjectUtil.isNull(apply)) {
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "申请单不存在");
        }
        //校验该申请单下是否有待出发的子订单，若没有，则不允许重新调度
        long result = govPublicCarOrderService.count(new LambdaQueryWrapper<GovPublicCarOrder>()
                .eq(GovPublicCarOrder::getApplyNo, apply.getApplyNo())
                .eq(GovPublicCarOrder::getCompanyId, apply.getCompanyId())
                .eq(GovPublicCarOrder::getOrderStatus, GovPublicCarOrderStatusEnum.PENDING_DEPARTURE.getCode()));
        if (result == 0) {
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "该申请单下没有待出发的行程单，不允许重新调度");
        }
        //修改申请单
        LambdaUpdateWrapper<GovPublicCarApply> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(GovPublicCarApply::getApplyNo, param.getApplyNo())
                .set(GovPublicCarApply::getDrivingType, param.getDrivingType())
                .set(GovPublicCarApply::getUpdateCode, param.getLoginUserCode())
                .set(GovPublicCarApply::getUpdateName, param.getLoginUserName());
        govPublicCarApplyService.update(updateWrapper);
        //插入调度日志
        GovApplyOperationLog operationLog = new GovApplyOperationLog();
        operationLog.setApplyNo(param.getApplyNo());
        operationLog.setOperationType(ApplyOperationTypeEnum.RESCHEDULE.getCode());
        operationLog.setOperationRemark(ApplyOperationTypeEnum.RESCHEDULE.getName());
        operationLog.setOperatorCode(param.getLoginUserCode());
        operationLog.setOperatorName(param.getLoginUserName());
        operationLog.setOperatorMobile(param.getLoginUserMobile());
        govApplyOperationLogService.save(operationLog);
        //处理更新子行程
        List<ReDispatchVehicleDriverInfo> dispatchVehicleDriverInfos = param.getReDispatchVehicleDriverInfos();
        //车辆编码
        List<String> vehicleNoList = dispatchVehicleDriverInfos.stream().map(DispatchVehicleDriverInfo::getVehicleNo).collect(Collectors.toList());
        //司机编码
        List<String> userCodeList = dispatchVehicleDriverInfos.stream().map(DispatchVehicleDriverInfo::getUserCode).collect(Collectors.toList());
        //查询车辆详细信息和司机详细信息并转map
        Map<String, GovVehicleBaseInfo> vehicleMap = govVehicleBaseInfoService.list(new LambdaQueryWrapper<GovVehicleBaseInfo>().in(GovVehicleBaseInfo::getVehicleNo, vehicleNoList)).stream().collect(Collectors.toMap(GovVehicleBaseInfo::getVehicleNo, Function.identity()));
        Map<String, GovUser> userInfoMap = govUserService.list(new LambdaQueryWrapper<GovUser>().in(GovUser::getUserCode, userCodeList)).stream().collect(Collectors.toMap(GovUser::getUserCode, Function.identity()));
        //更新子行程信息
        Boolean pushFlag = Boolean.FALSE;
        for (ReDispatchVehicleDriverInfo reDispatchVehicleDriverInfo : dispatchVehicleDriverInfos) {
            //返回为true，则推送消息
            if (updateSubOrderInfo(apply, vehicleMap, userInfoMap, reDispatchVehicleDriverInfo, param, pushFlag)) {
                pushFlag = Boolean.TRUE;
            }
        }
        //通知下单人&主乘车人重新调度消息
        try {
            if (pushFlag) {
                pushMessageForCreator(apply);
            }
        } catch (Exception e) {
            log.info("通知下单人&主乘车人重新调度消息失败{}", e.getMessage());
        }
    }

    private void pushMessageForCreator(GovPublicCarApply apply) {
        //查询获取最新的申请单
        //查询下单人以及主乘车人
        List<GovPublicCarOrderUserInfo> govPublicCarOrderUserInfos = govPublicCarOrderUserInfoService.list(new LambdaQueryWrapper<GovPublicCarOrderUserInfo>()
                .eq(GovPublicCarOrderUserInfo::getApplyNo, apply.getApplyNo())
                .in(GovPublicCarOrderUserInfo::getUserType, Arrays.asList(GovPublicCarOrderUserTypeEnum.ORDER_PLACER.getCode()
                        , GovPublicCarOrderUserTypeEnum.PRIMARY_RIDER.getCode())));
        //查询司机
        List<GovPublicCarOrderUserInfo> govPublicCarOrderDriverInfos = govPublicCarOrderUserInfoService.list(new LambdaQueryWrapper<GovPublicCarOrderUserInfo>()
                .eq(GovPublicCarOrderUserInfo::getApplyNo, apply.getApplyNo())
                .eq(GovPublicCarOrderUserInfo::getUserType, GovPublicCarOrderUserTypeEnum.DRIVER.getCode()));
        //查询车辆信息
        List<GovPublicCarOrderVehicleInfo> govPublicCarOrderVehicleInfos = govPublicCarOrderVehicleInfoService.list(new LambdaQueryWrapper<GovPublicCarOrderVehicleInfo>()
                .eq(GovPublicCarOrderVehicleInfo::getApplyNo, apply.getApplyNo()));
        //按照订单号分组
        Map<String, GovPublicCarOrderVehicleInfo> govPublicCarOrderVehicleInfoMap = govPublicCarOrderVehicleInfos.stream().collect(Collectors.toMap(GovPublicCarOrderVehicleInfo::getOrderNo, Function.identity(), (key1, key2) -> key1));
        Map<String, String> extendParamMap = new HashMap<>();
        extendParamMap.put(PushMessageUtil.MSG_PARAM_APPLY_DRIVER_NAME, govPublicCarOrderDriverInfos.stream()
                .map(item -> item.getUserName() + "(联系电话：" + item.getUserMobile() + "，车牌号：" + govPublicCarOrderVehicleInfoMap.get(item.getOrderNo()).getVehicleLicense() + ")")
                .collect(Collectors.joining("、")));
        extendParamMap.put(PushMessageUtil.MSG_PARAM_APPLY_START_TIME, DateUtils.format(apply.getExpectedPickupTime(), DateUtils.TIME_FORMAT_V3));
        extendParamMap.put(PushMessageUtil.MSG_PARAM_APPLY_END_TIME, DateUtils.format(apply.getExpectedReturnTime(), DateUtils.TIME_FORMAT_V3));
        Map<String, String> exsitParamMap = new HashMap<>();
        for (GovPublicCarOrderUserInfo govPublicCarOrderUserInfo : govPublicCarOrderUserInfos) {
            //去重
            if (!exsitParamMap.containsKey(govPublicCarOrderUserInfo.getUserCode())) {
                MessageTrackingSender.MessageTrackingParam pushParam = new MessageTrackingSender.MessageTrackingParam();
                pushParam.setCompanyId(govPublicCarOrderUserInfo.getCompanyId());
                pushParam.setScene(GovMsgSceneEnum.RE_SCHEDULE_NOTICE_ORDER_PERSON_AND_MAIN_PASSENGER);
                pushParam.setReceiverCodeSet(new HashSet<>(Collections.singletonList(govPublicCarOrderUserInfo.getUserCode())));
                extendParamMap.put(PushMessageUtil.MSG_PARAM_APPLY_USER_NAME, govPublicCarOrderUserInfo.getUserName());
                pushParam.setExtendParamMap(extendParamMap);
                pushParam.setMobile(new HashSet<>());
                messageTrackingSender.sendMessageTracking(pushParam);
                exsitParamMap.put(govPublicCarOrderUserInfo.getUserCode(), govPublicCarOrderUserInfo.getUserName());
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void approveResult(BpmMessageSendApproveResultDTO bpmMessageSendApproveResultDTO) {
        String applyNo = bpmMessageSendApproveResultDTO.getBusinessNo();
        //获取申请单
        GovPublicCarApply apply = govPublicCarApplyService.getOne(new LambdaQueryWrapper<GovPublicCarApply>().eq(GovPublicCarApply::getApplyNo, applyNo));
        if (apply == null) {
            log.info("公务用车订单接收审批流消息，找不到相关申请单：{}", applyNo);
            return;
        }
        //不是待审核状态的单子直接丢弃
        if (!ApplyApprovalStatusEnum.PENDING.getCode().equals(apply.getApprovalStatus())) {
            log.info("公务用车订单接收审批流消息，申请单状态不是待审核状态：{}", applyNo);
            return;
        }
        BpmProcessInstanceResultEnum bpmProcessInstanceResultEnum = BpmProcessInstanceResultEnum.getEnum(bpmMessageSendApproveResultDTO.getResult());
        Integer scheduleType = apply.getScheduleType();
        //通过userCode查询用户的手机号
        LambdaQueryWrapper<GovUser> userLambdaQueryWrapper = new LambdaQueryWrapper<>();
        userLambdaQueryWrapper.eq(GovUser::getUserCode, bpmMessageSendApproveResultDTO.getApproverCode()).last("limit 1");
        GovUser approveUser = govUserService.getOne(userLambdaQueryWrapper);
        //申请单日志
        GovApplyOperationLog operationLog = new GovApplyOperationLog();
        operationLog.setApplyNo(apply.getApplyNo());
        operationLog.setOperatorCode(approveUser.getUserCode());
        operationLog.setOperatorName(approveUser.getUserName());
        operationLog.setOperatorMobile(approveUser.getMobile());
        operationLog.setCreateTime(bpmMessageSendApproveResultDTO.getApproverTime());
        //订单更新
        GovPublicCarApply govPublicCarApply = new GovPublicCarApply();
        govPublicCarApply.setApplyId(apply.getApplyId());
        govPublicCarApply.setUpdateTime(bpmMessageSendApproveResultDTO.getApproverTime());
        govPublicCarApply.setApprovalCompletedTime(bpmMessageSendApproveResultDTO.getApproverTime());
        switch (bpmProcessInstanceResultEnum) {
            //审批通过
            case APPROVE:
                //修改订单状态为通过
                govPublicCarApply.setApprovalStatus(ApplyApprovalStatusEnum.APPROVED.getCode());
                //无需调度，则直接生成子订单并且为待出发
                if (ObjectUtil.equals(scheduleType, GovPublicCarScheduleTypeEnum.NO_SCHEDULE.getCode())) {
                    //查询之前保存的临时表信息
                    List<GovPublicApplyVehicleDriverInfo> govPublicApplyVehicleDriverInfos = govPublicApplyVehicleDriverInfoService.list(new LambdaQueryWrapper<GovPublicApplyVehicleDriverInfo>().eq(GovPublicApplyVehicleDriverInfo::getApplyNo, applyNo));
                    //车辆编码
                    List<String> vehicleNoList = govPublicApplyVehicleDriverInfos.stream().map(GovPublicApplyVehicleDriverInfo::getVehicleNo).collect(Collectors.toList());
                    //司机编码
                    List<String> userCodeList = govPublicApplyVehicleDriverInfos.stream().map(GovPublicApplyVehicleDriverInfo::getDriverCode).collect(Collectors.toList());
                    //查询车辆详细信息和司机详细信息并转map
                    Map<String, GovVehicleBaseInfo> vehicleMap = govVehicleBaseInfoService.list(new LambdaQueryWrapper<GovVehicleBaseInfo>().in(GovVehicleBaseInfo::getVehicleNo, vehicleNoList)).stream().collect(Collectors.toMap(GovVehicleBaseInfo::getVehicleNo, Function.identity()));
                    Map<String, GovUser> userInfoMap = govUserService.list(new LambdaQueryWrapper<GovUser>().in(GovUser::getUserCode, userCodeList)).stream().collect(Collectors.toMap(GovUser::getUserCode, Function.identity()));
                    //生成子行程
                    List<GovPublicCarOrderUserInfo> list = govPublicCarOrderUserInfoService.list(new LambdaQueryWrapper<GovPublicCarOrderUserInfo>().eq(GovPublicCarOrderUserInfo::getApplyNo, apply.getApplyNo()));
                    for (GovPublicApplyVehicleDriverInfo dispatchVehicleDriverInfo : govPublicApplyVehicleDriverInfos) {
                        approveGenerateOrder(bpmMessageSendApproveResultDTO, apply, vehicleMap, userInfoMap, dispatchVehicleDriverInfo, approveUser, list);
                    }
                    govPublicCarApply.setScheduleStatus(ApplySchedulingStatusEnum.NOT_REQUIRED.getCode());
                    //调度完成，发调度完成通知
                    try {
                        pushMessageForCreatorAndPassenger(apply.getApplyNo());
                    } catch (Exception e) {
                        log.info("发送消息异常，{}", e.getMessage());
                    }
                } else {
                    govPublicCarApply.setScheduleStatus(ApplySchedulingStatusEnum.PENDING.getCode());
                    //给调度员发送待调度信息
                    try {
                        pushDispatchMessage(apply, applyNo);
                    } catch (Exception e) {
                        log.info("发送消息异常，{}", e.getMessage());
                    }
                }
                //插入申请单日志，审核通过
                //插入审核日志
                operationLog.setOperationType(ApplyOperationTypeEnum.APPROVE.getCode());
                operationLog.setOperationRemark(ApplyOperationTypeEnum.APPROVE.getName());
                break;
            //驳回
            case REJECT:
            case BACK:
                //插入审核日志
                operationLog.setOperationType(ApplyOperationTypeEnum.REJECT_APPROVAL.getCode());
                operationLog.setOperationRemark(ApplyOperationTypeEnum.REJECT_APPROVAL.getName());
                govPublicCarApply.setApprovalStatus(ApplyApprovalStatusEnum.REJECTED.getCode());
                break;
            case CANCEL:
                operationLog.setOperationType(ApplyOperationTypeEnum.WITHDRAW_APPROVAL.getCode());
                operationLog.setOperationRemark(ApplyOperationTypeEnum.WITHDRAW_APPROVAL.getName());
                govPublicCarApply.setApprovalStatus(ApplyApprovalStatusEnum.WITHDRAWN.getCode());
                break;
            default:
                log.info("公务用车订单接收审批流消息，审批结果未知：{}", bpmMessageSendApproveResultDTO);
        }
        govApplyOperationLogService.save(operationLog);
        //更新订单表
        govPublicCarApplyService.updateById(govPublicCarApply);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void autoCloseApply(String applyNo) {
        //查询申请单号
        GovPublicCarApply apply = govPublicCarApplyService.getOne(new LambdaQueryWrapper<GovPublicCarApply>().eq(GovPublicCarApply::getApplyNo, applyNo));
        GovCarApplyReqDTO param = new GovCarApplyReqDTO();
        param.setApplyNo(applyNo);
        param.setLoginUserName("system");
        param.setLoginUserCode("0");
        param.setLoginUserId(0);
        param.setLoginUserMobile("");
        param.setLoginUserBelongDeptCode("");
        param.setLoginUserBelongDeptName("");
        param.setLoginUserBelongStructName("");
        param.setLoginUserBelongStructCode("");
        param.setLoginCompanyId(apply.getCompanyId());
        param.setLoginCompanyName(apply.getCompanyName());
        cancelApply(param, "超时自动取消");
    }

    @Override
    public void generateUsers() {
        //获取所有订单表
        List<GovPublicCarOrder> orderList = govPublicCarOrderService.list(new LambdaQueryWrapper<GovPublicCarOrder>().eq(GovPublicCarOrder::getOrderType, GovPublicCarOrderTypeEnum.PUBLIC_USE_CAR_TYPE.getCode()));
        if (CollectionUtils.isNotEmpty(orderList)) {
            //根据订单的申请单号查询下单人&&乘车人
            for (GovPublicCarOrder govPublicCarOrder : orderList) {
                long count = govPublicCarOrderUserInfoService.count(new LambdaQueryWrapper<GovPublicCarOrderUserInfo>()
                        .eq(GovPublicCarOrderUserInfo::getApplyNo, govPublicCarOrder.getApplyNo())
                        .in(GovPublicCarOrderUserInfo::getUserType, GovPublicCarOrderUserTypeEnum.ORDER_PLACER.getCode()
                                , GovPublicCarOrderUserTypeEnum.PRIMARY_RIDER.getCode(), GovPublicCarOrderUserTypeEnum.SECONDARY_RIDER.getCode())
                        .eq(GovPublicCarOrderUserInfo::getOrderNo, govPublicCarOrder.getOrderNo()));
                if (count == 0) {
                    List<GovPublicCarOrderUserInfo> userInfos = govPublicCarOrderUserInfoService.list(new LambdaQueryWrapper<GovPublicCarOrderUserInfo>()
                            .eq(GovPublicCarOrderUserInfo::getApplyNo, govPublicCarOrder.getApplyNo())
                            .in(GovPublicCarOrderUserInfo::getUserType, GovPublicCarOrderUserTypeEnum.ORDER_PLACER.getCode()
                                    , GovPublicCarOrderUserTypeEnum.PRIMARY_RIDER.getCode(), GovPublicCarOrderUserTypeEnum.SECONDARY_RIDER.getCode())
                            .eq(GovPublicCarOrderUserInfo::getOrderNo, ""));
                    if (CollectionUtils.isNotEmpty(userInfos)) {
                        for (GovPublicCarOrderUserInfo userInfo : userInfos) {
                            userInfo.setId(null);
                            userInfo.setOrderNo(govPublicCarOrder.getOrderNo());
                        }
                        govPublicCarOrderUserInfoService.saveBatch(userInfos);
                    }
                }
            }
        }

    }

    private void pushDispatchMessage(GovPublicCarApply apply, String applyNo) {
        List<String> userCodeLists = new ArrayList<>();
        if (ObjectUtil.equal(GovPublicCarApplyTypeEnum.PUBLIC_CAR.getCode(), apply.getApplyType())) {
            userCodeLists = govUserRoleRelationMapper.selectByDeptCode(
                    SpecialRoleEnum.DISPATCHER_STAFF.getRoleCode()
                    , apply.getCompanyId()
                    , apply.getVehicleBelongDeptCode()
                    , ""
                    , 1);
        } else {
            //判断是否是分时租赁，日租走原有逻辑，分时走新逻辑
            if (Objects.equals(apply.getRentType(), GovPublicCarRentTypeEnum.DAILY.getCode())) {
                userCodeLists = govUserRoleRelationMapper.selectByDeptCode(
                        SpecialRoleEnum.RENT_DISPATCHER.getRoleCode()
                        , apply.getCompanyId()
                        , ""
                        , apply.getSupplierCode()
                        , 1);
            } else {
                //查询用车人部门
                List<String> applyNoList = new ArrayList<>();
                applyNoList.add(applyNo);
                Map<String, List<GovPublicCarOrderUserInfo>> applyNoUserInfoMap = userInfoService.getApplyNoUserInfoMap(applyNoList);
                if (applyNoUserInfoMap != null) {
                    List<GovPublicCarOrderUserInfo> govPublicCarOrderUserInfos = applyNoUserInfoMap.get(applyNo);
                    //用车人信息
                    GovPublicCarOrderUserInfo passengerOrderUserInfo = govPublicCarOrderUserInfos.stream()
                            .filter(user -> Objects.equals(user.getUserType(),
                                    GovPublicCarOrderUserTypeEnum.PRIMARY_RIDER.getCode()))
                            .findFirst()
                            .orElse(null);
                    //通过用车人部门查询，车辆管理单位
                    if (passengerOrderUserInfo != null) {
                        //车辆管理单位
                        String managedCarStructCode = govStructService.getManagedCarStructCode(passengerOrderUserInfo.getDeptCode());
                        if (StringUtils.isNotBlank(managedCarStructCode)) {
                            userCodeLists = govUserRoleRelationMapper.selectByDeptCode(
                                    SpecialRoleEnum.DISPATCHER_STAFF.getRoleCode()
                                    , apply.getCompanyId()
                                    , managedCarStructCode
                                    , ""
                                    , 1);
                        }
                    }
                }
                if (CollectionUtils.isEmpty(userCodeLists)) {
                    log.warn("调度人员信息不存在，无法通知调度");
                }
            }

        }
        //查询下单人
        GovPublicCarOrderUserInfo creator = govPublicCarOrderUserInfoService.getOne(new LambdaQueryWrapper<GovPublicCarOrderUserInfo>().eq(GovPublicCarOrderUserInfo::getApplyNo, applyNo).eq(GovPublicCarOrderUserInfo::getUserType, GovPublicCarOrderUserTypeEnum.ORDER_PLACER.getCode()).last("limit 1"));
        Set<String> userCodeList = new HashSet<>(userCodeLists);
        if (CollectionUtils.isNotEmpty(userCodeList)) {
            log.info("调度员信息{}", JSON.toJSONString(userCodeList));
            try {
                MessageTrackingSender.MessageTrackingParam pushParam = new MessageTrackingSender.MessageTrackingParam();
                pushParam.setCompanyId(apply.getCompanyId());
                pushParam.setScene(GovMsgSceneEnum.WAIT_SCHEDULE_NOTICE_SCHEDULER);
                pushParam.setReceiverCodeSet(userCodeList);
                Map<String, String> extendParamMap = new HashMap<>();
                extendParamMap.put(PushMessageUtil.MSG_PARAM_APPLY_START_TIME, DateUtils.format(apply.getExpectedPickupTime(), DateUtils.TIME_FORMAT_V3));
                extendParamMap.put(PushMessageUtil.MSG_PARAM_APPLY_END_TIME, DateUtils.format(apply.getExpectedReturnTime(), DateUtils.TIME_FORMAT_V3));
                extendParamMap.put(PushMessageUtil.MSG_PARAM_APPLY_START_ADDRESS, apply.getEstimatedDepartureShortLocation());
                extendParamMap.put(PushMessageUtil.MSG_PARAM_APPLY_END_ADDRESS, apply.getEstimatedDestinationShortLocation());
                if (ObjectUtil.isNotNull(creator)) {
                    extendParamMap.put(PushMessageUtil.MSG_PARAM_APPLY_CREATOR_NAME, creator.getUserName());
                } else {
                    extendParamMap.put(PushMessageUtil.MSG_PARAM_APPLY_CREATOR_NAME, "");
                }
                pushParam.setExtendParamMap(extendParamMap);
                pushParam.setMobile(new HashSet<>());
                messageTrackingSender.sendMessageTracking(pushParam);
            } catch (Exception e) {
                log.info("发送消息异常{}", e.getMessage());
            }
        }
    }

    private void approveGenerateOrder(BpmMessageSendApproveResultDTO bpmMessageSendApproveResultDTO, GovPublicCarApply apply, Map<String, GovVehicleBaseInfo> vehicleMap, Map<String, GovUser> userInfoMap, GovPublicApplyVehicleDriverInfo dispatchVehicleDriverInfo, GovUser approveUser, List<GovPublicCarOrderUserInfo> list) {
        String applyNo = apply.getApplyNo();
        String orderNo;
        if (ObjectUtil.equal(apply.getApplyType(), GovPublicCarApplyTypeEnum.PUBLIC_CAR.getCode())) {
            orderNo = sequenceGenerator.generate(new Date(), PUBLIC_GOV_CAR_ORDER_PREFIX);
        } else {
            orderNo = sequenceGenerator.generate(new Date(), PUBLIC_SOC_GOV_CAR_ORDER_PREFIX);
        }
        GovVehicleBaseInfo govVehicleBaseInfo = vehicleMap.get(dispatchVehicleDriverInfo.getVehicleNo());
        GovUser govUser = userInfoMap.get(dispatchVehicleDriverInfo.getDriverCode());
        //生成订单并插入数据库
        GovPublicCarOrder order = new GovPublicCarOrder();
        order.setOrderNo(orderNo);
        order.setOrderType(GovPublicCarOrderTypeEnum.PUBLIC_USE_CAR_TYPE.getCode());
        order.setUseType(apply.getApplyType());
        order.setVehicleNo(govVehicleBaseInfo.getVehicleNo());
        order.setVehicleLicense(govVehicleBaseInfo.getVehicleLicense());
        order.setVehicleVin(govVehicleBaseInfo.getVehicleVin());
        order.setOrderStatus(GovPublicCarOrderStatusEnum.PENDING_DEPARTURE.getCode());
        order.setVerifyStatus(GovPublicCarVerifyStatusEnum.NO_VERIFICATION_REQUIRED.getCode());
        order.setRelatedFeesStatus(GovPublicCarRelatedFeesStatusEnum.UNASSOCIATED.getCode());
        order.setCompanyId(apply.getCompanyId());
        order.setCompanyName(apply.getCompanyName());
        order.setApplyNo(applyNo);
        order.setUpdateCode(bpmMessageSendApproveResultDTO.getApproverCode());
        order.setUpdateName(bpmMessageSendApproveResultDTO.getApproverName());
        order.setCreateTime(bpmMessageSendApproveResultDTO.getApproverTime());
        order.setUpdateTime(bpmMessageSendApproveResultDTO.getApproverTime());
        //查询主用车人信息
        GovPublicCarOrderUserInfo primaryUserInfo = govPublicCarOrderUserInfoService.getOne(new LambdaQueryWrapper<GovPublicCarOrderUserInfo>().eq(GovPublicCarOrderUserInfo::getApplyNo, applyNo).eq(GovPublicCarOrderUserInfo::getUserType, GovPublicCarOrderUserTypeEnum.PRIMARY_RIDER.getCode()).last("limit 1"));
        if (!primaryUserInfo.getDeptCode().equals(apply.getVehicleBelongDeptCode())) {
            order.setCrossType(GovPublicCarCrossTypeEnum.CROSS.getCode());
        } else {
            order.setCrossType(GovPublicCarCrossTypeEnum.NO_CROSS.getCode());
        }
        if (ObjectUtil.equals(apply.getApplyType(), GovPublicCarApplyTypeEnum.SOCIAL_RENT.getCode())) {
            if (Objects.equals(apply.getRentType(), GovPublicCarRentTypeEnum.DAILY.getCode())) {
                LambdaQueryWrapper<GovBillingConfiguration> queryWrapper = new LambdaQueryWrapper<GovBillingConfiguration>()
                        .eq(GovBillingConfiguration::getCompanyId, apply.getCompanyId())
                        .eq(GovBillingConfiguration::getSupplierCode, govVehicleBaseInfo.getSupplierServiceCode())
                        .eq(GovBillingConfiguration::getVehicleType, govVehicleBaseInfo.getVehicleType())
                        .last("limit 1");
                GovBillingConfiguration govBillingConfiguration = govBillingConfigurationService.getOne(queryWrapper);
                if (ObjectUtil.isNotNull(govBillingConfiguration)) {
                    order.setBillingConfigurationVersionId(govBillingConfiguration.getBillingConfigurationVersionId());
                }
            } else if (Objects.equals(apply.getRentType(), GovPublicCarRentTypeEnum.TIME_SHARE.getCode())) {
                //分时
                TimeShareConfigQueryReqDTO req = new TimeShareConfigQueryReqDTO();
                req.setCompanyId(govVehicleBaseInfo.getCompanyId());
                req.setSupplierCode(govVehicleBaseInfo.getSupplierServiceCode());
                req.setVehicleType(govVehicleBaseInfo.getVehicleType());
                req.setVehicleBrandId(govVehicleBaseInfo.getVehicleBrandId());
                req.setVehicleSeriesId(govVehicleBaseInfo.getVehicleSeriesId());
                TimeShareConfigRespDTO detail = govTimeShareConfigService.detail(req);
                if (Objects.nonNull(detail) && StringUtils.isNotBlank(detail.getSnapshotCode())) {
                    order.setTimeShareSnapshotCode(detail.getSnapshotCode());
                }

                //计价时间配置
                List<GovCompanyConfigItemValueDTO> configItemValueDTOS = govPublicBusinessConfigService.selectByDeptCodeAndBusinessCodeAndItemCode(govVehicleBaseInfo.getCompanyId(), primaryUserInfo.getDeptCode(),
                        PublicConfigEnum.BusinessConfigEnum.HOUR_USE_CAR_DURATION_FILED.getCode(), PublicConfigEnum.BusinessConfigItemEnum.USE_CAR_DURATION_FILED_ITEM.getCode());
                GovCompanyConfigItemValueDTO configItemValueDTO = configItemValueDTOS.get(0);
                // 计价时间配置: 实际开始/结束时间
                Integer timeShareCalculateConfig = Integer.parseInt(configItemValueDTO.getConfigValue());
                order.setTimeShareCalculateConfig(timeShareCalculateConfig);
            }
        }
        //插入订单
        govPublicCarOrderService.save(order);
        //根据申请单号查询所有人员信息
        if (CollectionUtils.isNotEmpty(list)) {
            for (GovPublicCarOrderUserInfo govPublicCarOrderUserInfo : list) {
                govPublicCarOrderUserInfo.setId(null);
                govPublicCarOrderUserInfo.setOrderNo(orderNo);
            }
            govPublicCarOrderUserInfoService.saveBatch(list);
        }
        //插入司机信息
        GovPublicCarOrderUserInfo driverInfo = new GovPublicCarOrderUserInfo();
        driverInfo.setUserType(GovPublicCarOrderUserTypeEnum.DRIVER.getCode());
        driverInfo.setUserCode(govUser.getUserCode());
        driverInfo.setUserName(govUser.getUserName());
        driverInfo.setUserMobile(govUser.getMobile());
        driverInfo.setStructCode(govUser.getBelongStructCode());
        driverInfo.setDeptCode(govUser.getBelongDeptCode());
        Map<String, GovStruct> govStructMap = govStructService.list(new LambdaQueryWrapper<GovStruct>().in(GovStruct::getStructCode, Arrays.asList(govUser.getBelongStructCode(), govUser.getBelongDeptCode()))).stream().collect(Collectors.toMap(GovStruct::getStructCode, Function.identity()));
        GovStruct struct = govStructMap.get(govUser.getBelongStructCode());
        if (Objects.nonNull(struct)) {
            driverInfo.setStructName(struct.getStructName());
        }
        GovStruct dept = govStructMap.get(govUser.getBelongDeptCode());
        if (Objects.nonNull(dept)) {
            driverInfo.setDeptName(dept.getStructName());
        }
        driverInfo.setCompanyId(apply.getCompanyId());
        driverInfo.setCompanyName(apply.getCompanyName());
        driverInfo.setApplyNo(applyNo);
        driverInfo.setOrderNo(orderNo);
        govPublicCarOrderUserInfoService.save(driverInfo);
        //插入车辆信息
        GovPublicCarOrderVehicleInfo govPublicCarOrderVehicleInfo = new GovPublicCarOrderVehicleInfo();
        govPublicCarOrderVehicleInfo.setOrderNo(orderNo);
        govPublicCarOrderVehicleInfo.setCompanyId(apply.getCompanyId());
        govPublicCarOrderVehicleInfo.setVehicleNo(govVehicleBaseInfo.getVehicleNo());
        govPublicCarOrderVehicleInfo.setVehicleLicense(govVehicleBaseInfo.getVehicleLicense());
        govPublicCarOrderVehicleInfo.setVehicleVin(govVehicleBaseInfo.getVehicleVin());
        govPublicCarOrderVehicleInfo.setVehicleType(dispatchVehicleDriverInfo.getVehicleType());
        //查询设备信息
        GovGpsDevice govGpsDevice = govGpsDeviceService.getOne(new LambdaQueryWrapper<GovGpsDevice>().eq(GovGpsDevice::getVehicleNo, govVehicleBaseInfo.getVehicleNo()).eq(GovGpsDevice::getDeleteStatus, GovGpsDeviceEnum.DeviceDeleteStatusEnum.NORMAL.getCode()).last("LIMIT 1"));
        if (ObjectUtil.isNotNull(govGpsDevice)) {
            govPublicCarOrderVehicleInfo.setDeviceId(govGpsDevice.getDeviceNo());
            govPublicCarOrderVehicleInfo.setDeviceType(govGpsDevice.getDeviceType());
        }
        govPublicCarOrderVehicleInfo.setVehicleBrandId(govVehicleBaseInfo.getVehicleBrandId());
        govPublicCarOrderVehicleInfo.setVehicleBrandName(govVehicleBaseInfo.getVehicleBrandName());
        govPublicCarOrderVehicleInfo.setVehicleSeriesId(govVehicleBaseInfo.getVehicleSeriesId());
        govPublicCarOrderVehicleInfo.setVehicleSeriesName(govVehicleBaseInfo.getVehicleSeriesName());
        govPublicCarOrderVehicleInfo.setUseAttribute(govVehicleBaseInfo.getUseAttribute());
        govPublicCarOrderVehicleInfo.setVehicleBelongDeptCode(govVehicleBaseInfo.getVehicleBelongDeptCode());
        govPublicCarOrderVehicleInfo.setVehicleBelongDeptName(govVehicleBaseInfo.getVehicleBelongDeptName());
        govPublicCarOrderVehicleInfo.setVehicleUseDeptCode(govVehicleBaseInfo.getVehicleUseDeptCode());
        govPublicCarOrderVehicleInfo.setVehicleUseDeptName(govVehicleBaseInfo.getVehicleUseDeptName());
        govPublicCarOrderVehicleInfo.setVehicleUseStructCode(govVehicleBaseInfo.getVehicleUseStructCode());
        govPublicCarOrderVehicleInfo.setVehicleUseStructName(govVehicleBaseInfo.getVehicleUseStructName());
        govPublicCarOrderVehicleInfo.setApplyNo(applyNo);
        govPublicCarOrderVehicleInfo.setSupplierCode(govVehicleBaseInfo.getSupplierServiceCode());
        govPublicCarOrderVehicleInfo.setSupplierName(govVehicleBaseInfo.getSupplierServiceName());
        govPublicCarOrderVehicleInfo.setManageCarType(govVehicleBaseInfo.getManageCarType());
        govPublicCarOrderVehicleInfo.setVehicleServiceType(govVehicleBaseInfo.getVehicleServiceType());
        govPublicCarOrderVehicleInfoService.save(govPublicCarOrderVehicleInfo);
        //插入日志
        GovOrderOperationLog operationLog = new GovOrderOperationLog();
        operationLog.setOrderNo(orderNo);
        operationLog.setOperationTime(bpmMessageSendApproveResultDTO.getApproverTime());
        operationLog.setOperationType(GovPublicCarOrderOperationTypeEnum.CREATE.getCode());
        operationLog.setOperationDescription(GovPublicCarOrderOperationTypeEnum.CREATE.getName());
        operationLog.setOrderStatus(GovPublicCarOrderStatusEnum.PENDING_DEPARTURE.getCode());
        operationLog.setOrderStatusName(GovPublicCarOrderStatusEnum.PENDING_DEPARTURE.getName());
        operationLog.setOperatorCode(bpmMessageSendApproveResultDTO.getApproverCode());
        if (ObjectUtil.isNotNull(approveUser)) {
            operationLog.setOperatorMobile(approveUser.getMobile());
        }
        operationLog.setOperatorName(bpmMessageSendApproveResultDTO.getApproverName());
        govOrderOperationLogService.save(operationLog);

    }

    private Boolean updateSubOrderInfo(GovPublicCarApply apply, Map<String, GovVehicleBaseInfo> vehicleMap, Map<String, GovUser> userInfoMap, ReDispatchVehicleDriverInfo reDispatchVehicleDriverInfo, ReDispatchApplyReqDTO param, Boolean pushFlag) {
        GovVehicleBaseInfo govVehicleBaseInfo = vehicleMap.get(reDispatchVehicleDriverInfo.getVehicleNo());
        GovUser govUser = userInfoMap.get(reDispatchVehicleDriverInfo.getUserCode());
        //查询现有子行程
        GovPublicCarOrder govPublicCarOrder = govPublicCarOrderService.getOne(new LambdaQueryWrapper<GovPublicCarOrder>().eq(GovPublicCarOrder::getOrderNo, reDispatchVehicleDriverInfo.getOrderNo()));
        if (!govPublicCarOrder.getOrderStatus().equals(GovPublicCarOrderStatusEnum.PENDING_DEPARTURE.getCode())) {
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "订单状态发生变更，请退出页面重新进入");
        }
        if (ObjectUtil.equal(apply.getApplyType(), GovPublicCarApplyTypeEnum.SOCIAL_RENT.getCode())
                && ObjectUtil.equal(apply.getRentType(), GovPublicCarRentTypeEnum.TIME_SHARE.getCode())
                && StringUtils.isEmpty(govVehicleBaseInfo.getVehicleBelongDeptCode())) {
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "所选车辆信息未维护分时租赁单位");
        }
        //查看订单类型是否为驾驶员
        //根据订单号，获取旧的车辆信息
        GovPublicCarOrderVehicleInfo oldVehicleInfo = govPublicCarOrderVehicleInfoService.getOne(new LambdaQueryWrapper<GovPublicCarOrderVehicleInfo>().eq(GovPublicCarOrderVehicleInfo::getOrderNo, reDispatchVehicleDriverInfo.getOrderNo()));
        //根据订单号获取旧的驾驶员信息
        GovPublicCarOrderUserInfo oldUserInfo = govPublicCarOrderUserInfoService.getOne(new LambdaQueryWrapper<GovPublicCarOrderUserInfo>().eq(GovPublicCarOrderUserInfo::getOrderNo, reDispatchVehicleDriverInfo.getOrderNo()).eq(GovPublicCarOrderUserInfo::getUserType, GovPublicCarOrderUserTypeEnum.DRIVER.getCode()).last("limit 1"));
        if (ObjectUtil.equals(apply.getDrivingType(), GovPublicCarDrivingTypeEnum.DRIVER_DRIVE.getCode()) ||
                ObjectUtil.equals(apply.getDrivingType(), GovPublicCarDrivingTypeEnum.UNIT_DRIVE.getCode())) {
            GovPublicCarOrderUserInfo primaryRiderInfo = govPublicCarOrderUserInfoService.getOne(new LambdaQueryWrapper<GovPublicCarOrderUserInfo>()
                    .eq(GovPublicCarOrderUserInfo::getApplyNo, apply.getApplyNo())
                    .eq(GovPublicCarOrderUserInfo::getUserType, GovPublicCarOrderUserTypeEnum.PRIMARY_RIDER.getCode()).last("limit 1"));
            //车牌号和司机都发生变更->老的司机接收到行程取消信息，新的司机收到调度完成的信息
            try {
                if (!ObjectUtil.equals(oldUserInfo.getUserCode(), reDispatchVehicleDriverInfo.getUserCode())
                        && !ObjectUtil.equals(oldVehicleInfo.getVehicleNo(), reDispatchVehicleDriverInfo.getVehicleNo())) {
                    pushFlag = Boolean.TRUE;
                    //新司机发送消息
                    pushDriverDispatch(apply, govUser, primaryRiderInfo, govVehicleBaseInfo);
                    //老司机发送行程取消信息
                    pushDriverCancel(apply, oldUserInfo, primaryRiderInfo, oldVehicleInfo.getVehicleLicense());
                    //只有司机都发生变更->老的司机接收到行程取消信息，新的司机收到调度完成的信息
                } else if (!ObjectUtil.equals(oldUserInfo.getUserCode(), reDispatchVehicleDriverInfo.getUserCode())
                        && ObjectUtil.equals(oldVehicleInfo.getVehicleNo(), reDispatchVehicleDriverInfo.getVehicleNo())) {
                    pushFlag = Boolean.TRUE;
                    //新司机发送消息
                    pushDriverDispatch(apply, govUser, primaryRiderInfo, govVehicleBaseInfo);
                    //老司机发送行程取消信息
                    pushDriverCancel(apply, oldUserInfo, primaryRiderInfo, govVehicleBaseInfo.getVehicleLicense());
                    //只有车牌号发生变更->给司机发送重新调度车辆变更的通知
                } else if (ObjectUtil.equals(oldUserInfo.getUserCode(), reDispatchVehicleDriverInfo.getUserCode())
                        && !ObjectUtil.equals(oldVehicleInfo.getVehicleNo(), reDispatchVehicleDriverInfo.getVehicleNo())) {
                    pushFlag = Boolean.TRUE;
                    pushReDispatchVehicle(apply, reDispatchVehicleDriverInfo, primaryRiderInfo, govVehicleBaseInfo);
                } else {
                    log.info("车牌号和司机均未变更，暂不发送消息");
                }
            } catch (Exception e) {
                log.info("发送消息异常，{}", e.getMessage());
            }
        } else {
            //自驾情况，如果信息变更也需要发送
            if (!ObjectUtil.equals(oldUserInfo.getUserCode(), reDispatchVehicleDriverInfo.getUserCode())
                    && !ObjectUtil.equals(oldVehicleInfo.getVehicleNo(), reDispatchVehicleDriverInfo.getVehicleNo())) {
                pushFlag = Boolean.TRUE;
                //只有司机都发生变更->老的司机接收到行程取消信息，新的司机收到调度完成的信息
            } else if (!ObjectUtil.equals(oldUserInfo.getUserCode(), reDispatchVehicleDriverInfo.getUserCode())
                    && ObjectUtil.equals(oldVehicleInfo.getVehicleNo(), reDispatchVehicleDriverInfo.getVehicleNo())) {
                pushFlag = Boolean.TRUE;
                //只有车牌号发生变更->给司机发送重新调度车辆变更的通知
            } else if (ObjectUtil.equals(oldUserInfo.getUserCode(), reDispatchVehicleDriverInfo.getUserCode())
                    && !ObjectUtil.equals(oldVehicleInfo.getVehicleNo(), reDispatchVehicleDriverInfo.getVehicleNo())) {
                pushFlag = Boolean.TRUE;
            } else {
                log.info("车牌号和司机均未变更，暂不发送消息");
            }
        }
        //更新子行程订单信息
        LambdaUpdateWrapper<GovPublicCarOrder> govPublicCarOrderLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        govPublicCarOrderLambdaUpdateWrapper.eq(GovPublicCarOrder::getOrderNo, reDispatchVehicleDriverInfo.getOrderNo())
                .set(GovPublicCarOrder::getVehicleNo, govVehicleBaseInfo.getVehicleNo())
                .set(GovPublicCarOrder::getVehicleLicense, govVehicleBaseInfo.getVehicleLicense())
                .set(GovPublicCarOrder::getVehicleVin, govVehicleBaseInfo.getVehicleVin())
                .set(GovPublicCarOrder::getUpdateCode, param.getLoginUserCode())
                .set(GovPublicCarOrder::getUpdateName, param.getLoginUserName());
        if (ObjectUtil.equals(apply.getApplyType(), GovPublicCarApplyTypeEnum.SOCIAL_RENT.getCode())) {
            //日租
            if (ObjectUtil.equals(apply.getRentType(), GovPublicCarRentTypeEnum.DAILY.getCode())) {
                LambdaQueryWrapper<GovBillingConfiguration> queryWrapper = new LambdaQueryWrapper<GovBillingConfiguration>()
                        .eq(GovBillingConfiguration::getCompanyId, apply.getCompanyId())
                        .eq(GovBillingConfiguration::getSupplierCode, govVehicleBaseInfo.getSupplierServiceCode())
                        .eq(GovBillingConfiguration::getVehicleType, govVehicleBaseInfo.getVehicleType())
                        .last("limit 1");
                GovBillingConfiguration govBillingConfiguration = govBillingConfigurationService.getOne(queryWrapper);
                if (ObjectUtil.isNotNull(govBillingConfiguration)) {
                    govPublicCarOrderLambdaUpdateWrapper.set(GovPublicCarOrder::getBillingConfigurationVersionId, govBillingConfiguration.getBillingConfigurationVersionId());
                } else {
                    govPublicCarOrderLambdaUpdateWrapper.set(GovPublicCarOrder::getBillingConfigurationVersionId, null);
                }
            } else if (ObjectUtil.equals(apply.getRentType(), GovPublicCarRentTypeEnum.TIME_SHARE.getCode())) {
                //分时
                TimeShareConfigQueryReqDTO req = new TimeShareConfigQueryReqDTO();
                req.setCompanyId(govVehicleBaseInfo.getCompanyId());
                req.setSupplierCode(govVehicleBaseInfo.getSupplierServiceCode());
                req.setVehicleType(govVehicleBaseInfo.getVehicleType());
                req.setVehicleBrandId(govVehicleBaseInfo.getVehicleBrandId());
                req.setVehicleSeriesId(govVehicleBaseInfo.getVehicleSeriesId());
                TimeShareConfigRespDTO detail = govTimeShareConfigService.detail(req);
                if (Objects.nonNull(detail) && StringUtils.isNotBlank(detail.getSnapshotCode())) {
                    govPublicCarOrderLambdaUpdateWrapper.set(GovPublicCarOrder::getTimeShareSnapshotCode, detail.getSnapshotCode());
                }
            }
        }
        govPublicCarOrderService.update(govPublicCarOrderLambdaUpdateWrapper);
        //删除之前调度员&司机的信息
        govPublicCarOrderUserInfoService.remove(new LambdaQueryWrapper<GovPublicCarOrderUserInfo>().eq(GovPublicCarOrderUserInfo::getApplyNo, apply.getApplyNo())
                .eq(GovPublicCarOrderUserInfo::getOrderNo, govPublicCarOrder.getOrderNo())
                .in(GovPublicCarOrderUserInfo::getUserType, Arrays.asList(GovPublicCarOrderUserTypeEnum.SCHEDULER.getCode(), GovPublicCarOrderUserTypeEnum.DRIVER.getCode())));
        //插入司机信息
        GovPublicCarOrderUserInfo driverInfo = new GovPublicCarOrderUserInfo();
        driverInfo.setUserType(GovPublicCarOrderUserTypeEnum.DRIVER.getCode());
        driverInfo.setUserCode(govUser.getUserCode());
        driverInfo.setUserName(govUser.getUserName());
        driverInfo.setUserMobile(govUser.getMobile());
        driverInfo.setStructCode(govUser.getBelongStructCode());
        driverInfo.setDeptCode(govUser.getBelongDeptCode());
        Map<String, GovStruct> govStructMap = govStructService.list(new LambdaQueryWrapper<GovStruct>().in(GovStruct::getStructCode, Arrays.asList(govUser.getBelongStructCode(), govUser.getBelongDeptCode()))).stream().collect(Collectors.toMap(GovStruct::getStructCode, Function.identity()));
        GovStruct struct = govStructMap.get(govUser.getBelongStructCode());
        if (Objects.nonNull(struct)) {
            driverInfo.setStructName(struct.getStructName());
        }
        GovStruct dept = govStructMap.get(govUser.getBelongDeptCode());
        if (Objects.nonNull(dept)) {
            driverInfo.setDeptName(dept.getStructName());
        }
        driverInfo.setCompanyId(apply.getCompanyId());
        driverInfo.setCompanyName(apply.getCompanyName());
        driverInfo.setApplyNo(apply.getApplyNo());
        driverInfo.setOrderNo(govPublicCarOrder.getOrderNo());
        govPublicCarOrderUserInfoService.save(driverInfo);
        //插入调度员信息
        GovPublicCarOrderUserInfo dispatchUserInfo = new GovPublicCarOrderUserInfo();
        dispatchUserInfo.setUserType(GovPublicCarOrderUserTypeEnum.SCHEDULER.getCode());
        dispatchUserInfo.setUserCode(param.getLoginUserCode());
        dispatchUserInfo.setUserName(param.getLoginUserName());
        dispatchUserInfo.setUserMobile(param.getLoginUserMobile());
        dispatchUserInfo.setStructCode(param.getLoginUserBelongStructCode());
        dispatchUserInfo.setStructName(param.getLoginUserBelongStructName());
        dispatchUserInfo.setDeptCode(param.getLoginUserBelongDeptCode());
        dispatchUserInfo.setDeptName(param.getLoginUserBelongDeptName());
        dispatchUserInfo.setCompanyId(param.getLoginCompanyId());
        dispatchUserInfo.setCompanyName(param.getLoginCompanyName());
        dispatchUserInfo.setApplyNo(apply.getApplyNo());
        dispatchUserInfo.setOrderNo(govPublicCarOrder.getOrderNo());
        govPublicCarOrderUserInfoService.save(dispatchUserInfo);
        //删除之前的车辆信息
        govPublicCarOrderVehicleInfoService.remove(new LambdaQueryWrapper<GovPublicCarOrderVehicleInfo>().eq(GovPublicCarOrderVehicleInfo::getOrderNo, govPublicCarOrder.getOrderNo()));
        //插入车辆信息
        GovPublicCarOrderVehicleInfo govPublicCarOrderVehicleInfo = new GovPublicCarOrderVehicleInfo();
        govPublicCarOrderVehicleInfo.setOrderNo(govPublicCarOrder.getOrderNo());
        govPublicCarOrderVehicleInfo.setCompanyId(apply.getCompanyId());
        govPublicCarOrderVehicleInfo.setVehicleNo(govVehicleBaseInfo.getVehicleNo());
        govPublicCarOrderVehicleInfo.setVehicleLicense(govVehicleBaseInfo.getVehicleLicense());
        govPublicCarOrderVehicleInfo.setVehicleVin(govVehicleBaseInfo.getVehicleVin());
        govPublicCarOrderVehicleInfo.setVehicleType(reDispatchVehicleDriverInfo.getVehicleType());
        //查询设备信息
        GovGpsDevice govGpsDevice = govGpsDeviceService.getOne(new LambdaQueryWrapper<GovGpsDevice>().eq(GovGpsDevice::getVehicleNo, govVehicleBaseInfo.getVehicleNo()).eq(GovGpsDevice::getDeleteStatus, GovGpsDeviceEnum.DeviceDeleteStatusEnum.NORMAL.getCode()).last("LIMIT 1"));
        if (ObjectUtil.isNotNull(govGpsDevice)) {
            govPublicCarOrderVehicleInfo.setDeviceId(govGpsDevice.getDeviceNo());
            govPublicCarOrderVehicleInfo.setDeviceType(govGpsDevice.getDeviceType());
        }
        govPublicCarOrderVehicleInfo.setVehicleBrandId(govVehicleBaseInfo.getVehicleBrandId());
        govPublicCarOrderVehicleInfo.setVehicleBrandName(govVehicleBaseInfo.getVehicleBrandName());
        govPublicCarOrderVehicleInfo.setVehicleSeriesId(govVehicleBaseInfo.getVehicleSeriesId());
        govPublicCarOrderVehicleInfo.setVehicleSeriesName(govVehicleBaseInfo.getVehicleSeriesName());
        govPublicCarOrderVehicleInfo.setUseAttribute(govVehicleBaseInfo.getUseAttribute());
        govPublicCarOrderVehicleInfo.setVehicleBelongDeptCode(govVehicleBaseInfo.getVehicleBelongDeptCode());
        govPublicCarOrderVehicleInfo.setVehicleBelongDeptName(govVehicleBaseInfo.getVehicleBelongDeptName());
        govPublicCarOrderVehicleInfo.setVehicleUseDeptCode(govVehicleBaseInfo.getVehicleUseDeptCode());
        govPublicCarOrderVehicleInfo.setVehicleUseDeptName(govVehicleBaseInfo.getVehicleUseDeptName());
        govPublicCarOrderVehicleInfo.setVehicleUseStructCode(govVehicleBaseInfo.getVehicleUseStructCode());
        govPublicCarOrderVehicleInfo.setVehicleUseStructName(govVehicleBaseInfo.getVehicleUseStructName());
        govPublicCarOrderVehicleInfo.setApplyNo(apply.getApplyNo());
        govPublicCarOrderVehicleInfo.setSupplierCode(govVehicleBaseInfo.getSupplierServiceCode());
        govPublicCarOrderVehicleInfo.setSupplierName(govVehicleBaseInfo.getSupplierServiceName());
        govPublicCarOrderVehicleInfo.setManageCarType(govVehicleBaseInfo.getManageCarType());
        govPublicCarOrderVehicleInfo.setVehicleServiceType(govVehicleBaseInfo.getVehicleServiceType());
        govPublicCarOrderVehicleInfoService.save(govPublicCarOrderVehicleInfo);
        //插入日志
        GovOrderOperationLog operationLog = new GovOrderOperationLog();
        operationLog.setOrderNo(govPublicCarOrder.getOrderNo());
        operationLog.setOperationType(GovPublicCarOrderOperationTypeEnum.ORDER_SCHEDULE.getCode());
        operationLog.setOperationDescription(GovPublicCarOrderOperationTypeEnum.ORDER_SCHEDULE.getName());
        operationLog.setOrderStatus(GovPublicCarOrderStatusEnum.PENDING_DEPARTURE.getCode());
        operationLog.setOrderStatusName(GovPublicCarOrderStatusEnum.PENDING_DEPARTURE.getName());
        operationLog.setOperatorCode(param.getLoginUserCode());
        operationLog.setOperatorName(param.getLoginUserName());
        operationLog.setOperatorMobile(param.getLoginUserMobile());
        govOrderOperationLogService.save(operationLog);
        return pushFlag;
    }

    private void pushReDispatchVehicle(GovPublicCarApply apply, ReDispatchVehicleDriverInfo reDispatchVehicleDriverInfo, GovPublicCarOrderUserInfo primaryRiderInfo, GovVehicleBaseInfo govVehicleBaseInfo) {
        Map<String, String> oldExtendMap = new HashMap<>();
        oldExtendMap.put(PushMessageUtil.MSG_PARAM_APPLY_START_TIME, DateUtils.format(apply.getExpectedPickupTime(), DateUtils.TIME_FORMAT_V3));
        oldExtendMap.put(PushMessageUtil.MSG_PARAM_APPLY_END_TIME, DateUtils.format(apply.getExpectedReturnTime(), DateUtils.TIME_FORMAT_V3));
        oldExtendMap.put(PushMessageUtil.MSG_PARAM_APPLY_START_ADDRESS, apply.getEstimatedDepartureShortLocation());
        oldExtendMap.put(PushMessageUtil.MSG_PARAM_APPLY_END_ADDRESS, apply.getEstimatedDestinationShortLocation());
        MessageTrackingSender.MessageTrackingParam oldPushParam = new MessageTrackingSender.MessageTrackingParam();
        oldPushParam.setCompanyId(apply.getCompanyId());
        oldPushParam.setScene(GovMsgSceneEnum.RE_SCHEDULE_NOTICE_DRIVER);
        oldPushParam.setReceiverCodeSet(new HashSet<>(Collections.singletonList(reDispatchVehicleDriverInfo.getUserCode())));
        oldExtendMap.put(PushMessageUtil.MSG_PARAM_APPLY_PASSENGER_NAME, primaryRiderInfo.getUserName());
        oldExtendMap.put(PushMessageUtil.MSG_PARAM_APPLY_PASSENGER_PHONE, primaryRiderInfo.getUserMobile());
        oldExtendMap.put(PushMessageUtil.MSG_PARAM_APPLY_VEHICLE_LICENSE, govVehicleBaseInfo.getVehicleLicense());
        oldPushParam.setExtendParamMap(oldExtendMap);
        oldPushParam.setMobile(new HashSet<>());
        messageTrackingSender.sendMessageTracking(oldPushParam);
    }

    private void pushDriverDispatch(GovPublicCarApply apply, GovUser govUser, GovPublicCarOrderUserInfo primaryRiderInfo, GovVehicleBaseInfo govVehicleBaseInfo) {
        Map<String, String> extendParamMap = new HashMap<>();
        extendParamMap.put(PushMessageUtil.MSG_PARAM_APPLY_START_TIME, DateUtils.format(apply.getExpectedPickupTime(), DateUtils.TIME_FORMAT_V3));
        extendParamMap.put(PushMessageUtil.MSG_PARAM_APPLY_END_TIME, DateUtils.format(apply.getExpectedReturnTime(), DateUtils.TIME_FORMAT_V3));
        extendParamMap.put(PushMessageUtil.MSG_PARAM_APPLY_START_ADDRESS, apply.getEstimatedDepartureShortLocation());
        extendParamMap.put(PushMessageUtil.MSG_PARAM_APPLY_END_ADDRESS, apply.getEstimatedDestinationShortLocation());
        MessageTrackingSender.MessageTrackingParam pushParam = new MessageTrackingSender.MessageTrackingParam();
        pushParam.setCompanyId(apply.getCompanyId());
        pushParam.setScene(GovMsgSceneEnum.SCHEDULE_FINISH_NOTICE_DRIVER);
        pushParam.setReceiverCodeSet(new HashSet<>(Collections.singletonList(govUser.getUserCode())));
        extendParamMap.put(PushMessageUtil.MSG_PARAM_APPLY_PASSENGER_NAME, primaryRiderInfo.getUserName());
        extendParamMap.put(PushMessageUtil.MSG_PARAM_APPLY_PASSENGER_PHONE, primaryRiderInfo.getUserMobile());
        extendParamMap.put(PushMessageUtil.MSG_PARAM_APPLY_VEHICLE_LICENSE, govVehicleBaseInfo.getVehicleLicense());
        pushParam.setExtendParamMap(extendParamMap);
        pushParam.setMobile(new HashSet<>());
        messageTrackingSender.sendMessageTracking(pushParam);
    }

    private void pushDriverCancel(GovPublicCarApply apply, GovPublicCarOrderUserInfo oldUserInfo, GovPublicCarOrderUserInfo primaryRiderInfo, String vehicleLicense) {
        Map<String, String> oldExtendMap = new HashMap<>();
        oldExtendMap.put(PushMessageUtil.MSG_PARAM_APPLY_START_TIME, DateUtils.format(apply.getExpectedPickupTime(), DateUtils.TIME_FORMAT_V3));
        oldExtendMap.put(PushMessageUtil.MSG_PARAM_APPLY_END_TIME, DateUtils.format(apply.getExpectedReturnTime(), DateUtils.TIME_FORMAT_V3));
        oldExtendMap.put(PushMessageUtil.MSG_PARAM_APPLY_START_ADDRESS, apply.getEstimatedDepartureShortLocation());
        oldExtendMap.put(PushMessageUtil.MSG_PARAM_APPLY_END_ADDRESS, apply.getEstimatedDestinationShortLocation());
        MessageTrackingSender.MessageTrackingParam oldPushParam = new MessageTrackingSender.MessageTrackingParam();
        oldPushParam.setCompanyId(apply.getCompanyId());
        oldPushParam.setScene(GovMsgSceneEnum.TRIP_CANCEL_NOTICE_DRIVER);
        oldPushParam.setReceiverCodeSet(new HashSet<>(Collections.singletonList(oldUserInfo.getUserCode())));
        oldExtendMap.put(PushMessageUtil.MSG_PARAM_APPLY_PASSENGER_NAME, primaryRiderInfo.getUserName());
        oldExtendMap.put(PushMessageUtil.MSG_PARAM_APPLY_PASSENGER_PHONE, primaryRiderInfo.getUserMobile());
        oldExtendMap.put(PushMessageUtil.MSG_PARAM_APPLY_VEHICLE_LICENSE, vehicleLicense);
        oldPushParam.setExtendParamMap(oldExtendMap);
        oldPushParam.setMobile(new HashSet<>());
        messageTrackingSender.sendMessageTracking(oldPushParam);
    }

    private void buildSubOrderInfo(GovPublicCarApply apply, Map<String, GovVehicleBaseInfo> vehicleMap, Map<String, GovUser> userInfoMap, DispatchVehicleDriverInfo dispatchVehicleDriverInfo, DispatchApplyReqDTO param, List<GovPublicCarOrderUserInfo> list) {
        String orderNo;
        if (ObjectUtil.equal(apply.getApplyType(), GovPublicCarApplyTypeEnum.PUBLIC_CAR.getCode())) {
            orderNo = sequenceGenerator.generate(new Date(), PUBLIC_GOV_CAR_ORDER_PREFIX);
        } else {
            orderNo = sequenceGenerator.generate(new Date(), PUBLIC_SOC_GOV_CAR_ORDER_PREFIX);
        }
        GovVehicleBaseInfo govVehicleBaseInfo = vehicleMap.get(dispatchVehicleDriverInfo.getVehicleNo());
        GovUser govUser = userInfoMap.get(dispatchVehicleDriverInfo.getUserCode());
        //生成订单并插入数据库
        GovPublicCarOrder order = new GovPublicCarOrder();
        order.setOrderNo(orderNo);
        order.setOrderType(GovPublicCarOrderTypeEnum.PUBLIC_USE_CAR_TYPE.getCode());
        order.setUseType(apply.getApplyType());
        order.setVehicleNo(govVehicleBaseInfo.getVehicleNo());
        order.setVehicleLicense(govVehicleBaseInfo.getVehicleLicense());
        order.setVehicleVin(govVehicleBaseInfo.getVehicleVin());
        order.setOrderStatus(GovPublicCarOrderStatusEnum.PENDING_DEPARTURE.getCode());
        order.setVerifyStatus(GovPublicCarVerifyStatusEnum.NO_VERIFICATION_REQUIRED.getCode());
        order.setRelatedFeesStatus(GovPublicCarRelatedFeesStatusEnum.UNASSOCIATED.getCode());
        order.setCompanyId(apply.getCompanyId());
        order.setCompanyName(apply.getCompanyName());
        order.setApplyNo(apply.getApplyNo());
        order.setUpdateCode(param.getLoginUserCode());
        order.setUpdateName(param.getLoginUserName());
        //查询主用车人信息
        GovPublicCarOrderUserInfo primaryUserInfo = govPublicCarOrderUserInfoService.getOne(new LambdaQueryWrapper<GovPublicCarOrderUserInfo>().eq(GovPublicCarOrderUserInfo::getApplyNo, apply.getApplyNo()).eq(GovPublicCarOrderUserInfo::getUserType, GovPublicCarOrderUserTypeEnum.PRIMARY_RIDER.getCode()).last("limit 1"));
        if (!primaryUserInfo.getDeptCode().equals(param.getUseVehicleDeptCode())) {
            order.setCrossType(GovPublicCarCrossTypeEnum.CROSS.getCode());
        } else {
            order.setCrossType(GovPublicCarCrossTypeEnum.NO_CROSS.getCode());
        }
        if (ObjectUtil.equals(apply.getApplyType(), GovPublicCarApplyTypeEnum.SOCIAL_RENT.getCode())) {
            if (Objects.equals(apply.getRentType(), GovPublicCarRentTypeEnum.DAILY.getCode())) {
                LambdaQueryWrapper<GovBillingConfiguration> queryWrapper = new LambdaQueryWrapper<GovBillingConfiguration>()
                        .eq(GovBillingConfiguration::getCompanyId, apply.getCompanyId())
                        .eq(GovBillingConfiguration::getSupplierCode, govVehicleBaseInfo.getSupplierServiceCode())
                        .eq(GovBillingConfiguration::getVehicleType, govVehicleBaseInfo.getVehicleType())
                        .last("limit 1");
                GovBillingConfiguration govBillingConfiguration = govBillingConfigurationService.getOne(queryWrapper);
                if (ObjectUtil.isNotNull(govBillingConfiguration)) {
                    order.setBillingConfigurationVersionId(govBillingConfiguration.getBillingConfigurationVersionId());
                }
            } else if (Objects.equals(apply.getRentType(), GovPublicCarRentTypeEnum.TIME_SHARE.getCode())) {
                //分时计价规则配置
                TimeShareConfigQueryReqDTO req = new TimeShareConfigQueryReqDTO();
                req.setCompanyId(govVehicleBaseInfo.getCompanyId());
                req.setSupplierCode(govVehicleBaseInfo.getSupplierServiceCode());
                req.setVehicleType(govVehicleBaseInfo.getVehicleType());
                req.setVehicleBrandId(govVehicleBaseInfo.getVehicleBrandId());
                req.setVehicleSeriesId(govVehicleBaseInfo.getVehicleSeriesId());
                TimeShareConfigRespDTO detail = govTimeShareConfigService.detail(req);
                if (Objects.nonNull(detail) && StringUtils.isNotBlank(detail.getSnapshotCode())) {
                    order.setTimeShareSnapshotCode(detail.getSnapshotCode());
                }

                //计价时间配置
                List<GovCompanyConfigItemValueDTO> configItemValueDTOS = govPublicBusinessConfigService.selectByDeptCodeAndBusinessCodeAndItemCode(govVehicleBaseInfo.getCompanyId(), primaryUserInfo.getDeptCode(),
                        PublicConfigEnum.BusinessConfigEnum.HOUR_USE_CAR_DURATION_FILED.getCode(), PublicConfigEnum.BusinessConfigItemEnum.USE_CAR_DURATION_FILED_ITEM.getCode());
                GovCompanyConfigItemValueDTO configItemValueDTO = configItemValueDTOS.get(0);
                // 计价时间配置: 实际开始/结束时间
                Integer timeShareCalculateConfig = Integer.parseInt(configItemValueDTO.getConfigValue());
                order.setTimeShareCalculateConfig(timeShareCalculateConfig);
            }
        }
        //插入订单
        govPublicCarOrderService.save(order);
        //根据申请单号查询所有人员信息
        if (CollectionUtils.isNotEmpty(list)) {
            for (GovPublicCarOrderUserInfo govPublicCarOrderUserInfo : list) {
                govPublicCarOrderUserInfo.setId(null);
                govPublicCarOrderUserInfo.setOrderNo(orderNo);
            }
            govPublicCarOrderUserInfoService.saveBatch(list);
        }
        //插入调度员信息
        GovPublicCarOrderUserInfo dispatchUserInfo = new GovPublicCarOrderUserInfo();
        dispatchUserInfo.setUserType(GovPublicCarOrderUserTypeEnum.SCHEDULER.getCode());
        dispatchUserInfo.setUserCode(param.getLoginUserCode());
        dispatchUserInfo.setUserName(param.getLoginUserName());
        dispatchUserInfo.setUserMobile(param.getLoginUserMobile());
        dispatchUserInfo.setStructCode(param.getLoginUserBelongStructCode());
        dispatchUserInfo.setStructName(param.getLoginUserBelongStructName());
        dispatchUserInfo.setDeptCode(param.getLoginUserBelongDeptCode());
        dispatchUserInfo.setDeptName(param.getLoginUserBelongDeptName());
        dispatchUserInfo.setCompanyId(param.getLoginCompanyId());
        dispatchUserInfo.setCompanyName(param.getLoginCompanyName());
        dispatchUserInfo.setApplyNo(apply.getApplyNo());
        dispatchUserInfo.setOrderNo(orderNo);
        govPublicCarOrderUserInfoService.save(dispatchUserInfo);
        //插入司机信息
        GovPublicCarOrderUserInfo driverInfo = new GovPublicCarOrderUserInfo();
        driverInfo.setUserType(GovPublicCarOrderUserTypeEnum.DRIVER.getCode());
        driverInfo.setUserCode(govUser.getUserCode());
        driverInfo.setUserName(govUser.getUserName());
        driverInfo.setUserMobile(govUser.getMobile());
        driverInfo.setStructCode(govUser.getBelongStructCode());
        driverInfo.setDeptCode(govUser.getBelongDeptCode());
        Map<String, GovStruct> govStructMap = govStructService.list(new LambdaQueryWrapper<GovStruct>().in(GovStruct::getStructCode, Arrays.asList(govUser.getBelongStructCode(), govUser.getBelongDeptCode()))).stream().collect(Collectors.toMap(GovStruct::getStructCode, Function.identity()));
        GovStruct struct = govStructMap.get(govUser.getBelongStructCode());
        if (Objects.nonNull(struct)) {
            driverInfo.setStructName(struct.getStructName());
        }
        GovStruct dept = govStructMap.get(govUser.getBelongDeptCode());
        if (Objects.nonNull(dept)) {
            driverInfo.setDeptName(dept.getStructName());
        }
//        driverInfo.setStructName(govStructMap.get(govUser.getBelongStructCode()).getStructName());
//        driverInfo.setDeptName(govStructMap.get(govUser.getBelongDeptCode()).getStructName());
        driverInfo.setCompanyId(apply.getCompanyId());
        driverInfo.setCompanyName(apply.getCompanyName());
        driverInfo.setApplyNo(apply.getApplyNo());
        driverInfo.setOrderNo(orderNo);
        govPublicCarOrderUserInfoService.save(driverInfo);
        //插入车辆信息
        GovPublicCarOrderVehicleInfo govPublicCarOrderVehicleInfo = new GovPublicCarOrderVehicleInfo();
        govPublicCarOrderVehicleInfo.setOrderNo(orderNo);
        govPublicCarOrderVehicleInfo.setCompanyId(apply.getCompanyId());
        govPublicCarOrderVehicleInfo.setVehicleNo(govVehicleBaseInfo.getVehicleNo());
        govPublicCarOrderVehicleInfo.setVehicleLicense(govVehicleBaseInfo.getVehicleLicense());
        govPublicCarOrderVehicleInfo.setVehicleVin(govVehicleBaseInfo.getVehicleVin());
        govPublicCarOrderVehicleInfo.setVehicleType(dispatchVehicleDriverInfo.getVehicleType());
        //查询设备信息
        GovGpsDevice govGpsDevice = govGpsDeviceService.getOne(new LambdaQueryWrapper<GovGpsDevice>().eq(GovGpsDevice::getVehicleNo, govVehicleBaseInfo.getVehicleNo()).eq(GovGpsDevice::getDeleteStatus, GovGpsDeviceEnum.DeviceDeleteStatusEnum.NORMAL.getCode()).last("LIMIT 1"));
        if (ObjectUtil.isNotNull(govGpsDevice)) {
            govPublicCarOrderVehicleInfo.setDeviceId(govGpsDevice.getDeviceNo());
            govPublicCarOrderVehicleInfo.setDeviceType(govGpsDevice.getDeviceType());
        }
        govPublicCarOrderVehicleInfo.setVehicleBrandId(govVehicleBaseInfo.getVehicleBrandId());
        govPublicCarOrderVehicleInfo.setVehicleBrandName(govVehicleBaseInfo.getVehicleBrandName());
        govPublicCarOrderVehicleInfo.setVehicleSeriesId(govVehicleBaseInfo.getVehicleSeriesId());
        govPublicCarOrderVehicleInfo.setVehicleSeriesName(govVehicleBaseInfo.getVehicleSeriesName());
        govPublicCarOrderVehicleInfo.setUseAttribute(govVehicleBaseInfo.getUseAttribute());
        if (ObjectUtil.equal(apply.getApplyType(), GovPublicCarApplyTypeEnum.SOCIAL_RENT.getCode())
                && ObjectUtil.equal(apply.getRentType(), GovPublicCarRentTypeEnum.TIME_SHARE.getCode())
                && StringUtils.isEmpty(govVehicleBaseInfo.getVehicleBelongDeptCode())) {
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "所选车辆信息未维护分时租赁单位");
        }
        govPublicCarOrderVehicleInfo.setVehicleBelongDeptCode(govVehicleBaseInfo.getVehicleBelongDeptCode());
        govPublicCarOrderVehicleInfo.setVehicleBelongDeptName(govVehicleBaseInfo.getVehicleBelongDeptName());
        govPublicCarOrderVehicleInfo.setVehicleUseDeptCode(govVehicleBaseInfo.getVehicleUseDeptCode());
        govPublicCarOrderVehicleInfo.setVehicleUseDeptName(govVehicleBaseInfo.getVehicleUseDeptName());
        govPublicCarOrderVehicleInfo.setVehicleUseStructCode(govVehicleBaseInfo.getVehicleUseStructCode());
        govPublicCarOrderVehicleInfo.setVehicleUseStructName(govVehicleBaseInfo.getVehicleUseStructName());
        govPublicCarOrderVehicleInfo.setApplyNo(apply.getApplyNo());
        govPublicCarOrderVehicleInfo.setSupplierCode(govVehicleBaseInfo.getSupplierServiceCode());
        govPublicCarOrderVehicleInfo.setSupplierName(govVehicleBaseInfo.getSupplierServiceName());
        govPublicCarOrderVehicleInfo.setManageCarType(govVehicleBaseInfo.getManageCarType());
        govPublicCarOrderVehicleInfo.setVehicleServiceType(govVehicleBaseInfo.getVehicleServiceType());
        govPublicCarOrderVehicleInfoService.save(govPublicCarOrderVehicleInfo);
        //插入日志
        GovOrderOperationLog operationLog = new GovOrderOperationLog();
        operationLog.setOrderNo(orderNo);
        operationLog.setOperationType(GovPublicCarOrderOperationTypeEnum.CREATE.getCode());
        operationLog.setOperationDescription(GovPublicCarOrderOperationTypeEnum.CREATE.getName());
        operationLog.setOrderStatus(GovPublicCarOrderStatusEnum.PENDING_DEPARTURE.getCode());
        operationLog.setOrderStatusName(GovPublicCarOrderStatusEnum.PENDING_DEPARTURE.getName());
        operationLog.setOperatorCode(param.getLoginUserCode());
        operationLog.setOperatorName(param.getLoginUserName());
        operationLog.setOperatorMobile(param.getLoginUserMobile());
        govOrderOperationLogService.save(operationLog);
    }

    private void buildOperationInfo(GovCarCreateApplyReqDTO param, String applyNo) {
        GovApplyOperationLog operationLog = new GovApplyOperationLog();
        operationLog.setApplyNo(applyNo);
        operationLog.setOperationType(ApplyOperationTypeEnum.CREATE_ORDER.getCode());
        operationLog.setOperationRemark(ApplyOperationTypeEnum.CREATE_ORDER.getName());
        operationLog.setOperatorCode(param.getLoginUserCode());
        operationLog.setOperatorName(param.getLoginUserName());
        operationLog.setOperatorMobile(param.getLoginUserMobile());
        govApplyOperationLogService.save(operationLog);
    }

    private void buildUsersInfo(GovCarCreateApplyReqDTO param, List<GovOrderUserInfoDTO> passengersInfoList, String applyNo) {
        List<GovPublicCarOrderUserInfo> userInfos = new ArrayList<>();
        GovPublicCarOrderUserInfo creatorInfo = new GovPublicCarOrderUserInfo();
        creatorInfo.setApplyNo(applyNo);
        creatorInfo.setUserType(GovPublicCarOrderUserTypeEnum.ORDER_PLACER.getCode());
        creatorInfo.setUserCode(param.getLoginUserCode());
        creatorInfo.setUserName(param.getLoginUserName());
        creatorInfo.setUserMobile(param.getLoginUserMobile());
        creatorInfo.setStructCode(param.getLoginUserBelongStructCode());
        creatorInfo.setStructName(param.getLoginUserBelongStructName());
        creatorInfo.setDeptCode(param.getLoginUserBelongDeptCode());
        creatorInfo.setDeptName(param.getLoginUserBelongDeptName());
        creatorInfo.setCompanyId(param.getLoginCompanyId());
        creatorInfo.setCompanyName(param.getLoginCompanyName());
        userInfos.add(creatorInfo);
        //4. 主乘车人&次乘车人
        for (int i = 0; i < passengersInfoList.size(); i++) {
            GovOrderUserInfoDTO passengersInfo = passengersInfoList.get(i);
            GovPublicCarOrderUserInfo govPublicCarOrderUserInfo = BeanUtil.copyObject(passengersInfo, GovPublicCarOrderUserInfo.class);
            govPublicCarOrderUserInfo.setApplyNo(applyNo);
            if (i == 0) {
                govPublicCarOrderUserInfo.setUserType(GovPublicCarOrderUserTypeEnum.PRIMARY_RIDER.getCode());
            } else {
                govPublicCarOrderUserInfo.setUserType(GovPublicCarOrderUserTypeEnum.SECONDARY_RIDER.getCode());
            }
            govPublicCarOrderUserInfo.setCompanyId(param.getLoginCompanyId());
            govPublicCarOrderUserInfo.setCompanyName(param.getLoginCompanyName());
            userInfos.add(govPublicCarOrderUserInfo);
        }
        govPublicCarOrderUserInfoService.saveBatch(userInfos);
    }

    private void buildReserveInfo(GovCarCreateApplyReqDTO param, List<GovApplyUserVehicleDTO> vehicleUserList, String applyNo) {
        List<GovPublicApplyVehicleDriverInfo> govPublicApplyVehicleDriverInfos = new ArrayList<>();
        for (GovApplyUserVehicleDTO govApplyUserVehicleDTO : vehicleUserList) {
            GovPublicApplyVehicleDriverInfo govPublicApplyVehicleDriverInfo = new GovPublicApplyVehicleDriverInfo();
            govPublicApplyVehicleDriverInfo.setApplyNo(applyNo);
            govPublicApplyVehicleDriverInfo.setCompanyId(param.getLoginCompanyId());
            if (ObjectUtil.equals(param.getScheduleType(), GovPublicCarScheduleTypeEnum.NO_SCHEDULE.getCode())) {
                if (StringUtils.isNotEmpty(govApplyUserVehicleDTO.getVehicleNo())) {
                    //查询车辆信息
                    GovVehicleBaseInfo vehicleBaseInfo = govVehicleBaseInfoService.getOne(new LambdaQueryWrapper<GovVehicleBaseInfo>().eq(GovVehicleBaseInfo::getVehicleNo, govApplyUserVehicleDTO.getVehicleNo()).last("limit 1"));
                    if (ObjectUtil.isNotNull(vehicleBaseInfo)) {
                        govPublicApplyVehicleDriverInfo.setVehicleId(vehicleBaseInfo.getId());
                        govPublicApplyVehicleDriverInfo.setVehicleUseDeptCode(vehicleBaseInfo.getVehicleUseDeptCode());
                    }
                }
                govPublicApplyVehicleDriverInfo.setVehicleNo(govApplyUserVehicleDTO.getVehicleNo());
                govPublicApplyVehicleDriverInfo.setVehicleLicense(govApplyUserVehicleDTO.getVehicleLicense());
                govPublicApplyVehicleDriverInfo.setVehicleVin(govApplyUserVehicleDTO.getVehicleVin());
            }
            govPublicApplyVehicleDriverInfo.setVehicleType(govApplyUserVehicleDTO.getVehicleType());
            if (!(ObjectUtil.equals(param.getScheduleType(), GovPublicCarScheduleTypeEnum.SCHEDULE.getCode()) &&
                    ObjectUtil.equals(param.getDrivingType(), GovPublicCarDrivingTypeEnum.DRIVER_DRIVE.getCode()))) {
                govPublicApplyVehicleDriverInfo.setDriverId(govApplyUserVehicleDTO.getUserId());
                govPublicApplyVehicleDriverInfo.setDriverCode(govApplyUserVehicleDTO.getUserCode());
                govPublicApplyVehicleDriverInfo.setDriverName(govApplyUserVehicleDTO.getUserName());
                govPublicApplyVehicleDriverInfo.setDriverMobile(govApplyUserVehicleDTO.getUserMobile());
            }
            govPublicApplyVehicleDriverInfos.add(govPublicApplyVehicleDriverInfo);
        }
        govPublicApplyVehicleDriverInfoService.saveBatch(govPublicApplyVehicleDriverInfos);
    }

    private String buildOrderInfo(GovCarCreateApplyReqDTO param, Boolean isActive) {
        //1.申请单构建
        String applyNo;
        if (ObjectUtil.equal(param.getApplyType(), GovPublicCarApplyTypeEnum.PUBLIC_CAR.getCode())) {
            applyNo = sequenceGenerator.generate(new Date(), PUBLIC_GOV_CAR_APPLY_PREFIX);
        } else {
            applyNo = sequenceGenerator.generate(new Date(), PUBLIC_SOC_GOV_CAR_APPLY_PREFIX);
        }
        GovPublicCarApply govPublicCarApply = new GovPublicCarApply();
        govPublicCarApply.setApplyNo(applyNo);
        govPublicCarApply.setExpectedPickupTime(param.getExpectedPickupTime());
        govPublicCarApply.setExpectedReturnTime(param.getExpectedReturnTime());
        govPublicCarApply.setEstimatedDepartureLongLocation(param.getEstimatedDepartureLocation());
        govPublicCarApply.setEstimatedDepartureShortLocation(param.getEstimatedDepartureShortLocation());
        govPublicCarApply.setEstimatedDepartureLatitude(param.getEstimatedDepartureLatitude());
        govPublicCarApply.setEstimatedDepartureLongitude(param.getEstimatedDepartureLongitude());
        govPublicCarApply.setEstimatedDestinationLongLocation(param.getEstimatedDestinationLocation());
        govPublicCarApply.setEstimatedDestinationShortLocation(param.getEstimatedDestinationShortLocation());
        govPublicCarApply.setEstimatedDestinationLatitude(param.getEstimatedDestinationLatitude());
        govPublicCarApply.setEstimatedDestinationLongitude(param.getEstimatedDestinationLongitude());
        buildCityInfo(param, govPublicCarApply);
        govPublicCarApply.setCarUseReason(param.getCarUseReason());
        govPublicCarApply.setOrderUserMemo(param.getOrderUserMemo());
        govPublicCarApply.setScheduleType(param.getScheduleType());
        govPublicCarApply.setDrivingType(param.getDrivingType());
        govPublicCarApply.setApplyType(param.getApplyType());
        govPublicCarApply.setSupplierCode(param.getSupplierCode());
        govPublicCarApply.setSupplierName(param.getSupplierName());
        govPublicCarApply.setRentType(param.getRentType());
        //车辆所属单位创建申请单统一按照用车人单位设置
        govPublicCarApply.setVehicleBelongDeptCode(param.getUseVehicleDeptCode());
        govPublicCarApply.setVehicleBelongDeptName(param.getUseVehicleDeptName());
        govPublicCarApply.setApprovalStatus(ApplyApprovalStatusEnum.NOT_REQUIRED.getCode());
        //审核开关没开&&调度类型为无需调度
        if (!isActive && ObjectUtil.equals(param.getScheduleType(), GovPublicCarScheduleTypeEnum.NO_SCHEDULE.getCode())) {
            govPublicCarApply.setScheduleStatus(ApplySchedulingStatusEnum.NOT_REQUIRED.getCode());
        }
        if (!isActive && ObjectUtil.equals(param.getScheduleType(), GovPublicCarScheduleTypeEnum.SCHEDULE.getCode())) {
            govPublicCarApply.setScheduleStatus(ApplySchedulingStatusEnum.PENDING.getCode());
        }
        //计算订单保留时间至多久
        govPublicCarApply.setOrderRetentionDate(DateUtils.addHoursToDate(param.getExpectedReturnTime(), param.getVehicleRetentionDuration()));
        govPublicCarApply.setOrderRetentionDateValue(param.getVehicleRetentionDuration());
        govPublicCarApply.setCompanyId(param.getLoginCompanyId());
        govPublicCarApply.setCompanyName(param.getLoginCompanyName());
        govPublicCarApplyService.save(govPublicCarApply);
        return applyNo;
    }

    private void buildCityInfo(GovCarCreateApplyReqDTO param, GovPublicCarApply govPublicCarApply) {
        if (StringUtils.isEmpty(param.getStartCityCode())) {
            AddressResult addressResult = new AddressResult();
            try {
                addressResult = baiduMapApiClient.pointToAddress(param.getEstimatedDepartureLatitude(), param.getEstimatedDepartureLongitude());
            } catch (Exception e) {
                log.info("百度坐标转换地理位置失败，错误信息：{}", e.getMessage());
            }
            GovCityDic cityDic;
            if (ObjectUtil.isNotNull(addressResult)) {
                //根据百度的citycode转化为mrcar自己的城市code
                cityDic = govCityDicService.getOne(new LambdaQueryWrapper<GovCityDic>().eq(GovCityDic::getBaiduCode, addressResult.getCityCode()).last("limit 1"));
                if (ObjectUtil.isNotNull(cityDic)) {
                    govPublicCarApply.setEstimatedDepartureCityCode(cityDic.getCityCode().toString());
                    govPublicCarApply.setEstimatedDepartureCityName(cityDic.getCityName());
                }
            }
            try {
                addressResult = baiduMapApiClient.pointToAddress(param.getEstimatedDestinationLatitude(), param.getEstimatedDestinationLongitude());
            } catch (Exception e) {
                log.info("百度坐标转换地理位置失败，错误信息：{}", e.getMessage());
            }
            if (ObjectUtil.isNotNull(addressResult)) {
                //根据百度的citycode转化为mrcar自己的城市code
                cityDic = govCityDicService.getOne(new LambdaQueryWrapper<GovCityDic>().eq(GovCityDic::getBaiduCode, addressResult.getCityCode()).last("limit 1"));
                if (ObjectUtil.isNotNull(cityDic)) {
                    govPublicCarApply.setEstimatedDestinationCityCode(cityDic.getCityCode().toString());
                    govPublicCarApply.setEstimatedDestinationCityName(cityDic.getCityName());
                }
            }
        } else {
            govPublicCarApply.setEstimatedDepartureCityCode(param.getStartCityCode());
            govPublicCarApply.setEstimatedDepartureCityName(param.getStartCityName());
            govPublicCarApply.setEstimatedDestinationCityCode(param.getEndCityCode());
            govPublicCarApply.setEstimatedDestinationCityName(param.getEndCityName());
        }
    }

    private void doApproval(GovCarCreateApplyReqDTO param, String applyNo) {
        ApplyStartDTO applyStartDTO = new ApplyStartDTO();
        if (ObjectUtil.equals(param.getApplyType(), GovPublicCarApplyTypeEnum.PUBLIC_CAR.getCode())) {
            applyStartDTO.setBusinessType(ModelEnum.BusinessTypeEnum.CAR_USE_APPROVAL.getCode());
        } else {
            applyStartDTO.setBusinessType(ModelEnum.BusinessTypeEnum.CAR_SOCIAL_RENT_APPROVAL.getCode());
        }
        applyStartDTO.setBusinessNo(applyNo);
        applyStartDTO.setLoginCompanyId(param.getLoginCompanyId());
        applyStartDTO.setLoginCompanyName(param.getLoginCompanyName());
        applyStartDTO.setLoginUserId(param.getLoginUserId());
        applyStartDTO.setLoginUserName(param.getLoginUserName());
        applyStartDTO.setLoginDeptId(param.getLoginUserBelongDeptId());
        applyStartDTO.setLoginDeptName(param.getLoginUserBelongDeptName());
        Map<String, Object> variables = new HashMap<>();
        if (StringUtils.isNotEmpty(param.getOrderUserMemo())) {
            variables.put("applyRemark", param.getOrderUserMemo());
        }
        applyStartDTO.setVariables(JSON.toJSONString(variables));
        String approveId = workflowApprovalService.startApproval(applyStartDTO);
        //更新订单上的审批id
        LambdaUpdateWrapper<GovPublicCarApply> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(GovPublicCarApply::getApplyNo, applyNo);
        lambdaUpdateWrapper.eq(GovPublicCarApply::getCompanyId, param.getLoginCompanyId());
        lambdaUpdateWrapper.set(GovPublicCarApply::getApprovalId, approveId);
        lambdaUpdateWrapper.set(GovPublicCarApply::getApprovalStatus, ApplyApprovalStatusEnum.PENDING.getCode());
        govPublicCarApplyService.update(lambdaUpdateWrapper);
    }

    /**
     * 审批是否激活
     */
    private Boolean workFlowIsActive(Integer loginCompanyId, Byte code, Integer loginDeptId) {
        ApplyStartSwitchDTO applyStartSwitchDTO = new ApplyStartSwitchDTO();
        applyStartSwitchDTO.setBusinessType(code);
        applyStartSwitchDTO.setLoginCompanyId(loginCompanyId);
        applyStartSwitchDTO.setLoginDeptId(loginDeptId);
        return workflowApprovalService.isApprovalActive(applyStartSwitchDTO);
    }
}
