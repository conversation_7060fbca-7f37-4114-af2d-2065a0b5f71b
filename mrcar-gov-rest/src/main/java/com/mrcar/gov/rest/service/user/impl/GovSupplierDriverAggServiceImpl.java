package com.mrcar.gov.rest.service.user.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.metadata.CellExtra;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.row.SimpleRowHeightStyleStrategy;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.izu.excel.annotation.ExportExcelWeb;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.file.storage.FileInfo;
import com.izu.file.storage.FileStorageService;
import com.izu.file.storage.UploadPretreatment;
import com.izu.framework.exception.ApiException;
import com.izu.framework.resp.InfoCode;
import com.izu.framework.response.PageDTO;
import com.izu.framework.web.util.BeanUtil;
import com.mrcar.gov.asset.domain.GovVehicleBaseInfo;
import com.mrcar.gov.asset.service.GovVehicleBaseInfoService;
import com.mrcar.gov.common.constant.RestErrorCode;
import com.mrcar.gov.common.constant.asset.VehicleSourceTypeEnum;
import com.mrcar.gov.common.constant.user.DriverEnum;
import com.mrcar.gov.common.constant.user.GovUserAddSourceEnum;
import com.mrcar.gov.common.constant.user.GovUserStatusEnum;
import com.mrcar.gov.common.constant.user.GovUserTypeEnum;
import com.mrcar.gov.common.dto.BaseDTO;
import com.mrcar.gov.common.dto.BatchImportReqDTO;
import com.mrcar.gov.common.dto.user.req.*;
import com.mrcar.gov.common.dto.user.resp.BatchDTO;
import com.mrcar.gov.common.dto.user.resp.GovDriverRespDTO;
import com.mrcar.gov.common.util.ExcelDownloadUtil;
import com.mrcar.gov.rest.service.user.GovDriverAggService;
import com.mrcar.gov.rest.service.user.GovSupplierDriverAggService;
import com.mrcar.gov.rest.util.excel.SelectDataSheetMergeWriteHandler;
import com.mrcar.gov.user.domain.GovDriver;
import com.mrcar.gov.user.domain.GovOrgInfo;
import com.mrcar.gov.user.domain.GovUser;
import com.mrcar.gov.user.service.GovDriverService;
import com.mrcar.gov.user.service.GovOrgInfoService;
import com.mrcar.gov.user.service.GovSupplierDriverService;
import com.mrcar.gov.user.service.GovUserService;
import com.mrcar.gov.user.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/12/27 15:57
 */
@Service
@Slf4j
public class GovSupplierDriverAggServiceImpl implements GovSupplierDriverAggService {


    @Resource
    private GovSupplierDriverService govSupplierDriverService;
    @Resource
    private GovUserService govUserService;
    @Resource
    private GovDriverAggService govDriverAggService;
    @Resource
    private GovOrgInfoService govOrgInfoService;

    @Resource
    private GovDriverService govDriverService;


    @Resource
    private GovVehicleBaseInfoService govVehicleBaseInfoService;


    @Resource
    private FileStorageService fileStorageService;


    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean saveDriver(GovSupplierDriverSaveOrEditReqDTO reqDTO) {
        GovDriver govDriver = this.govSupplierDriverService.saveOrEditDriver(reqDTO);
        GovUser govUser = govUserService.getOne(new LambdaQueryWrapper<GovUser>().eq(GovUser::getUserCode, govDriver.getUserCode()));
        if (Objects.nonNull(govUser)) {
            if (StringUtils.isNotEmpty(reqDTO.getVehicleCode())) {
                govDriverAggService.bindGovVehicleDriverRelation(govDriver.getUserCode(), govUser.getCompanyId(), reqDTO.getVehicleCode());
            } else {
                //解除绑定
                govDriverAggService.releaseGovVehicleDriverRelation(govDriver.getUserCode(), govUser.getCompanyId());
            }
        }
        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BatchDTO importExcel(BatchImportReqDTO reqDTO) {
        InputStream file = ExcelDownloadUtil.downloadFile(reqDTO.getExcelUrl());
//        InputStream file = null;
//        try {
//            file = new FileInputStream("/Users/<USER>/Downloads/车辆导入模板.xlsx");
//        } catch (FileNotFoundException e) {
//            throw new RuntimeException(e);
//        }
        //1. 读取excel 封装数据
        List<GovSupplierDriverSaveOrEditReqDTO> dataList = readExcelToDataList(file);
        // 2. 校验并保存合法数据，返回失败数据
        List<GovSupplierDriverSaveOrEditReqDTO> errorData = checkAndSaveDataReturnErrorData(dataList, reqDTO);
        //3.  写出失败的
        String errorFileUrl = writeFailData2Exel(errorData);

        // 4.返回结果
        BatchDTO batchDTO = new BatchDTO();
        batchDTO.setError(errorData.size());
        batchDTO.setTotal(dataList.size());
        batchDTO.setSuccess(dataList.size() - errorData.size());
        batchDTO.setDownloadUrl(errorFileUrl);
        return batchDTO;
    }

    @Override
    public void importExcelTemplate(BaseDTO baseDTO, HttpServletRequest request, HttpServletResponse response) {
        try {
            // 导出模板名称
            String fileName = "供应商司机录入模板";
            response.setContentType("application/msexcel");
            response.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码
            String fileNameEncode = URLEncoder.encode(fileName, "UTF-8");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileNameEncode + ExcelTypeEnum.XLSX.getValue());
            // 设置下拉框内容
            Map<Integer, List<String>> selectMap = buildSelectMap(baseDTO);
            //表头
            List<?> list = new ArrayList<>();
            EasyExcelFactory.write(response.getOutputStream())
                    // 设置字典
                    .registerWriteHandler(new SelectDataSheetMergeWriteHandler(selectMap))
                    // 设置行高度
                    .registerWriteHandler(new SimpleRowHeightStyleStrategy((short) 35, (short) 22))
                    // 此处对应的是实体类
                    .head(GovSupplierDriverExcelDTO.class)
                    // 设置导出格式为xls后缀
                    .excelType(ExcelTypeEnum.XLSX)
                    .sheet("供应商司机录入模板")
                    .doWrite(list);
        } catch (IOException e) {
            log.error("下载供应商司机录入模板异常", e);
            throw new ApiException(RestErrorCode.EXCEL_DOWNLOAD_ERROR);
        }

    }

    @Override
    @ExportExcelWeb(fileName = "供应商司机列表.xlsx", filePath = "/data/logs/mrcar-gov/tmp", sheet = "供应商司机列表",
            c = GovSupplierDriverExcelDTO.class, isImportant = false)
    public com.izu.framework.web.rest.response.PageDTO<GovSupplierDriverExcelDTO> exportExcel(GovSupplierDriverListReqDTO reqDTO, IzuEasyExcelSession izuEasyExcelSession, HttpServletRequest request, HttpServletResponse response) {
        reqDTO.setPage(izuEasyExcelSession.getPageNo());
        com.izu.framework.response.PageDTO<GovDriverRespDTO> page = listDriver(reqDTO);
        if (Objects.isNull(page) || CollectionUtils.isEmpty(page.getResult())) {
            return null;
        }
        List<GovDriverRespDTO> result = page.getResult();
        List<GovSupplierDriverExcelDTO> exportList = result.stream().map(item -> {
            GovSupplierDriverExcelDTO govSupplierDriverExcelDTO = new GovSupplierDriverExcelDTO();
            govSupplierDriverExcelDTO.setUserName(item.getUserName());
            govSupplierDriverExcelDTO.setMobile(item.getMobile());
            govSupplierDriverExcelDTO.setSupplierServiceName(item.getSupplierServiceName());
            govSupplierDriverExcelDTO.setSupplierOutletsName(item.getSupplierOutletsName());
            govSupplierDriverExcelDTO.setVehicleLicense(item.getVehicleLicense());
            govSupplierDriverExcelDTO.setGender(item.getGenderDesc());
            govSupplierDriverExcelDTO.setCertNo(item.getCertNo());
            if (item.getBrithDate() != null) {
                govSupplierDriverExcelDTO.setBrithDate(DateUtil.date2String(item.getBrithDate(), DateUtil.DATE_FORMAT));
            }
            govSupplierDriverExcelDTO.setPoliticalStatus(item.getPoliticalStatusDesc());
            govSupplierDriverExcelDTO.setEducationStatus(item.getEducationStatusDesc());
            govSupplierDriverExcelDTO.setEmail(item.getEmail());
            govSupplierDriverExcelDTO.setAddress(item.getAddress());
            govSupplierDriverExcelDTO.setLicenseType(item.getLicenseType());
            if (item.getFirstPickupTime() != null) {
                govSupplierDriverExcelDTO.setFirstPickupTime(DateUtil.date2String(item.getFirstPickupTime(), DateUtil.DATE_FORMAT));
            }
            if (item.getDriverLicenceValidityPeriod() != null) {
                govSupplierDriverExcelDTO.setDriverLicenceValidityPeriod(DateUtil.date2String(item.getDriverLicenceValidityPeriod(), DateUtil.DATE_FORMAT));
            }
            govSupplierDriverExcelDTO.setIssuingOrgan(item.getIssuingOrgan());

            if (item.getDriverADate() != null) {
                govSupplierDriverExcelDTO.setDriverADate(DateUtil.date2String(item.getDriverADate(), DateUtil.DATE_FORMAT));
            }
            govSupplierDriverExcelDTO.setRemark(item.getRemark());

            return govSupplierDriverExcelDTO;
        }).collect(Collectors.toList());
        return new com.izu.framework.web.rest.response.PageDTO<>(page.getPage(), page.getPageSize(), page.getTotal(), exportList);

    }

    private Map<Integer, List<String>> buildSelectMap(BaseDTO baseDTO) {
        Map<Integer, List<String>> selectMap = new HashMap<>();
        //  2. 租赁供应商

        Integer userType = baseDTO.getLoginUserType();
        List<GovOrgInfo> govOrgInfoList = Lists.newArrayList();
        if (GovUserTypeEnum.NORMAL.getCode().equals(userType) || GovUserTypeEnum.DRIVER.getCode().equals(userType) || GovUserTypeEnum.TECHNICAL_SUPPORT.getCode().equals(userType)) {
            // 当前用户= 政府员工或司机, 模板可选择所有供应商
            govOrgInfoList = govOrgInfoService.list(new LambdaQueryWrapper<GovOrgInfo>()
                    .eq(GovOrgInfo::getOrgStatus, 1)
                    .eq(GovOrgInfo::getOrgType, 4)
                    .select(GovOrgInfo::getOrgName));
        } else {
            // 当前用户= 供应商员工或司机, 模板可选择自己供应商
            String orgNo = baseDTO.getLoginOrgNo();
            govOrgInfoList = govOrgInfoService.list(new LambdaQueryWrapper<GovOrgInfo>()
                    .eq(Objects.nonNull(orgNo), GovOrgInfo::getOrgNo, orgNo)
                    .eq(GovOrgInfo::getOrgStatus, 1)
                    .eq(GovOrgInfo::getOrgType, 4)
                    .select(GovOrgInfo::getOrgName));
        }
        selectMap.put(2, govOrgInfoList.stream().map(GovOrgInfo::getOrgName).collect(Collectors.toList()));
        //  5 性别
        List<String> genderList = Lists.newArrayList("男", "女");
        selectMap.put(5, genderList);

        //8. 政治面貌
        List<String> politicalStatusList = Arrays.stream(DriverEnum.PoliticalStatusEnum.values()).map(DriverEnum.PoliticalStatusEnum::getDesc).collect(Collectors.toList());
        selectMap.put(8, politicalStatusList);
        //9. 文化程度
        List<String> educationStatusList = Arrays.stream(DriverEnum.EducationStatusEnum.values()).map(DriverEnum.EducationStatusEnum::getDesc).collect(Collectors.toList());
        selectMap.put(9, educationStatusList);
        // 12 准驾车型
        List<String> licenseTypeList = Lists.newArrayList("A1", "A2", "A3", "B1", "B2", "C1", "C2", "C3", "C4", "C5");
        selectMap.put(12, licenseTypeList);
        return selectMap;
    }

    private String writeFailData2Exel(List<GovSupplierDriverSaveOrEditReqDTO> errorData) {

        if (CollectionUtils.isEmpty(errorData)) {
            return "";
        }
        // 1.整理数据
        List<List<String>> errorList = Lists.newArrayList();
        for (GovSupplierDriverSaveOrEditReqDTO errorDatum : errorData) {
            List<String> error = Lists.newArrayList();
            String userName = Objects.nonNull(errorDatum.getUserName()) ? errorDatum.getUserName() : "";
            error.add(userName);
            String mobile = Objects.nonNull(errorDatum.getMobile()) ? errorDatum.getMobile() : "";
            error.add(mobile);
            String supplierServiceName = Objects.nonNull(errorDatum.getSupplierServiceName()) ? errorDatum.getSupplierServiceName() : "";
            error.add(supplierServiceName);
            String supplierOutletsName = Objects.nonNull(errorDatum.getSupplierOutletsName()) ? errorDatum.getSupplierOutletsName() : "";
            error.add(supplierOutletsName);
            String vehicleLicense = Objects.nonNull(errorDatum.getVehicleLicense()) ? errorDatum.getVehicleLicense() : "";
            error.add(vehicleLicense);
            String genderStr = Objects.nonNull(errorDatum.getGenderStr()) ? errorDatum.getGenderStr() : "";
            error.add(genderStr);
            String cerNo = Objects.nonNull(errorDatum.getCertNo()) ? errorDatum.getCertNo() : "";
            error.add(cerNo);
            String brithDateStr = Objects.nonNull(errorDatum.getBrithDateStr()) ? errorDatum.getBrithDateStr() : "";
            error.add(brithDateStr);

            String politicalStatusStr = Objects.nonNull(errorDatum.getPoliticalStatus()) ? DriverEnum.PoliticalStatusEnum.getDescByCode(errorDatum.getPoliticalStatus()) : "";
            error.add(politicalStatusStr);
            String educationStatusStr = Objects.nonNull(errorDatum.getEducationStatus()) ? DriverEnum.EducationStatusEnum.getDescByCode(errorDatum.getEducationStatus()) : "";
            error.add(educationStatusStr);
            String email = Objects.nonNull(errorDatum.getEmail()) ? errorDatum.getEmail() : "";
            error.add(email);

            String address = Objects.nonNull(errorDatum.getAddress()) ? errorDatum.getAddress() : "";
            error.add(address);
            String licenseType = Objects.nonNull(errorDatum.getLicenseType()) ? errorDatum.getLicenseType() : "";
            error.add(licenseType);

            String firstPickupTimedStr = Objects.nonNull(errorDatum.getFirstPickupTimedStr()) ? errorDatum.getFirstPickupTimedStr() : "";
            error.add(firstPickupTimedStr);
            String driverLicenceValidityPeriodStr = Objects.nonNull(errorDatum.getDriverLicenceValidityPeriodStr()) ? errorDatum.getDriverLicenceValidityPeriodStr() : "";
            error.add(driverLicenceValidityPeriodStr);

            String issuingOrgan = Objects.nonNull(errorDatum.getIssuingOrgan()) ? errorDatum.getIssuingOrgan() : "";
            error.add(issuingOrgan);

            String driverADateStr = Objects.nonNull(errorDatum.getDriverADateStr()) ? errorDatum.getDriverADateStr() : "";
            error.add(driverADateStr);

            String remark = Objects.nonNull(errorDatum.getRemark()) ? errorDatum.getRemark() : "";
            error.add(remark);

            String errMsg = Objects.nonNull(errorDatum.getRemark()) ? errorDatum.getErrMsg() : "";
            error.add(errMsg);

            errorList.add(error);
        }
        // 2.整理表头
        List<List<String>> headerList = Lists.newArrayList();
        headerList.add(Lists.newArrayList("*司机姓名"));
        headerList.add(Lists.newArrayList("*司机手机号"));
        headerList.add(Lists.newArrayList("*租赁供应商"));
        headerList.add(Lists.newArrayList("网点"));
        headerList.add(Lists.newArrayList("绑定车辆"));
        headerList.add(Lists.newArrayList("性别"));
        headerList.add(Lists.newArrayList("身份证号"));
        headerList.add(Lists.newArrayList("生日\nYYYY-MM-DD 或 YYYY/MM/DD"));
        headerList.add(Lists.newArrayList("政治面貌"));
        headerList.add(Lists.newArrayList("文化程度"));
        headerList.add(Lists.newArrayList("电子邮件"));
        headerList.add(Lists.newArrayList("详细地址"));
        headerList.add(Lists.newArrayList("准驾车型"));
        headerList.add(Lists.newArrayList("驾照初领日期\nYYYY-MM-DD 或 YYYY/MM/DD"));
        headerList.add(Lists.newArrayList("驾驶证有效日期\nYYYY-MM-DD 或 YYYY/MM/DD"));
        headerList.add(Lists.newArrayList("发证机关"));
        headerList.add(Lists.newArrayList("增驾A日期\nYYYY-MM-DD 或 YYYY/MM/DD"));
        headerList.add(Lists.newArrayList("备注"));
        headerList.add(Lists.newArrayList("错误原因"));

        String url = "";
        try (ByteArrayOutputStream byteOut = new ByteArrayOutputStream()) {
            ExcelWriter excelWriter = EasyExcel.write(byteOut).build();
            WriteSheet writeSheet = EasyExcel.writerSheet(0, "供应商驾驶员").build();
            writeSheet.setHead(headerList);
            excelWriter.write(errorList, writeSheet);
            excelWriter.finish();
            byteOut.close();
            String fileName = "驾驶员导入失败数据" + System.currentTimeMillis() + ExcelTypeEnum.XLSX.getValue();
            UploadPretreatment uploadPretreatment = fileStorageService.of(byteOut.toByteArray());
            uploadPretreatment.setSaveFilename(fileName);
            FileInfo upload = uploadPretreatment.upload();
            if (Objects.nonNull(upload)) {
                url = upload.getUrl();
            }

//            FileOutputStream outputStream = new FileOutputStream("/Users/<USER>/Downloads/" + fileName);
//            outputStream.write(byteOut.toByteArray());
//            outputStream.flush();
//            outputStream.close();
//            url = fileName;

        } catch (Exception e) {
            log.error("驾驶员导入失败数据文件生产异常", e);
            throw new ApiException(InfoCode.SYSTEM_EXCEPTION, "驾驶员导入失败数据文件生产异常");
        }
        return url;

    }


    private List<GovSupplierDriverSaveOrEditReqDTO> checkAndSaveDataReturnErrorData(List<GovSupplierDriverSaveOrEditReqDTO> dataList, BatchImportReqDTO reqDTO) {
        Set<String> mobileSet = dataList.stream()
                .map(GovSupplierDriverSaveOrEditReqDTO::getMobile)
                .filter(StringUtils::isNotEmpty).collect(Collectors.toSet());

        Set<String> certNoSet = dataList.stream()
                .map(GovSupplierDriverSaveOrEditReqDTO::getCertNo)
                .filter(StringUtils::isNotEmpty).collect(Collectors.toSet());


        // 租赁供应商
        Map<String, GovOrgInfo> supplierServiceNameCodeMap = new HashMap<>();
        Set<String> supplierServiceNameSet = dataList.stream()
                .map(GovSupplierDriverSaveOrEditReqDTO::getSupplierServiceName)
                .filter(StringUtils::isNotEmpty).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(supplierServiceNameSet)) {
            LambdaQueryWrapper<GovOrgInfo> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(GovOrgInfo::getCompanyId, reqDTO.getLoginCompanyId());
            wrapper.in(GovOrgInfo::getOrgName, supplierServiceNameSet);
            List<GovOrgInfo> list = govOrgInfoService.list(wrapper);
            if (CollectionUtils.isNotEmpty(list)) {
                list.forEach(govOrgInfo -> supplierServiceNameCodeMap.put(govOrgInfo.getOrgName(), govOrgInfo));
            }
        }

        // 车辆
        Map<String, Map<String, GovVehicleBaseInfo>> orgCodeVehicleMap = new HashMap<>();
        if (!supplierServiceNameCodeMap.isEmpty()) {
            Set<String> getOrgNoSet = supplierServiceNameCodeMap.values().stream().map(GovOrgInfo::getOrgNo).collect(Collectors.toSet());
            LambdaQueryWrapper<GovVehicleBaseInfo> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(GovVehicleBaseInfo::getSourceType, VehicleSourceTypeEnum.SOCIAL_VEHICLE.getCode());
            wrapper.in(GovVehicleBaseInfo::getSupplierServiceCode, getOrgNoSet);
            List<GovVehicleBaseInfo> list = govVehicleBaseInfoService.list(wrapper);
            for (GovVehicleBaseInfo govVehicleBaseInfo : list) {
                String supplierServiceCode = govVehicleBaseInfo.getSupplierServiceCode();
                if (orgCodeVehicleMap.containsKey(supplierServiceCode)) {
                    orgCodeVehicleMap.get(supplierServiceCode).put(govVehicleBaseInfo.getVehicleLicense(), govVehicleBaseInfo);
                } else {
                    Map<String, GovVehicleBaseInfo> vehicleMap = new HashMap<>();
                    vehicleMap.put(govVehicleBaseInfo.getVehicleLicense(), govVehicleBaseInfo);
                    orgCodeVehicleMap.put(supplierServiceCode, vehicleMap);
                }
            }
        }


        Map<String, GovUser> govUserMobileMap = new HashMap<>();
        Set<String> userCodeSet = new HashSet<>();
        if (CollectionUtils.isNotEmpty(mobileSet)) {
            List<GovUser> govUserList = this.govUserService.list(
                    new LambdaQueryWrapper<GovUser>()
                            .in(GovUser::getMobile, mobileSet)
                            .eq(GovUser::getUserStatus, GovUserStatusEnum.NORMAL.getCode()));
            if (CollectionUtils.isNotEmpty(govUserList)) {
                govUserList.forEach(govUser -> {
                    govUserMobileMap.put(govUser.getMobile(), govUser);
                    userCodeSet.add(govUser.getUserCode());
                });
            }
        }


        Map<String, GovDriver> govDriverCertNoMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(certNoSet)) {
            List<GovDriver> govDriverList = govDriverService.list(new LambdaQueryWrapper<GovDriver>()
                    .in(GovDriver::getCertNo, certNoSet)
                    .in(GovDriver::getOfficeStatus, Arrays.asList(DriverEnum.OfficeStatusEnum.NORMAL.getCode(), DriverEnum.OfficeStatusEnum.HAVE_HOLIDAY.getCode())));
            if (CollectionUtils.isNotEmpty(govDriverList)) {
                govDriverList.forEach(govUser -> govDriverCertNoMap.put(govUser.getCertNo(), govUser));
            }
        }


        Map<String, GovDriver> govDriverUserCodeMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(userCodeSet)) {
            List<GovDriver> driverList = govDriverService.list(new LambdaQueryWrapper<GovDriver>()
                    .in(GovDriver::getUserCode, userCodeSet)
                    .in(GovDriver::getOfficeStatus, Arrays.asList(DriverEnum.OfficeStatusEnum.NORMAL.getCode(), DriverEnum.OfficeStatusEnum.HAVE_HOLIDAY.getCode())));
            if (CollectionUtils.isNotEmpty(driverList)) {
                driverList.forEach(govUser -> govDriverUserCodeMap.put(govUser.getUserCode(), govUser));
            }
        }


        List<GovSupplierDriverSaveOrEditReqDTO> errorData = new ArrayList<>();
        List<GovSupplierDriverSaveOrEditReqDTO> successData = new ArrayList<>();


        Set<String> successMobileSet = new HashSet<>();

        for (GovSupplierDriverSaveOrEditReqDTO govSupplierDriver : dataList) {
            StringBuilder builder = new StringBuilder();
            // 校验司机姓名
            String userName = govSupplierDriver.getUserName();
            if (StringUtils.isEmpty(userName)) {
                builder.append("A.司机姓名不能为空\n");
            } else if (userName.length() > 20) {
                builder.append("A.司机姓名不能超过20个汉字\n");
            }

            // 校验手机号
            String mobile = govSupplierDriver.getMobile();
            String supplierServiceName = govSupplierDriver.getSupplierServiceName();
            if (StringUtils.isEmpty(mobile)) {
                builder.append("B.司机手机号不能为空\n");
            } else if (!mobile.matches("^1[3-9]\\d{9}$")) {
                builder.append("B.司机手机号不符合规则\n");
            } else if (govUserMobileMap.containsKey(mobile) &&
                    supplierServiceNameCodeMap.containsKey(supplierServiceName)
                    && govUserMobileMap.get(mobile).getSupplierServiceCode().equals(supplierServiceNameCodeMap.get(supplierServiceName).getOrgNo())) {
                builder.append("B.司机手机号已有本企业的启用账号\n");
            } else if (govUserMobileMap.containsKey(mobile) &&
                    supplierServiceNameCodeMap.containsKey(supplierServiceName)
                    && !govUserMobileMap.get(mobile).getSupplierServiceCode().equals(supplierServiceNameCodeMap.get(supplierServiceName).getOrgNo())) {
                builder.append("B.司机手机号已有其他企业下的启用账号,请联系运营核实后重新录入\n");
            } else if (successMobileSet.contains(mobile)) {
                builder.append("B.本次提交司机手机号重复\n");
            }
            //  备注:司机手机号已有本企业员工，直接加司机角色,覆盖更新员工姓名，不作为错淏阻骞

            // 租赁供应商

            if (StringUtils.isBlank(supplierServiceName)) {
                builder.append("C.租赁供应商不能为空\n");
            } else if (!supplierServiceNameCodeMap.containsKey(supplierServiceName)) {
                builder.append("C.租赁供应商不存在,请按照模板下拉选项导入\n");
            } else {
                // 赋值租赁供应商编码
                govSupplierDriver.setSupplierServiceCode(supplierServiceNameCodeMap.get(supplierServiceName).getOrgNo());
            }

            // 网点
            String supplierOutletsName = govSupplierDriver.getSupplierOutletsName();
            if (supplierOutletsName.length() > 50) {
                builder.append("D.网点信息最多50个字,请修改信息内容\n");
            }

            // 绑定车辆
            String vehicleLicense = govSupplierDriver.getVehicleLicense();
            String supplierServiceCode = govSupplierDriver.getSupplierServiceCode();
            if (StringUtils.isNotBlank(vehicleLicense) && StringUtils.isNotBlank(supplierServiceCode)) {
                Map<String, GovVehicleBaseInfo> vehicleLicenseMap = orgCodeVehicleMap.get(supplierServiceCode);
                if (vehicleLicenseMap != null) {
                    GovVehicleBaseInfo govVehicleBaseInfo = vehicleLicenseMap.get(vehicleLicense);
                    if (govVehicleBaseInfo == null) {
                        builder.append("E.供应商下不存在此车辆:车辆不存在,请重新修改车牌号\n");
                    }
//                    else if (!supplierServiceCode.equals(govVehicleBaseInfo.getSupplierServiceCode())) {
//                        builder.append("E.供应商下存在此车辆,但车辆所属供应商与司机供应商不一致:所填司机与车辆不属于同一家供应商,请修改车牌号\n");
//                    }
                    else {
                        govSupplierDriver.setVehicleCode(govVehicleBaseInfo.getVehicleNo());
                    }
                }
            }
            // 性别 F
            String genderStr = govSupplierDriver.getGenderStr();
            if ("男".equals(genderStr)) {
                govSupplierDriver.setGender(1);
            } else if ("女".equals(genderStr)) {
                govSupplierDriver.setGender(2);
            }

            // 校验 身份证号
            String certNo = govSupplierDriver.getCertNo();
            if (StringUtils.isNotBlank(certNo)
                    && !certNo.matches("(^[1-9]\\d{5}(18|19|20)\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])\\d{3}[0-9Xx]$)|(^[1-9]\\d{5}\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])\\d{3}$)")) {
                builder.append("G.身份证不符合规则,请修改后重新录入\n");
            }

            // 生日 H
            String brithDateStr = govSupplierDriver.getBrithDateStr();
            if (StringUtils.isNotBlank(brithDateStr)) {
                try {
                    if (brithDateStr.contains("-")) {
                        govSupplierDriver.setBrithDate(DateUtil.format2Date(brithDateStr, DateUtil.DATE_FORMAT));
                    } else if (brithDateStr.contains("/")) {
                        govSupplierDriver.setBrithDate(DateUtil.format2Date(brithDateStr, "yyyy/MM/dd"));
                    }
                } catch (Exception e) {
                    govSupplierDriver.setBrithDate(null);
                }
            }

            //政治面貌 I

            //文化程度  J

            //电子邮件 K
            String email = govSupplierDriver.getEmail();
            if (StringUtils.isNotBlank(email)) {
                if (!email.matches("^\\w+([-+.]\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*$")) {
                    builder.append("k.不合法的电子邮件,请重新输入");
                }
            }
            // 详细地址 L
            String address = govSupplierDriver.getAddress();
            if (StringUtils.isNotBlank(address) && address.length() > 30) {
                builder.append("L.详细地址最多30个字,请修改信息内容\n");
            }

            // 准驾车型 M
            String licenseType = govSupplierDriver.getLicenseType();
            if (StringUtils.isBlank(licenseType)) {
                //为空默认C1
                govSupplierDriver.setLicenseType("C1");
            }

            // 驾照初领日日期  N
            String firstPickupTimedStr = govSupplierDriver.getFirstPickupTimedStr();
            if (StringUtils.isNotBlank(firstPickupTimedStr)) {
                try {
                    if (firstPickupTimedStr.contains("-")) {
                        govSupplierDriver.setFirstPickupTime(DateUtil.format2Date(firstPickupTimedStr, DateUtil.DATE_FORMAT));
                    } else if (firstPickupTimedStr.contains("/")) {
                        govSupplierDriver.setFirstPickupTime(DateUtil.format2Date(firstPickupTimedStr, "yyyy/MM/dd"));
                    }
                } catch (Exception e) {
                    govSupplierDriver.setFirstPickupTime(null);
                }
            }

            // 驾驶证有效期 O
            String driverLicenceValidityPeriodStr = govSupplierDriver.getDriverLicenceValidityPeriodStr();
            if (StringUtils.isNotBlank(driverLicenceValidityPeriodStr)) {
                try {
                    if (driverLicenceValidityPeriodStr.contains("-")) {
                        govSupplierDriver.setDriverLicenceValidityPeriod(DateUtil.format2Date(driverLicenceValidityPeriodStr, DateUtil.DATE_FORMAT));
                    } else if (driverLicenceValidityPeriodStr.contains("/")) {
                        govSupplierDriver.setDriverLicenceValidityPeriod(DateUtil.format2Date(driverLicenceValidityPeriodStr, "yyyy/MM/dd"));
                    }
                } catch (Exception e) {
                    govSupplierDriver.setDriverLicenceValidityPeriod(null);
                }
            }

            // 发证机关  P  最大50汉字
            String issuingOrgan = govSupplierDriver.getIssuingOrgan();
            if (StringUtils.isNotBlank(issuingOrgan) && issuingOrgan.length() > 50) {
                builder.append("P.发证机关最多50个字,请修改信息内容\n");
            }

            // 增加A日期 Q
            String driverADateStr = govSupplierDriver.getDriverADateStr();
            if (StringUtils.isNotBlank(driverADateStr)) {
                try {
                    if (driverADateStr.contains("-")) {
                        govSupplierDriver.setDriverADate(DateUtil.format2Date(driverADateStr, DateUtil.DATE_FORMAT));
                    } else if (driverADateStr.contains("/")) {
                        govSupplierDriver.setDriverADate(DateUtil.format2Date(driverADateStr, "yyyy/MM/dd"));
                    }
                } catch (Exception e) {
                    govSupplierDriver.setDriverADate(null);
                }
            }
            // 备注 R
            String remark = govSupplierDriver.getRemark();
            if (StringUtils.isNotBlank(remark) && remark.length() > 100) {
                builder.append("R.备注最多100个字,请修改后重新上传\n");
            }

            // 有错误信息
            if (builder.length() > 0) {
                govSupplierDriver.setErrMsg(builder.toString());
                errorData.add(govSupplierDriver);
            } else {
                successMobileSet.add(govSupplierDriver.getMobile());
                successData.add(govSupplierDriver);
            }
        }

        // 保存
        if (CollectionUtils.isNotEmpty(successData)) {
            successData.stream().parallel().forEach(o -> {
                BeanUtils.copyProperties(reqDTO, o);
                govSupplierDriverService.saveOrEditDriver(o);
            });
        }
        return errorData;

    }


    /**
     * 读取excel 封装数据
     */
    private List<GovSupplierDriverSaveOrEditReqDTO> readExcelToDataList(InputStream file) {
        List<GovSupplierDriverSaveOrEditReqDTO> dataList = new ArrayList<>();
        EasyExcel.read(file, new ReadListener() {
            @Override
            public void onException(Exception e, AnalysisContext analysisContext) throws Exception {
            }

            @Override
            public void invoke(Object o, AnalysisContext analysisContext) {
                LinkedHashMap<Integer, Object> cellMap = (LinkedHashMap<Integer, Object>) o;
                GovSupplierDriverSaveOrEditReqDTO save = new GovSupplierDriverSaveOrEditReqDTO();
                int cellNum = 0;
                // 司机姓名
                Object cell = cellMap.get(cellNum++);
                String userName = cell == null ? "" : cell.toString().trim();
                save.setUserName(userName);

                // 司机手机号
                cell = cellMap.get(cellNum++);
                String mobile = cell == null ? "" : cell.toString().trim();
                save.setMobile(mobile);
                // 租赁供应商
                cell = cellMap.get(cellNum++);
                String supplierServiceName = cell == null ? "" : cell.toString().trim();
                save.setSupplierServiceName(supplierServiceName);

                //  供应商网点名称
                cell = cellMap.get(cellNum++);
                String supplierOutletsName = cell == null ? "" : cell.toString().trim();
                save.setSupplierOutletsName(supplierOutletsName);

                //  绑定车辆
                cell = cellMap.get(cellNum++);
                String vehicleLicense = cell == null ? "" : cell.toString().trim();
                save.setVehicleLicense(vehicleLicense);

                // 性别
                cell = cellMap.get(cellNum++);
                String genderStr = cell == null ? "" : cell.toString().trim();
                save.setGenderStr(genderStr);


                // 身份证号
                cell = cellMap.get(cellNum++);
                String certNo = cell == null ? "" : cell.toString().trim();
                save.setCertNo(certNo);


                //  出生日期；格式：yyyy-MM-dd、 yyyy/MM/dd
                cell = cellMap.get(cellNum++);
                String brithDateStr = cell == null ? "" : cell.toString().trim();
                save.setBrithDateStr(brithDateStr);

                //政治面貌
                cell = cellMap.get(cellNum++);
                String politicalStatusStr = cell == null ? "" : cell.toString().trim();
                save.setPoliticalStatus(DriverEnum.PoliticalStatusEnum.getCodeByDesc(politicalStatusStr));

                //文化程度
                cell = cellMap.get(cellNum++);
                String educationStatusStr = cell == null ? "" : cell.toString().trim();
                save.setEducationStatus(DriverEnum.EducationStatusEnum.getCodeByDesc(educationStatusStr));

                //电子邮件
                cell = cellMap.get(cellNum++);
                String email = cell == null ? "" : cell.toString().trim();
                save.setEmail(email);
                // 详细地址
                cell = cellMap.get(cellNum++);
                String address = cell == null ? "" : cell.toString().trim();
                save.setAddress(address);

                // 准驾类型；取值A1、A2、A3、B1、B2、C1、C2、C3、C4、C5
                cell = cellMap.get(cellNum++);
                String licenseType = cell == null ? "" : cell.toString().trim();
                save.setLicenseType(licenseType);

                //  初次领证日期；格式：yyyy-MM-dd、 yyyy/MM/dd
                cell = cellMap.get(cellNum++);
                String firstPickupTimeStr = cell == null ? "" : cell.toString().trim();
                save.setFirstPickupTimedStr(firstPickupTimeStr);

                //  司机驾驶证有效期；格式：yyyy-MM-dd、 yyyy/MM/dd
                cell = cellMap.get(cellNum++);
                String driverLicenceValidityPeriodStr = cell == null ? "" : cell.toString().trim();
                save.setDriverLicenceValidityPeriodStr(driverLicenceValidityPeriodStr);

                //发证机关
                cell = cellMap.get(cellNum++);
                String issuingOrgan = cell == null ? "" : cell.toString().trim();
                save.setIssuingOrgan(issuingOrgan);

                //增加A日期
                cell = cellMap.get(cellNum++);
                String driverADateStr = cell == null ? "" : cell.toString().trim();
                save.setDriverADateStr(driverADateStr);

                // 备注
                cell = cellMap.get(cellNum++);
                String remark = cell == null ? "" : cell.toString().trim();
                save.setRemark(remark);

                dataList.add(save);
            }

            @Override
            public void extra(CellExtra cellExtra, AnalysisContext analysisContext) {
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            }

            @Override
            public boolean hasNext(AnalysisContext analysisContext) {
                return true;
            }

            @Override
            public void invokeHead(Map map, AnalysisContext analysisContext) {
            }
        }).sheet(0).doRead();

        return dataList;
    }


    @Transactional(rollbackFor = Exception.class)
    public Boolean saveDriverV2(GovSupplierDriverSaveOrEditReqDTO reqDTO) {
        GovDriverEditRequestDTO govDriverEditRequestDTO = BeanUtil.copyObject(reqDTO, GovDriverEditRequestDTO.class);
        govDriverEditRequestDTO.setAddSource(GovUserAddSourceEnum.SUPPLIER_DRIVER_ADD);
        govDriverAggService.saveDriver(govDriverEditRequestDTO);

        return Boolean.TRUE;
    }


    @Override
    public GovDriverRespDTO getDriverDetail(GovSupplierDriverDetailReqDTO reqDTO) {
        GovDriverRespDTO respDTO = govDriverAggService.getDriverDetail(reqDTO.getDriverId());
        if (Objects.isNull(respDTO)) {
            return null;
        }
        if (StringUtils.isBlank(respDTO.getSupplierServiceCode())) {
            return respDTO;
        }
        GovOrgInfo govOrgInfo = govOrgInfoService.getOne(new LambdaQueryWrapper<GovOrgInfo>()
                .eq(GovOrgInfo::getOrgNo, respDTO.getSupplierServiceCode()).eq(GovOrgInfo::getCompanyId, respDTO.getCompanyId()));
        if (Objects.isNull(govOrgInfo)) {
            return respDTO;
        }
        respDTO.setSupplierServiceName(govOrgInfo.getOrgName());
        return respDTO;
//        GovSupplierDriverDetailRespDTO resp = BeanUtil.copyObject(respDTO, GovSupplierDriverDetailRespDTO.class);
//        GovUser govUser = govUserService.getOne(new LambdaQueryWrapper<GovUser>()
//                .eq(GovUser::getUserCode, respDTO.getUserCode()).eq(GovUser::getCompanyId, respDTO.getCompanyId()));
//        if(Objects.nonNull(govUser)){
//            resp.setSupplierOutletsCode(StrUtils.getOrDefaultEmpty(govUser.getSupplierOutletsCode()));
//            resp.setSupplierOutletsPoint(StrUtils.getOrDefaultEmpty(govUser.getSupplierOutletsPoint()));
//            resp.setSupplierOutletsName(StrUtils.getOrDefaultEmpty(govUser.getSupplierOutletsName()));
//            resp.setSupplierServiceCode(StrUtils.getOrDefaultEmpty(govUser.getSupplierServiceCode()));
//        }
//        return resp;
    }

    @Override
    public PageDTO<GovDriverRespDTO> listDriver(GovSupplierDriverListReqDTO reqDTO) {
        DriverQueryDTO driverQueryDTO = BeanUtil.copyObject(reqDTO, DriverQueryDTO.class);
        driverQueryDTO.setUserTypeList(Lists.newArrayList(GovUserTypeEnum.SOCIAL_DRIVER.getCode()));
        List<String> supplierCodeList = Lists.newArrayList();
        if (StringUtils.isNotBlank(reqDTO.getSupplierServiceCode())) {
            supplierCodeList.add(reqDTO.getSupplierServiceCode());
        }
        if (StringUtils.isNotBlank(reqDTO.getLoginOrgNo())) {
            supplierCodeList.add(reqDTO.getLoginOrgNo());
        }
        if (CollectionUtils.isNotEmpty(supplierCodeList)) {
            driverQueryDTO.setSupplierServiceCodeList(supplierCodeList);
        }
        return govDriverAggService.listSupplierDriver(driverQueryDTO);

    }


}
