package com.mrcar.gov.rest.service.config;

import com.mrcar.gov.common.dto.config.GovCompanyConfigItemValueDTO;
import com.mrcar.gov.common.dto.config.GovCompanyConfigItemValueListDTO;
import com.mrcar.gov.common.dto.config.req.GovCompanyConfigItemValueReqDTO;
import com.mrcar.gov.common.dto.config.req.GovCompanyConfigItemValueSaveBatchDTO;
import com.mrcar.gov.common.dto.config.req.GovCompanyConfigValueReqDTO;
import com.mrcar.gov.common.dto.config.GovPublicCategoryConfigDTO;
import com.mrcar.gov.config.domain.GovPublicBusinessConfig;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【gov_public_business_config(公共配置业务表)】的数据库操作Service
* @createDate 2025-07-07 10:44:45
*/
public interface GovPublicBusinessConfigService extends IService<GovPublicBusinessConfig> {

    List<GovPublicCategoryConfigDTO> getAllConfigItemAndValueByCompanyId(Integer companyId, String structCode);

    void updateCompanyConfigItemVue(GovCompanyConfigItemValueSaveBatchDTO req);

    List<GovCompanyConfigItemValueDTO> selectByDeptCodeAndBusinessCodeAndItemCode(Integer companyId,String structCode ,String businessConfigCode, String businessConfigItemCode);

    List<GovCompanyConfigItemValueListDTO> getConfigTable(Integer loginCompanyId, String structCode);

    Map<String, Object> getSourceInfo(Integer loginCompanyId, String structCode);

    void clearStructConfig(Integer loginCompanyId, String structCode);

    Map<String, Object> selectByStructCodeAndBusinessCode(GovCompanyConfigValueReqDTO req);
}
