package com.mrcar.gov.rest.service.bi.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.izu.framework.web.rest.response.PageDTO;
import com.mrcar.gov.asset.domain.GovVehicleBaseInfo;
import com.mrcar.gov.asset.service.GovGpsVehicleRelationService;
import com.mrcar.gov.asset.service.GovVehicleBaseInfoService;
import com.mrcar.gov.common.constant.asset.VehicleDeviceStatusEnum;
import com.mrcar.gov.common.constant.user.GovDataPermTypeEnum;
import com.mrcar.gov.common.dto.asset.relation.resp.GpsVehicleRelationListRespDTO;
import com.mrcar.gov.common.dto.asset.request.RealTimeDataQueryReqDTO;
import com.mrcar.gov.common.dto.asset.response.MonitorVehicleDTO;
import com.mrcar.gov.common.dto.asset.response.RealTimeVehicleDataRespDTO;
import com.mrcar.gov.common.dto.asset.response.VehicleDeviceStatusDTO;
import com.mrcar.gov.common.util.DateUtils;
import com.mrcar.gov.iot.service.GaodeMapService;
import com.mrcar.gov.iot.service.VehicleDeviceService;
import com.mrcar.gov.rest.service.asset.VehicleDeviceAggService;
import com.mrcar.gov.rest.service.bi.RealTimeMonitorService;
import com.mrcar.gov.common.constant.user.DriverEnum;
import com.mrcar.iot.domain.GovVehicleLocation;
import com.mrcar.iot.domain.GpsDeviceMeta;
import com.mrcar.thirdparty.baidu.AddressResult;
import com.mrcar.thirdparty.baidu.BaiduMapApiClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/12/16 20:13
 */
@Service
@Slf4j
public class RealTimeMonitorServiceImpl implements RealTimeMonitorService {

    private static final Logger logger = LoggerFactory.getLogger(RealTimeMonitorServiceImpl.class);

    @Resource
    private GovVehicleBaseInfoService govVehicleBaseInfoService;

    @Resource
    private VehicleDeviceAggService vehicleDeviceAggService;

    @Autowired
    private BaiduMapApiClient baiduMapApiClient;

    @Autowired
    private GaodeMapService gaodeMapService;

    @Autowired
    private VehicleDeviceService vehicleDeviceService;

    @Autowired
    private GovGpsVehicleRelationService govGpsVehicleRelationService;

    //临界速度
    @NacosValue(value = "${car.map.critical.speed:0}", autoRefreshed = true)
    private Integer CRITICAL_SPEED;

    // 定位轨迹页面，离线时间,超过这个时间，认为设备离线，单位：分钟
    @NacosValue("${location.page.offline.time:60}")
    private int offlineTime;

    @Override
    public RealTimeVehicleDataRespDTO queryMonitorVehicleData(RealTimeDataQueryReqDTO realTimeDataQueryReqDTO) {
        // 根据条件, 查询所有备选的车辆信息
        if (Objects.equals(realTimeDataQueryReqDTO.getLoginDataPermType(), GovDataPermTypeEnum.SELF.getCode())) {
            return new RealTimeVehicleDataRespDTO();
        }
        // 车辆类型、任务状态、车牌号、部门信息、所属公司
        LambdaQueryWrapper<GovVehicleBaseInfo> condition =
                new LambdaQueryWrapper<GovVehicleBaseInfo>()
                        .eq(Objects.nonNull(realTimeDataQueryReqDTO.getVehicleType()), GovVehicleBaseInfo::getVehicleType, realTimeDataQueryReqDTO.getVehicleType())
                        .eq(Objects.nonNull(realTimeDataQueryReqDTO.getWorkStatus()), GovVehicleBaseInfo::getWorkStatus, realTimeDataQueryReqDTO.getWorkStatus())
                        .eq(StringUtils.isNotBlank(realTimeDataQueryReqDTO.getVehicleLicense()), GovVehicleBaseInfo::getVehicleLicense, realTimeDataQueryReqDTO.getVehicleLicense())
                        .in(GovVehicleBaseInfo::getVehicleBelongDeptCode, realTimeDataQueryReqDTO.getDeptCodes())
                        .eq(GovVehicleBaseInfo::getCompanyId, realTimeDataQueryReqDTO.getLoginCompanyId());
        // 指定部门查询权限
        if (Objects.equals(realTimeDataQueryReqDTO.getLoginDataPermType(), GovDataPermTypeEnum.ASSIGN_STRUCT.getCode())) {
            condition.and((wrapper) ->
                    wrapper.in(GovVehicleBaseInfo::getVehicleBelongDeptCode, realTimeDataQueryReqDTO.getDataCodeSet())
                            .or().in(GovVehicleBaseInfo::getVehicleUseDeptCode, realTimeDataQueryReqDTO.getDataCodeSet())
                            .or().in(GovVehicleBaseInfo::getVehicleUseStructCode, realTimeDataQueryReqDTO.getDataCodeSet()));
        }
        // 拼接结果
        RealTimeVehicleDataRespDTO result = new RealTimeVehicleDataRespDTO();
        // 统计字段
        int totalCount = 0;
        int taskCount = 0;
        int noTaskCount = 0;
        int onlineCount = 0;
        int offlineCount = 0;
        int unbindCount = 0;
        int drivingCount = 0;
        int stopCount = 0;
        // 经纬度最大最小范围
        BigDecimal latMax = null;
        BigDecimal latMin = null;
        BigDecimal lngMax = null;
        BigDecimal lngMin = null;
        // 统计
        long timer1 = System.currentTimeMillis();
        // 查询车辆
        List<GovVehicleBaseInfo> vehicles = govVehicleBaseInfoService.list(condition);
        logger.info("查询车辆信息, cost : {}ms", (System.currentTimeMillis() - timer1));
        // 需要批量获取车辆信息
        List<List<GovVehicleBaseInfo>> partitions = ListUtils.partition(vehicles, 50);
        // 以车的纬度, 获取最新的定位数据
        Map<String, VehicleStatusResult> stats = new HashMap<>();

        for (List<GovVehicleBaseInfo> partition : partitions) {

            long timer2 = System.currentTimeMillis();
            // 查询绑定关系
            List<GpsVehicleRelationListRespDTO> devices =
                    govGpsVehicleRelationService.getCurrentGovGpsVehicleRelationListByVehicleNo(
                            partition.stream().map(GovVehicleBaseInfo::getVehicleNo).collect(Collectors.toList()));
            logger.info("查询设备绑定关系, cost : {}ms", (System.currentTimeMillis() - timer2));

            // 按车辆分组
            Map<String, List<GpsVehicleRelationListRespDTO>> deviceMap =
                    devices.stream().collect(Collectors.groupingBy(GpsVehicleRelationListRespDTO::getVehicleNo));

            long timer3 = System.currentTimeMillis();
            // 查询最新点位
            List<GovVehicleLocation> locations =
                    vehicleDeviceService.getLatestLocations(
                            devices.stream().map(GpsVehicleRelationListRespDTO::getDeviceSysNo).collect(Collectors.toList()));
            Map<String, GovVehicleLocation> locationMap =
                    locations.stream().collect(
                            Collectors.toMap(s -> s.getDeviceMeta().getDeviceSysNo(), Function.identity(),
                                    (v1, v2) -> v1.getTimestamp().after(v2.getTimestamp()) ? v1 : v2));
            logger.info("查询设备点位信息, cost : {}ms", (System.currentTimeMillis() - timer3));

            for (GovVehicleBaseInfo vehicle : partition) {
                VehicleStatusResult stat = new VehicleStatusResult();
                stat.vehicleNo = vehicle.getVehicleNo(); // 车辆编号
                // 车辆绑定的所有设备信息
                List<GpsVehicleRelationListRespDTO> bindDevices = deviceMap.get(vehicle.getVehicleNo());
                if (bindDevices == null || bindDevices.isEmpty()) {
                    // 未绑定
                    stat.status = VehicleDeviceStatusEnum.UNBIND.getCode();
                    stats.put(vehicle.getVehicleNo(), stat);
                    continue;
                }
                List<String> bindDeviceSysNos = bindDevices.stream()
                        .map(GpsVehicleRelationListRespDTO::getDeviceSysNo).collect(Collectors.toList());
                // 查询对应的最新点位
                List<GovVehicleLocation> vehicleLatestLocations = new ArrayList<>();
                for (String bindDeviceSysNo : bindDeviceSysNos) {
                    GovVehicleLocation location = locationMap.get(bindDeviceSysNo);
                    if (Objects.nonNull(location) &&
                            Objects.equals(location.getDeviceMeta().getVehicleNo(), vehicle.getVehicleNo())) {
                        vehicleLatestLocations.add(location);
                    }
                }
                // 车辆绑定的所有设备的最新点位
                GovVehicleLocation latestLocation = vehicleLatestLocations.stream()
                        .max(Comparator.comparing(s -> s.getTimestamp().getTime()))
                        .orElse(null);
                // 设备状态
                stat.status = checkVehicleOnlineStatus(latestLocation);
                // 设备最新点位
                if (latestLocation != null) {
                    stat.latitude = latestLocation.getLatitude();
                    stat.longitude = latestLocation.getLongitude();
                    stat.speed = latestLocation.getSpeed();
                    stat.timestamp = latestLocation.getTimestamp();
                }
                stats.put(vehicle.getVehicleNo(), stat);
            }
        }

        // 统计指标计数
        for (GovVehicleBaseInfo vehicle : vehicles) {

            VehicleStatusResult stat = stats.get(vehicle.getVehicleNo());

            // 对车辆状态进行筛选
            if (Objects.nonNull(realTimeDataQueryReqDTO.getDeviceStatus())
                    && !Objects.equals(stat.status, realTimeDataQueryReqDTO.getDeviceStatus())) {
                continue;
            }

            // 总车辆数
            totalCount += 1;
            // 任务统计
            if (Objects.equals(vehicle.getWorkStatus(),
                    DriverEnum.WorkingStatusEnum.HAVING_WORKING.getCode())) {
                taskCount += 1;
            } else {
                noTaskCount += 1;
            }
            // 在线统计
            if (Objects.equals(stat.status, VehicleDeviceStatusEnum.ONLINE.getCode())) {
                onlineCount += 1;
                // 速度统计
                if (Objects.nonNull(stat.speed) && stat.speed > CRITICAL_SPEED) {
                    drivingCount += 1;
                } else {
                    stopCount += 1;
                }
            } else if (Objects.equals(stat.status, VehicleDeviceStatusEnum.OFFLINE.getCode())) {
                offlineCount += 1;
            } else {
                unbindCount += 1;
            }

            // 经纬度范围
            if (Objects.nonNull(stat.timestamp)) {
                // 纬度
                if (latMin == null) {
                    latMin = stat.latitude;
                } else {
                    latMin = latMin.min(stat.latitude);
                }
                if (latMax == null) {
                    latMax = stat.latitude;
                } else {
                    latMax = latMax.max(stat.latitude);
                }
                // 经度
                if (lngMin == null) {
                    lngMin = stat.longitude;
                } else {
                    lngMin = lngMin.min(stat.longitude);
                }
                if (lngMax == null) {
                    lngMax = stat.longitude;
                } else {
                    lngMax = lngMax.max(stat.longitude);
                }
            }
        }

        result.setVehicleSum((long) totalCount);
        result.setTaskVehicleSum((long) taskCount);
        result.setNoTaskVehicleSum((long) noTaskCount);
        result.setOnlineVehicleSum((long) onlineCount);
        result.setOfflineVehicleSum((long) offlineCount);
        result.setUnbindVehicleSum((long) unbindCount);
        result.setDrivingVehicleSum((long) drivingCount);
        result.setStopVehicleSum((long) stopCount);

        result.setLatMax(latMax);
        result.setLatMin(latMin);
        result.setLngMax(lngMax);
        result.setLngMin(lngMin);

        return result;
    }

    // 车辆最新统计结果
    static class VehicleStatusResult {
        // 车辆编号
        String vehicleNo;
        // 上报时间
        Date timestamp;
        // 经纬度信息
        BigDecimal longitude;
        BigDecimal latitude;
        // 车速
        Integer speed;
        // 车辆设备状态
        Integer status;
    }

    // 判断车辆在线状态
    private Integer checkVehicleOnlineStatus(GovVehicleLocation location) {
        if (location == null) {
            return VehicleDeviceStatusEnum.OFFLINE.getCode();
        }
        VehicleDeviceStatusEnum status =
                (System.currentTimeMillis() - location.getTimestamp().getTime() > (long) this.offlineTime * 60 * 1000) ?
                        VehicleDeviceStatusEnum.OFFLINE : VehicleDeviceStatusEnum.ONLINE;
        return status.getCode();
    }


    @Override
    public MonitorVehicleDTO queryMonitorVehicleLocation(String vehicleNo) {

        LambdaQueryWrapper<GovVehicleBaseInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(GovVehicleBaseInfo::getVehicleNo, vehicleNo);
        GovVehicleBaseInfo govVehicleBaseInfo = govVehicleBaseInfoService.getOne(queryWrapper);
        Optional<List<GovVehicleLocation>> govVehicleLocations = Optional.ofNullable(vehicleDeviceAggService.batchQueryVehicleLocation(Lists.newArrayList(vehicleNo)));
        //设置定位信息

        MonitorVehicleDTO monitorVehicleDTO = new MonitorVehicleDTO();
        monitorVehicleDTO.setVehicleNo(govVehicleBaseInfo.getVehicleNo());
        monitorVehicleDTO.setVehicleLicense(govVehicleBaseInfo.getVehicleLicense());
        monitorVehicleDTO.setVehicleVin(govVehicleBaseInfo.getVehicleVin());
        monitorVehicleDTO.setVehicleBelongDeptCode(govVehicleBaseInfo.getVehicleBelongDeptCode());

        if (govVehicleLocations.isPresent()) {
            List<GovVehicleLocation> govVehicleLocationList = govVehicleLocations.orElse(Lists.newArrayList());
            if (CollUtil.isNotEmpty(govVehicleLocationList)&&Objects.nonNull(govVehicleLocationList.get(0))) {
                GovVehicleLocation govVehicleLocation = govVehicleLocationList.get(0);
                Integer deviceStatus = vehicleDeviceAggService.getDeviceStatus(govVehicleLocation.getTimestamp().getTime());
                monitorVehicleDTO.setVehicleOnlineStatus(deviceStatus);
                monitorVehicleDTO.setVehicleOnlineStatusName(VehicleDeviceStatusEnum.getDeviceStatusDesc(deviceStatus));
                monitorVehicleDTO.setSpeed(govVehicleLocation.getSpeed());
                monitorVehicleDTO.setLastReportTime(DateUtils.format(govVehicleLocation.getTimestamp(), DateUtils.TIME_FORMAT));
                monitorVehicleDTO.setLongitude(govVehicleLocation.getLongitude());
                monitorVehicleDTO.setLatitude(govVehicleLocation.getLatitude());
                monitorVehicleDTO.setLngBaidu(govVehicleLocation.getLngBaidu());
                monitorVehicleDTO.setLatBaidu(govVehicleLocation.getLatBaidu());
                monitorVehicleDTO.setDirection(govVehicleLocation.getDirection());
                GpsDeviceMeta deviceMeta = govVehicleLocation.getDeviceMeta();
                if (deviceMeta != null) {
                    monitorVehicleDTO.setDeviceSysNo(deviceMeta.getDeviceSysNo());
                    monitorVehicleDTO.setDeviceNo(deviceMeta.getDeviceNo());
                    monitorVehicleDTO.setDeviceType(deviceMeta.getDeviceType());
                    monitorVehicleDTO.setSimNo(deviceMeta.getSimNo());
                }

                try {
                    // 统一, 采用高德地图Map
                    String address = gaodeMapService.reverseGeocodingForAddress(
                            govVehicleLocation.getLongitude().toPlainString() + "," + govVehicleLocation.getLatitude().toPlainString());
                    monitorVehicleDTO.setLastLocation(address);
                } catch (Exception e) {
                    log.info("queryMonitorVehicleLocation-高德坐标转换地理位置失败，错误信息：{}", e.getMessage());
                }
            }

        }

        return monitorVehicleDTO;
    }

    private RealTimeVehicleDataRespDTO buildVehicleStatics(List<GovVehicleBaseInfo> govVehicleBaseInfos) {
        RealTimeVehicleDataRespDTO realTimeVehicleDataRespDTO = new RealTimeVehicleDataRespDTO();
        realTimeVehicleDataRespDTO.setVehicleSum(Long.valueOf(govVehicleBaseInfos.size()));
        long taskCount = govVehicleBaseInfos.stream().filter(vehicle -> Objects.equals(vehicle.getWorkStatus(), DriverEnum.WorkingStatusEnum.HAVING_WORKING.getCode())).count();
        realTimeVehicleDataRespDTO.setTaskVehicleSum(taskCount);
        long noTaskCount = govVehicleBaseInfos.stream().filter(vehicle -> Objects.equals(vehicle.getWorkStatus(), DriverEnum.WorkingStatusEnum.HAVING_NO_WORKING.getCode())).count();
        realTimeVehicleDataRespDTO.setNoTaskVehicleSum(noTaskCount);
        List<VehicleDeviceStatusDTO> vehicleOnOfflineUnbindStatusList = vehicleDeviceAggService.getVehicleOnOfflineUnbindStatus(govVehicleBaseInfos);
        long onlineCount = vehicleOnOfflineUnbindStatusList.stream().filter(vehicleDeviceStatusDTO -> Objects.equals(vehicleDeviceStatusDTO.getDeviceStatus(), VehicleDeviceStatusEnum.ONLINE.getCode())).count();
        realTimeVehicleDataRespDTO.setOnlineVehicleSum(onlineCount);
        long offlineCount = vehicleOnOfflineUnbindStatusList.stream().filter(vehicleDeviceStatusDTO -> Objects.equals(vehicleDeviceStatusDTO.getDeviceStatus(), VehicleDeviceStatusEnum.OFFLINE.getCode())).count();
        realTimeVehicleDataRespDTO.setOfflineVehicleSum(offlineCount);
        long unbindCount = vehicleOnOfflineUnbindStatusList.stream().filter(vehicleDeviceStatusDTO -> Objects.equals(vehicleDeviceStatusDTO.getDeviceStatus(), VehicleDeviceStatusEnum.UNBIND.getCode())).count();
        realTimeVehicleDataRespDTO.setUnbindVehicleSum(unbindCount);
        //行驶车辆和停车车辆
        long drivingCount = vehicleOnOfflineUnbindStatusList.stream().filter(vehicleDeviceStatusDTO -> Objects.equals(vehicleDeviceStatusDTO.getDeviceStatus(), VehicleDeviceStatusEnum.ONLINE.getCode()))
                .filter(vehicleDeviceStatusDTO -> vehicleDeviceStatusDTO.getSpeed()!=null&&vehicleDeviceStatusDTO.getSpeed()>CRITICAL_SPEED).count();
        realTimeVehicleDataRespDTO.setDrivingVehicleSum(drivingCount);
        long stopCount = vehicleOnOfflineUnbindStatusList.stream()
                .filter(vehicleDeviceStatusDTO -> vehicleDeviceStatusDTO.getSpeed()!=null&&vehicleDeviceStatusDTO.getSpeed()<=CRITICAL_SPEED).count();
        realTimeVehicleDataRespDTO.setStopVehicleSum(stopCount);
        return realTimeVehicleDataRespDTO;

    }

    private PageDTO<MonitorVehicleDTO> buildVehicleLocation(List<GovVehicleBaseInfo> govVehicleBaseInfos) {
        PageDTO<MonitorVehicleDTO> pageDTO = new PageDTO<>();

        return pageDTO;
    }
}
