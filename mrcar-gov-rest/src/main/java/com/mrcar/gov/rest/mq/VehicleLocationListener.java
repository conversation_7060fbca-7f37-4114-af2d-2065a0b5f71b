package com.mrcar.gov.rest.mq;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson2.JSON;
import com.izu.cache.core.redis.RedisCache;
import com.izu.framework.web.util.RandomStringUtil;
import com.izu.mq.core.MQProducer;
import com.mrcar.gov.asset.domain.GovGpsDevice;
import com.mrcar.gov.asset.service.GovGpsDeviceService;
import com.mrcar.gov.asset.service.GovVehicleBaseInfoService;
import com.mrcar.gov.bi.service.GovVehicleMileageStatService;
import com.mrcar.gov.common.dto.asset.response.VehicleInfoDTO;
import com.mrcar.gov.common.dto.device.VehicleGpsLocation;
import com.mrcar.gov.model.mqtt.mq.MqttTopic;
import com.mrcar.gov.model.mqtt.mq.VehicleLocationMqDTO;
import com.mrcar.iot.domain.GovVehicleLocation;
import com.mrcar.iot.domain.GpsDeviceMeta;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.MessageModel;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Objects;

import static com.mrcar.gov.common.constant.device.DeviceConstant.MQ_TOPIC_GPS_POSITION;
import static com.mrcar.gov.common.constant.device.DeviceConstant.REDIS_KEY_VEHICLE_LATEST_POSITION;

/**
 * 车辆实时定位监听器.
 *
 * <AUTHOR>
 * @date 2024-11-19
 */
@Component
@RocketMQMessageListener(
        topic = MqttTopic.TOPIC_VEHICLE_LOCATION_TOPIC,
        consumerGroup = "vehicle_location_group",
        consumeMode = ConsumeMode.CONCURRENTLY,
        messageModel = MessageModel.CLUSTERING,
        nameServer = "mrcar")
public class VehicleLocationListener implements RocketMQListener<String> {

    private static final Logger logger = LoggerFactory.getLogger(VehicleLocationListener.class);

    @Autowired
    private MQProducer producer;
    @Autowired
    private GovGpsDeviceService deviceService;
    @Autowired
    private GovVehicleBaseInfoService vehicleBaseInfoService;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private RedisCache cache;
    @Autowired
    private GovVehicleMileageStatService mileageStatService;

    @Override
    public void onMessage(String message) {
        MDC.put("reqId", RandomStringUtil.genRandomString(8));
        try {
            logger.info("定位消息:{}", message);
            // 车辆定位
            VehicleLocationMqDTO location = JSON.parseObject(message, VehicleLocationMqDTO.class);
            // 查询设备信息
            GovGpsDevice device = deviceService.getBySimNoForMqFromCache(location.getSimNo());
            if (Objects.isNull(device)) {
                logger.error("定位消息, 设备未找到, sim : {}", location.getSimNo());
                return;
            }

            // 查询车辆信息
            VehicleInfoDTO vehicleInfoDTO = vehicleBaseInfoService.quickGetVehicleInfo(device.getVehicleNo());

            if (Objects.isNull(vehicleInfoDTO)) {
                logger.error("定位消息, 车辆未找到, sim : {}", location.getSimNo());
                return;
            }

            // 保存点位信息
            GovVehicleLocation row = new GovVehicleLocation();
            // 时间戳
            row.setTimestamp(location.getCreateDate());
            // 元信息
            GpsDeviceMeta meta = new GpsDeviceMeta();
            meta.setDeviceSysNo(device.getDeviceSysNo());
            meta.setDeviceNo(device.getDeviceNo());
            meta.setSimNo(device.getSimNo());
            meta.setDeviceType(device.getDeviceType());
            meta.setVehicleNo(device.getVehicleNo());
            meta.setVehicleVin(vehicleInfoDTO.getVehicleVin());
            meta.setVehicleLicense(vehicleInfoDTO.getVehicleLicense());
            meta.setCompanyId(vehicleInfoDTO.getCompanyId());
            row.setDeviceMeta(meta);
            // 其他属性
            BeanUtils.copyProperties(location, row);
            mongoTemplate.insert(row);

            // 设置缓存
            String cacheValue = this.cache.getCacheObject(String.format(REDIS_KEY_VEHICLE_LATEST_POSITION, device.getDeviceSysNo()));
            if (StringUtils.isEmpty(cacheValue)) {
                logger.info("定位消息 simNo:{} 缓存中无数据", device.getSimNo());
                // 设置缓存
                this.cache.setCacheObject(String.format(REDIS_KEY_VEHICLE_LATEST_POSITION, device.getDeviceSysNo()), JSON.toJSONString(row));
            } else {
                    GovVehicleLocation govVehicleLocation = JSON.parseObject(cacheValue, GovVehicleLocation.class);
                    if (Objects.isNull(govVehicleLocation)) {
                        // 设置缓存
                        this.cache.setCacheObject(String.format(REDIS_KEY_VEHICLE_LATEST_POSITION, device.getDeviceSysNo()), JSON.toJSONString(row));
                    } else {
                        logger.info("定位消息 simNo:{} 缓存定位时间:{},最新时间:{}", device.getSimNo(),
                                DateUtil.format(govVehicleLocation.getTimestamp(), "YYYY-MM-dd HH:mm:ss"),
                                DateUtil.format(row.getTimestamp(), "YYYY-MM-dd HH:mm:ss"));
                        if (govVehicleLocation.getTimestamp().before(row.getTimestamp()) && !row.getTimestamp().after(new Date())) {
                            // 设置缓存
                            this.cache.setCacheObject(String.format(REDIS_KEY_VEHICLE_LATEST_POSITION, device.getDeviceSysNo()), JSON.toJSONString(row));
                            // 动态更新里程信息
                            this.mileageStatService.accumulateUpdateDeviceMileage(govVehicleLocation, row);
                        }
                    }
            }

            // 转围栏所用对象
            VehicleGpsLocation record = new VehicleGpsLocation();
            record.setCarNo(vehicleInfoDTO.getVehicleLicense());
            record.setDeviceId(device.getSimNo());
            record.setSimNo(device.getSimNo());
            record.setVehicleVin(vehicleInfoDTO.getVehicleVin());
            record.setManfactCode(String.valueOf(device.getManufactId()));
            record.setDeviceType(String.valueOf(device.getDeviceType()));
            record.setLongitude(location.getLongitude());
            record.setLatitude(location.getLatitude());
            record.setLngBaidu(location.getLngBaidu());
            record.setLatBaidu(location.getLatBaidu());
            record.setSpeed(String.valueOf(location.getSpeed()));
            record.setDirection(String.valueOf(location.getDirection()));
            record.setCreateDate(location.getCreateDate());
            record.setVehicleNo(device.getVehicleNo());
            record.setVehicleType(vehicleInfoDTO.getVehicleType());

            producer.publishMessage(MQ_TOPIC_GPS_POSITION, null, device.getDeviceSysNo(), JSON.toJSONString(record));

        } finally {
            MDC.remove("reqId");
        }
    }
}
