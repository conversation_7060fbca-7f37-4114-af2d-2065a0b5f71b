package com.mrcar.gov.rest.service.business.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.izu.framework.response.PageDTO;
import com.izu.framework.web.util.BeanUtil;
import com.mrcar.gov.base.service.SequenceGenerator;
import com.mrcar.gov.business.domain.GovMsgNotice;
import com.mrcar.gov.business.domain.GovMsgNoticeAttachment;
import com.mrcar.gov.business.domain.GovMsgPushLog;
import com.mrcar.gov.business.domain.GovMsgPushRecord;
import com.mrcar.gov.business.domain.SimpleGovMsgNoticeAttachment;
import com.mrcar.gov.business.service.GovMsgNoticeAttachmentService;
import com.mrcar.gov.business.service.GovMsgNoticeService;
import com.mrcar.gov.business.service.GovMsgPushRecordService;
import com.mrcar.gov.common.constant.business.GovMsgNoticePublishDeptModelEnum;
import com.mrcar.gov.common.constant.business.GovMsgNoticeStatusEnum;
import com.mrcar.gov.common.constant.business.GovMsgPushChannelEnum;
import com.mrcar.gov.common.constant.business.GovMsgPushLogStatusEnum;
import com.mrcar.gov.common.constant.business.GovMsgPushRecordStatusEnum;
import com.mrcar.gov.common.constant.business.GovMsgPushStatusEnum;
import com.mrcar.gov.common.constant.business.GovMsgPushTypeEnum;
import com.mrcar.gov.common.constant.business.GovMsgReachStatusEnum;
import com.mrcar.gov.common.constant.business.GovMsgReadStatusEnum;
import com.mrcar.gov.common.constant.business.GovMsgTypeEnum;
import com.mrcar.gov.common.constant.user.GovStructStatusEnum;
import com.mrcar.gov.common.constant.user.GovUserStatusEnum;
import com.mrcar.gov.common.dto.business.request.GovMsgNoticeDetailReqDTO;
import com.mrcar.gov.common.dto.business.request.GovMsgNoticeListReqDTO;
import com.mrcar.gov.common.dto.business.request.GovMsgNoticePublishReqDTO;
import com.mrcar.gov.common.dto.business.response.GovMsgNoticeDetailRespDTO;
import com.mrcar.gov.common.dto.business.response.GovMsgNoticeListDTO;
import com.mrcar.gov.common.dto.user.req.GovStructRequestDTO;
import com.mrcar.gov.common.dto.user.resp.GovStructRespondDTO;
import com.mrcar.gov.common.dto.user.resp.GovStructSimpleDTO;
import com.mrcar.gov.common.thread.ThreadPoolConfig;
import com.mrcar.gov.rest.service.business.GovMsgNoticeAggService;
import com.mrcar.gov.base.common.SeqPrefix;
import com.mrcar.gov.user.domain.GovStruct;
import com.mrcar.gov.user.domain.GovUser;
import com.mrcar.gov.user.service.GovStructService;
import com.mrcar.gov.user.service.GovUserService;
import org.apache.commons.collections.CollectionUtils;
import org.jsoup.Jsoup;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.util.HtmlUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/12/28 08:37
 */
@Service
public class GovMsgNoticeAggServiceImpl implements GovMsgNoticeAggService {

    private static final String SYSTEM_DEPT_CODE = "0";
    private static final String SYSTEM_DEPT_NAME = "系统";

    @Resource
    private GovMsgNoticeService govMsgNoticeService;
    @Resource
    private GovUserService govUserService;
    @Resource
    private GovStructService govStructService;



    @Override
    public GovMsgNoticeDetailRespDTO detail(GovMsgNoticeDetailReqDTO reqDTO) {
        GovMsgNoticeDetailRespDTO respDTO = govMsgNoticeService.detail(reqDTO);
        if (Objects.isNull(respDTO)) {
            return null;
        }
        if (Objects.nonNull(respDTO.getPushDeptCodeList())) {
            respDTO.setStructList(assignStructProcess(new HashSet<>(respDTO.getPushDeptCodeList()), reqDTO.getLoginCompanyId(), null));
        }
        if (Objects.equals(respDTO.getPublishDeptCode(), SYSTEM_DEPT_CODE)){
            respDTO.setPublishDeptName(SYSTEM_DEPT_NAME);
        }
        fillDeptName(respDTO);
        return respDTO;
    }

    private void fillDeptName(GovMsgNoticeDetailRespDTO respDTO) {
        GovStruct govStruct = govStructService.getOne(new LambdaQueryWrapper<GovStruct>()
                .eq(GovStruct::getCompanyId, respDTO.getCompanyId()).eq(GovStruct::getStructCode, respDTO.getPublishDeptCode()));
        if (Objects.nonNull(govStruct)) {
            respDTO.setPublishDeptName(govStruct.getStructName());
        }
    }

    private List<GovStructSimpleDTO> assignStructProcess(Set<String> relationCodeSet, Integer companyId, Integer status) {
        // 查询所有部门信息
        GovStructRequestDTO govStructRequestDTO = new GovStructRequestDTO();
        govStructRequestDTO.setLoginCompanyId(companyId);
        List<GovStructRespondDTO> govStructRespondDTOS = govStructService.getGovStructTree(govStructRequestDTO);
        if (CollectionUtils.isEmpty(govStructRespondDTOS)) {
            return Lists.newArrayList();
        }
        return buildAssignStructList(govStructRespondDTOS, relationCodeSet, status);
    }

    private List<GovStructSimpleDTO> buildAssignStructList(List<GovStructRespondDTO> govStructRespondDTOS, Set<String> relationCodeSet, Integer status) {
        if (CollectionUtils.isEmpty(govStructRespondDTOS)) {
            return Lists.newArrayList();
        }
        List<GovStructSimpleDTO> dataList = new ArrayList<>(govStructRespondDTOS.size());
        for (GovStructRespondDTO govStructRespondDTO : govStructRespondDTOS) {
            if (Objects.nonNull(status) && !Objects.equals(govStructRespondDTO.getStatus(), status)) {
                continue;
            }
            GovStructSimpleDTO govStructSimple = BeanUtil.copyObject(govStructRespondDTO, GovStructSimpleDTO.class);
            govStructSimple.setChecked(relationCodeSet.contains(govStructRespondDTO.getStructCode()));

            List<GovStructSimpleDTO> structSimpleList = buildAssignStructList(govStructRespondDTO.getSubGovStructList(), relationCodeSet, status);
            govStructSimple.setSubGovStructList(structSimpleList);
            dataList.add(govStructSimple);
        }
        return dataList;
    }


    @Override
    public PageDTO<GovMsgNoticeListDTO> listNotice(GovMsgNoticeListReqDTO reqDTO) {
        PageDTO<GovMsgNoticeListDTO> pageDTO = govMsgNoticeService.listNotice(reqDTO);
        if(CollectionUtils.isEmpty(pageDTO.getResult())){
            return pageDTO;
        }
        List<GovMsgNoticeListDTO> noticeList = pageDTO.getResult();
        fillListUserName(noticeList, reqDTO.getLoginCompanyId());
        fillListDeptName(noticeList, reqDTO.getLoginCompanyId());
        noticeList.forEach(notice -> {
            int pushNum = 0;
            if(Objects.nonNull(notice.getPushNum())){
                pushNum = notice.getPushNum();
            }
            int readNum = 0;
            if(Objects.nonNull(notice.getReadNum())){
                readNum = notice.getReadNum();
            }
            notice.setUnReadNum(pushNum - readNum);
            if(pushNum > 0){
                BigDecimal reaRatio = new BigDecimal(readNum).
                        divide(new BigDecimal(pushNum), 10, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP);
                notice.setReadRatio(reaRatio.toString() + "%");
            }
        });
        return pageDTO;
    }
    // 填充列表部门名称
    private void fillListDeptName(List<GovMsgNoticeListDTO> noticeList, Integer companyId) {
        Set<String> deptCodeSet = noticeList.stream().map(GovMsgNoticeListDTO::getPublishDeptCode).collect(Collectors.toSet());
        List<GovStruct> govStructList = govStructService.list(
                new LambdaQueryWrapper<GovStruct>().in(GovStruct::getStructCode, deptCodeSet).eq(GovStruct::getCompanyId, companyId));
        Map<String, GovStruct> structMap = Maps.newHashMap();
        if(CollectionUtils.isNotEmpty(govStructList)){
            structMap.putAll(govStructList.stream().collect(Collectors.toMap(GovStruct::getStructCode, Function.identity(), (s1, s2) -> s1)));
        }
        noticeList.forEach(notice -> {
            GovStruct struct = structMap.get(notice.getPublishDeptCode());
            if(Objects.isNull(struct)){
                return;
            }
            notice.setPublishDeptName(struct.getStructName());
        });
    }
    // 填充列表用户名称
    private void fillListUserName(List<GovMsgNoticeListDTO> noticeList, Integer companyId) {
        Set<String> userCodeSet = noticeList.stream().map(GovMsgNoticeListDTO::getPublisherCode).collect(Collectors.toSet());
        List<GovUser> govUserList = govUserService.list(
                new LambdaQueryWrapper<GovUser>().in(GovUser::getUserCode, userCodeSet).eq(GovUser::getCompanyId, companyId));
        Map<String, GovUser> userMap = Maps.newHashMap();
        if(CollectionUtils.isNotEmpty(govUserList)){
            userMap.putAll(govUserList.stream().collect(Collectors.toMap(GovUser::getUserCode, Function.identity(), (s1, s2) -> s1)));
        }
        noticeList.forEach(notice -> {
            GovUser user = userMap.get(notice.getPublisherCode());
            if(Objects.isNull(user)){
                return;
            }
            notice.setPublisherName(user.getUserName());
        });
    }

    @Resource
    private MongoTemplate mongoTemplate;
    @Resource
    private SequenceGenerator sequenceGenerator;

    @Resource
    private GovMsgPushRecordService govMsgPushRecordService;
    @Resource
    private GovMsgNoticeAttachmentService govMsgNoticeAttachmentService;

    @Override
    public Boolean publish(GovMsgNoticePublishReqDTO reqDTO) {
        // 直接发布，如果没有保存，这里还要有保存效果
        Boolean result = govMsgNoticeService.publish(reqDTO);
        // 进行公告推送。先同步推送，后需改为异步.后续优化 TODO
        GovMsgNotice govMsgNotice = govMsgNoticeService.getOne(new LambdaQueryWrapper<GovMsgNotice>()
                .eq(GovMsgNotice::getNoticeNo, reqDTO.getNoticeNo())
                .eq(GovMsgNotice::getCompanyId, reqDTO.getLoginCompanyId()));
        Object pushDeptCodeListObj = govMsgNotice.getPushDeptCodeList();
        List<String> pushDeptCodeList = Lists.newArrayList();
        if(Objects.equals(govMsgNotice.getPushDeptModel(), GovMsgNoticePublishDeptModelEnum.ALL.getCode())){
            List<GovStruct> govStructs = govStructService.list(new LambdaQueryWrapper<GovStruct>().eq(GovStruct::getCompanyId, reqDTO.getLoginCompanyId())
                    .eq(GovStruct::getStatus, GovStructStatusEnum.NORMAL.getCode()).select(GovStruct::getStructCode));
            if(CollectionUtils.isNotEmpty(govStructs)){
                pushDeptCodeList.addAll(govStructs.stream().map(GovStruct::getStructCode).collect(Collectors.toList()));
            }
        }else{
            pushDeptCodeList.addAll(JSON.parseArray(pushDeptCodeListObj.toString(), String.class));
        }
        if(CollectionUtils.isEmpty(pushDeptCodeList)){
            return true;
        }
        String batchNo = sequenceGenerator.generate(new Date(), SeqPrefix.GOV_MSG_PUSH_BATCH_NO_PREFIX);
        List<GovMsgPushRecord> govMsgPushRecordList = new ArrayList<>();
        GovMsgPushRecord govMsgPushRecord = new GovMsgPushRecord();
        String recordNo = sequenceGenerator.generate(new Date(), SeqPrefix.GOV_MSG_PUSH_RECORD_NO_PREFIX);
        govMsgPushRecord.setRecordNo(recordNo);
        govMsgPushRecord.setCompanyId(reqDTO.getLoginCompanyId());
        govMsgPushRecord.setBatchNo(batchNo);
        govMsgPushRecord.setMsgType(GovMsgTypeEnum.NOTICE.getCode());
        govMsgPushRecord.setMsgModule(govMsgNotice.getMsgModule());
        govMsgPushRecord.setMsgNo(govMsgNotice.getNoticeNo());
        govMsgPushRecord.setMsgName(govMsgNotice.getNoticeName());
        govMsgPushRecord.setPushStartTime(new Date());
        govMsgPushRecord.setRecordStatus(GovMsgPushRecordStatusEnum.NORMAL.getCode());
        govMsgPushRecord.setPushType(GovMsgPushTypeEnum.IN_SITE.getCode());
        govMsgPushRecord.setPushChannel(GovMsgPushChannelEnum.PC.getCode());
        govMsgPushRecord.setPushStatus(GovMsgPushStatusEnum.PUSHING.getCode());
        govMsgPushRecord.setCreateCode(reqDTO.getLoginUserCode());
        govMsgPushRecord.setCreateTime(new Date());
        govMsgPushRecord.setCreateName(reqDTO.getLoginUserName());

        govMsgPushRecordList.add(govMsgPushRecord);
        govMsgPushRecordService.saveBatch(govMsgPushRecordList);
        govMsgNoticeService.update(new LambdaUpdateWrapper<GovMsgNotice>().eq(GovMsgNotice::getNoticeNo, govMsgNotice.getNoticeNo())
                .eq(GovMsgNotice::getCompanyId, reqDTO.getLoginCompanyId())
                .set(GovMsgNotice::getNoticeStatus, GovMsgNoticeStatusEnum.PUSHING.getCode())
                .set(GovMsgNotice::getUpdateCode, reqDTO.getLoginUserCode())
                .set(GovMsgNotice::getUpdateTime, new Date()));

        ThreadPoolConfig.getMsgCenterThreadPool().submit(() -> {
            senNoticeMsg(reqDTO, govMsgNotice, recordNo, batchNo, pushDeptCodeList);
        });

        return true;
    }

    private void senNoticeMsg(GovMsgNoticePublishReqDTO reqDTO,
                              GovMsgNotice govMsgNotice,
                              String recordNo,
                              String batchNo,
                              List<String> pushDeptCodeList){

        List<GovMsgNoticeAttachment> attachmentList = govMsgNoticeAttachmentService.list(new LambdaQueryWrapper<GovMsgNoticeAttachment>()
                .eq(GovMsgNoticeAttachment::getNoticeNo, reqDTO.getNoticeNo())
                .eq(GovMsgNoticeAttachment::getCompanyId, reqDTO.getLoginCompanyId()));

        List<SimpleGovMsgNoticeAttachment> simpleAttachmentList = BeanUtil.copyList(attachmentList, SimpleGovMsgNoticeAttachment.class);

        // 分页查询下面的人员
        LambdaQueryWrapper<GovUser> userQryWrapper = new LambdaQueryWrapper<>();
        userQryWrapper.and(wrapper -> wrapper.in(
                        GovUser::getBelongDeptCode, pushDeptCodeList).or().in(GovUser::getBelongStructCode, pushDeptCodeList))
                .eq(GovUser::getUserStatus, GovUserStatusEnum.NORMAL.getCode())
                .eq(GovUser::getCompanyId, reqDTO.getLoginCompanyId());
        // 每个端生成一条记录
        int page = 1;
        int pageSize = 100;
        int total = 0;
        while(true){
            IPage<GovUser> userPage = govUserService.getBaseMapper().selectPage(new Page<GovUser>(page, pageSize), userQryWrapper);
            if(Objects.isNull(userPage) || CollectionUtils.isEmpty(userPage.getRecords())){
                return;
            }
            total = (int)userPage.getTotal();
            List<GovUser> govUsers = userPage.getRecords();
            List<GovMsgPushLog> govMsgPushLogList = govUsers.stream().map(govUser -> {
                GovMsgPushLog pushLog = new GovMsgPushLog();
                pushLog.setPublishDeptCode(reqDTO.getLoginUserBelongDeptCode());
                pushLog.setPublishDeptName(reqDTO.getLoginUserBelongDeptName());
                pushLog.setMsgModule(govMsgNotice.getMsgModule());
                pushLog.setCompanyId(reqDTO.getLoginCompanyId());
                pushLog.setLogNo(sequenceGenerator.generate(new Date(), SeqPrefix.GOV_MSG_PUSH_LOG_NO_PREFIX));
                pushLog.setRecordNo(recordNo);
                pushLog.setBatchNo(batchNo);
                pushLog.setMsgType(GovMsgTypeEnum.NOTICE.getCode());
                pushLog.setMsgNo(govMsgNotice.getNoticeNo());
                pushLog.setMsgName(govMsgNotice.getNoticeName());
                pushLog.setReceiverCode(govUser.getUserCode());
                pushLog.setReceiverName(govUser.getUserName());
                pushLog.setReceiverMobile(govUser.getMobile());
                pushLog.setPushStartTime(new Date());
                /**
                 * 当前场景，推送结束时间先和 开始时间设置为一致，后续如果走websocket，则需要修改
                 */
                pushLog.setPushEndTime(new Date());
                pushLog.setLogStatus(GovMsgPushLogStatusEnum.NORMAL.getCode());
                pushLog.setPushType(GovMsgPushTypeEnum.IN_SITE.getCode());
                pushLog.setPushChannel(GovMsgPushChannelEnum.PC.getCode());
                pushLog.setReadStatus(GovMsgReadStatusEnum.UNREAD.getCode());
                //站内信，直接触达
                pushLog.setReachStatus(GovMsgReachStatusEnum.REACHED.getCode());
                pushLog.setPushStatus(GovMsgPushStatusEnum.SUCCESS.getCode());
                pushLog.setCreateCode(reqDTO.getLoginUserCode());
                pushLog.setCreateName(reqDTO.getLoginUserName());
                pushLog.setCreateTime(new Date());
                // 这里需要处理掉格式
                pushLog.setMsgContent(HtmlUtils.htmlEscape(govMsgNotice.getNoticeContent()));
                if(CollectionUtils.isNotEmpty(simpleAttachmentList)){
                    pushLog.setAttachmentUrlList(simpleAttachmentList);
                }
                return pushLog;
            }).collect(Collectors.toList());
            // 向log表插入数据
            mongoTemplate.insertAll(govMsgPushLogList);
            govMsgPushRecordService.update(new LambdaUpdateWrapper<GovMsgPushRecord>()
                    .eq(GovMsgPushRecord::getRecordNo, recordNo)
                    .eq(GovMsgPushRecord::getCompanyId, reqDTO.getLoginCompanyId())
                    .set(GovMsgPushRecord::getPushNum, total)
                    .set(GovMsgPushRecord::getUpdateCode, reqDTO.getLoginUserCode())
                    .set(GovMsgPushRecord::getUpdateTime, new Date())
                    .set(GovMsgPushRecord::getUpdateName, reqDTO.getLoginUserName())
                    .setSql("reach_num=reach_num + " + govMsgPushLogList.size() )
                    .setSql("read_num=read_num + " + 0 ));

            if(govUsers.size() < pageSize){
                break;
            }
            page++;
        }
        govMsgPushRecordService.update(new LambdaUpdateWrapper<GovMsgPushRecord>().eq(GovMsgPushRecord::getRecordNo, recordNo)
                .eq(GovMsgPushRecord::getCompanyId, reqDTO.getLoginCompanyId())
                .set(GovMsgPushRecord::getPushStatus, GovMsgPushStatusEnum.SUCCESS.getCode())
                .set(GovMsgPushRecord::getUpdateName, reqDTO.getLoginUserName())
                .set(GovMsgPushRecord::getUpdateCode, reqDTO.getLoginUserCode())
                .set(GovMsgPushRecord::getUpdateTime, new Date()));
        govMsgNoticeService.update(new LambdaUpdateWrapper<GovMsgNotice>().eq(GovMsgNotice::getNoticeNo, govMsgNotice.getNoticeNo())
                .eq(GovMsgNotice::getCompanyId, reqDTO.getLoginCompanyId())
                .set(GovMsgNotice::getNoticeStatus, GovMsgNoticeStatusEnum.PUSHED.getCode())
                .set(GovMsgNotice::getUpdateCode, reqDTO.getLoginUserCode())
                .set(GovMsgNotice::getUpdateTime, new Date()));
    }
}
