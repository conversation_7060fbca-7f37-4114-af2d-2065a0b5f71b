package com.mrcar.gov.rest.controller.workflow;

import com.izu.framework.resp.RestResp;
import com.izu.framework.response.PageDTO;
import com.mrcar.gov.common.dto.workflow.StringIdDTO;
import com.mrcar.gov.common.dto.workflow.enums.ModelEnum;
import com.mrcar.gov.common.dto.workflow.process.BpmProcessDefinitionListReqDTO;
import com.mrcar.gov.common.dto.workflow.process.BpmProcessDefinitionPageItemRespDTO;
import com.mrcar.gov.common.dto.workflow.process.BpmProcessDefinitionPageReqDTO;
import com.mrcar.gov.common.dto.workflow.process.BpmProcessDefinitionRespDTO;
import com.mrcar.gov.workflow.service.impl.BpmProcessDefinitionService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 工作流-流程定义
 * <p>
 * 该控制器提供了与流程定义相关的接口，包括流程定义的分页、列表获取以及 BPMN XML 的获取。
 */
@RestController
@RequestMapping("/bpm/process-definition")
@Validated
public class BpmProcessDefinitionController {

    @Resource
    private BpmProcessDefinitionService bpmDefinitionService;

    /**
     * 获得流程定义分页
     *
     * @param pageReqVO 流程定义分页请求参数
     * @return 分页结果
     */
    @PostMapping("/page")
    public RestResp<PageDTO<BpmProcessDefinitionPageItemRespDTO>> getProcessDefinitionPage(@RequestBody BpmProcessDefinitionPageReqDTO pageReqVO) {
        return RestResp.ok(bpmDefinitionService.getProcessDefinitionPage(pageReqVO));
    }

    /**
     * 获得流程定义列表
     *
     * @param listReqVO 流程定义列表请求参数
     * @return 流程定义列表
     */
    @PostMapping("/list")
    public RestResp<List<BpmProcessDefinitionRespDTO>> getProcessDefinitionList(@RequestBody BpmProcessDefinitionListReqDTO listReqVO) {
        // PC后台发起流程只允许发起OA类型流程
        listReqVO.setCategory(ModelEnum.CategoryEnum.OA.getCode());
        return RestResp.ok(bpmDefinitionService.getProcessDefinitionList(listReqVO));
    }

    /**
     * 获得流程定义的 BPMN XML
     *
     * @param idDTO 流程定义 ID 数据传输对象
     * @return 流程定义的 BPMN XML 字符串
     */
    @PostMapping("/get-bpmn-xml")
    public RestResp<String> getProcessDefinitionBpmnXML(@RequestBody StringIdDTO idDTO) {
        return RestResp.ok(bpmDefinitionService.getProcessDefinitionBpmnXML(idDTO.getId()));
    }

}
