## \u8BBE\u7F6E\u670D\u52A1\u5668\u7AEF\u53E3
server.port=8080

spring.profiles.active=${env.name}

spring.application.name=mrcar-rest

# support circular reference
spring.main.allow-circular-references=true



spring.servlet.multipart.enabled=true
spring.servlet.multipart.max-file-size=100MB
spring.servlet.multipart.max-request-size=200MB



nacos.config.bootstrap.enable=true
nacos.config.auto-refresh=true
nacos.config.max-retry=10
nacos.config.context-path=/

nacos.config.data-id=mrcar-gov-rest.properties
nacos.config.group=mrcar-gov-rest

nacos.config.server-addr=${izunacos.serverAddress}
nacos.config.username=${izunacos.username}
nacos.config.password=${izunacos.password}
nacos.config.namespace=${izunacos.namespace}

nacos.config.ext-config[0].data-ids=mrcar-gov-jdbc.properties
nacos.config.ext-config[0].group=mysql
nacos.config.ext-config[0].type=properties
nacos.config.ext-config[0].auto-refresh=true

nacos.config.ext-config[1].data-ids=mrcar-gov-mq.properties
nacos.config.ext-config[1].group=mq
nacos.config.ext-config[1].type=properties
nacos.config.ext-config[1].auto-refresh=true

nacos.config.ext-config[2].data-ids=mrcar-gov-redis.properties
nacos.config.ext-config[2].group=redis
nacos.config.ext-config[2].type=properties
nacos.config.ext-config[2].auto-refresh=true

nacos.config.ext-config[3].data-ids=mrcar-gov-mongo.properties
nacos.config.ext-config[3].group=mongo
nacos.config.ext-config[3].type=properties
nacos.config.ext-config[3].auto-refresh=true

nacos.config.ext-config[4].data-ids=mrcar-gov-third.properties
nacos.config.ext-config[4].group=thirdparty
nacos.config.ext-config[4].type=properties
nacos.config.ext-config[4].auto-refresh=true

nacos.config.ext-config[5].data-id=gov_job.properties
nacos.config.ext-config[5].group=job
nacos.config.ext-config[5].type=properties
nacos.config.ext-config[5].auto-refresh=true



## set global insert or update strategy
mybatis-plus.global-config.db-config.update-strategy=not_empty



# ??????
izu.file-storage.enable=true
izu.file-storage.default-platform=aliyun-oss
izu.file-storage.thumbnail-suffix=.min.jpg

# \u5DE5\u4F5C\u6D41 Flowable \u914D\u7F6E
# \u8BBE\u7F6E\u4E3A false\uFF0C\u53EF\u901A\u8FC7 https://github.com/flowable/flowable-sql \u521D\u59CB\u5316
flowable.database-schema-update=false
# flowable6 \u9ED8\u8BA4 true \u751F\u6210\u4FE1\u606F\u8868\uFF0C\u65E0\u9700\u624B\u52A8\u8BBE\u7F6E
flowable.db-history-used=true
# \u8BBE\u7F6E\u4E3A false\uFF0C\u7981\u7528 /resources/processes \u81EA\u52A8\u90E8\u7F72 BPMN XML \u6D41\u7A0B
flowable.check-process-definitions=false
# full\uFF1A\u4FDD\u5B58\u5386\u53F2\u6570\u636E\u7684\u6700\u9AD8\u7EA7\u522B\uFF0C\u53EF\u4FDD\u5B58\u5168\u90E8\u6D41\u7A0B\u76F8\u5173\u7EC6\u8282\uFF0C\u5305\u62EC\u6D41\u7A0B\u6D41\u8F6C\u5404\u8282\u70B9\u53C2\u6570
flowable.history-level=full

# \u662F\u5426\u5F00\u542F\u8BF7\u6C42\u65E5\u5FD7
izu.framework.request.log=true




