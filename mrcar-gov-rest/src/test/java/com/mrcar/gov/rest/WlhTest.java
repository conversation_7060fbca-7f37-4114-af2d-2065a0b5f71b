
package com.mrcar.gov.rest;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.izu.framework.resp.RestResp;
import com.izu.framework.response.PageDTO;
import com.mrcar.gov.common.dto.BaseDTO;
import com.mrcar.gov.common.dto.BatchImportReqDTO;
import com.mrcar.gov.common.dto.BatchImportResultDTO;
import com.mrcar.gov.common.dto.apply.req.OrderNoReqDTO;
import com.mrcar.gov.common.dto.bi.BIHomeBaseDTO;
import com.mrcar.gov.common.dto.bi.resp.CitySummaryRespDTO;
import com.mrcar.gov.common.dto.order.req.GovSocTimeShareBillQueryReq;
import com.mrcar.gov.common.dto.order.resp.GovSocTimeShareDailyListRespDTO;
import com.mrcar.gov.common.dto.session.*;
import com.mrcar.gov.common.dto.user.req.DriverQueryDTO;
import com.mrcar.gov.common.dto.user.req.GovDriverImportReqDTO;
import com.mrcar.gov.common.dto.user.req.RoleUserSimpleRequestDTO;
import com.mrcar.gov.common.dto.user.resp.BatchDTO;
import com.mrcar.gov.common.dto.user.resp.GovDriverRespDTO;
import com.mrcar.gov.rest.controller.apply.GovOrderController;
import com.mrcar.gov.rest.controller.bi.HomeDashboardController;
import com.mrcar.gov.rest.controller.order.GovSocTimeShareDailyBillController;
import  com.mrcar.gov.rest.controller.user.GovDriverController;
import com.mrcar.gov.rest.controller.user.GovSupplierDriverController;
import com.mrcar.gov.rest.service.user.impl.GovSupplierDriverAggServiceImpl;
import com.mrcar.gov.rest.util.SessionUtil;
import com.mrcar.gov.user.service.GovUserRoleRelationService;
import com.mrcar.gov.user.service.UserAccountLoginService;
import liquibase.logging.LogService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.junit.Test;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Objects;


@Slf4j
public class WlhTest extends BaseTest {

    @Autowired
    private HomeDashboardController homeDashboardController;

    @Autowired
    private GovSupplierDriverController govSupplierDriverController;

    @Autowired
    private UserAccountLoginService loginService;
    @Autowired
    GovDriverController govDriverController;

    @Autowired
    GovSupplierDriverAggServiceImpl govSupplierDriverAggServiceImpl;

    @Test
    public void test() {
        String json="{ \"belongDept\":\"\", \"belongStruct\":\"\", \"belongStructId\":217, \"dataCodeSet\":[ \"1\" ], \"licenseType\":\"\", \"loginCompanyId\":1, \"loginCompanyName\":\"四川省\", \"loginDataPermType\":1, \"loginUserBelongDeptCode\":\"STR2411170002204\", \"loginUserBelongDeptId\":3, \"loginUserBelongDeptName\":\"省政府\", \"loginUserBelongStructCode\":\"STR2505140002029\", \"loginUserBelongStructName\":\"省政府下设部门AA\", \"loginUserCode\":\"USR2411250014996\", \"loginUserId\":117, \"loginUserMobile\":\"***********\", \"loginUserName\":\"牛子联\", \"loginUserType\":99, \"mobile\":\"\", \"page\":1, \"pageSize\":10, \"selectedBelongDept\":\"SC00001\", \"userName\":\"\" }";

        DriverQueryDTO batchImportReqDTO = JSONObject.parseObject(json, DriverQueryDTO.class);
        RestResp<PageDTO<GovDriverRespDTO>> driverList = govDriverController.getDriverList(batchImportReqDTO);
        System.out.println(JSON.toJSONString(driverList));
    }

    @Test
    public void test2() {
        RoleUserSimpleRequestDTO roleUserSimple = new RoleUserSimpleRequestDTO();
        roleUserSimple.setRoleCode("gov_order_dispatcher");
    }




    /**
     * 填充通用 BaseDTO 的基本信息。
     *
     * @param baseDTO     通用 BaseDTO 对象
     * @param sessionInfo 用户会话信息
     */
    private void fillBaseInfo(BaseDTO baseDTO, GovUserSessionInfoDTO sessionInfo) {
        if (Objects.nonNull(sessionInfo.getBaseInfo())) {
            AccountBaseInfo account = sessionInfo.getBaseInfo();
            baseDTO.setLoginUserId(account.getUserId());
            baseDTO.setLoginUserName(account.getUserName());
            baseDTO.setLoginUserCode(account.getUserCode());
            baseDTO.setLoginUserMobile(account.getMobile());
            baseDTO.setLoginUserBelongDeptId(account.getBelongDeptId());
            baseDTO.setLoginUserBelongDeptCode(account.getBelongDeptCode());
            baseDTO.setLoginUserBelongDeptName(account.getBelongDeptName());
            baseDTO.setBelongStructId(account.getBelongStructId());
            baseDTO.setLoginUserBelongStructCode(account.getBelongStructCode());
            baseDTO.setLoginUserBelongStructName(account.getBelongStructName());
            baseDTO.setLoginUserType(account.getUserType());
            OrgInfo orgInfo = sessionInfo.getOrgInfo();
            if (Objects.nonNull(orgInfo)) {
                if (StringUtils.isNotBlank(orgInfo.getOrgNo())) {
                    baseDTO.setLoginOrgNo(orgInfo.getOrgNo());
                }
                if (StringUtils.isNotBlank(orgInfo.getOrgName())) {
                    baseDTO.setLoginOrgName(orgInfo.getOrgName());
                }
                if (Objects.nonNull(orgInfo.getOrgType())) {
                    baseDTO.setLoginOrgType(orgInfo.getOrgType());
                }
            }
        }
    }

    /**
     * 填充通用 BaseDTO 的公司信息。
     *
     * @param baseDTO     通用 BaseDTO 对象
     * @param sessionInfo 用户会话信息
     */
    private void fillCompanyInfo(BaseDTO baseDTO, GovUserSessionInfoDTO sessionInfo) {
        if (Objects.nonNull(sessionInfo.getCompanyInfo())) {
            CompanyInfo companyInfo = sessionInfo.getCompanyInfo();
            baseDTO.setLoginCompanyId(companyInfo.getCompanyId());
            baseDTO.setLoginCompanyName(companyInfo.getCompanyName());
        }
    }

    /**
     * 填充通用 BaseDTO 的数据权限信息。
     *
     * @param baseDTO     通用 BaseDTO 对象
     * @param sessionInfo 用户会话信息
     */
    private void fillDataPermInfo(BaseDTO baseDTO, GovUserSessionInfoDTO sessionInfo) {
        if (Objects.nonNull(sessionInfo.getDataPerm())) {
            AccountDataPerm dataPerm = sessionInfo.getDataPerm();
            baseDTO.setLoginDataPermType(dataPerm.getDataPermType());
            if (CollectionUtils.isNotEmpty(dataPerm.getDataSetCode())) {
                baseDTO.setDataCodeSet(dataPerm.getDataSetCode());
            }
        }
    }


    public static void main(String[] args) {

    }
}
