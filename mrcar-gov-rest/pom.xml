<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.izu</groupId>
        <artifactId>izu-parent</artifactId>
        <version>1.2.0-SNAPSHOT</version>
        <relativePath/>
    </parent>

    <groupId>com.mrcar</groupId>
    <artifactId>mrcar-gov-rest</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>jar</packaging>

    <name>mrcar-gov-rest</name>
    <url>http://maven.apache.org</url>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>

        <dependency>
            <groupId>com.mrcar</groupId>
            <artifactId>mrcar-gov-common</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.mrcar</groupId>
            <artifactId>mrcar-gov-user</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.mrcar</groupId>
            <artifactId>mrcar-gov-iot</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.mrcar</groupId>
            <artifactId>mrcar-gov-thirdparty</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.mrcar</groupId>
            <artifactId>mrcar-gov-config</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.mrcar</groupId>
            <artifactId>mrcar-gov-asset</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.mrcar</groupId>
            <artifactId>mrcar-gov-order</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.mrcar</groupId>
            <artifactId>mrcar-gov-workflow</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <!--基础组件start-->
        <dependency>
            <groupId>com.izu.framework</groupId>
            <artifactId>web-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.izu.remote</groupId>-->
<!--            <artifactId>remote-spring-boot-starter</artifactId>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>nacos-config-spring-boot-starter</artifactId>
        </dependency>
        <!--基础组件end-->
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>

        <!--数据库配置start-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>
        <!--数据库配置end-->

        <!-- 验证码  -->
        <dependency>
            <groupId>com.izu</groupId>
            <artifactId>spring-boot-starter-captcha</artifactId>
            <version>1.1.1-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-websocket</artifactId>
        </dependency>

        <!-- 解决RocketMQ发送ClassNotFound问题 -->
        <dependency>
            <groupId>com.izu.mq</groupId>
            <artifactId>mq-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-common</artifactId>
            <version>4.9.3</version>
        </dependency>

        <dependency>
            <groupId>com.mrcar</groupId>
            <artifactId>mrcar-gov-model</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.izu</groupId>
            <artifactId>mrcar-gov-business</artifactId>
            <version>1.2.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <!-- <dependency>
             <groupId>com.alibaba</groupId>
             <artifactId>easyexcel</artifactId>
             <version>3.2.1</version>
         </dependency>-->

        <dependency>
            <groupId>com.mrcar</groupId>
            <artifactId>mrcar-gov-bi</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-csv</artifactId>
            <version>1.9.0</version>
        </dependency>
    </dependencies>

    <profiles>
        <profile>
            <id>dev</id>
            <properties>
                <env.name>dev</env.name>
                <mybatis.logImpl>STDOUT_LOGGING</mybatis.logImpl>
                <env.type>ALI</env.type>
                <izunacos.serverAddress>non-prd-nacos.imrcar.com:18848</izunacos.serverAddress>
                <izunacos.namespace>mrcar-gov-dev</izunacos.namespace>
                <izunacos.username>javaclient</izunacos.username>
                <izunacos.password>ikTVCjnNyQPe3Jt1</izunacos.password>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <env.name>test</env.name>
                <mybatis.logImpl>STDOUT_LOGGING</mybatis.logImpl>
                <env.type>ALI</env.type>
                <izunacos.serverAddress>non-prd-nacos.imrcar.com:18848</izunacos.serverAddress>
                <izunacos.namespace>mrcar-gov-test</izunacos.namespace>
                <izunacos.username>javaclient</izunacos.username>
                <izunacos.password>ikTVCjnNyQPe3Jt1</izunacos.password>
            </properties>
        </profile>
        <profile>
            <id>pre</id>
            <properties>
                <env.name>pre</env.name>
                <mybatis.logImpl>NO_LOGGING</mybatis.logImpl>
                <env.type>ALI</env.type>
                <izunacos.serverAddress>non-prd-nacos.imrcar.com:18848</izunacos.serverAddress>
                <izunacos.namespace>mrcar-gov-pre</izunacos.namespace>
                <izunacos.username>javaclient</izunacos.username>
                <izunacos.password>ikTVCjnNyQPe3Jt1</izunacos.password>
            </properties>
        </profile>
        <profile>
            <id>online</id>
            <properties>
                <env.name>online</env.name>
                <mybatis.logImpl>NO_LOGGING</mybatis.logImpl>
                <env.type>MrCar</env.type>
                <izunacos.serverAddress>nacos-server.imrcar.com:18848</izunacos.serverAddress>
                <izunacos.namespace>mrcar-gov-prd</izunacos.namespace>
                <izunacos.username>javaclient</izunacos.username>
                <izunacos.password>kFA3xoIIZtLTT4H5</izunacos.password>
            </properties>
        </profile>
    </profiles>
    <!-- 多环境配置END -->

    <build>
        <finalName>mrcar-gov-rest</finalName>
        <resources>
            <!-- 包含当前环境下的*.properties (保持目录结构) -->
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <includes>
                    <include>logback-spring.xml</include>
                    <include>*.properties</include>
                    <include>*.csv</include>
                    <include>images/**</include>
                    <include>META-INF/services/**</include>
                    <include>template/**</include>
                </includes>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <target>1.8</target>
                    <source>1.8</source>
                    <compilerArgument>-Xlint:all</compilerArgument>
                    <showWarnings>true</showWarnings>
                    <showDeprecation>true</showDeprecation>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.7.9</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
           <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <configuration>
                    <encoding>UTF-8</encoding>
                    <nonFilteredFileExtensions>
                        <nonFilteredFileExtension>xlsx</nonFilteredFileExtension>
                        <nonFilteredFileExtension>xls</nonFilteredFileExtension>
                        <nonFilteredFileExtension>pdf</nonFilteredFileExtension>
                        <nonFilteredFileExtension>pdf</nonFilteredFileExtension>
                        <nonFilteredFileExtension>ttc</nonFilteredFileExtension>
                        <nonFilteredFileExtension>xlsm</nonFilteredFileExtension>
                    </nonFilteredFileExtensions>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
