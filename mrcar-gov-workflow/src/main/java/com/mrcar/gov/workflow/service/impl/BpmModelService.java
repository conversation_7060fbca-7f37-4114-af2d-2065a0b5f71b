package com.mrcar.gov.workflow.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.PrimitiveArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.page.PageMethod;
import com.izu.framework.exception.ApiException;
import com.izu.framework.resp.InfoCode;
import com.izu.framework.response.PageDTO;
import com.izu.framework.web.util.BeanUtil;
import com.mrcar.gov.base.common.SeqPrefix;
import com.mrcar.gov.base.service.SequenceGenerator;
import com.mrcar.gov.common.constant.workflow.WorkflowErrorCode;
import com.mrcar.gov.common.dto.workflow.definition.BpmModelMetaInfoRespDTO;
import com.mrcar.gov.common.dto.workflow.definition.BpmProcessDefinitionCreateReqDTO;
import com.mrcar.gov.common.dto.workflow.enums.BpmModelFormTypeEnum;
import com.mrcar.gov.common.dto.workflow.enums.ModelEnum;
import com.mrcar.gov.common.dto.workflow.model.*;
import com.mrcar.gov.common.util.CollectionUtils;
import com.mrcar.gov.common.util.EmojiUtils;
import com.mrcar.gov.common.util.ValidationUtils;
import com.mrcar.gov.user.domain.GovStruct;
import com.mrcar.gov.user.service.GovStructService;
import com.mrcar.gov.workflow.convert.BpmModelConvert;
import com.mrcar.gov.workflow.domain.ActReModel;
import com.mrcar.gov.workflow.domain.ActReModelExample;
import com.mrcar.gov.workflow.domain.BpmForm;
import com.mrcar.gov.workflow.domain.BpmTaskAssignRule;
import com.mrcar.gov.workflow.ext.ManagementExService;
import com.mrcar.gov.workflow.mapper.ex.ActReModelExMapper;
import com.mrcar.gov.workflow.mapper.ex.BpmTaskAssignRuleExMapper;
import com.mrcar.gov.workflow.utils.FlowableModelValidateUtil;
import com.mrcar.gov.workflow.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.flowable.bpmn.converter.BpmnXMLConverter;
import org.flowable.bpmn.model.BpmnModel;
import org.flowable.common.engine.impl.db.SuspensionState;
import org.flowable.common.engine.impl.util.io.BytesStreamSource;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.repository.Deployment;
import org.flowable.engine.repository.Model;
import org.flowable.engine.repository.ModelQuery;
import org.flowable.engine.repository.ProcessDefinition;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * @Description: Flowable流程模型实现
 * @author: hxc
 * @Date: 2022/8/1
 **/
@Service
@Validated
@Slf4j
public class BpmModelService {

    public static final String PROCESS_DEFINITION_ID_NULL = "DEFAULT";
    @Resource
    private RepositoryService repositoryService;
    @Resource
    @Lazy // 解决循环依赖
    private BpmProcessDefinitionService processDefinitionService;
    @Resource
    private BpmFormService bpmFormService;
    @Resource
    private BpmTaskAssignRuleService taskAssignRuleService;
    @Resource
    private ActReModelExMapper modelMapper;
    @Resource
    private ManagementExService managementExService;
    @Resource
    private BpmTaskAssignRuleExMapper taskRuleMapper;

    @Resource
    private SequenceGenerator sequenceGenerator;
    @Resource
    private GovStructService govStructService;

    public PageDTO<BpmModelPageItemRespDTO> getModelPage(BpmModelPageReqDTO pageReqDTO) {
        // 1. 参数准备
        Integer structId = pageReqDTO.getStructId();
        Integer loginCompanyId = pageReqDTO.getLoginCompanyId();
        String key = pageReqDTO.getKey();
        String name = pageReqDTO.getName();
        String category = pageReqDTO.getCategory();
        Byte businessType = pageReqDTO.getBusinessType();

        // 2. 构建查询条件（不使用PageHelper）
        ActReModelExample where = new ActReModelExample();
        ActReModelExample.Criteria criteria = where.createCriteria();
        if (StringUtils.isNotBlank(key)) {
            criteria.andKeyEqualTo(key);
        }
        if (StringUtils.isNotBlank(name)) {
            criteria.andNameLike("%" + name + "%");
        }
        if (StringUtils.isNotBlank(category)) {
            criteria.andCategoryEqualTo(category);
        }
        if (businessType != null) {
            criteria.andBusinessTypeEqualTo(businessType);
        }
        criteria.andTenantIdEqualTo(loginCompanyId + "");
        where.setOrderByClause("CREATE_TIME_ desc");

        // 3. 查询全量数据并过滤
        List<ActReModel> allModels = modelMapper.selectByExample(where);
        List<ActReModel> filteredModels = getDistinctBusinessTypeModel(allModels, loginCompanyId, structId);
        int total = filteredModels.size();

        // 4. 内存分页处理
        int fromIndex = (pageReqDTO.getPageNum() - 1) * pageReqDTO.getPageSize();
        int toIndex = Math.min(fromIndex + pageReqDTO.getPageSize(), total);
        List<ActReModel> pageModels = total > 0 ?
                filteredModels.subList(fromIndex, toIndex) : Collections.emptyList();

        // 5. 后续数据组装（保持原逻辑）
        Set<Long> formIds = CollectionUtils.convertSet(pageModels, model -> {
            BpmModelMetaInfoRespDTO metaInfo = JsonUtils.parseObject(model.getMetaInfo(), BpmModelMetaInfoRespDTO.class);
            return metaInfo != null ? metaInfo.getFormId() : null;
        });
        Map<Long, BpmForm> formMap = bpmFormService.getFormMap(formIds, loginCompanyId);

        Set<String> deploymentIds = new HashSet<>();
        pageModels.forEach(model -> CollectionUtils.addIfNotNull(deploymentIds, model.getDeploymentId()));
        Map<String, Deployment> deploymentMap = processDefinitionService.getDeploymentMap(deploymentIds);

        List<ProcessDefinition> processDefinitions = processDefinitionService.getProcessDefinitionListByDeploymentIds(deploymentIds);
        Map<String, ProcessDefinition> processDefinitionMap = CollectionUtils.convertMap(processDefinitions, ProcessDefinition::getDeploymentId);

        // 6. 结果转换
        List<BpmModelPageItemRespDTO> result = BpmModelConvert.INSTANCE.convertList(pageModels, formMap, deploymentMap, processDefinitionMap);
        result.forEach(resp -> {
            if (ModelEnum.CategoryEnum.OA.getCode().equals(resp.getCategory())) {
                resp.setSubProcessStr("-");
                ActReModelExample example = new ActReModelExample();
                example.createCriteria().andParentModelIdEqualTo(resp.getId());
                List<ActReModel> subModels = modelMapper.selectByExample(example);
                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(subModels)) {
                    resp.setSubProcessStr(subModels.stream().map(ActReModel::getName).collect(Collectors.joining(",")));
                }
            }
            buildStructInfo(resp, pageReqDTO.getStructId());
        });

        return new PageDTO<>(pageReqDTO.getPageNum(), pageReqDTO.getPageSize(), total, result);
    }


    private void buildStructInfo(BpmModelPageItemRespDTO modelRespDTO, Integer structId) {
        Integer actStructId = modelRespDTO.getStructId();
        if (actStructId != null) {
            GovStruct govStruct = govStructService.getBaseMapper().selectById(actStructId);
            modelRespDTO.setStructName(govStruct.getStructName());
            modelRespDTO.setConfigType((actStructId.equals(structId)) ? 1 : 2);//1本单位配置 2继承配置
            if (govStruct.getParentId() == -1) {//根节点
                modelRespDTO.setStructName(govStruct.getStructName() + "（根节点）");
            }
        } else {
            modelRespDTO.setStructName("本企业");
            modelRespDTO.setConfigType(2);
        }


    }

    /**
     * 增加模型 部门维度后
     * 相同businessType因为区分单位 会存在多个。 根据单位找上级进行去重
     */
    private List<ActReModel> getDistinctBusinessTypeModel(List<ActReModel> actReModels, Integer loginCompanyId, Integer structId) {
        // 预加载公司所有单位（避免重复查询）
        List<GovStruct> govStructs = govStructService.getBaseMapper().selectList(
                new LambdaQueryWrapper<GovStruct>()
                        .in(GovStruct::getStructType, 1, 2)
                        .eq(GovStruct::getCompanyId, loginCompanyId));

        // 转为ID映射便于快速查找
        Map<Integer, GovStruct> structMap = govStructs.stream()
                .collect(Collectors.toMap(GovStruct::getId, Function.identity()));

        GovStruct currentStruct = structMap.get(structId);
        if (currentStruct == null) {
            log.info("审批流模型列表查询部门id在公司中不存在，部门Id{}", structId);
            return actReModels;
        }

        return actReModels.stream()
                .collect(Collectors.groupingBy(ActReModel::getBusinessType))
                .values().stream()
                .map(models -> findSuitableModel(models, currentStruct, structMap))
                .filter(Optional::isPresent)
                .map(Optional::get)
                .collect(Collectors.toList());
    }

    private Optional<ActReModel> findSuitableModel(List<ActReModel> models, GovStruct currentStruct, Map<Integer, GovStruct> structMap) {
        GovStruct tempStruct = currentStruct;
        while (tempStruct != null) {
            final Integer currentId = tempStruct.getId();
            Optional<ActReModel> matched = models.stream()
                    .filter(m -> currentId.equals(m.getStructId()))
                    .findFirst();
            if (matched.isPresent()) {
                return matched;
            }
            tempStruct = structMap.get(tempStruct.getParentId());
        }
        return models.stream().filter(m -> m.getStructId() == null).findFirst();
    }

    @Transactional(rollbackFor = Exception.class)
    public String createModel(@Valid BpmModelCreateReqDTO createReqDTO, String bpmnXml) {
        checkKeyNCName(createReqDTO.getKey());
        if (EmojiUtils.containsEmoji(createReqDTO.getName())) {
            throw new ApiException(WorkflowErrorCode.DO_NOT_INPUT_CONTAINS_EMOJI_REASON);
        }
        // 校验流程标识已经存在
        List<ActReModel> list = this.getActReModelByKey(createReqDTO.getKey(), createReqDTO.getLoginCompanyId(), createReqDTO.getStructId());
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(list)) {
            InfoCode render = WorkflowErrorCode.render(WorkflowErrorCode.MODEL_KEY_EXISTS, createReqDTO.getKey());
            throw new ApiException(render);
        }
        //OA表单需求-OA表单允许建造多个业务类型的流程
        String category = createReqDTO.getCategory();
        if (category.equals(ModelEnum.CategoryEnum.DEFAULT.getCode())) {
            // 校验流程业务类型已经存在
            ActReModel actReModel = this.getModelByBusinessType(createReqDTO.getBusinessType(), createReqDTO.getLoginCompanyId(), createReqDTO.getStructId());
            if (actReModel != null) {
                throw new ApiException(WorkflowErrorCode.render(WorkflowErrorCode.MODEL_BUSINESS_TYPE_EXISTS, ModelEnum.BusinessTypeEnum.getNameByCode(createReqDTO.getBusinessType())));
            }
        }
        // 创建流程定义
        Model model = repositoryService.newModel();
        BpmModelConvert.INSTANCE.copy(model, createReqDTO);
        model.setTenantId(createReqDTO.getLoginCompanyId() + "");
        // 保存流程定义
        repositoryService.saveModel(model);
        // 保存 BPMN XML
        saveModelBpmnXml(model, bpmnXml);
        ActReModel actReModelUpdate = new ActReModel();
        actReModelUpdate.setId(model.getId());
        actReModelUpdate.setBusinessType(createReqDTO.getBusinessType());
        actReModelUpdate.setStructId(createReqDTO.getStructId());//新增部门单位
        actReModelUpdate.setConnectSwitch(createReqDTO.getConnectSwitch() == 0 ? Boolean.FALSE : Boolean.TRUE);
        if (createReqDTO.getConnectSwitch() == 1) {
            actReModelUpdate.setParentModelId(createReqDTO.getProcessId());
            actReModelUpdate.setParentModelName(createReqDTO.getProcessName());
        }
        // 保存业务类型
        managementExService.updateModelById(actReModelUpdate);
        return model.getId();
    }

    private List<ActReModel> getActReModelByKey(String key, Integer loginCompanyId, Integer structId) {
        ActReModelExample where = new ActReModelExample();
        ActReModelExample.Criteria criteria = where.createCriteria();
        criteria.andKeyEqualTo(key);
        criteria.andTenantIdEqualTo(loginCompanyId + "");
        criteria.andStructIdEqualTo(structId);
        return modelMapper.selectByExample(where);
    }

    private ActReModel getModelByBusinessType(Byte businessType, Integer companyId, Integer structId) {
        ActReModelExample where = new ActReModelExample();
        ActReModelExample.Criteria criteria = where.createCriteria();
        criteria.andBusinessTypeEqualTo(businessType);
        criteria.andTenantIdEqualTo(companyId + "");
        criteria.andStructIdEqualTo(structId);
        List<ActReModel> models = modelMapper.selectByExample(where);
        if (CollectionUtils.isAnyEmpty(models)) {
            return null;
        }
        return models.get(0);
    }

    public BpmModelRespDTO getModel(String id) {
        ActReModel model = modelMapper.selectByPrimaryKey(id);
        if (model == null) {
            return null;
        }
        BpmModelRespDTO modelRespDTO = BpmModelConvert.INSTANCE.convert(model);
        // 拼接 bpmn XML
        byte[] bpmnBytes = repositoryService.getModelEditorSource(id);
        modelRespDTO.setBpmnXml(StrUtil.utf8Str(bpmnBytes));
        return modelRespDTO;
    }

    @Transactional(rollbackFor = Exception.class) // 因为进行多个操作，所以开启事务
    public void updateModel(@Valid BpmModelUpdateReqDTO updateReqDTO) {
        // 校验流程模型存在
        Model model = repositoryService.getModel(updateReqDTO.getId());
        if (model == null) {
            throw new ApiException(WorkflowErrorCode.MODEL_NOT_EXISTS);
        }
        if (StringUtils.isNotBlank(updateReqDTO.getName())) {
            if (EmojiUtils.containsEmoji(updateReqDTO.getName())) {
                throw new ApiException(WorkflowErrorCode.DO_NOT_INPUT_CONTAINS_EMOJI_REASON);
            }
        }

        // 修改流程定义
        BpmModelConvert.INSTANCE.copy(model, updateReqDTO);
        // 更新模型
        repositoryService.saveModel(model);
        // 更新 BPMN XML
        saveModelBpmnXml(model, updateReqDTO.getBpmnXml());
        ActReModel actReModel = new ActReModel();
        actReModel.setId(model.getId());
        actReModel.setBusinessType(updateReqDTO.getBusinessType());
        //修改流程定义的关联流程字段
        actReModel.setConnectSwitch(updateReqDTO.getConnectSwitch() == 0 ? Boolean.FALSE : Boolean.TRUE);
        if (updateReqDTO.getConnectSwitch() == 1) {
            //校验该流程是否已经存在其他子流程
            ActReModelExample actReModelExample = new ActReModelExample();
            actReModelExample.createCriteria().andParentModelIdEqualTo(model.getId());
            List<ActReModel> actReModels = modelMapper.selectByExample(actReModelExample);
            if (!org.springframework.util.CollectionUtils.isEmpty(actReModels)) {
                throw new ApiException(WorkflowErrorCode.MODEL_CONNECT_ERROR);
            }
            actReModel.setParentModelId(updateReqDTO.getProcessId());
            actReModel.setParentModelName(updateReqDTO.getProcessName());
        } else {
            actReModel.setParentModelId("");
            actReModel.setParentModelName("");
        }
        // 更新业务类型
        managementExService.updateModelById(actReModel);
    }

    @Transactional(rollbackFor = Exception.class) // 因为进行多个操作，所以开启事务
    public void deployModel(BpmModelUpdateReqDTO modelUpdateReqDTO) {
        String id = modelUpdateReqDTO.getId();
        // 校验流程模型存在
        Model model = repositoryService.getModel(id);
        if (ObjectUtils.isEmpty(model)) {
            throw new ApiException(WorkflowErrorCode.MODEL_NOT_EXISTS);
        }
        // 校验流程图
        byte[] bpmnBytes = repositoryService.getModelEditorSource(model.getId());
        if (bpmnBytes == null) {
            throw new ApiException(WorkflowErrorCode.MODEL_NOT_EXISTS);
        }
        // 校验表单已配
        BpmForm form = checkFormConfig(model.getMetaInfo());
        //校验任务分配规则已配置
        taskAssignRuleService.checkTaskAssignRuleAllConfig(id);

        BpmProcessDefinitionCreateReqDTO definitionCreateReqDTO = BpmModelConvert.INSTANCE.convert2(model, form);
        definitionCreateReqDTO.setBpmnBytes(bpmnBytes);
        //校验模型是否发生修改。如果未修改，则不允许创建
        if (processDefinitionService.isProcessDefinitionEquals(definitionCreateReqDTO)) { // 流程定义的信息相等
            ProcessDefinition oldProcessInstance = processDefinitionService.getProcessDefinitionByDeploymentId(model.getDeploymentId());
            if (oldProcessInstance != null && taskAssignRuleService.isTaskAssignRulesEquals(model.getId(), oldProcessInstance.getId())) {
                throw new ApiException(WorkflowErrorCode.MODEL_DEPLOY_FAIL_TASK_INFO_EQUALS);
            }
        }
        definitionCreateReqDTO.setLoginCompanyId(modelUpdateReqDTO.getLoginCompanyId());
        definitionCreateReqDTO.setLoginCompanyName(modelUpdateReqDTO.getLoginCompanyName());
        definitionCreateReqDTO.setLoginUserId(modelUpdateReqDTO.getLoginUserId());
        definitionCreateReqDTO.setLoginUserName(modelUpdateReqDTO.getLoginUserName());
        // 创建流程定义
        String definitionId = processDefinitionService.createProcessDefinition(definitionCreateReqDTO);

        // 将老的流程定义进行挂起。也就是说，只有最新部署的流程定义，才可以发起任务。
        updateProcessDefinitionSuspended(model.getDeploymentId());

        // 更新 model 的 deploymentId，进行关联
        ProcessDefinition definition = processDefinitionService.getProcessDefinition(definitionId);
        model.setDeploymentId(definition.getDeploymentId());
        repositoryService.saveModel(model);

        //复制任务分配规则
        taskAssignRuleService.copyTaskAssignRules(id, definition.getId(), modelUpdateReqDTO);

    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteModel(BpmModelUpdateReqDTO modelUpdateReqDTO) {
        String id = modelUpdateReqDTO.getId();
        // 校验流程模型存在
        Model model = repositoryService.getModel(id);
        if (model == null) {
            throw new ApiException(WorkflowErrorCode.MODEL_NOT_EXISTS);
        }
        ActReModelExample actReModelExample = new ActReModelExample();
        actReModelExample.createCriteria().andParentModelIdEqualTo(id);
        List<ActReModel> actReModels = modelMapper.selectByExample(actReModelExample);
        //校验该流程是否被关联
        if (actReModels != null && actReModels.size() > 0) {
            throw new ApiException(WorkflowErrorCode.MODEL_CONNECT_SUB_EXISTS);
        }
        // 执行删除
        repositoryService.deleteModel(id);
        // 禁用流程实例
        updateProcessDefinitionSuspended(model.getDeploymentId());
    }

    public void updateModelState(BpmModelUpdateStateReqDTO reqDTO) {
        String id = reqDTO.getId();
        Integer state = reqDTO.getState();
        // 校验流程模型存在
        Model model = repositoryService.getModel(id);
        if (model == null) {
            throw new ApiException(WorkflowErrorCode.MODEL_NOT_EXISTS);
        }
        // 校验流程定义存在
        ProcessDefinition definition = processDefinitionService.getProcessDefinitionByDeploymentId(model.getDeploymentId());
        if (definition == null) {
            throw new ApiException(WorkflowErrorCode.PROCESS_DEFINITION_NOT_EXISTS);
        }
        //关闭激活状态
        if (state == 2 && model.getCategory().equals(ModelEnum.CategoryEnum.OA.getCode())) {
            //查询是否被关联
            ActReModelExample actReModelExample = new ActReModelExample();
            actReModelExample.createCriteria().andParentModelIdEqualTo(id);
            List<ActReModel> actReModels = modelMapper.selectByExample(actReModelExample);
            if (actReModels != null && actReModels.size() > 0) {
                actReModels.forEach(actReModel -> {
                    // 获得流程定义查看当前关联的子流程状态是否有激活态的，如果有，则不允许关闭
                    ProcessDefinition processDefinition = processDefinitionService.getProcessDefinitionByDeploymentId(actReModel.getDeploymentId());
                    if (processDefinition != null && !processDefinition.isSuspended()) {
                        throw new ApiException(WorkflowErrorCode.MODEL_CONNECT_SUB_UPDATE);
                    }
                });
            }
            //激活时需要校验父流程是否此时已经激活
        } else if (state == 1 && model.getCategory().equals(ModelEnum.CategoryEnum.OA.getCode())) {
            ActReModel actReModel = modelMapper.selectByPrimaryKey(model.getId());
            if (StringUtils.isNotEmpty(actReModel.getParentModelId())) {
                ActReModel parentModel = modelMapper.selectByPrimaryKey(actReModel.getParentModelId());
                if (parentModel != null) {
                    // 查看父流程的激活状态
                    ProcessDefinition processDefinition = processDefinitionService.getProcessDefinitionByDeploymentId(parentModel.getDeploymentId());
                    if (processDefinition != null && processDefinition.isSuspended()) {
                        throw new ApiException(WorkflowErrorCode.MODEL_ACTIVE_SUB_ERROR);
                    }
                }
            }
        }
        // 更新状态
        processDefinitionService.updateProcessDefinitionState(definition.getId(), state);
    }

    public BpmnModel getBpmnModel(String id) {
        byte[] bpmnBytes = repositoryService.getModelEditorSource(id);
        if (PrimitiveArrayUtil.isEmpty(bpmnBytes)) {
            return null;
        }
        BpmnXMLConverter converter = new BpmnXMLConverter();
        return converter.convertToBpmnModel(new BytesStreamSource(bpmnBytes), true, true);
    }

    private void checkKeyNCName(String key) {
        if (!ValidationUtils.isXmlNCName(key)) {
            throw new ApiException(WorkflowErrorCode.MODEL_KEY_VALID);
        }
    }

    /**
     * 校验流程表单已配置
     *
     * @param metaInfoStr 流程模型 metaInfo 字段
     * @return 流程表单
     */
    private BpmForm checkFormConfig(String metaInfoStr) {
        BpmModelMetaInfoRespDTO metaInfo = JsonUtils.parseObject(metaInfoStr, BpmModelMetaInfoRespDTO.class);
        if (metaInfo == null || metaInfo.getFormType() == null) {
            throw new ApiException(WorkflowErrorCode.MODEL_DEPLOY_FAIL_FORM_NOT_CONFIG);
        }
        // 校验表单存在
        if (Objects.equals(metaInfo.getFormType(), BpmModelFormTypeEnum.NORMAL.getType())) {
            BpmForm form = bpmFormService.getFormById(metaInfo.getFormId());
            if (form == null) {
                throw new ApiException(WorkflowErrorCode.FORM_NOT_EXISTS);
            }
            return form;
        }
        return null;
    }

    private void saveModelBpmnXml(Model model, String bpmnXml) {
        if (CharSequenceUtil.isEmpty(bpmnXml)) {
            return;
        }
        // 校验流程图合法性
        FlowableModelValidateUtil.validateModel(bpmnXml);
        repositoryService.addModelEditorSource(model.getId(), CharSequenceUtil.utf8Bytes(bpmnXml));
    }

    /**
     * 挂起 deploymentId 对应的流程定义。 这里一个deploymentId 只关联一个流程定义
     *
     * @param deploymentId 流程发布Id.
     */
    private void updateProcessDefinitionSuspended(String deploymentId) {
        if (CharSequenceUtil.isEmpty(deploymentId)) {
            return;
        }
        ProcessDefinition oldDefinition = processDefinitionService.getProcessDefinitionByDeploymentId(deploymentId);
        if (oldDefinition == null) {
            return;
        }
        processDefinitionService.updateProcessDefinitionState(oldDefinition.getId(), SuspensionState.SUSPENDED.getStateCode());
    }

//    public void copyModel(String modelId){
//        Model model = repositoryService.getModel(modelId);
//        ActReModel actReModel = modelMapper.selectByPrimaryKey(modelId);
//        Byte businessType = actReModel.getBusinessType();
//        List<BpmTaskAssignRule> ruleList = taskRuleMapper.selectListByModelIdAndProcessDefinitionId(modelId, PROCESS_DEFINITION_ID_NULL);
//        // 查询当前有效的企业
//        List<CompanyInfoDTO> allCompanyList = companyApi.getAllCompanyList();
//        if (CollectionUtils.isAnyEmpty(allCompanyList)){
//            return;
//        }
//        allCompanyList.forEach(companyInfoDTO -> {
//            Integer companyId = companyInfoDTO.getCompanyId();
//            copyDefaultModel2Customer(model,ruleList,companyId,businessType);
//        });
//    }

    private void copyDefaultModel2Customer(Model copyModel, List<BpmTaskAssignRule> ruleList, Integer companyId, Byte businessType) {

        // 获得流程模型
        ActReModelExample where = new ActReModelExample();
        ActReModelExample.Criteria criteria = where.createCriteria();
        criteria.andBusinessTypeEqualTo(businessType);
        criteria.andCategoryEqualTo(ModelEnum.CategoryEnum.DEFAULT.getCode());
        criteria.andTenantIdEqualTo(companyId + "");
        List<ActReModel> modelList = modelMapper.selectByExample(where);
        if (!modelList.isEmpty()) {
            ActReModel actReModel = modelList.get(0);
            Model model = repositoryService.getModel(actReModel.getId());
            // 修改流程定义
            BpmModelConvert.INSTANCE.copy(model, copyModel);
            // 更新模型
            repositoryService.saveModel(model);
            return;
        }
        String copyModelId = copyModel.getId();
        // 创建流程定义
        Model model = repositoryService.newModel();
        BpmModelConvert.INSTANCE.copy(model, copyModel);
        model.setTenantId(companyId + "");
        // 获得流程图
        byte[] bpmnBytes = repositoryService.getModelEditorSource(copyModelId);
        // 保存流程定义
        repositoryService.saveModel(model);
        ActReModel actReModel = new ActReModel();
        actReModel.setId(model.getId());
        actReModel.setBusinessType(businessType);
        // 保存业务类型
        managementExService.updateModelById(actReModel);
        // 保存 BPMN XML
        repositoryService.addModelEditorSource(model.getId(), bpmnBytes);
        // 保存任务分配规则
        ruleList.forEach(rule -> {
            BpmTaskAssignRule assignRule = new BpmTaskAssignRule();
            assignRule.setCompanyId(companyId);
            assignRule.setProcessDefinitionId(PROCESS_DEFINITION_ID_NULL);
            assignRule.setModelId(model.getId());
            assignRule.setTaskDefinitionKey(rule.getTaskDefinitionKey());
            assignRule.setType(rule.getType());
            assignRule.setOptions("");
            taskRuleMapper.insertSelective(assignRule);
        });
    }

    public boolean initModelToCompany(Integer companyId) {

        ModelQuery modelQuery = repositoryService.createModelQuery();
        modelQuery.modelCategory(ModelEnum.CategoryEnum.DEFAULT.getCode());
        modelQuery.modelTenantId("0");
        modelQuery.deployed();
        // 执行查询
        List<Model> modelList = modelQuery.list();
        modelList.forEach(model -> initModel(model, companyId));
        return true;
    }

    private void initModel(Model model, Integer companyId) {

        String modelId = model.getId();
        ActReModel actReModel = modelMapper.selectByPrimaryKey(modelId);
        Byte businessType = actReModel.getBusinessType();
        List<BpmTaskAssignRule> ruleList = taskRuleMapper.selectListByModelIdAndProcessDefinitionId(modelId, PROCESS_DEFINITION_ID_NULL);
        copyDefaultModel2Customer(model, ruleList, companyId, businessType);
    }

    public void updateModelToCompany(BpmModelUpdateReqDTO updateReqDTO) {

        Byte businessType = updateReqDTO.getBusinessType();
        // 获得流程模型
        ActReModelExample where = new ActReModelExample();
        ActReModelExample.Criteria criteria = where.createCriteria();
        criteria.andBusinessTypeEqualTo(businessType);
        criteria.andCategoryEqualTo(ModelEnum.CategoryEnum.DEFAULT.getCode());
        criteria.andIdNotIn(Arrays.asList(updateReqDTO.getId()));
        List<ActReModel> modelList = modelMapper.selectByExample(where);
        if (CollectionUtils.isAnyEmpty(modelList)) {
            return;
        }
        modelList.forEach(model -> updateCustomerModel(model.getId(), updateReqDTO));
    }

    private void updateCustomerModel(String modelId, BpmModelUpdateReqDTO updateReqDTO) {

        // 校验流程模型存在
        Model model = repositoryService.getModel(modelId);
        if (model == null) {
            return;
        }
        // 修改流程定义
        BpmModelConvert.INSTANCE.copyDefault(model, updateReqDTO);
        // 更新模型
        repositoryService.saveModel(model);
    }

    public Boolean switchModelBatchApproval(BpmModelUpdateReqDTO bpmModelUpdateReqDTO) {
        //
        String id = bpmModelUpdateReqDTO.getId();
        if (StringUtils.isBlank(id)) {
            throw new ApiException(WorkflowErrorCode.MODEL_ID_NOT_EXIST);
        }
        ActReModel actReModel = modelMapper.selectByPrimaryKey(id);
        if (Objects.isNull(actReModel)) {
            throw new ApiException(WorkflowErrorCode.MODEL_NOT_EXISTS);
        }
        ActReModel update = new ActReModel();
        update.setId(id);
        update.setBatchApproval(actReModel.getBatchApproval() ? false : true);
        int i = modelMapper.updateByPrimaryKeySelective(update);
        return i > 0;
    }

    public List<BpmModelSearchConnectProcessRespDTO> searchProcess(BpmModelSearchConnectProcessReqDTO bpmModelSearchConnectProcessReqDTO) {
        List<BpmModelSearchConnectProcessRespDTO> list = new ArrayList<>();
        ActReModelExample actReModelExample = new ActReModelExample();
        ActReModelExample.Criteria criteria = actReModelExample.createCriteria();
        criteria
                .andCategoryEqualTo(ModelEnum.CategoryEnum.OA.getCode())
                .andTenantIdEqualTo(bpmModelSearchConnectProcessReqDTO.getLoginCompanyId().toString());
        if (StringUtils.isNotEmpty(bpmModelSearchConnectProcessReqDTO.getProcessId())) {
            criteria.andIdNotEqualTo(bpmModelSearchConnectProcessReqDTO.getProcessId()).andParentModelIdEqualTo("");
        }
        List<ActReModel> actReModelList = modelMapper.selectByExample(actReModelExample);
        if (org.springframework.util.CollectionUtils.isEmpty(actReModelList)) {
            return list;
        }
        list = actReModelList.stream().map(actReModel -> {
            BpmModelSearchConnectProcessRespDTO respDTO = new BpmModelSearchConnectProcessRespDTO();
            respDTO.setProcessId(actReModel.getId());
            respDTO.setProcessName(actReModel.getName());
            return respDTO;
        }).collect(Collectors.toList());
        return list;
    }

    public PageDTO<BpmProviderModelPageReqDTO> getProviderModelPage(BpmModelPageReqDTO pageReqDTO) {
        Integer companyId = pageReqDTO.getCompanyId();
        Integer page = pageReqDTO.getPageNum();
        Integer pageSize = pageReqDTO.getPageSize();
        String key = pageReqDTO.getKey();
        String name = pageReqDTO.getName();
        String category = pageReqDTO.getCategory();
        if (StringUtils.isEmpty(category)) {
            category = ModelEnum.CategoryEnum.OA.getCode();
        }
        Byte businessType = pageReqDTO.getBusinessType();
        List<ActReModel> actReModels;
        int total;
        Page<?> p = PageMethod.startPage(page, pageSize, true);
        try {
            ActReModelExample where = new ActReModelExample();
            ActReModelExample.Criteria criteria = where.createCriteria();
            if (StringUtils.isNotBlank(key)) {
                criteria.andKeyEqualTo(key);
            }
            if (StringUtils.isNotBlank(name)) {
                criteria.andNameLike("%" + name + "%");
            }
            if (StringUtils.isNotBlank(category)) {
                criteria.andCategoryEqualTo(category);
            }
            if (businessType != null) {
                criteria.andBusinessTypeEqualTo(businessType);
            }
            if (companyId != null) {
                criteria.andTenantIdEqualTo(companyId + "");
            }
            where.setOrderByClause("CREATE_TIME_ desc");
            actReModels = modelMapper.selectByExample(where);
            total = (int) p.getTotal();
        } finally {
            PageMethod.clearPage();
        }
        // 获得 Form Map
        Set<Long> formIds = CollectionUtils.convertSet(actReModels, model -> {
            BpmModelMetaInfoRespDTO metaInfo = JsonUtils.parseObject(model.getMetaInfo(), BpmModelMetaInfoRespDTO.class);
            return metaInfo != null ? metaInfo.getFormId() : null;
        });
        Map<Long, BpmForm> formMap = bpmFormService.getFormMap(formIds, companyId);
        // 获得 Deployment Map
        Set<String> deploymentIds = new HashSet<>();
        actReModels.forEach(model -> CollectionUtils.addIfNotNull(deploymentIds, model.getDeploymentId()));
        Map<String, Deployment> deploymentMap = processDefinitionService.getDeploymentMap(deploymentIds);
        // 获得 ProcessDefinition Map
        List<ProcessDefinition> processDefinitions = processDefinitionService.getProcessDefinitionListByDeploymentIds(deploymentIds);
        Map<String, ProcessDefinition> processDefinitionMap = CollectionUtils.convertMap(processDefinitions, ProcessDefinition::getDeploymentId);
        // 拼接结果
        List<BpmModelPageItemRespDTO> bpmModelPageItemRespDTOS = BpmModelConvert.INSTANCE.convertList(actReModels, formMap, deploymentMap, processDefinitionMap);
        bpmModelPageItemRespDTOS.forEach(modelRespDTO -> {
            //只有OA流程才有展示父子关联逻辑
            if (ModelEnum.CategoryEnum.OA.getCode().equals(modelRespDTO.getCategory())) {
                modelRespDTO.setSubProcessStr("-");
                //查询关联的子流程
                ActReModelExample actReModelExample = new ActReModelExample();
                actReModelExample.createCriteria().andParentModelIdEqualTo(modelRespDTO.getId());
                List<ActReModel> actReModelsList = modelMapper.selectByExample(actReModelExample);
                //设置子流程
                if (actReModelsList != null && actReModelsList.size() > 0) {
                    modelRespDTO.setSubProcessStr(actReModelsList.stream().map(ActReModel::getName).collect(Collectors.joining(",")));
                }
            }
        });
        List<BpmProviderModelPageReqDTO> bpmProviderModelPageReqDTOS = BeanUtil.copyList(bpmModelPageItemRespDTOS, BpmProviderModelPageReqDTO.class);
        List<Integer> companyIds = bpmProviderModelPageReqDTOS.stream().map(BpmProviderModelPageReqDTO::getCompanyId).distinct().collect(Collectors.toList());
        //查询公司的名字
        return new PageDTO<BpmProviderModelPageReqDTO>(page, pageSize, total, bpmProviderModelPageReqDTOS);
    }

    @Transactional(rollbackFor = Exception.class)
    public String createCurrentUnitModel(BpmModelCurrentUnitCreateReqDTO req) {
        String modelId = req.getModelId();
        ActReModel actReModel = modelMapper.selectByPrimaryKey(modelId);
        if (actReModel == null) {
            throw new ApiException(WorkflowErrorCode.EXTEND_MODEL_NOT_EXISTS);
        }
        BpmModelCreateReqDTO bpmModelCreateReqDTO = bulidBpmModelCreateReqDTO(req, actReModel);
        return createModel(bpmModelCreateReqDTO, null);
    }

    private BpmModelCreateReqDTO bulidBpmModelCreateReqDTO(BpmModelCurrentUnitCreateReqDTO req, ActReModel actReModel) {
        BpmModelCreateReqDTO bpmModelCreateReqDTO = BeanUtil.copyObject(req, BpmModelCreateReqDTO.class);
        bpmModelCreateReqDTO.setKey(sequenceGenerator.generate(new Date(), SeqPrefix.GOV_WORKFLOW_MODEL_PREFIX));
        bpmModelCreateReqDTO.setName(actReModel.getName());
        bpmModelCreateReqDTO.setCategory(actReModel.getCategory());
        bpmModelCreateReqDTO.setBusinessType(actReModel.getBusinessType());
        BpmModelMetaInfoRespDTO bpmModelMetaInfoRespDTO = JsonUtils.parseObject(actReModel.getMetaInfo(), BpmModelMetaInfoRespDTO.class);
        bpmModelCreateReqDTO.setFormType(bpmModelMetaInfoRespDTO.getFormType());
        bpmModelCreateReqDTO.setFormId(bpmModelMetaInfoRespDTO.getFormId());
        bpmModelCreateReqDTO.setDescription(bpmModelMetaInfoRespDTO.getDescription());
        bpmModelCreateReqDTO.setFormCustomViewPath(bpmModelMetaInfoRespDTO.getFormCustomViewPath());
        bpmModelCreateReqDTO.setStructId(req.getStructId());
        return bpmModelCreateReqDTO;
    }
}
