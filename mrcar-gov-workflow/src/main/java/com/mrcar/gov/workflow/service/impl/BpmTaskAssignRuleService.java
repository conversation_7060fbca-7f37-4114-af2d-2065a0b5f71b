package com.mrcar.gov.workflow.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.google.common.annotations.VisibleForTesting;
import com.google.gson.Gson;
import com.izu.framework.exception.ApiException;
import com.mrcar.gov.asset.service.GovVehicleBaseInfoService;
import com.mrcar.gov.common.constant.user.GovUserStatusEnum;
import com.mrcar.gov.common.constant.workflow.WorkflowErrorCode;
import com.mrcar.gov.common.dto.user.resp.GovStructForWorkflowDTO;
import com.mrcar.gov.common.dto.user.resp.UserListForWorkflowDTO;
import com.mrcar.gov.common.dto.workflow.enums.BpmTaskAssignRuleTypeEnum;
import com.mrcar.gov.common.dto.workflow.model.BpmModelUpdateReqDTO;
import com.mrcar.gov.common.dto.workflow.rule.BpmTaskAssignRuleCreateReqDTO;
import com.mrcar.gov.common.dto.workflow.rule.BpmTaskAssignRuleRespDTO;
import com.mrcar.gov.common.dto.workflow.rule.BpmTaskAssignRuleUpdateReqDTO;
import com.mrcar.gov.common.util.CollectionUtils;
import com.mrcar.gov.user.service.UserForWorkflowService;
import com.mrcar.gov.workflow.convert.BpmTaskAssignRuleConvert;
import com.mrcar.gov.workflow.domain.BpmProcessInstanceExt;
import com.mrcar.gov.workflow.domain.BpmTaskAssignRule;
import com.mrcar.gov.workflow.domain.BpmUserGroup;
import com.mrcar.gov.workflow.mapper.ex.BpmProcessInstanceExtExMapper;
import com.mrcar.gov.workflow.mapper.ex.BpmTaskAssignRuleExMapper;
import com.mrcar.gov.workflow.utils.FlowableUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.flowable.bpmn.model.BpmnModel;
import org.flowable.bpmn.model.UserTask;
import org.flowable.common.engine.api.FlowableException;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.runtime.ProcessInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.hutool.core.text.CharSequenceUtil.format;
import static com.mrcar.gov.common.constant.workflow.WorkflowErrorCode.TASK_CREATE_FAIL_NO_CANDIDATE_USER;
import static com.mrcar.gov.workflow.utils.JsonUtils.toJsonString;

/**
 * BPM 任务分配规则 Service 实现类
 */
@Service
@Validated
@Slf4j
public class BpmTaskAssignRuleService {

    /**
     * {processDefinitionId} 空串，用于标识属于流程模型，而不属于流程定义
     * 不使用空串的原因，Oracle 针对空串，会处理成 null，进而导致无法检索
     */
    public static final String PROCESS_DEFINITION_ID_NULL = "DEFAULT";

    @Resource
    private BpmTaskAssignRuleExMapper taskRuleMapper;

    @Resource
    private BpmProcessInstanceExtExMapper processInstanceExtMapper;
    @Resource
    @Lazy // 解决循环依赖
    private BpmModelService modelService;
    @Resource
    @Lazy // 解决循环依赖
    private BpmProcessDefinitionService processDefinitionService;
    @Resource
    @Lazy // 解决循环依赖
    private BpmProcessInstanceService bpmProcessInstanceService;
    @Resource
    private BpmUserGroupService userGroupService;
    @Lazy // 解决循环依赖
    @Resource
    private RuntimeService runtimeService;

    @Autowired
    private UserForWorkflowService userForWorkflowService;

    @Autowired
    private GovVehicleBaseInfoService govVehicleBaseInfoService;

    public List<BpmTaskAssignRule> getTaskAssignRuleListByProcessDefinitionId(String processDefinitionId, String taskDefinitionKey) {
        return taskRuleMapper.selectListByProcessDefinitionId(processDefinitionId, taskDefinitionKey);
    }

    public List<BpmTaskAssignRule> getTaskAssignRuleListByModelId(String modelId) {
        return taskRuleMapper.selectListByModelId(modelId);
    }

    public List<BpmTaskAssignRuleRespDTO> getTaskAssignRuleList(String modelId, String processDefinitionId) {
        // 获得规则
        List<BpmTaskAssignRule> rules = Collections.emptyList();
        BpmnModel model = null;
        if (CharSequenceUtil.isNotEmpty(modelId)) {
            rules = getTaskAssignRuleListByModelId(modelId);
            model = modelService.getBpmnModel(modelId);
        } else if (CharSequenceUtil.isNotEmpty(processDefinitionId)) {
            rules = getTaskAssignRuleListByProcessDefinitionId(processDefinitionId, null);
            model = processDefinitionService.getBpmnModel(processDefinitionId);
        }
        if (model == null) {
            return Collections.emptyList();
        }
        // 获得用户任务，只有用户任务才可以设置分配规则
        List<UserTask> userTasks = FlowableUtils.getBpmnModelElements(model, UserTask.class);
        if (CollUtil.isEmpty(userTasks)) {
            return Collections.emptyList();
        }
        // 转换数据
        return BpmTaskAssignRuleConvert.INSTANCE.convertList(userTasks, rules);
    }

    public Long createTaskAssignRule(@Valid BpmTaskAssignRuleCreateReqDTO reqVO) {
        // 校验参数
        validTaskAssignRuleOptions(reqVO.getType(), reqVO.getOptions());
        // 校验是否已经配置
        BpmTaskAssignRule existRule = taskRuleMapper.selectListByModelIdAndTaskDefinitionKey(
                reqVO.getModelId(), reqVO.getTaskDefinitionKey());
        if (existRule != null) {
            throw new ApiException(WorkflowErrorCode.render(WorkflowErrorCode.TASK_ASSIGN_RULE_EXISTS, reqVO.getModelId(), reqVO.getTaskDefinitionKey()));
        }

        // 存储
        BpmTaskAssignRule rule = BpmTaskAssignRuleConvert.INSTANCE.convert(reqVO);
        rule.setProcessDefinitionId(PROCESS_DEFINITION_ID_NULL); // 只有流程模型，才允许新建
        Integer loginUserId = reqVO.getLoginUserId();
        String loginUserName = reqVO.getLoginUserName();
        Integer loginCompanyId = reqVO.getLoginCompanyId();
        Date now = new Date();
        rule.setCreateTime(now);
        rule.setUpdateTime(now);
        rule.setCreterId(loginUserId);
        rule.setCreterName(loginUserName);
        rule.setUpdateId(loginUserId);
        rule.setUpdateName(loginUserName);
        rule.setCompanyId(loginCompanyId);
        taskRuleMapper.insertSelective(rule);
        return rule.getId();
    }

    public void updateTaskAssignRule(@Valid BpmTaskAssignRuleUpdateReqDTO reqVO) {
        // 校验参数
        validTaskAssignRuleOptions(reqVO.getType(), reqVO.getOptions());
        // 校验是否存在
        BpmTaskAssignRule existRule = taskRuleMapper.selectByPrimaryKey(reqVO.getId());
        if (existRule == null) {
            throw new ApiException(WorkflowErrorCode.render(WorkflowErrorCode.TASK_ASSIGN_RULE_NOT_EXISTS));
        }
        // 只允许修改流程模型的规则
        if (!Objects.equals(PROCESS_DEFINITION_ID_NULL, existRule.getProcessDefinitionId())) {
            throw new ApiException(WorkflowErrorCode.render(WorkflowErrorCode.TASK_UPDATE_FAIL_NOT_MODEL));
        }

        BpmTaskAssignRule rule = BpmTaskAssignRuleConvert.INSTANCE.convert(reqVO);
        rule.setUpdateId(reqVO.getLoginUserId());
        rule.setUpdateName(reqVO.getLoginUserName());
        // 执行更新
        taskRuleMapper.updateByPrimaryKeySelective(rule);
    }

    public boolean isTaskAssignRulesEquals(String modelId, String processDefinitionId) {
        // 调用 VO 接口的原因是，过滤掉流程模型不需要的规则，保持和 copyTaskAssignRules 方法的一致性
        List<BpmTaskAssignRuleRespDTO> modelRules = getTaskAssignRuleList(modelId, null);
        List<BpmTaskAssignRuleRespDTO> processInstanceRules = getTaskAssignRuleList(null, processDefinitionId);
        if (modelRules.size() != processInstanceRules.size()) {
            return false;
        }

        // 遍历，匹配对应的规则
        Map<String, BpmTaskAssignRuleRespDTO> processInstanceRuleMap = CollectionUtils.convertMap(processInstanceRules,
                BpmTaskAssignRuleRespDTO::getTaskDefinitionKey);
        for (BpmTaskAssignRuleRespDTO modelRule : modelRules) {
            BpmTaskAssignRuleRespDTO processInstanceRule = processInstanceRuleMap.get(modelRule.getTaskDefinitionKey());
            if (processInstanceRule == null) {
                return false;
            }
            if (!ObjectUtil.equals(modelRule.getType(), processInstanceRule.getType())
                    || !ObjectUtil.equal(modelRule.getOptions(), processInstanceRule.getOptions())) {
                return false;
            }
        }
        return true;
    }

    public void copyTaskAssignRules(String fromModelId, String toProcessDefinitionId, BpmModelUpdateReqDTO modelUpdateReqDTO) {
        List<BpmTaskAssignRuleRespDTO> rules = getTaskAssignRuleList(fromModelId, null);
        if (CollUtil.isEmpty(rules)) {
            return;
        }
        // 开始复制
        List<BpmTaskAssignRule> newRules = BpmTaskAssignRuleConvert.INSTANCE.convertList2(rules);
        newRules.forEach(rule -> {
            rule.setProcessDefinitionId(toProcessDefinitionId);
            rule.setId(null);
            rule.setCreterId(modelUpdateReqDTO.getLoginUserId());
            rule.setCreterName(modelUpdateReqDTO.getLoginUserName());
            rule.setUpdateId(modelUpdateReqDTO.getLoginUserId());
            rule.setUpdateName(modelUpdateReqDTO.getLoginUserName());
            rule.setCompanyId(modelUpdateReqDTO.getLoginCompanyId());
            taskRuleMapper.insertSelective(rule);
        });
    }

    public void checkTaskAssignRuleAllConfig(String id) {
        // 一个用户任务都没配置，所以无需配置规则
        List<BpmTaskAssignRuleRespDTO> taskAssignRules = getTaskAssignRuleList(id, null);
        if (CollUtil.isEmpty(taskAssignRules)) {
            return;
        }
        // 校验未配置规则的任务
        taskAssignRules.forEach(rule -> {
            if (rule.getType() == null) {
                throw new ApiException(WorkflowErrorCode.render(WorkflowErrorCode.MODEL_DEPLOY_FAIL_TASK_ASSIGN_RULE_NOT_CONFIG, rule.getTaskDefinitionName()));
            }
            BpmTaskAssignRuleTypeEnum ruleType = BpmTaskAssignRuleTypeEnum.fromType(rule.getType());
            if (StringUtils.isBlank(rule.getOptions())
                    && ruleType != BpmTaskAssignRuleTypeEnum.INITIATOR_DEPT_LEADER
                    && ruleType != BpmTaskAssignRuleTypeEnum.INITIATOR_VEHICLE_UNIT_ROLE) {
                throw new ApiException(WorkflowErrorCode.render(WorkflowErrorCode.MODEL_DEPLOY_FAIL_TASK_ASSIGN_RULE_NOT_CONFIG, rule.getTaskDefinitionName()));
            }
        });
    }

    private void validTaskAssignRuleOptions(Integer type, String options) {
        switch (BpmTaskAssignRuleTypeEnum.fromType(type)) {
            case INITIATOR_VEHICLE_UNIT_ROLE: // 发起车辆单位的角色
            case INITIATOR_DEPT_LEADER: // 发起人部门的负责人
            case USER: // 用户
            case ASSIGN_ROLE: // 指定角色
                // 无规则值，无需校验
                break;
            case USER_GROUP: // 用户组
                List<String> list = Arrays.asList(options.split(","));
                Set<Long> ids = list.stream().map(Long::valueOf).collect(Collectors.toSet());
                // 校验用户组是否有效
                userGroupService.validUserGroups(ids);
                break;
            default:
                throw new IllegalArgumentException(CharSequenceUtil.format("未知的规则类型({})", type));
        }
    }

    public Set<Long> calculateTaskCandidateUsers(DelegateExecution execution) {
        BpmTaskAssignRule rule = getTaskRule(execution);
        return calculateTaskCandidateUsers(execution, rule);
    }

    @VisibleForTesting
    BpmTaskAssignRule getTaskRule(DelegateExecution execution) {
        List<BpmTaskAssignRule> taskRules = getTaskAssignRuleListByProcessDefinitionId(
                execution.getProcessDefinitionId(), execution.getCurrentActivityId());
        if (CollUtil.isEmpty(taskRules)) {
            throw new FlowableException(format("流程任务({}/{}/{}) 找不到符合的任务规则",
                    execution.getId(), execution.getProcessDefinitionId(), execution.getCurrentActivityId()));
        }
        if (taskRules.size() > 1) {
            throw new FlowableException(format("流程任务({}/{}/{}) 找到过多任务规则({})",
                    execution.getId(), execution.getProcessDefinitionId(), execution.getCurrentActivityId()));
        }
        return taskRules.get(0);
    }

    Set<Long> calculateTaskCandidateUsers(DelegateExecution execution, BpmTaskAssignRule rule) {
        Set<Long> assigneeUserIds;
        BpmTaskAssignRuleTypeEnum ruleType = BpmTaskAssignRuleTypeEnum.fromType(Integer.valueOf(rule.getType()));

        switch (ruleType) {
            case INITIATOR_DEPT_LEADER:
                assigneeUserIds = calculateTaskCandidateUsersByInitiatorDeptLeader(execution);
                break;
            case INITIATOR_VEHICLE_UNIT_ROLE:
                assigneeUserIds = calculateTaskCandidateUsersByInitiatorVehicleUnitRole(execution, rule);
                break;
            case USER:
                assigneeUserIds = calculateTaskCandidateUsersByUser(rule);
                break;
            case USER_GROUP:
                assigneeUserIds = calculateTaskCandidateUsersByUserGroup(rule);
                break;
            case ASSIGN_ROLE:
                assigneeUserIds = calculateTaskCandidateUsersByAssignRole(execution, rule);
                break;
            default:
                throw new IllegalArgumentException("未知的规则类型: " + rule.getType());
        }

        // 移除被禁用的用户
        removeDisableUsers(assigneeUserIds);
        // 如果候选人为空，抛出异常
        if (CollUtil.isEmpty(assigneeUserIds)) {
            log.error("[calculateTaskCandidateUsers][流程任务({}/{}/{}) 任务规则({}) 找不到候选人]", execution.getId(),
                    execution.getProcessDefinitionId(), execution.getCurrentActivityId(), toJsonString(rule));
            throw new ApiException(TASK_CREATE_FAIL_NO_CANDIDATE_USER);
        }
        return assigneeUserIds;
    }

    @VisibleForTesting
    void removeDisableUsers(Set<Long> assigneeUserIds) {
        if (CollUtil.isEmpty(assigneeUserIds)) {
            return;
        }
        Map<Integer, UserListForWorkflowDTO> userMap = userForWorkflowService.getUserListByUserId(assigneeUserIds.stream().mapToInt(Long::intValue).boxed().collect(Collectors.toList()), true)
                .stream()
                .collect(Collectors.toMap(UserListForWorkflowDTO::getUserId, Function.identity()));
        assigneeUserIds.removeIf(id -> {
            UserListForWorkflowDTO dto = userMap.get(Integer.valueOf(id + ""));
            return dto == null || Objects.equals(dto.getUserStatus(), GovUserStatusEnum.STOP.getCode());
        });
    }

    /**
     * 发起车辆所属单位参数
     **/
    public static final String PROCESS_VARIABLES_BELONG_DEPT_CODE_PARAMETER = "vehicleBelongDeptCode";

    private Set<Long> calculateTaskCandidateUsersByInitiatorVehicleUnitRole(DelegateExecution execution, BpmTaskAssignRule rule) {
        log.info("发起车辆所属单位参数，查询executionId{}", execution.getId());
        Object variable = runtimeService.getVariable(execution.getId(), PROCESS_VARIABLES_BELONG_DEPT_CODE_PARAMETER);
        if (variable == null) {
            return new HashSet<>();
        }
        // 新的计算发起车辆单位角色的候选用户逻辑
        String vehicleBelongDeptCode = variable.toString();
        if (StringUtils.isEmpty(vehicleBelongDeptCode)) {
            return new HashSet<>();
        }
        // 获取使用人单位下符合角色的员工
        List<String> roleCodeList = Arrays.asList(rule.getOptions().split(","));
        List<UserListForWorkflowDTO> userListByRoleAndStruct = userForWorkflowService.getUserListByRoleAndStruct(roleCodeList, null, vehicleBelongDeptCode);
        if (CollUtil.isEmpty(userListByRoleAndStruct)) {
            return new HashSet<>();
        }
        return userListByRoleAndStruct.stream().map(UserListForWorkflowDTO::getUserId).map(Long::valueOf).collect(Collectors.toSet());
    }

    private Set<Long> calculateTaskCandidateUsersByUser(BpmTaskAssignRule rule) {
        String options = rule.getOptions();
        List<String> list = Arrays.asList(options.split(","));
        return list.stream().map(Long::valueOf).collect(Collectors.toSet());
    }

    private Set<Long> calculateTaskCandidateUsersByUserGroup(BpmTaskAssignRule rule) {
        List<String> list = Arrays.asList(rule.getOptions().split(","));
        List<BpmUserGroup> userGroups = userGroupService.getUserGroupList(list.stream().map(Long::valueOf).collect(Collectors.toSet()));
        Set<Long> userIds = new HashSet<>();
        userGroups.forEach(group -> {
            List<String> memberUserIds = Arrays.asList(group.getMemberUserIds().split(","));
            userIds.addAll(memberUserIds.stream().map(Long::valueOf).collect(Collectors.toSet()));
        });
        return userIds;
    }

    private Set<Long> calculateTaskCandidateUsersByInitiatorDeptLeader(DelegateExecution execution) {
        // 获得发起人
        ProcessInstance processInstance = bpmProcessInstanceService.getProcessInstance(execution.getProcessInstanceId());
        Long startUserId = Long.valueOf(processInstance.getStartUserId());
        // 获得发起人所在单位
        GovStructForWorkflowDTO structByUser = userForWorkflowService.getStructByUser(startUserId);
        if (Objects.isNull(structByUser)) {
            return Collections.emptySet();
        }
        return structByUser.getLeaderIdList().stream().map(Long::valueOf).collect(Collectors.toSet());
    }

    private Set<Long> calculateTaskCandidateUsersByAssignRole(DelegateExecution execution, BpmTaskAssignRule rule) {
        List<String> roleCodeList = Arrays.asList(rule.getOptions().split(","));
        ProcessInstance processInstance = bpmProcessInstanceService.getProcessInstance(execution.getProcessInstanceId());
        Integer companyId = Integer.valueOf(processInstance.getTenantId());
        // 根据角色编码和公司id获取有效用户列表
        List<UserListForWorkflowDTO> userListByRole = userForWorkflowService.getUserListByRole(roleCodeList, companyId);
        if (CollUtil.isEmpty(userListByRole)) {
            return Collections.emptySet();
        }
        return userListByRole.stream().map(UserListForWorkflowDTO::getUserId).map(Long::valueOf).collect(Collectors.toSet());
    }
}
