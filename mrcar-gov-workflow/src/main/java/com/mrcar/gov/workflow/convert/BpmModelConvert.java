package com.mrcar.gov.workflow.convert;

import cn.hutool.core.text.CharSequenceUtil;
import com.mrcar.gov.common.dto.workflow.definition.BpmModelMetaInfoRespDTO;
import com.mrcar.gov.common.dto.workflow.definition.BpmProcessDefinitionCreateReqDTO;
import com.mrcar.gov.common.dto.workflow.model.*;
import com.mrcar.gov.common.util.CollectionUtils;
import com.mrcar.gov.user.service.GovStructService;
import com.mrcar.gov.workflow.domain.ActReModel;
import com.mrcar.gov.workflow.domain.BpmForm;
import com.mrcar.gov.workflow.utils.JsonUtils;
import org.flowable.common.engine.impl.db.SuspensionState;
import org.flowable.engine.repository.Deployment;
import org.flowable.engine.repository.Model;
import org.flowable.engine.repository.ProcessDefinition;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 流程模型 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface BpmModelConvert {

    BpmModelConvert INSTANCE = Mappers.getMapper(BpmModelConvert.class);


    default List<BpmModelPageItemRespDTO> convertList(List<ActReModel> list, Map<Long, BpmForm> formMap,
                                                      Map<String, Deployment> deploymentMap,
                                                      Map<String, ProcessDefinition> processDefinitionMap) {
        return CollectionUtils.convertList(list, model -> {
            BpmModelMetaInfoRespDTO metaInfo = JsonUtils.parseObject(model.getMetaInfo(), BpmModelMetaInfoRespDTO.class);
            BpmForm form = metaInfo != null ? formMap.get(metaInfo.getFormId()) : null;
            Deployment deployment = model.getDeploymentId() != null ? deploymentMap.get(model.getDeploymentId()) : null;
            ProcessDefinition processDefinition = model.getDeploymentId() != null ? processDefinitionMap.get(model.getDeploymentId()) : null;
            return convert(model, form, deployment, processDefinition);
        });
    }

    default BpmModelPageItemRespDTO convert(ActReModel model, BpmForm form, Deployment deployment, ProcessDefinition processDefinition) {
        BpmModelPageItemRespDTO modelRespDTO = new BpmModelPageItemRespDTO();
        modelRespDTO.setId(model.getId());
        modelRespDTO.setCreateTime(model.getCreateTime());
        modelRespDTO.setCompanyId(Integer.valueOf(model.getTenantId()));
        // 通用 copy
        copyTo(model, modelRespDTO);
        // Form
        if (form != null) {
            modelRespDTO.setFormId(form.getId());
            modelRespDTO.setFormName(form.getName());
        }
        // ProcessDefinition
        modelRespDTO.setProcessDefinition(this.convert(processDefinition));
        if (modelRespDTO.getProcessDefinition() != null) {
            modelRespDTO.getProcessDefinition().setSuspensionState(processDefinition.isSuspended() ?
                    SuspensionState.SUSPENDED.getStateCode() : SuspensionState.ACTIVE.getStateCode());
            modelRespDTO.getProcessDefinition().setDeploymentTime(deployment.getDeploymentTime());
            modelRespDTO.getProcessDefinition().setBatchApproval(model.getBatchApproval());
        }
        modelRespDTO.setParentProcessId(model.getParentModelId());
        modelRespDTO.setConnectSwitch(model.getConnectSwitch() ? 1 : 0);
        modelRespDTO.setParentProcessStr("-");
        //只有当前流程关联了父流程，则设置父流程名字
        if (modelRespDTO.getConnectSwitch() == 1) {
            //设置父流程
            modelRespDTO.setParentProcessStr(model.getParentModelName());
        }

        return modelRespDTO;
    }

    default BpmModelRespDTO convert(ActReModel model) {
        BpmModelRespDTO modelRespDTO = new BpmModelRespDTO();
        modelRespDTO.setId(model.getId());
        modelRespDTO.setCreateTime(model.getCreateTime());
        modelRespDTO.setBusinessType(model.getBusinessType());
        // 通用 copy
        copyTo(model, modelRespDTO);
        return modelRespDTO;
    }

    default void copyTo(ActReModel model, BpmModelBaseDTO to) {
        to.setName(model.getName());
        to.setKey(model.getKey());
        to.setCategory(model.getCategory());
        to.setBusinessType(model.getBusinessType());
        to.setStructId(model.getStructId());
        // metaInfo
        BpmModelMetaInfoRespDTO metaInfo = JsonUtils.parseObject(model.getMetaInfo(), BpmModelMetaInfoRespDTO.class);
        copyTo(metaInfo, to);
    }

    BpmModelCreateReqDTO convert(BpmModeImportReqDTO bean);

    default BpmProcessDefinitionCreateReqDTO convert2(Model model, BpmForm form) {
        BpmProcessDefinitionCreateReqDTO createReqDTO = new BpmProcessDefinitionCreateReqDTO();
        createReqDTO.setModelId(model.getId());
        createReqDTO.setName(model.getName());
        createReqDTO.setKey(model.getKey());
        createReqDTO.setCategory(model.getCategory());
        BpmModelMetaInfoRespDTO metaInfo = JsonUtils.parseObject(model.getMetaInfo(), BpmModelMetaInfoRespDTO.class);
        // metaInfo
        copyTo(metaInfo, createReqDTO);
        // form
        if (form != null) {
            createReqDTO.setFormConf(form.getConf());
            createReqDTO.setFormFields(form.getFields());
        }
        return createReqDTO;
    }

    void copyTo(BpmModelMetaInfoRespDTO from, @MappingTarget BpmProcessDefinitionCreateReqDTO to);

    void copyTo(BpmModelMetaInfoRespDTO from, @MappingTarget BpmModelBaseDTO to);

    BpmModelPageItemRespDTO.ProcessDefinition convert(ProcessDefinition bean);

    default void copy(Model model, BpmModelCreateReqDTO bean) {
        model.setName(bean.getName());
        model.setKey(bean.getKey());
        model.setCategory(bean.getCategory());
        model.setMetaInfo(buildMetaInfoStr(JsonUtils.parseObject(model.getMetaInfo(), BpmModelMetaInfoRespDTO.class),
                bean.getDescription(), bean.getFormType(), bean.getFormId(),
                null, bean.getFormCustomViewPath()));
    }

    default void copy(Model model, BpmModelUpdateReqDTO bean) {
        model.setName(bean.getName());
        model.setCategory(bean.getCategory());
        model.setMetaInfo(buildMetaInfoStr(JsonUtils.parseObject(model.getMetaInfo(), BpmModelMetaInfoRespDTO.class),
                bean.getDescription(), bean.getFormType(), bean.getFormId(),
                bean.getFormCustomCreatePath(), bean.getFormCustomViewPath()));

    }

    default void copyDefault(Model model, BpmModelUpdateReqDTO bean) {
        model.setMetaInfo(buildMetaInfoStr(JsonUtils.parseObject(model.getMetaInfo(), BpmModelMetaInfoRespDTO.class),
                bean.getDescription(), bean.getFormType(), bean.getFormId(),
                bean.getFormCustomCreatePath(), bean.getFormCustomViewPath()));

    }

    default void copy(Model model, Model bean) {
        model.setName(bean.getName());
        model.setKey(bean.getKey());
        model.setCategory(bean.getCategory());
        model.setMetaInfo(bean.getMetaInfo());
    }

    default String buildMetaInfoStr(BpmModelMetaInfoRespDTO metaInfo, String description, Integer formType,
                                    Long formId, String formCustomCreatePath, String formCustomViewPath) {
        if (metaInfo == null) {
            metaInfo = new BpmModelMetaInfoRespDTO();
        }
        // 只有非空，才进行设置，避免更新时的覆盖
        if (CharSequenceUtil.isNotEmpty(description)) {
            metaInfo.setDescription(description);
        } else {
            metaInfo.setDescription("");
        }
        if (Objects.nonNull(formType)) {
            metaInfo.setFormType(formType);
            metaInfo.setFormId(formId);
            metaInfo.setFormCustomCreatePath(formCustomCreatePath);
            metaInfo.setFormCustomViewPath(formCustomViewPath);
        }
        return JsonUtils.toJsonString(metaInfo);
    }
}
