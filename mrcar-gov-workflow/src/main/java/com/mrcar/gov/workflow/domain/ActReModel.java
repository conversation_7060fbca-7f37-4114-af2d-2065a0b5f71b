package com.mrcar.gov.workflow.domain;

import java.util.Date;

/**
 * （表名：act_re_model）
 * <br>
 * <br>
 * 【重要提示】：<br>
 * &nbsp;&nbsp;此类通过 Mybatis Generator 逆向生成，禁止手动修改！<br>（因项目需求持续性会发生变更，当调整数据表字段时，需要再重新逆向生成此类）
 **/
public class ActReModel {
    /**
     *
     **/
    private String id;

    /**
     *
     **/
    private Integer rev;

    /**
     *
     **/
    private String name;

    /**
     *
     **/
    private String key;

    /**
     *
     **/
    private String category;

    /**
     *
     **/
    private Date createTime;

    /**
     *
     **/
    private Date lastUpdateTime;

    /**
     *
     **/
    private Integer version;

    /**
     *
     **/
    private String metaInfo;

    /**
     *
     **/
    private String deploymentId;

    /**
     *
     **/
    private String editorSourceValueId;

    /**
     *
     **/
    private String editorSourceExtraValueId;

    /**
     *
     **/
    private String tenantId;

    /**
     * 业务类型
     **/
    private Byte businessType;

    /**
     * 批量审批 1:是, 0:否
     **/
    private Boolean batchApproval;

    /**
     * 是否关联上级流程 0：否 1：是
     **/
    private Boolean connectSwitch;

    /**
     * 关联父流程id
     **/
    private String parentModelId;

    /**
     * 关联父级流程name
     **/
    private String parentModelName;


    /**
     * 流程对应公司部位单位/区域
     */
    private Integer structId;


    /**
     *
     **/
    public String getId() {
        return id;
    }

    /**
     *
     **/
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     *
     **/
    public Integer getRev() {
        return rev;
    }

    /**
     *
     **/
    public void setRev(Integer rev) {
        this.rev = rev;
    }

    /**
     *
     **/
    public String getName() {
        return name;
    }

    /**
     *
     **/
    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    /**
     *
     **/
    public String getKey() {
        return key;
    }

    /**
     *
     **/
    public void setKey(String key) {
        this.key = key == null ? null : key.trim();
    }

    /**
     *
     **/
    public String getCategory() {
        return category;
    }

    /**
     *
     **/
    public void setCategory(String category) {
        this.category = category == null ? null : category.trim();
    }

    /**
     *
     **/
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     **/
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     *
     **/
    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    /**
     *
     **/
    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    /**
     *
     **/
    public Integer getVersion() {
        return version;
    }

    /**
     *
     **/
    public void setVersion(Integer version) {
        this.version = version;
    }

    /**
     *
     **/
    public String getMetaInfo() {
        return metaInfo;
    }

    /**
     *
     **/
    public void setMetaInfo(String metaInfo) {
        this.metaInfo = metaInfo == null ? null : metaInfo.trim();
    }

    /**
     *
     **/
    public String getDeploymentId() {
        return deploymentId;
    }

    /**
     *
     **/
    public void setDeploymentId(String deploymentId) {
        this.deploymentId = deploymentId == null ? null : deploymentId.trim();
    }

    /**
     *
     **/
    public String getEditorSourceValueId() {
        return editorSourceValueId;
    }

    /**
     *
     **/
    public void setEditorSourceValueId(String editorSourceValueId) {
        this.editorSourceValueId = editorSourceValueId == null ? null : editorSourceValueId.trim();
    }

    /**
     *
     **/
    public String getEditorSourceExtraValueId() {
        return editorSourceExtraValueId;
    }

    /**
     *
     **/
    public void setEditorSourceExtraValueId(String editorSourceExtraValueId) {
        this.editorSourceExtraValueId = editorSourceExtraValueId == null ? null : editorSourceExtraValueId.trim();
    }

    /**
     *
     **/
    public String getTenantId() {
        return tenantId;
    }

    /**
     *
     **/
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId == null ? null : tenantId.trim();
    }

    /**
     * 业务类型
     **/
    public Byte getBusinessType() {
        return businessType;
    }

    /**
     * 业务类型
     **/
    public void setBusinessType(Byte businessType) {
        this.businessType = businessType;
    }

    /**
     * 批量审批 1:是, 0:否
     **/
    public Boolean getBatchApproval() {
        return batchApproval;
    }

    /**
     * 批量审批 1:是, 0:否
     **/
    public void setBatchApproval(Boolean batchApproval) {
        this.batchApproval = batchApproval;
    }

    /**
     * 是否关联上级流程 0：否 1：是
     **/
    public Boolean getConnectSwitch() {
        return connectSwitch;
    }

    /**
     * 是否关联上级流程 0：否 1：是
     **/
    public void setConnectSwitch(Boolean connectSwitch) {
        this.connectSwitch = connectSwitch;
    }

    /**
     * 关联父流程id
     **/
    public String getParentModelId() {
        return parentModelId;
    }

    /**
     * 关联父流程id
     **/
    public void setParentModelId(String parentModelId) {
        this.parentModelId = parentModelId == null ? null : parentModelId.trim();
    }

    /**
     * 关联父级流程name
     **/
    public String getParentModelName() {
        return parentModelName;
    }

    /**
     * 关联父级流程name
     **/
    public void setParentModelName(String parentModelName) {
        this.parentModelName = parentModelName == null ? null : parentModelName.trim();
    }

    public Integer getStructId() {
        return this.structId;
    }

    public void setStructId(Integer structId) {
        this.structId = structId;
    }
}