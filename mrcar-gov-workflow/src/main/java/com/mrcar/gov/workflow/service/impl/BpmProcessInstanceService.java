package com.mrcar.gov.workflow.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.page.PageMethod;
import com.izu.framework.exception.ApiException;
import com.izu.framework.response.PageDTO;
import com.izu.framework.web.util.BeanUtil;
import com.mrcar.gov.base.service.SequenceGenerator;
import com.mrcar.gov.common.dto.workflow.BaseDTO;
import com.mrcar.gov.common.dto.workflow.copy.CopyProcessSaveReqDTO;
import com.mrcar.gov.common.dto.workflow.enums.BpmModelCategoryEnum;
import com.mrcar.gov.common.dto.workflow.enums.BpmProcessInstanceDeleteReasonEnum;
import com.mrcar.gov.common.dto.workflow.enums.BpmProcessInstanceResultEnum;
import com.mrcar.gov.common.dto.workflow.enums.BpmProcessInstanceStatusEnum;
import com.mrcar.gov.common.dto.workflow.instance.*;
import com.mrcar.gov.common.dto.workflow.task.BpmTaskRejectReqDTO;
import com.mrcar.gov.common.dto.workflow.task.BpmTaskRespDTO;
import com.mrcar.gov.common.util.CollectionUtils;
import com.mrcar.gov.common.util.EmojiUtils;
import com.mrcar.gov.base.common.SeqPrefix;
import com.mrcar.gov.user.domain.GovStruct;
import com.mrcar.gov.user.domain.GovUser;
import com.mrcar.gov.user.service.GovStructService;
import com.mrcar.gov.user.service.GovUserService;
import com.mrcar.gov.workflow.convert.BpmProcessInstanceConvert;
import com.mrcar.gov.workflow.domain.*;
import com.mrcar.gov.workflow.event.BpmProcessInstanceResultEventPublisher;
import com.mrcar.gov.workflow.mapper.BpmProcessInstanceCopyMapper;
import com.mrcar.gov.workflow.mapper.ex.ActReModelExMapper;
import com.mrcar.gov.workflow.mapper.ex.BpmProcessDefinitionExtExMapper;
import com.mrcar.gov.workflow.mapper.ex.BpmProcessInstanceExtExMapper;
import com.mrcar.gov.workflow.message.BpmMessageService;
import com.mrcar.gov.workflow.service.CustomContinueProcessOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.flowable.bpmn.model.BpmnModel;
import org.flowable.bpmn.model.FlowElement;
import org.flowable.bpmn.model.StartEvent;
import org.flowable.common.engine.impl.identity.Authentication;
import org.flowable.common.engine.impl.service.CommonEngineServiceImpl;
import org.flowable.engine.HistoryService;
import org.flowable.engine.ManagementService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.delegate.event.FlowableCancelledEvent;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.impl.persistence.entity.ExecutionEntity;
import org.flowable.engine.impl.persistence.entity.ExecutionEntityManager;
import org.flowable.engine.impl.util.CommandContextUtil;
import org.flowable.engine.impl.util.ProcessDefinitionUtil;
import org.flowable.engine.impl.util.TaskHelper;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.task.api.Task;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

import static com.mrcar.gov.common.constant.workflow.WorkflowErrorCode.*;

/**
 * 流程实例 Service 实现类
 * <p>
 * ProcessDefinition & ProcessInstance & Execution & Task 的关系：
 * 1. https://blog.csdn.net/bobozai86/article/details/105210414
 * <p>
 * HistoricProcessInstance & ProcessInstance 的关系：
 * 1.https://my.oschina.net/843294669/blog/719024
 * 简单来说，前者 = 历史 + 运行中的流程实例，后者仅是运行中的流程实例
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class BpmProcessInstanceService extends CommonEngineServiceImpl {

    @Resource
    private RuntimeService runtimeService;
    @Resource
    private BpmProcessInstanceExtExMapper processInstanceExtMapper;
    @Resource
    @Lazy
    private BpmTaskService taskService;
    @Resource
    private BpmProcessDefinitionService processDefinitionService;
    @Resource
    private HistoryService historyService;
    @Resource
    private BpmProcessInstanceResultEventPublisher processInstanceResultEventPublisher;
    @Resource
    private BpmMessageService messageService;
    @Resource
    private BpmProcessDefinitionExtExMapper processDefinitionMapper;
    @Resource
    private ActReModelExMapper modelMapper;
    @Resource
    ManagementService managementService;
    @Resource
    private BpmProcessInstanceCopyMapper bpmProcessInstanceCopyMapper;
    @Resource
    private SequenceGenerator sequenceGenerator;
    @Resource
    private GovUserService govUserService;

    @Resource
    private GovStructService govStructService;

    public ProcessInstance getProcessInstance(String id) {
        return runtimeService.createProcessInstanceQuery().processInstanceId(id).singleResult();
    }

    public BpmProcessInstanceExt getProcessInstanceExt(String id) {
        return processInstanceExtMapper.selectByProcessInstanceId(id);
    }

    public List<ProcessInstance> getProcessInstances(Set<String> ids) {
        return runtimeService.createProcessInstanceQuery().processInstanceIds(ids).list();
    }

    public PageDTO<BpmProcessInstancePageItemRespDTO> getMyProcessInstancePage(BpmProcessInstanceMyPageReqDTO pageReqDTO) {
        // 通过 BpmProcessInstanceExt 表，先查询到对应的分页
        Integer pageNum = pageReqDTO.getPageNum();
        Integer pageSize = pageReqDTO.getPageSize();
        List<BpmProcessInstanceExt> bpmProcessInstanceExts;
        try {
            Page<Object> page = PageMethod.startPage(pageNum, pageSize, true);
            bpmProcessInstanceExts = processInstanceExtMapper.selectPage(pageReqDTO);
            if (bpmProcessInstanceExts.isEmpty()) {
                return new PageDTO<BpmProcessInstancePageItemRespDTO>(pageNum, pageSize, 0, Collections.emptyList());
            }
            long total = page.getTotal();
            // 获得流程 Task Map
            List<String> processInstanceIds = CollectionUtils.convertList(bpmProcessInstanceExts, BpmProcessInstanceExt::getProcessInstanceId);
            Map<String, List<Task>> taskMap = taskService.getTaskMapByProcessInstanceIds(processInstanceIds);
            // 针对会签、或签，同一个审批任务多人审批，展示一个审批任务
            Map<String, List<Task>> taskMapDistinct = taskMap.entrySet().stream()
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            taskListEntry -> taskListEntry.getValue().stream()
                                    .collect(
                                            Collectors.collectingAndThen(
                                                    Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(task -> StringUtils.isNotBlank(task.getName()) ? task.getName() : ""))),
                                                    ArrayList::new
                                            )
                                    )));
            // 转换返回
            List<BpmProcessInstancePageItemRespDTO> bpmProcessInstancePageItemRespDTOS = BpmProcessInstanceConvert.INSTANCE.convertPage(bpmProcessInstanceExts, taskMapDistinct);
            //查询当前页的流程定义，哪些是还有子流程关联的
            List<String> modelIds = bpmProcessInstanceExts.stream().map(BpmProcessInstanceExt::getModelId).distinct().filter(StringUtils::isNotBlank).collect(Collectors.toList());
            if (modelIds.size() > 0) {
                //去模型库查询哪些有子流程
                List<String> searchModelDTOS = modelMapper.selectSubProcessModel(modelIds);
                if (searchModelDTOS != null && searchModelDTOS.size() > 0) {
                    bpmProcessInstancePageItemRespDTOS.forEach(item -> {
                        //首先有下级流程&&当前任务流程为2通过&&（子流程结果为0未发起下级流程||子流程结果为3不通过||子流程结果为4已取消||子流程结果为5退回）
                        Boolean buttonApprove = searchModelDTOS.contains(item.getModelId())
                                && BpmProcessInstanceResultEnum.APPROVE.getResult().intValue() == item.getResult()
                                && (item.getAssociationProcessStatus() == 0
                                || BpmProcessInstanceResultEnum.REJECT.getResult().equals(item.getAssociationProcessStatus())
                                || BpmProcessInstanceResultEnum.CANCEL.getResult().equals(item.getAssociationProcessStatus())
                                || BpmProcessInstanceResultEnum.BACK.getResult().equals(item.getAssociationProcessStatus()));
                        item.setButtonApprove(buttonApprove);
                    });
                }
            }
            return new PageDTO<BpmProcessInstancePageItemRespDTO>(pageNum, pageSize, Integer.parseInt(total + ""), bpmProcessInstancePageItemRespDTOS);
        } catch (Exception e) {
            log.error("查询我的流程列表异常", e);
            return new PageDTO<BpmProcessInstancePageItemRespDTO>(pageNum, pageSize, 0, Collections.emptyList());
        } finally {
            PageMethod.clearPage();
        }

    }

    @Transactional(rollbackFor = Exception.class)
    public String createProcessInstance(@Valid BpmProcessInstanceCreateReqDTO createReqDTO) {
        //校验是否有非法字符输入
        String variables = createReqDTO.getVariables();
        if (EmojiUtils.containsEmoji(variables)) {
            throw new ApiException(DO_NOT_INPUT_CONTAINS_EMOJI_REASON);
        }
        String processDefinitionId = createReqDTO.getProcessDefinitionId();
        // 获得流程定义
        ProcessDefinition definition = processDefinitionService.getProcessDefinition(processDefinitionId);
        BpmProcessDefinitionExt definitionExt = processDefinitionMapper.selectByProcessDefinitionId(processDefinitionId);
        ActReModel model = modelMapper.selectByPrimaryKey(definitionExt.getModelId());
        if (model == null) {
            throw new ApiException(PROCESS_DEFINITION_BPMN_MODEL_NOT_EXISTS);
        }
        // 生成自定义流程编号
        String businessNo = sequenceGenerator.generate(new Date(), SeqPrefix.CUSTOM_WORKFLOW_PROCESS_NUMBER);
        // 发起流程
        return createProcessInstance0(createReqDTO, definition, businessNo, model.getBusinessType());
    }

    @Transactional(rollbackFor = Exception.class)
    public String applyStart(@Valid ApplyStartDTO applyStartDTO) {
        Byte businessType = applyStartDTO.getBusinessType();
        String businessNo = applyStartDTO.getBusinessNo();
        String variables = applyStartDTO.getVariables();
        // 校验是否已存在回退驳回的流程
        BpmProcessInstanceExtExample instanceExtExample = new BpmProcessInstanceExtExample();
        BpmProcessInstanceExtExample.Criteria instanceExtWhere = instanceExtExample.createCriteria();
        instanceExtWhere.andBusinessTypeEqualTo(businessType);
        instanceExtWhere.andBusinessNoEqualTo(businessNo);
        List<BpmProcessInstanceExt> instanceExts = processInstanceExtMapper.selectByExample(instanceExtExample);
        if (!instanceExts.isEmpty()) {
            BpmProcessInstanceExt instanceExt = instanceExts.get(0);
            if (BpmProcessInstanceResultEnum.PROCESS.getResult().equals(instanceExt.getResult())) {
                throw new ApiException(PROCESS_INSTANCE_START_FAIL_IN_PROCESS);
            } else if (BpmProcessInstanceResultEnum.BACK.getResult().equals(instanceExt.getResult())) {
                String processInstanceId = instanceExt.getProcessInstanceId();
                // 重启流程
                startProcessInstance(processInstanceId, variables);
                // 修改流程状态
                updateProcessInstanceExtBack(processInstanceId, BpmProcessInstanceResultEnum.PROCESS.getResult());
                return processInstanceId;
            }
        }
        // 获得流程模型
        ActReModelExample where = new ActReModelExample();
        ActReModelExample.Criteria criteria = where.createCriteria();
        criteria.andBusinessTypeEqualTo(businessType);
        criteria.andTenantIdEqualTo(applyStartDTO.getLoginCompanyId() + "");
        List<ActReModel> model = modelMapper.selectByExample(where);
        if (CollectionUtils.isAnyEmpty(model)) {
            throw new ApiException(PROCESS_DEFINITION_NOT_EXISTS);
        }
        // 获得流程定义
        //获取对应单位的流程
        ActReModel actReModel = getActReModelByStructId(model, applyStartDTO.getLoginDeptId(), applyStartDTO.getLoginCompanyId());
        String deploymentId = actReModel.getDeploymentId();
        if (StringUtils.isBlank(deploymentId)) {
            throw new ApiException(PROCESS_DEFINITION_NOT_EXISTS);
        }
        // 获得流程定义
        ProcessDefinition definition = processDefinitionService.getProcessDefinitionByDeploymentId(deploymentId);
        BpmProcessInstanceCreateReqDTO createReqDTO = BeanUtil.copyObject(applyStartDTO, BpmProcessInstanceCreateReqDTO.class);
        createReqDTO.setVariables(variables);
        // 发起流程
        return createProcessInstance0(createReqDTO, definition, businessNo, businessType);
    }

    public BpmProcessInstanceRespDTO getProcessInstanceDTO(String id) {
        // 获得流程实例
        HistoricProcessInstance processInstance = getHistoricProcessInstance(id);
        if (processInstance == null) {
            return null;
        }
        BpmProcessInstanceExt processInstanceExt = processInstanceExtMapper.selectByProcessInstanceId(id);
        Assert.notNull(processInstanceExt, "流程实例拓展({}) 不存在", id);

        // 获得流程定义
        ProcessDefinition processDefinition = processDefinitionService.getProcessDefinition(processInstance.getProcessDefinitionId());
        Assert.notNull(processDefinition, "流程定义({}) 不存在", processInstance.getProcessDefinitionId());
        BpmProcessDefinitionExt processDefinitionExt = processDefinitionService.getProcessDefinitionExt(processInstance.getProcessDefinitionId());
        Assert.notNull(processDefinitionExt, "流程定义拓展({}) 不存在", id);
        String bpmnXml = processDefinitionService.getProcessDefinitionBpmnXML(processInstance.getProcessDefinitionId());
        // 拼接结果
        BpmProcessInstanceRespDTO bpmProcessInstanceRespDTO = BpmProcessInstanceConvert.INSTANCE.convert2(processInstance, processInstanceExt, processDefinition, processDefinitionExt, bpmnXml);
        //查询是否有相关流程实例相关抄送记录
        BpmProcessInstanceCopyExample bpmProcessInstanceCopyExample = new BpmProcessInstanceCopyExample();
        bpmProcessInstanceCopyExample.createCriteria().andProcessInstanceIdEqualTo(id);
        List<BpmProcessInstanceCopy> bpmProcessInstanceCopies = bpmProcessInstanceCopyMapper.selectByExample(bpmProcessInstanceCopyExample);
        if (CollectionUtil.isNotEmpty(bpmProcessInstanceCopies)) {
            List<CopyProcessSaveReqDTO.CopyUser> copyUsers = bpmProcessInstanceCopies.stream().map(item -> {
                CopyProcessSaveReqDTO.CopyUser copyUser = new CopyProcessSaveReqDTO.CopyUser();
                copyUser.setCopyUserName(item.getCopyUserName());
                copyUser.setCopyUserMobile(item.getCopyUserMobile());
                copyUser.setCopyUserId(item.getCopyUserId());
                return copyUser;
            }).distinct().collect(Collectors.toList());
            bpmProcessInstanceRespDTO.setCopyUserList(copyUsers);
        }
        return bpmProcessInstanceRespDTO;
    }

    public void cancelProcessInstance(@Valid BpmProcessInstanceCancelReqDTO cancelReqDTO) {
        // 校验流程实例存在
        ProcessInstance instance = getProcessInstance(cancelReqDTO.getId());
        if (instance == null) {
            throw new ApiException(PROCESS_INSTANCE_CANCEL_FAIL_NOT_EXISTS);
        }
        // 只能取消自己的 当前登录用户ID为0为系统取消
        if (cancelReqDTO.getLoginUserId() != 0 && !Objects.equals(instance.getStartUserId(), String.valueOf(cancelReqDTO.getLoginUserId()))) {
            throw new ApiException(PROCESS_INSTANCE_CANCEL_FAIL_NOT_SELF);
        }

        // 通过删除流程实例，实现流程实例的取消,
        // 删除流程实例，正则执行任务ACT_RU_TASK. 任务会被删除。通过历史表查询
        deleteProcessInstance(cancelReqDTO.getId(), BpmProcessInstanceDeleteReasonEnum.CANCEL_TASK.format(cancelReqDTO.getReason()));
        //查询实例扩展表
        BpmProcessInstanceExt processInstanceExt = processInstanceExtMapper.selectByProcessInstanceId(instance.getId());
        //如果是OA
        if (BpmModelCategoryEnum.OA.getType().equals(processInstanceExt.getCategory())) {
            //如果当前实例的parent_process_instance_id不为空，此时说明子流程正在完成审批，需要将主流程中的冗余的子流程状态和结果更新
            if (StringUtils.isNotEmpty(processInstanceExt.getParentProcessInstanceId())) {
                BpmProcessInstanceExt updateInstance = new BpmProcessInstanceExt();
                updateInstance.setProcessInstanceId(processInstanceExt.getParentProcessInstanceId());
                updateInstance.setSubProcessInstanceStatus(BpmProcessInstanceStatusEnum.FINISH.getStatus());
                updateInstance.setSubProcessInstanceResult(BpmProcessInstanceResultEnum.CANCEL.getResult());
                processInstanceExtMapper.updateByProcessInstanceId(updateInstance);
            }
        }
        if (cancelReqDTO.getIsBusiness() == null || !cancelReqDTO.getIsBusiness()) {
            // 非业务口取消发送取消通知消息
            BpmProcessInstanceExt bpmProcessInstanceExt = processInstanceExtMapper.selectByProcessInstanceId(instance.getProcessInstanceId());
            // 发送流程被取消的消息
            messageService.sendMessageWhenTaskCancel(BpmProcessInstanceConvert.INSTANCE.convert2ApprovedResult(bpmProcessInstanceExt, cancelReqDTO, BpmProcessInstanceResultEnum.CANCEL.getResult(), cancelReqDTO.getReason()));
        }
    }

    /**
     * 获得历史的流程实例
     *
     * @param id 流程实例的编号
     * @return 历史的流程实例
     */
    public HistoricProcessInstance getHistoricProcessInstance(String id) {
        return historyService.createHistoricProcessInstanceQuery().processInstanceId(id).singleResult();
    }

    public List<HistoricProcessInstance> getHistoricProcessInstances(Set<String> ids) {
        return historyService.createHistoricProcessInstanceQuery().processInstanceIds(ids).list();
    }

    public void createProcessInstanceExt(ProcessInstance instance) {
        // 获得流程定义
        ProcessDefinition definition = processDefinitionService.getProcessDefinition2(instance.getProcessDefinitionId());
        // 插入 BpmProcessInstanceExt 对象
        BpmProcessInstanceExt instanceExt = new BpmProcessInstanceExt();
        instanceExt.setProcessInstanceId(instance.getId());
        instanceExt.setProcessDefinitionId(definition.getId());
        instanceExt.setName(instance.getProcessDefinitionName());
        instanceExt.setStartUserId(Long.valueOf(instance.getStartUserId()));
        instanceExt.setCategory(definition.getCategory());
        instanceExt.setStatus(BpmProcessInstanceStatusEnum.RUNNING.getStatus());
        instanceExt.setResult(BpmProcessInstanceResultEnum.PROCESS.getResult());
        processInstanceExtMapper.insertSelective(instanceExt);
    }

    public void updateProcessInstanceExtCancel(FlowableCancelledEvent event) {
        // 判断是否为 Reject 不通过。如果是，则不进行更新
        if (BpmProcessInstanceDeleteReasonEnum.isRejectReason((String) event.getCause())) {
            return;
        }

        // 需要主动查询，因为 instance 只有 id 属性
        // 另外，此时如果去查询 ProcessInstance 的话，字段是不全的，所以去查询了 HistoricProcessInstance
        HistoricProcessInstance processInstance = getHistoricProcessInstance(event.getProcessInstanceId());
        // 更新拓展表
        BpmProcessInstanceExt instanceExt = new BpmProcessInstanceExt();
        instanceExt.setProcessInstanceId(event.getProcessInstanceId());
        instanceExt.setEndTime(new Date()); // 由于 ProcessInstance 里没有办法拿到 endTime，所以这里设置
        instanceExt.setStatus(BpmProcessInstanceStatusEnum.FINISH.getStatus());
        instanceExt.setResult(BpmProcessInstanceResultEnum.CANCEL.getResult());
        processInstanceExtMapper.updateByProcessInstanceId(instanceExt);

        // 发送流程实例的状态事件
        processInstanceResultEventPublisher.sendProcessInstanceResultEvent(BpmProcessInstanceConvert.INSTANCE.convert(this, processInstance, Integer.valueOf(instanceExt.getResult())));
    }

    public void updateProcessInstanceExtComplete(ProcessInstance instance) {
        // 需要主动查询，因为 instance 只有 id 属性
        // 另外，此时如果去查询 ProcessInstance 的话，字段是不全的，所以去查询了 HistoricProcessInstance
        HistoricProcessInstance processInstance = getHistoricProcessInstance(instance.getId());

        // 更新拓展表
        BpmProcessInstanceExt instanceExt = new BpmProcessInstanceExt();
        instanceExt.setProcessInstanceId(instance.getProcessInstanceId());
        instanceExt.setEndTime(new Date()); // 由于 ProcessInstance 里没有办法拿到 endTime，所以这里设置
        instanceExt.setStatus(BpmProcessInstanceStatusEnum.FINISH.getStatus());
        instanceExt.setResult(BpmProcessInstanceResultEnum.APPROVE.getResult()); // 如果正常完全，说明审批通过
        processInstanceExtMapper.updateByProcessInstanceId(instanceExt);

        // 发送流程实例的状态事件
        processInstanceResultEventPublisher.sendProcessInstanceResultEvent(
                BpmProcessInstanceConvert.INSTANCE.convert(this, processInstance, Integer.valueOf(instanceExt.getResult())));
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                BpmProcessInstanceExt bpmProcessInstanceExt = processInstanceExtMapper.selectByProcessInstanceId(instance.getProcessInstanceId());
                List<BpmTaskRespDTO> taskListByProcessInstanceId = taskService.getTaskListByProcessInstanceId(bpmProcessInstanceExt.getProcessInstanceId());
                BaseDTO baseDTO = new BaseDTO();
                try {
                    if (CollectionUtil.isNotEmpty(taskListByProcessInstanceId)) {
                        //过滤出只是审批通过的
                        List<BpmTaskRespDTO> collect = taskListByProcessInstanceId.stream().filter(taskRespDTO -> BpmProcessInstanceResultEnum.APPROVE.getResult().equals(taskRespDTO.getResult())).collect(Collectors.toList());
                        GovUser approveUserInfo = govUserService.getById(collect.get(0).getAssigneeUser().getId());
                        if (ObjectUtil.isNotNull(approveUserInfo)) {
                            baseDTO.setLoginUserId(approveUserInfo.getUserId());
                            baseDTO.setLoginUserName(approveUserInfo.getUserName());
                            baseDTO.setLoginUserCode(approveUserInfo.getUserCode());
                        }
                    }
                } catch (Exception e) {
                    log.info("审批通过查询人员信息失败：{},{}", instance.getProcessInstanceId(), e.getMessage());
                }
                // 发送流程被通过的消息
                messageService.sendMessageWhenProcessInstanceApprove(BpmProcessInstanceConvert.INSTANCE.convert2ApprovedResult(bpmProcessInstanceExt, baseDTO, BpmProcessInstanceResultEnum.APPROVE.getResult(), null));
            }
        });
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateProcessInstanceExtReject(String id, String reason, BpmTaskRejectReqDTO reqDTO) {
        // 需要主动查询，因为 instance 只有 id 属性
        ProcessInstance processInstance = getProcessInstance(id);
        // 删除流程实例，以实现驳回任务时，取消整个审批流程
        deleteProcessInstance(id, CharSequenceUtil.format(BpmProcessInstanceDeleteReasonEnum.REJECT_TASK.format(reason)));

        // 更新 status + result
        // 注意，不能和上面的逻辑更换位置。因为 deleteProcessInstance 会触发流程的取消，进而调用 updateProcessInstanceExtCancel 方法，
        // 设置 result 为 BpmProcessInstanceStatusEnum.CANCEL，显然和 result 不一定是一致的
        //查询实例扩展表
        BpmProcessInstanceExt processInstanceExt = processInstanceExtMapper.selectByProcessInstanceId(processInstance.getId());
        Date endTime = new Date();
        BpmProcessInstanceExt instanceExt = new BpmProcessInstanceExt();
        instanceExt.setProcessInstanceId(id);
        instanceExt.setEndTime(endTime);
        instanceExt.setStatus(BpmProcessInstanceStatusEnum.FINISH.getStatus());
        instanceExt.setResult(BpmProcessInstanceResultEnum.REJECT.getResult());
        //如果是OA
        if (BpmModelCategoryEnum.OA.getType().equals(processInstanceExt.getCategory())) {
            //如果当前实例的parent_process_instance_id不为空，此时说明子流程正在完成审批，需要将主流程中的冗余的子流程状态和结果更新
            if (StringUtils.isNotEmpty(processInstanceExt.getParentProcessInstanceId())) {
                BpmProcessInstanceExt updateInstance = new BpmProcessInstanceExt();
                updateInstance.setProcessInstanceId(processInstanceExt.getParentProcessInstanceId());
                updateInstance.setSubProcessInstanceStatus(BpmProcessInstanceStatusEnum.FINISH.getStatus());
                updateInstance.setSubProcessInstanceResult(BpmProcessInstanceResultEnum.REJECT.getResult());
                processInstanceExtMapper.updateByProcessInstanceId(updateInstance);
            }
        }
        processInstanceExtMapper.updateByProcessInstanceId(instanceExt);

        BpmProcessInstanceExt bpmProcessInstanceExt = processInstanceExtMapper.selectByProcessInstanceId(id);
        BpmMessageSendApproveResultDTO bpmMessageSendApproveResultDTO = BpmProcessInstanceConvert.INSTANCE.convert2ApprovedResult(bpmProcessInstanceExt, reqDTO, BpmProcessInstanceResultEnum.REJECT.getResult(), reason);
        // 发送流程被不通过的消息
        messageService.sendMessageWhenProcessInstanceReject(bpmMessageSendApproveResultDTO);

        // 发送流程实例的状态事件
        processInstanceResultEventPublisher.sendProcessInstanceResultEvent(BpmProcessInstanceConvert.INSTANCE.convert(this, processInstance, Integer.valueOf(instanceExt.getResult())));
    }

    public void updateProcessInstanceExtBack(String id, Byte status) {
        BpmProcessInstanceExt instanceExt = new BpmProcessInstanceExt();
        instanceExt.setProcessInstanceId(id);
        instanceExt.setResult(status);
        processInstanceExtMapper.updateByProcessInstanceId(instanceExt);
    }

    private void deleteProcessInstance(String id, String reason) {
        runtimeService.deleteProcessInstance(id, reason);
    }

    private String createProcessInstance0(BpmProcessInstanceCreateReqDTO createReqDTO, ProcessDefinition definition, String businessKey, Byte businessType) {
        Integer userId = createReqDTO.getLoginUserId();
        String variables = createReqDTO.getVariables();
        // 校验流程定义
        if (definition == null) {
            throw new ApiException(PROCESS_DEFINITION_NOT_EXISTS);
        }
        if (definition.isSuspended()) {
            throw new ApiException(PROCESS_DEFINITION_IS_SUSPENDED);
        }

        Authentication.setAuthenticatedUserId(String.valueOf(userId));

        // 创建流程实例
        ProcessInstance instance = runtimeService.startProcessInstanceById(definition.getId(), businessKey, (Map<String, Object>) JSON.parse(variables));

        ProcessInstance processInstance = getProcessInstance(instance.getId());
        if (processInstance != null) {
            // 设置流程名字，针对零审批节点的任务存在直接结束流程情况
            runtimeService.setProcessInstanceName(instance.getId(), definition.getName());
        }
        BpmProcessInstanceExt instanceExt = new BpmProcessInstanceExt();
        instanceExt.setProcessInstanceId(instance.getId());
        instanceExt.setFormVariables(variables);
        instanceExt.setCreterId(createReqDTO.getLoginUserId());
        instanceExt.setCreterName(createReqDTO.getLoginUserName());
        instanceExt.setUpdateId(createReqDTO.getLoginUserId());
        instanceExt.setUpdateName(createReqDTO.getLoginUserName());
        instanceExt.setCompanyId(createReqDTO.getLoginCompanyId());
        instanceExt.setBusinessNo(businessKey);
        instanceExt.setBusinessType(businessType);
        instanceExt.setDeptId(createReqDTO.getLoginDeptId());
        instanceExt.setDeptName(createReqDTO.getLoginDeptName());
        //如果传入的父级流程id和name不为空，说明是从页面上点击发起下级流程开始的
        if (StringUtils.isNotEmpty(createReqDTO.getParentProcessInstanceId())) {
            //需要将当前的流程实例的父实例id，name，以及状态和结果赋值过来
            BpmProcessInstanceExt bpmProcessInstanceExt = processInstanceExtMapper.selectByProcessInstanceId(createReqDTO.getParentProcessInstanceId());
            instanceExt.setParentProcessInstanceId(createReqDTO.getParentProcessInstanceId());
            instanceExt.setParentProcessInstanceName(bpmProcessInstanceExt.getName());
            instanceExt.setParentProcessInstanceStatus(bpmProcessInstanceExt.getStatus());
            instanceExt.setParentProcessInstanceResult(bpmProcessInstanceExt.getResult());
            //更新父的实例上面的子流程相关信息
            BpmProcessInstanceExt updateParent = new BpmProcessInstanceExt();
            updateParent.setProcessInstanceId(createReqDTO.getParentProcessInstanceId());
            BpmProcessInstanceExt processInstanceExt = getProcessInstanceExt(instance.getId());
            updateParent.setSubProcessInstanceName(processInstanceExt.getName());
            updateParent.setSubProcessInstanceId(processInstanceExt.getProcessInstanceId());
            updateParent.setSubProcessInstanceStatus(processInstanceExt.getStatus());
            updateParent.setSubProcessInstanceResult(processInstanceExt.getResult());
            processInstanceExtMapper.updateByProcessInstanceId(updateParent);
        }
        if (processInstance != null) {
            BpmProcessDefinitionExt bpmProcessDefinitionExt = processDefinitionMapper.selectByProcessDefinitionId(processInstance.getProcessDefinitionId());
            instanceExt.setModelId(bpmProcessDefinitionExt.getModelId());
        }
        // 补全流程实例的拓展表
        processInstanceExtMapper.updateByProcessInstanceId(instanceExt);
        return instance.getId();
    }

    /**
     * 获得流程实例 Map
     *
     * @param ids 流程实例的编号集合
     * @return 流程实例列表 Map
     */
    public Map<String, ProcessInstance> getProcessInstanceMap(Set<String> ids) {
        return CollectionUtils.convertMap(getProcessInstances(ids), ProcessInstance::getProcessInstanceId);
    }

    /**
     * 获得历史的流程实例 Map
     *
     * @param ids 流程实例的编号集合
     * @return 历史的流程实例列表 Map
     */
    public Map<String, HistoricProcessInstance> getHistoricProcessInstanceMap(Set<String> ids) {
        return CollectionUtils.convertMap(getHistoricProcessInstances(ids), HistoricProcessInstance::getId);
    }

    public Boolean applyStartSwitch(ApplyStartSwitchDTO startSwitchDTO) {
        Byte businessType = startSwitchDTO.getBusinessType();
        // 获得流程模型
        ActReModelExample where = new ActReModelExample();
        ActReModelExample.Criteria criteria = where.createCriteria();
        criteria.andBusinessTypeEqualTo(businessType);
        criteria.andTenantIdEqualTo(String.valueOf(startSwitchDTO.getLoginCompanyId()));
        List<ActReModel> model = modelMapper.selectByExample(where);
        if (CollectionUtils.isAnyEmpty(model)) {
            return false;
        }
        //取最近一级单位的模型
        ActReModel actReModel = getActReModelByStructId(model, startSwitchDTO.getLoginDeptId(), startSwitchDTO.getLoginCompanyId());
        String deploymentId = actReModel.getDeploymentId();
        if (StringUtils.isBlank(deploymentId)) {
            return false;
        }
        // 获得流程定义
        ProcessDefinition processDefinition = processDefinitionService.getProcessDefinitionByDeploymentId(deploymentId);
        if (processDefinition == null) {
            return false;
        }
        return !processDefinition.isSuspended();
    }


    /**
     * 根据登录部门Id ,获取模型若不存在，则向上递归寻找，直到一级部门 仍无模型，默认返回get(0)
     */
    private ActReModel getActReModelByStructId(List<ActReModel> model, Integer loginDeptId, Integer loginCompanyId) {
        //获取当前公司所有单位
        List<GovStruct> govStructs = govStructService.getBaseMapper().selectList(new LambdaQueryWrapper<GovStruct>().
                in(GovStruct::getStructType, 1, 2).
                eq(GovStruct::getCompanyId, loginCompanyId));
        //获取当前部门信息，用于查询上级部门
        if (loginDeptId == null) {
            return model.get(0);
        }
        GovStruct currentGovStruct = govStructs.stream().filter(govStruct -> govStruct.getId().equals(loginDeptId)).findFirst().orElse(null);
        if (currentGovStruct == null) {//处理脏数据情况
            log.info("审批流查询部门不存在当前公司，部门Id{}", loginDeptId);
            return model.get(0);
        }
        while (true) {
            Integer structId = currentGovStruct.getId();
            Optional<ActReModel> unitActReModel = model.stream().filter(actReModel -> actReModel != null && structId.equals(actReModel.getStructId())).findFirst();//获取当前单位的对应的模型
            if (unitActReModel.isPresent()) {
                return unitActReModel.get();
            }
            Integer parentId = currentGovStruct.getParentId();
            currentGovStruct = govStructs.stream().filter(govStruct -> govStruct.getId().equals(parentId)).findFirst().orElse(null);
            if (currentGovStruct == null) {
                log.info("未找到匹配的审批模型，使用默认模型，部门ID: {}", loginDeptId);
                Optional<ActReModel> first = model.stream().filter(mod -> mod.getStructId() == null).findFirst();
                if (first.isPresent()) {
                    return first.get();
                }
                throw new ApiException(PROCESS_DEFINITION_NOT_EXISTS);
            }
        }
    }

    /**
     * 重新启动流程
     */
    public void startProcessInstance(String processInstanceId, String variables) {
        managementService.executeCommand(commandContext -> {

            ExecutionEntity processInstance = CommandContextUtil.getExecutionEntityManager(commandContext).findById(processInstanceId);// 流程实例
            String processDefinitionId = processInstance.getProcessDefinitionId();// 流程定义ID
            if (StringUtils.isNotBlank(variables)) {
                // 设置新变量
                processInstance.setVariables((Map<String, Object>) JSON.parse(variables));
            }
            // 创建目标节点运行实例
            ExecutionEntityManager executionEntityManager = CommandContextUtil.getExecutionEntityManager();
            BpmnModel bpmnModel = ProcessDefinitionUtil.getBpmnModel(processDefinitionId);
            Collection<FlowElement> flowElements = bpmnModel.getMainProcess().getFlowElements();
            Optional<FlowElement> startElementOpt = flowElements.stream().filter(StartEvent.class::isInstance).findFirst();
            FlowElement targetFlowElement = startElementOpt.get();
            ExecutionEntity newExecution = executionEntityManager.createChildExecution(processInstance);
            newExecution.setCurrentFlowElement(targetFlowElement);
            CommandContextUtil.getAgenda().planOperation(new CustomContinueProcessOperation(commandContext, newExecution));
            return null;
        });

    }

    /**
     * 修改运行实例
     *
     * @param processInstanceId
     * @param reason
     */
    public void deleteProcessChildExecutionsAndTasks(String processInstanceId, String reason) {
        managementService.executeCommand(commandContext -> {
            ExecutionEntityManager executionEntityManager = CommandContextUtil.getExecutionEntityManager(commandContext);
            ExecutionEntity processInstance = executionEntityManager.findById(processInstanceId);
            TaskHelper.deleteTasksByProcessInstanceId(processInstance.getId(), reason, false);
            // delete the execution BEFORE we delete the history, otherwise we will
            // produce orphan HistoricVariableInstance instances
            ExecutionEntity processInstanceExecutionEntity = processInstance.getProcessInstance();
            if (processInstanceExecutionEntity == null) {
                return null;
            }
            List<ExecutionEntity> childExecutions = executionEntityManager.collectChildren(processInstanceExecutionEntity);
            for (int i = childExecutions.size() - 1; i >= 0; i--) {
                ExecutionEntity childExecutionEntity = childExecutions.get(i);
                executionEntityManager.deleteExecutionAndRelatedData(childExecutionEntity, reason, false);
            }
            return null;
        });
    }

    public PageDTO<BpmProcessInstancePageItemRespDTO> providerPage(BpmProcessInstanceMyPageReqDTO pageReqVO) {
        // 通过 BpmProcessInstanceExt 表，先查询到对应的分页
        Integer pageNum = pageReqVO.getPageNum();
        Integer pageSize = pageReqVO.getPageSize();
        List<BpmProcessInstanceExt> bpmProcessInstanceExts;
        pageReqVO.setLoginUserId(null);// 避免只查询自己的，但是这里需要查询到所有用户的
        try {
            Page<Object> page = PageMethod.startPage(pageNum, pageSize, true);
            bpmProcessInstanceExts = processInstanceExtMapper.selectPage(pageReqVO);
            if (bpmProcessInstanceExts.isEmpty()) {
                return new PageDTO<BpmProcessInstancePageItemRespDTO>(pageNum, pageSize, 0, Collections.emptyList());
            }
            long total = page.getTotal();
            // 获得流程 Task Map
            List<String> processInstanceIds = CollectionUtils.convertList(bpmProcessInstanceExts, BpmProcessInstanceExt::getProcessInstanceId);
            Map<String, List<Task>> taskMap = taskService.getTaskMapByProcessInstanceIds(processInstanceIds);
            // 针对会签、或签，同一个审批任务多人审批，展示一个审批任务
            Map<String, List<Task>> taskMapDistinct = taskMap.entrySet().stream()
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            taskListEntry -> taskListEntry.getValue().stream()
                                    .collect(
                                            Collectors.collectingAndThen(
                                                    Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(task -> StringUtils.isNotBlank(task.getName()) ? task.getName() : ""))),
                                                    ArrayList::new
                                            )
                                    )));
            // 转换返回
            List<BpmProcessInstancePageItemRespDTO> bpmProcessInstancePageItemRespDTOS = BpmProcessInstanceConvert.INSTANCE.convertPage(bpmProcessInstanceExts, taskMapDistinct);
            List<Integer> companyIds = bpmProcessInstancePageItemRespDTOS.stream().map(item -> Integer.valueOf(item.getCompanyId())).distinct().collect(Collectors.toList());
//            //查询公司的名字
//            List<CompanyDTO> companyByIds = com.izu.user.restApi.CompanyApi.getCompanyByIds(companyIds);
//            Map<Integer, List<CompanyDTO>> companyMap = companyByIds.stream().collect(Collectors.groupingBy(CompanyDTO::getCompanyId));
//            bpmProcessInstancePageItemRespDTOS.forEach(bpmProviderFormPageRespDTO -> {
//                List<CompanyDTO> orDefault = companyMap.getOrDefault(Integer.valueOf(bpmProviderFormPageRespDTO.getCompanyId()), Collections.emptyList());
//                if(!orDefault.isEmpty()){
//                    bpmProviderFormPageRespDTO.setCompanyName(orDefault.get(0).getCompanyName());
//                }else {
//                    bpmProviderFormPageRespDTO.setCompanyName("");
//                }
//            });
            return new PageDTO<BpmProcessInstancePageItemRespDTO>(pageNum, pageSize, Integer.parseInt(total + ""), bpmProcessInstancePageItemRespDTOS);
        } catch (Exception e) {
            log.error("查询我的流程列表异常", e);
            return new PageDTO<BpmProcessInstancePageItemRespDTO>(pageNum, pageSize, 0, Collections.emptyList());
        } finally {
            PageMethod.clearPage();
        }
    }
}
