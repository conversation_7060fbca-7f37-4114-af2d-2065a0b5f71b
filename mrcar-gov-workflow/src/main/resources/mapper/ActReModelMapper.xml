<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mrcar.gov.workflow.mapper.ActReModelMapper">
    <resultMap id="BaseResultMap" type="com.mrcar.gov.workflow.domain.ActReModel">
        <id column="ID_" jdbcType="VARCHAR" property="id"/>
        <result column="REV_" jdbcType="INTEGER" property="rev"/>
        <result column="NAME_" jdbcType="VARCHAR" property="name"/>
        <result column="KEY_" jdbcType="VARCHAR" property="key"/>
        <result column="CATEGORY_" jdbcType="VARCHAR" property="category"/>
        <result column="CREATE_TIME_" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="LAST_UPDATE_TIME_" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
        <result column="VERSION_" jdbcType="INTEGER" property="version"/>
        <result column="META_INFO_" jdbcType="VARCHAR" property="metaInfo"/>
        <result column="DEPLOYMENT_ID_" jdbcType="VARCHAR" property="deploymentId"/>
        <result column="EDITOR_SOURCE_VALUE_ID_" jdbcType="VARCHAR" property="editorSourceValueId"/>
        <result column="EDITOR_SOURCE_EXTRA_VALUE_ID_" jdbcType="VARCHAR" property="editorSourceExtraValueId"/>
        <result column="TENANT_ID_" jdbcType="VARCHAR" property="tenantId"/>
        <result column="BUSINESS_TYPE" jdbcType="TINYINT" property="businessType"/>
        <result column="batch_approval" jdbcType="BIT" property="batchApproval"/>
        <result column="connect_switch" jdbcType="BIT" property="connectSwitch"/>
        <result column="parent_model_id" jdbcType="VARCHAR" property="parentModelId"/>
        <result column="parent_model_name" jdbcType="VARCHAR" property="parentModelName"/>
        <result column="struct_id" jdbcType="INTEGER" property="structId"/>

    </resultMap>
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        ID_
        , REV_, NAME_, KEY_, CATEGORY_, CREATE_TIME_, LAST_UPDATE_TIME_, VERSION_, META_INFO_,
    DEPLOYMENT_ID_, EDITOR_SOURCE_VALUE_ID_, EDITOR_SOURCE_EXTRA_VALUE_ID_, TENANT_ID_, 
    BUSINESS_TYPE, batch_approval, connect_switch, parent_model_id, parent_model_name,struct_id
    </sql>
    <select id="selectByExample" parameterType="com.mrcar.gov.workflow.domain.ActReModelExample"
            resultMap="BaseResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from act_re_model
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from act_re_model
        where ID_ = #{id,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from act_re_model
        where ID_ = #{id,jdbcType=VARCHAR}
    </delete>
    <delete id="deleteByExample" parameterType="com.mrcar.gov.workflow.domain.ActReModelExample">
        delete from act_re_model
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </delete>
    <insert id="insert" parameterType="com.mrcar.gov.workflow.domain.ActReModel">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.String">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into act_re_model (REV_, NAME_, KEY_,
        CATEGORY_, CREATE_TIME_, LAST_UPDATE_TIME_,
        VERSION_, META_INFO_, DEPLOYMENT_ID_,
        EDITOR_SOURCE_VALUE_ID_, EDITOR_SOURCE_EXTRA_VALUE_ID_,
        TENANT_ID_, BUSINESS_TYPE, batch_approval,
        connect_switch, parent_model_id, parent_model_name
        )
        values (#{rev,jdbcType=INTEGER}, #{name,jdbcType=VARCHAR}, #{key,jdbcType=VARCHAR},
        #{category,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{lastUpdateTime,jdbcType=TIMESTAMP},
        #{version,jdbcType=INTEGER}, #{metaInfo,jdbcType=VARCHAR}, #{deploymentId,jdbcType=VARCHAR},
        #{editorSourceValueId,jdbcType=VARCHAR}, #{editorSourceExtraValueId,jdbcType=VARCHAR},
        #{tenantId,jdbcType=VARCHAR}, #{businessType,jdbcType=TINYINT}, #{batchApproval,jdbcType=BIT},
        #{connectSwitch,jdbcType=BIT}, #{parentModelId,jdbcType=VARCHAR}, #{parentModelName,jdbcType=VARCHAR}
        )
    </insert>
    <insert id="insertSelective" parameterType="com.mrcar.gov.workflow.domain.ActReModel">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.String">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into act_re_model
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="rev != null">
                REV_,
            </if>
            <if test="name != null">
                NAME_,
            </if>
            <if test="key != null">
                KEY_,
            </if>
            <if test="category != null">
                CATEGORY_,
            </if>
            <if test="createTime != null">
                CREATE_TIME_,
            </if>
            <if test="lastUpdateTime != null">
                LAST_UPDATE_TIME_,
            </if>
            <if test="version != null">
                VERSION_,
            </if>
            <if test="metaInfo != null">
                META_INFO_,
            </if>
            <if test="deploymentId != null">
                DEPLOYMENT_ID_,
            </if>
            <if test="editorSourceValueId != null">
                EDITOR_SOURCE_VALUE_ID_,
            </if>
            <if test="editorSourceExtraValueId != null">
                EDITOR_SOURCE_EXTRA_VALUE_ID_,
            </if>
            <if test="tenantId != null">
                TENANT_ID_,
            </if>
            <if test="businessType != null">
                BUSINESS_TYPE,
            </if>
            <if test="batchApproval != null">
                batch_approval,
            </if>
            <if test="connectSwitch != null">
                connect_switch,
            </if>
            <if test="parentModelId != null">
                parent_model_id,
            </if>
            <if test="parentModelName != null">
                parent_model_name,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="rev != null">
                #{rev,jdbcType=INTEGER},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="key != null">
                #{key,jdbcType=VARCHAR},
            </if>
            <if test="category != null">
                #{category,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateTime != null">
                #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="version != null">
                #{version,jdbcType=INTEGER},
            </if>
            <if test="metaInfo != null">
                #{metaInfo,jdbcType=VARCHAR},
            </if>
            <if test="deploymentId != null">
                #{deploymentId,jdbcType=VARCHAR},
            </if>
            <if test="editorSourceValueId != null">
                #{editorSourceValueId,jdbcType=VARCHAR},
            </if>
            <if test="editorSourceExtraValueId != null">
                #{editorSourceExtraValueId,jdbcType=VARCHAR},
            </if>
            <if test="tenantId != null">
                #{tenantId,jdbcType=VARCHAR},
            </if>
            <if test="businessType != null">
                #{businessType,jdbcType=TINYINT},
            </if>
            <if test="batchApproval != null">
                #{batchApproval,jdbcType=BIT},
            </if>
            <if test="connectSwitch != null">
                #{connectSwitch,jdbcType=BIT},
            </if>
            <if test="parentModelId != null">
                #{parentModelId,jdbcType=VARCHAR},
            </if>
            <if test="parentModelName != null">
                #{parentModelName,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <select id="countByExample" parameterType="com.mrcar.gov.workflow.domain.ActReModelExample"
            resultType="java.lang.Long">
        select count(*) from act_re_model
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
    <update id="updateByExampleSelective" parameterType="map">
        update act_re_model
        <set>
            <if test="record.id != null">
                ID_ = #{record.id,jdbcType=VARCHAR},
            </if>
            <if test="record.rev != null">
                REV_ = #{record.rev,jdbcType=INTEGER},
            </if>
            <if test="record.name != null">
                NAME_ = #{record.name,jdbcType=VARCHAR},
            </if>
            <if test="record.key != null">
                KEY_ = #{record.key,jdbcType=VARCHAR},
            </if>
            <if test="record.category != null">
                CATEGORY_ = #{record.category,jdbcType=VARCHAR},
            </if>
            <if test="record.createTime != null">
                CREATE_TIME_ = #{record.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.lastUpdateTime != null">
                LAST_UPDATE_TIME_ = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.version != null">
                VERSION_ = #{record.version,jdbcType=INTEGER},
            </if>
            <if test="record.metaInfo != null">
                META_INFO_ = #{record.metaInfo,jdbcType=VARCHAR},
            </if>
            <if test="record.deploymentId != null">
                DEPLOYMENT_ID_ = #{record.deploymentId,jdbcType=VARCHAR},
            </if>
            <if test="record.editorSourceValueId != null">
                EDITOR_SOURCE_VALUE_ID_ = #{record.editorSourceValueId,jdbcType=VARCHAR},
            </if>
            <if test="record.editorSourceExtraValueId != null">
                EDITOR_SOURCE_EXTRA_VALUE_ID_ = #{record.editorSourceExtraValueId,jdbcType=VARCHAR},
            </if>
            <if test="record.tenantId != null">
                TENANT_ID_ = #{record.tenantId,jdbcType=VARCHAR},
            </if>
            <if test="record.businessType != null">
                BUSINESS_TYPE = #{record.businessType,jdbcType=TINYINT},
            </if>
            <if test="record.batchApproval != null">
                batch_approval = #{record.batchApproval,jdbcType=BIT},
            </if>
            <if test="record.connectSwitch != null">
                connect_switch = #{record.connectSwitch,jdbcType=BIT},
            </if>
            <if test="record.parentModelId != null">
                parent_model_id = #{record.parentModelId,jdbcType=VARCHAR},
            </if>
            <if test="record.parentModelName != null">
                parent_model_name = #{record.parentModelName,jdbcType=VARCHAR},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByExample" parameterType="map">
        update act_re_model
        set ID_ = #{record.id,jdbcType=VARCHAR},
        REV_ = #{record.rev,jdbcType=INTEGER},
        NAME_ = #{record.name,jdbcType=VARCHAR},
        KEY_ = #{record.key,jdbcType=VARCHAR},
        CATEGORY_ = #{record.category,jdbcType=VARCHAR},
        CREATE_TIME_ = #{record.createTime,jdbcType=TIMESTAMP},
        LAST_UPDATE_TIME_ = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
        VERSION_ = #{record.version,jdbcType=INTEGER},
        META_INFO_ = #{record.metaInfo,jdbcType=VARCHAR},
        DEPLOYMENT_ID_ = #{record.deploymentId,jdbcType=VARCHAR},
        EDITOR_SOURCE_VALUE_ID_ = #{record.editorSourceValueId,jdbcType=VARCHAR},
        EDITOR_SOURCE_EXTRA_VALUE_ID_ = #{record.editorSourceExtraValueId,jdbcType=VARCHAR},
        TENANT_ID_ = #{record.tenantId,jdbcType=VARCHAR},
        BUSINESS_TYPE = #{record.businessType,jdbcType=TINYINT},
        batch_approval = #{record.batchApproval,jdbcType=BIT},
        connect_switch = #{record.connectSwitch,jdbcType=BIT},
        parent_model_id = #{record.parentModelId,jdbcType=VARCHAR},
        parent_model_name = #{record.parentModelName,jdbcType=VARCHAR}
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByPrimaryKeySelective" parameterType="com.mrcar.gov.workflow.domain.ActReModel">
        update act_re_model
        <set>
            <if test="rev != null">
                REV_ = #{rev,jdbcType=INTEGER},
            </if>
            <if test="name != null">
                NAME_ = #{name,jdbcType=VARCHAR},
            </if>
            <if test="key != null">
                KEY_ = #{key,jdbcType=VARCHAR},
            </if>
            <if test="category != null">
                CATEGORY_ = #{category,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                CREATE_TIME_ = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateTime != null">
                LAST_UPDATE_TIME_ = #{lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="version != null">
                VERSION_ = #{version,jdbcType=INTEGER},
            </if>
            <if test="metaInfo != null">
                META_INFO_ = #{metaInfo,jdbcType=VARCHAR},
            </if>
            <if test="deploymentId != null">
                DEPLOYMENT_ID_ = #{deploymentId,jdbcType=VARCHAR},
            </if>
            <if test="editorSourceValueId != null">
                EDITOR_SOURCE_VALUE_ID_ = #{editorSourceValueId,jdbcType=VARCHAR},
            </if>
            <if test="editorSourceExtraValueId != null">
                EDITOR_SOURCE_EXTRA_VALUE_ID_ = #{editorSourceExtraValueId,jdbcType=VARCHAR},
            </if>
            <if test="tenantId != null">
                TENANT_ID_ = #{tenantId,jdbcType=VARCHAR},
            </if>
            <if test="businessType != null">
                BUSINESS_TYPE = #{businessType,jdbcType=TINYINT},
            </if>
            <if test="batchApproval != null">
                batch_approval = #{batchApproval,jdbcType=BIT},
            </if>
            <if test="connectSwitch != null">
                connect_switch = #{connectSwitch,jdbcType=BIT},
            </if>
            <if test="parentModelId != null">
                parent_model_id = #{parentModelId,jdbcType=VARCHAR},
            </if>
            <if test="parentModelName != null">
                parent_model_name = #{parentModelName,jdbcType=VARCHAR},
            </if>
            <if test="structId != null">
                struct_id = #{structId,jdbcType=INTEGER},
            </if>
        </set>
        where ID_ = #{id,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.mrcar.gov.workflow.domain.ActReModel">
        update act_re_model
        set REV_                          = #{rev,jdbcType=INTEGER},
            NAME_                         = #{name,jdbcType=VARCHAR},
            KEY_                          = #{key,jdbcType=VARCHAR},
            CATEGORY_                     = #{category,jdbcType=VARCHAR},
            CREATE_TIME_                  = #{createTime,jdbcType=TIMESTAMP},
            LAST_UPDATE_TIME_             = #{lastUpdateTime,jdbcType=TIMESTAMP},
            VERSION_                      = #{version,jdbcType=INTEGER},
            META_INFO_                    = #{metaInfo,jdbcType=VARCHAR},
            DEPLOYMENT_ID_                = #{deploymentId,jdbcType=VARCHAR},
            EDITOR_SOURCE_VALUE_ID_       = #{editorSourceValueId,jdbcType=VARCHAR},
            EDITOR_SOURCE_EXTRA_VALUE_ID_ = #{editorSourceExtraValueId,jdbcType=VARCHAR},
            TENANT_ID_                    = #{tenantId,jdbcType=VARCHAR},
            BUSINESS_TYPE                 = #{businessType,jdbcType=TINYINT},
            batch_approval                = #{batchApproval,jdbcType=BIT},
            connect_switch                = #{connectSwitch,jdbcType=BIT},
            parent_model_id               = #{parentModelId,jdbcType=VARCHAR},
            parent_model_name             = #{parentModelName,jdbcType=VARCHAR},
            struct_id                     = #{structId,jdbcType=INTEGER}
        where ID_ = #{id,jdbcType=VARCHAR}
    </update>
</mapper>