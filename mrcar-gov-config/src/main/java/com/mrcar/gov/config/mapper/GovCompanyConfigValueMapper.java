package com.mrcar.gov.config.mapper;

import com.mrcar.gov.common.dto.config.GovCompanyConfigItemValueDTO;
import com.mrcar.gov.config.domain.GovCompanyConfigValue;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
* <AUTHOR>
* @description 针对表【gov_company_config_value(企业配置值表)】的数据库操作Mapper
* @createDate 2025-04-16 14:42:34
* @Entity com.mrcar.gov.config.domain.GovCompanyConfigValue
*/
public interface GovCompanyConfigValueMapper extends BaseMapper<GovCompanyConfigValue> {

    List<GovCompanyConfigItemValueDTO> selectByStructCodeAndBusinessCodeAndItemCode(@Param("structCode")String structCode, @Param("businessCode") String businessCode, @Param("itemCode") String itemCode);

    void insertBatch(@Param("saveList") List<GovCompanyConfigValue> saveList);

    Set<String> selectStructCodeGroupByCompanyId(@Param("companyId") Integer companyId);
}




