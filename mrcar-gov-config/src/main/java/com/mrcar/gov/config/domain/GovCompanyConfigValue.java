package com.mrcar.gov.config.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;

/**
 * 企业配置值表
 * @TableName gov_company_config_value
 */
@TableName(value ="gov_company_config_value")
public class GovCompanyConfigValue {
    /**
     * 自增ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 所属企业ID
     */
    private Integer companyId;

    /**
     * 单位id
     */
    private Integer structId;

    /**
     * 单位code
     */
    private String structCode;

    /**
     * 业务编码配置编码
     */
    private String businessConfigCode;

    /**
     * 业务配置编码项编码
     */
    private String businessConfigItemCode;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 配置的值：开关/单选：{"value":"0","label":"否"}，多选:[{"value":"","label":""},{"value":"","label":""}]，文本："" 
     */
    private String configValue;

    /**
     * 自增ID
     */
    public Integer getId() {
        return id;
    }

    /**
     * 自增ID
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * 所属企业ID
     */
    public Integer getCompanyId() {
        return companyId;
    }

    /**
     * 所属企业ID
     */
    public void setCompanyId(Integer companyId) {
        this.companyId = companyId;
    }

    /**
     * 单位id
     */
    public Integer getStructId() {
        return structId;
    }

    /**
     * 单位id
     */
    public void setStructId(Integer structId) {
        this.structId = structId;
    }

    /**
     * 单位code
     */
    public String getStructCode() {
        return structCode;
    }

    /**
     * 单位code
     */
    public void setStructCode(String structCode) {
        this.structCode = structCode;
    }

    /**
     * 业务编码配置编码
     */
    public String getBusinessConfigCode() {
        return businessConfigCode;
    }

    /**
     * 业务编码配置编码
     */
    public void setBusinessConfigCode(String businessConfigCode) {
        this.businessConfigCode = businessConfigCode;
    }

    /**
     * 业务配置编码项编码
     */
    public String getBusinessConfigItemCode() {
        return businessConfigItemCode;
    }

    /**
     * 业务配置编码项编码
     */
    public void setBusinessConfigItemCode(String businessConfigItemCode) {
        this.businessConfigItemCode = businessConfigItemCode;
    }

    /**
     * 创建时间
     */
    public Date getCreateDate() {
        return createDate;
    }

    /**
     * 创建时间
     */
    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    /**
     * 更新时间
     */
    public Date getUpdateDate() {
        return updateDate;
    }

    /**
     * 更新时间
     */
    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    /**
     * 配置的值：开关/单选：{"value":"0","label":"否"}，多选:[{"value":"","label":""},{"value":"","label":""}]，文本："" 
     */
    public String getConfigValue() {
        return configValue;
    }

    /**
     * 配置的值：开关/单选：{"value":"0","label":"否"}，多选:[{"value":"","label":""},{"value":"","label":""}]，文本："" 
     */
    public void setConfigValue(String configValue) {
        this.configValue = configValue;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        GovCompanyConfigValue other = (GovCompanyConfigValue) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getCompanyId() == null ? other.getCompanyId() == null : this.getCompanyId().equals(other.getCompanyId()))
            && (this.getStructId() == null ? other.getStructId() == null : this.getStructId().equals(other.getStructId()))
            && (this.getStructCode() == null ? other.getStructCode() == null : this.getStructCode().equals(other.getStructCode()))
            && (this.getBusinessConfigCode() == null ? other.getBusinessConfigCode() == null : this.getBusinessConfigCode().equals(other.getBusinessConfigCode()))
            && (this.getBusinessConfigItemCode() == null ? other.getBusinessConfigItemCode() == null : this.getBusinessConfigItemCode().equals(other.getBusinessConfigItemCode()))
            && (this.getCreateDate() == null ? other.getCreateDate() == null : this.getCreateDate().equals(other.getCreateDate()))
            && (this.getUpdateDate() == null ? other.getUpdateDate() == null : this.getUpdateDate().equals(other.getUpdateDate()))
            && (this.getConfigValue() == null ? other.getConfigValue() == null : this.getConfigValue().equals(other.getConfigValue()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getCompanyId() == null) ? 0 : getCompanyId().hashCode());
        result = prime * result + ((getStructId() == null) ? 0 : getStructId().hashCode());
        result = prime * result + ((getStructCode() == null) ? 0 : getStructCode().hashCode());
        result = prime * result + ((getBusinessConfigCode() == null) ? 0 : getBusinessConfigCode().hashCode());
        result = prime * result + ((getBusinessConfigItemCode() == null) ? 0 : getBusinessConfigItemCode().hashCode());
        result = prime * result + ((getCreateDate() == null) ? 0 : getCreateDate().hashCode());
        result = prime * result + ((getUpdateDate() == null) ? 0 : getUpdateDate().hashCode());
        result = prime * result + ((getConfigValue() == null) ? 0 : getConfigValue().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", companyId=").append(companyId);
        sb.append(", structId=").append(structId);
        sb.append(", structCode=").append(structCode);
        sb.append(", businessConfigCode=").append(businessConfigCode);
        sb.append(", businessConfigItemCode=").append(businessConfigItemCode);
        sb.append(", createDate=").append(createDate);
        sb.append(", updateDate=").append(updateDate);
        sb.append(", configValue=").append(configValue);
        sb.append("]");
        return sb.toString();
    }
}