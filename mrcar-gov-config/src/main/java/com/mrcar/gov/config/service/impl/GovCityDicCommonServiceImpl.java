package com.mrcar.gov.config.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.izu.cache.core.redis.RedisCache;
import com.izu.framework.web.util.BeanUtil;
import com.mrcar.gov.common.config.GovCityDicDTO;
import com.mrcar.gov.common.service.config.GovCityDicCommonService;
import com.mrcar.gov.config.domain.GovCityDic;
import com.mrcar.gov.config.service.GovCityDicService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2025/3/1 13:43
 */
@Service
public class GovCityDicCommonServiceImpl implements GovCityDicCommonService {

    @Resource
    private RedisCache redisCache;

    private final int expireTime = 60 * 60 * 24;

    @Resource
    private GovCityDicService govCityDicService;

    @Override
    public GovCityDicDTO getByAdCode(Integer adCode) {
        String cacheKey = getRedisKey(adCode);
        String cacheValue = redisCache.getCacheObject(cacheKey);
        if(StringUtils.isNotBlank(cacheValue)){
            return JSONObject.parseObject(cacheValue, GovCityDicDTO.class);
        }
        GovCityDic dic = govCityDicService.getOne(new LambdaQueryWrapper<GovCityDic>().eq(GovCityDic::getCityCode,adCode));
        if(Objects.isNull(dic)){
            return null;
        }
        redisCache.setCacheObject(getRedisKey(adCode), JSON.toJSONString(dic), expireTime, TimeUnit.SECONDS);
        return BeanUtil.copyObject(dic, GovCityDicDTO.class);
    }

    private String getRedisKey(Integer adCode){
        return "cityDic:" + adCode;
    }
}
