package com.mrcar.gov.config.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

/**
 * 公共配置业务表
 * @TableName gov_public_business_config
 */
@TableName(value ="gov_public_business_config")
public class GovPublicBusinessConfig implements Serializable {
    /**
     * 自增ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 业务环节
     */
    private String businessLink;

    /**
     * 业务编码
     */
    private String businessConfigCode;

    /**
     * 业务名称
     */
    private String businessConfigName;

    /**
     * 分类code
     */
    private String categoryCode;

    /**
     * 分类名称
     */
    private String categoryName;

    /**
     * 用车备注
     */
    private String remark;

    /**
     * 单选/多选 0:单选  1:多选
     */
    private Integer selectType;

    /**
     * 是否必填 0:非必填 1:必填
     */
    private Integer isRequired;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 自增ID
     */
    public Integer getId() {
        return id;
    }

    /**
     * 自增ID
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * 业务环节
     */
    public String getBusinessLink() {
        return businessLink;
    }

    /**
     * 业务环节
     */
    public void setBusinessLink(String businessLink) {
        this.businessLink = businessLink;
    }

    /**
     * 业务编码
     */
    public String getBusinessConfigCode() {
        return businessConfigCode;
    }

    /**
     * 业务编码
     */
    public void setBusinessConfigCode(String businessConfigCode) {
        this.businessConfigCode = businessConfigCode;
    }

    /**
     * 业务名称
     */
    public String getBusinessConfigName() {
        return businessConfigName;
    }

    /**
     * 业务名称
     */
    public void setBusinessConfigName(String businessConfigName) {
        this.businessConfigName = businessConfigName;
    }

    /**
     * 分类code
     */
    public String getCategoryCode() {
        return categoryCode;
    }

    /**
     * 分类code
     */
    public void setCategoryCode(String categoryCode) {
        this.categoryCode = categoryCode;
    }

    /**
     * 分类名称
     */
    public String getCategoryName() {
        return categoryName;
    }

    /**
     * 分类名称
     */
    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    /**
     * 用车备注
     */
    public String getRemark() {
        return remark;
    }

    /**
     * 用车备注
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * 单选/多选 0:单选  1:多选
     */
    public Integer getSelectType() {
        return selectType;
    }

    /**
     * 单选/多选 0:单选  1:多选
     */
    public void setSelectType(Integer selectType) {
        this.selectType = selectType;
    }

    /**
     * 是否必填 0:非必填 1:必填
     */
    public Integer getIsRequired() {
        return isRequired;
    }

    /**
     * 是否必填 0:非必填 1:必填
     */
    public void setIsRequired(Integer isRequired) {
        this.isRequired = isRequired;
    }

    /**
     * 创建时间
     */
    public Date getCreateDate() {
        return createDate;
    }

    /**
     * 创建时间
     */
    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    /**
     * 更新时间
     */
    public Date getUpdateDate() {
        return updateDate;
    }

    /**
     * 更新时间
     */
    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        GovPublicBusinessConfig other = (GovPublicBusinessConfig) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getBusinessLink() == null ? other.getBusinessLink() == null : this.getBusinessLink().equals(other.getBusinessLink()))
            && (this.getBusinessConfigCode() == null ? other.getBusinessConfigCode() == null : this.getBusinessConfigCode().equals(other.getBusinessConfigCode()))
            && (this.getBusinessConfigName() == null ? other.getBusinessConfigName() == null : this.getBusinessConfigName().equals(other.getBusinessConfigName()))
            && (this.getCategoryCode() == null ? other.getCategoryCode() == null : this.getCategoryCode().equals(other.getCategoryCode()))
            && (this.getCategoryName() == null ? other.getCategoryName() == null : this.getCategoryName().equals(other.getCategoryName()))
            && (this.getRemark() == null ? other.getRemark() == null : this.getRemark().equals(other.getRemark()))
            && (this.getSelectType() == null ? other.getSelectType() == null : this.getSelectType().equals(other.getSelectType()))
            && (this.getIsRequired() == null ? other.getIsRequired() == null : this.getIsRequired().equals(other.getIsRequired()))
            && (this.getCreateDate() == null ? other.getCreateDate() == null : this.getCreateDate().equals(other.getCreateDate()))
            && (this.getUpdateDate() == null ? other.getUpdateDate() == null : this.getUpdateDate().equals(other.getUpdateDate()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getBusinessLink() == null) ? 0 : getBusinessLink().hashCode());
        result = prime * result + ((getBusinessConfigCode() == null) ? 0 : getBusinessConfigCode().hashCode());
        result = prime * result + ((getBusinessConfigName() == null) ? 0 : getBusinessConfigName().hashCode());
        result = prime * result + ((getCategoryCode() == null) ? 0 : getCategoryCode().hashCode());
        result = prime * result + ((getCategoryName() == null) ? 0 : getCategoryName().hashCode());
        result = prime * result + ((getRemark() == null) ? 0 : getRemark().hashCode());
        result = prime * result + ((getSelectType() == null) ? 0 : getSelectType().hashCode());
        result = prime * result + ((getIsRequired() == null) ? 0 : getIsRequired().hashCode());
        result = prime * result + ((getCreateDate() == null) ? 0 : getCreateDate().hashCode());
        result = prime * result + ((getUpdateDate() == null) ? 0 : getUpdateDate().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", businessLink=").append(businessLink);
        sb.append(", businessConfigCode=").append(businessConfigCode);
        sb.append(", businessConfigName=").append(businessConfigName);
        sb.append(", categoryCode=").append(categoryCode);
        sb.append(", categoryName=").append(categoryName);
        sb.append(", remark=").append(remark);
        sb.append(", selectType=").append(selectType);
        sb.append(", isRequired=").append(isRequired);
        sb.append(", createDate=").append(createDate);
        sb.append(", updateDate=").append(updateDate);
        sb.append("]");
        return sb.toString();
    }
}