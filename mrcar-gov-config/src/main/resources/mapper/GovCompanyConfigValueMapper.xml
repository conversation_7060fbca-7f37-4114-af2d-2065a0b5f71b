<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mrcar.gov.config.mapper.GovCompanyConfigValueMapper">

    <resultMap id="BaseResultMap" type="com.mrcar.gov.config.domain.GovCompanyConfigValue">
            <id property="id" column="id" />
            <result property="companyId" column="company_id" />
            <result property="structId" column="struct_id" />
            <result property="structCode" column="struct_code" />
            <result property="businessConfigCode" column="business_config_code" />
            <result property="businessConfigItemCode" column="business_config_item_code" />
            <result property="createDate" column="create_date" />
            <result property="updateDate" column="update_date" />
            <result property="configValue" column="config_value" />
    </resultMap>

    <sql id="Base_Column_List">
        id,company_id,struct_id,struct_code,business_config_code,business_config_item_code,create_date,
        update_date,config_value
    </sql>

    <select id="selectByStructCodeAndBusinessCodeAndItemCode"
            resultType="com.mrcar.gov.common.dto.config.GovCompanyConfigItemValueDTO">
        select item.item_code as itemCode,
        item.item_name as itemName,
        item.business_config_code as businessConfigCode,
        item.type as type,
        item.remark as remark,
        item.is_required as isRequired,
        item.select_type as selectType,
        item.default_value as defaultValue,
        item.data_type as dataType,
        item.max_value as `maxValue`,
        item.min_value as minValue,
        item.possible_values as possibleValues,
        IF(cv.config_value is null, item.default_value, cv.config_value) as configValue
        from gov_public_business_config_item as item
        left join gov_company_config_value as cv
        on item.item_code = cv.business_config_item_code
        and item.business_config_code = cv.business_config_code
        and cv.struct_code = #{structCode}
        <where>
            <if test="businessCode != null and businessCode != ''">
                and item.business_config_code = #{businessCode}
            </if>
            <if test="itemCode != null and itemCode != ''">
                and item.item_code = #{itemCode}
            </if>
        </where>
    </select>

    <select id="selectStructCodeGroupByCompanyId" resultType="java.lang.String">
        select struct_code from gov_company_config_value
        where company_id = #{companyId}
        group by struct_code
    </select>


    <insert id="insertBatch">
        insert into gov_company_config_value (company_id,struct_id,struct_code,business_config_code,business_config_item_code,config_value)
        values
        <foreach collection="saveList" item="item" separator=",">
            (#{item.companyId},#{item.structId},#{item.structCode},#{item.businessConfigCode},#{item.businessConfigItemCode},#{item.configValue})
        </foreach>
    </insert>


</mapper>
