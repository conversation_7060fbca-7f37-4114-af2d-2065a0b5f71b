<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mrcar.gov.config.mapper.GovPublicBusinessConfigMapper">

    <resultMap id="BaseResultMap" type="com.mrcar.gov.config.domain.GovPublicBusinessConfig">
            <id property="id" column="id" jdbcType="INTEGER"/>
             <result property="businessLink" column="business_link" jdbcType="VARCHAR" />
            <result property="businessConfigCode" column="business_config_code" jdbcType="VARCHAR"/>
            <result property="businessConfigName" column="business_config_name" jdbcType="VARCHAR"/>
            <result property="categoryCode" column="category_code" jdbcType="VARCHAR"/>
            <result property="categoryName" column="category_name" jdbcType="VARCHAR"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="selectType" column="select_type" jdbcType="INTEGER"/>
            <result property="isRequired" column="is_required" jdbcType="INTEGER"/>
            <result property="createDate" column="create_date" jdbcType="TIMESTAMP"/>
            <result property="updateDate" column="update_date" jdbcType="TIMESTAMP"/>
    </resultMap>



    <sql id="Base_Column_List">
        id,business_link,business_config_code,business_config_name,
        category_code,category_name,remark,
        select_type,is_required,create_date,
        update_date
    </sql>
</mapper>
