package com.mrcar.gov.asset.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.izu.cache.core.redis.RedisCache;
import com.izu.framework.response.PageDTO;
import com.izu.framework.util.BeanUtil;
import com.izu.framework.web.util.excel.Column;
import com.izu.framework.web.util.excel.ExSheet;
import com.mrcar.gov.asset.common.OilConsumptionEnum;
import com.mrcar.gov.asset.domain.GovCarRefuelingRecord;
import com.mrcar.gov.asset.domain.GovOilConsumption;
import com.mrcar.gov.asset.domain.GovVehicleBaseInfo;
import com.mrcar.gov.asset.mapper.GovOilConsumptionMapper;
import com.mrcar.gov.asset.mapper.GovVehicleBaseInfoMapper;
import com.mrcar.gov.asset.service.GovCarRefuelingRecordService;
import com.mrcar.gov.asset.mapper.GovCarRefuelingRecordMapper;
import com.mrcar.gov.asset.utils.DateUtil;
import com.mrcar.gov.common.constant.config.BaseEnum;
import com.mrcar.gov.common.constant.user.GovDataPermTypeEnum;
import com.mrcar.gov.common.constant.user.ManageCarTypeEnum;
import com.mrcar.gov.common.dto.asset.CarRefuelingRecordDTO;
import com.mrcar.gov.common.dto.asset.CarRefuelingRecordDetailDTO;
import com.mrcar.gov.common.dto.asset.OilConsumptionDTO;
import com.mrcar.gov.common.dto.asset.request.*;
import com.mrcar.gov.common.dto.asset.response.CarRefuelingRecordOCRResDTO;
import com.mrcar.gov.common.dto.config.BaseEnumDTO;
import com.mrcar.gov.common.dto.config.req.BaseEnumReqDTO;
import com.mrcar.gov.common.dto.thirdparty.dto.OcrGeneralDTO;
import com.mrcar.gov.common.dto.user.resp.GovOrgInfoListRespDTO;
import com.mrcar.gov.common.dto.workflow.enums.BpmProcessInstanceResultEnum;
import com.mrcar.gov.common.dto.workflow.instance.BpmMessageSendApproveResultDTO;
import com.mrcar.gov.common.util.DateUtils;
import com.mrcar.gov.common.util.excel.EasyExcelUtil;
import com.mrcar.gov.config.domain.GovBaseEnum;
import com.mrcar.gov.config.service.GovBaseEnumService;
import com.mrcar.gov.config.service.GovCompanyBussinessService;
import com.mrcar.thirdparty.ocr.OcrTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReentrantReadWriteLock;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【gov_car_refueling_record(加油记录表)】的数据库操作Service实现
* @createDate 2024-11-19 20:17:42
*/
@Slf4j
@Service
public class GovCarRefuelingRecordServiceImpl extends ServiceImpl<GovCarRefuelingRecordMapper, GovCarRefuelingRecord>
    implements GovCarRefuelingRecordService{

    @Resource
    private RedisCache redisCache;

    @Resource
    private GovCompanyBussinessService govCompanyBussinessService ;

    @Resource
    private OcrTemplate ocrTemplate;

    @Resource
    private GovBaseEnumService govBaseEnumService;

    @Resource
    private GovVehicleBaseInfoMapper govVehicleBaseInfoMapper;



    @Resource
    private GovOilConsumptionMapper govOilConsumptionMapper;

    @Resource
    private GovCarRefuelingRecordMapper govCarRefuelingRecordMapper;


    @Override
    public PageDTO<CarRefuelingRecordDTO> listPageApp(AppCarRefuelingRecordPageReqDTO reqDTO) {
        reqDTO.setLoginCompanyId(reqDTO.getLoginCompanyId());
        reqDTO.setLoginUserId(reqDTO.getLoginUserId());
        IPage<GovCarRefuelingRecord> page = new Page<>(reqDTO.getPage(), reqDTO.getPageSize());
        IPage<GovCarRefuelingRecord> pageList = govCarRefuelingRecordMapper.getPageList(page, reqDTO);
        List<CarRefuelingRecordDTO> govGpsDevicePageList = BeanUtil.copyList(pageList.getRecords(), CarRefuelingRecordDTO.class);
        return new PageDTO<>(reqDTO.getPage(), reqDTO.getPageSize(), Integer.parseInt(String.valueOf(pageList.getTotal())), govGpsDevicePageList);
    }


    /**
     * 识别加油小票
     * @param dto
     * @return
     */
    @Override
    public CarRefuelingRecordOCRResDTO toOCR(CarRefuelingRecordOCRReqDTO dto) {
        ReentrantReadWriteLock lock = new ReentrantReadWriteLock();
        ReentrantReadWriteLock.ReadLock readLock = lock.readLock();
        ReentrantReadWriteLock.WriteLock writeLock = lock.writeLock();
        CarRefuelingRecordOCRResDTO carRefuelingRecordOCRResDTO = null;
        // 判断是否开启ocr识别
        if (haveOCR(dto.getCompanyId())) {
            // 存储对ocr解析后的dto 有效期1天
            final String dtoKey = "ocr:dto:" + dto.getVoucherUrl();
            // 存储ocr识别的内容 有效期1天
            final String ocrKey = "ocr:result:" + dto.getVoucherUrl();
            try {
                // 获取读锁
                readLock.lock();
                String cacheObject = null; //redisCache.getCacheObject(dtoKey);
                if(StringUtils.isBlank(cacheObject)){
                    carRefuelingRecordOCRResDTO =  JSON.parseObject(cacheObject,CarRefuelingRecordOCRResDTO.class);
                }

            } finally {
                readLock.unlock();
            }
            if (carRefuelingRecordOCRResDTO == null) {
                try {
                    // 获取写锁
                    writeLock.lock();
                    carRefuelingRecordOCRResDTO = new CarRefuelingRecordOCRResDTO();
                    carRefuelingRecordOCRResDTO.setVoucherUrl(dto.getVoucherUrl());
                    String content = redisCache.getCacheObject(ocrKey);
                    if (StringUtils.isBlank(content)) {
                        OcrGeneralDTO ocrGeneralDTO = invokeOCR(dto.getVoucherUrl());
                        content = ocrGeneralDTO.getContent();
                        redisCache.setCacheObject(ocrKey, content, 86400, TimeUnit.SECONDS);
                    }
                    if (StringUtils.isNotBlank(content)) {
                        carRefuelingRecordOCRResDTO.setContent(content);
                        // 解析ocr结果
                        parseWords(carRefuelingRecordOCRResDTO);
                        // 将结果保存到内存里
                        redisCache.setCacheObject(dtoKey, carRefuelingRecordOCRResDTO, 86400, TimeUnit.SECONDS);
                    }
                } catch (Exception e) {
                    log.error("加油小票识别异常" + e.getMessage(), e);
                } finally {
                    writeLock.unlock();
                }
            }
        }
        // 如开启则进行识别并返回识别内容
        return carRefuelingRecordOCRResDTO;
    }


    private boolean haveOCR(Integer companyId) {
        return true;
    }

    private OcrGeneralDTO invokeOCR(String url) {
        try {
            return ocrTemplate.refuelTicketParser(url);
        } catch (Exception e) {
            log.error("ali-燃油小票ocr解析异常 " + e.getMessage(), e);
        }
        return new OcrGeneralDTO();
    }

    private void parseWords(CarRefuelingRecordOCRResDTO carRefuelingRecordOCRResDTO) {
        String content = carRefuelingRecordOCRResDTO.getContent();
        carRefuelingRecordOCRResDTO.setRefuelingTime(patternTime(getPattern(OilConsumptionEnum.OcrPatternEnum.TIME), content));
        carRefuelingRecordOCRResDTO.setAddFee(patternWords(getPattern(OilConsumptionEnum.OcrPatternEnum.PAY), content));
        carRefuelingRecordOCRResDTO.setAddValue(patternWords(getPattern(OilConsumptionEnum.OcrPatternEnum.VOLUME), content));
    }

    private List<Pattern> getPattern(OilConsumptionEnum.OcrPatternEnum ocrPatternEnum) {

        GovBaseEnum baseEnumDTO = govBaseEnumService.getEnumByCodeAndParentCode(new BaseEnumReqDTO() {{
            setCode(ocrPatternEnum.getCode());
            setParentCode(BaseEnum.FirstLevelEnum.CAR_REFUELING_RECORD_OCR_PATTERN.getCode());}});
        if (baseEnumDTO != null && StringUtils.isNotBlank(baseEnumDTO.getValue())) {
            return Arrays.stream(baseEnumDTO.getValue().split("@regex@")).filter(StringUtils::isNotBlank).map(Pattern::compile).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    private String patternWords(List<Pattern> patterns, String words) {
        try {
            for (Pattern pattern : patterns) {
                Matcher matcher = pattern.matcher(words);
                if (matcher.find()) {
                    return matcher.group();
                }
            }
        } catch (Exception e) {
            log.error("加油小票解析异常" + e.getMessage(), e);
        }
        return "";
    }

    private String patternTime(List<Pattern> patterns, String words) {
        try {
            for (Pattern pattern : patterns) {
                Matcher matcher = pattern.matcher(words);
                if (matcher.find()) {
                    int groupCount = matcher.groupCount();
                    String year = matcher.group(groupCount - 3);
                    String month = matcher.group(groupCount - 2);
                    String day = matcher.group(groupCount - 1);
                    String time = matcher.group(groupCount);
                    if (time == null) {
                        time = "00:00:00";
                    } else {
                        time = time.replaceAll("\\D|^:", ":");
                    }
                    return DateUtil.dateStringToString(year + "-" + month + "-" + day + " " + time, DateUtil.TIME_FORMAT);
                }
            }
        } catch (Exception e) {
            log.error("加油小票解析日期异常" + e.getMessage(), e);
        }
        return "";
    }


    /**
     * 保存加油记录
     */
    @Transactional(rollbackFor = Exception.class)
    public CarRefuelingRecordDetailDTO save(AppCarRefuelingRecordReqDTO dto, GovCarRefuelingRecord carRefuelingRecord,
                                            GovOilConsumption oilConsumption) {

        // 预保存生成油耗
        carRefuelingRecord.setWarnType(oilConsumption.getWarnType());
        // 判断是否有油耗分析
        if (StringUtils.isNotBlank(oilConsumption.getLastRefuelingCode())) {
            govOilConsumptionMapper.insertSelective(oilConsumption);
        }
        govCarRefuelingRecordMapper.insertSelective(carRefuelingRecord);

        CarRefuelingRecordDetailDTO carRefuelingRecordDetailDTO = null;

        // 是则返回油耗分析
        OilConsumptionDTO oilConsumptionDTO = BeanUtil.copyObject(oilConsumption, OilConsumptionDTO.class);
        if (oilConsumptionDTO != null) {
            oilConsumptionDTO.setId(oilConsumption.getId());
            oilConsumptionDTO.setCarRefuelingId(carRefuelingRecord.getId());
            carRefuelingRecordDetailDTO = BeanUtil.copyObject(carRefuelingRecord, CarRefuelingRecordDetailDTO.class);
            carRefuelingRecordDetailDTO.setId(carRefuelingRecord.getId());
            carRefuelingRecordDetailDTO.setOilConsumptionDTO(oilConsumptionDTO);
        }
        return carRefuelingRecordDetailDTO;
    }



    /**
     * pc列表分页查询
     * @param reqDTO
     * @return
     */
    @Override
    public PageDTO<CarRefuelingRecordDTO> listPage(CarRefuelingRecordPageReqDTO reqDTO) {

        if (!CollectionUtils.isEmpty(reqDTO.getVehicleBelongDeptCodes())) {
            reqDTO.setVehicleBelongDeptCode(null);
        }

        IPage<CarRefuelingRecordPageReqDTO> page = new Page<>(reqDTO.getPage(), reqDTO.getPageSize());
        IPage<GovCarRefuelingRecord> pageList = govCarRefuelingRecordMapper.listPc(page, reqDTO);
        List<CarRefuelingRecordDTO> govGpsDevicePageList = BeanUtil.copyList(pageList.getRecords(), CarRefuelingRecordDTO.class);

        if (!CollectionUtils.isEmpty(govGpsDevicePageList)) {
            List<String> codes = govGpsDevicePageList.stream().map(CarRefuelingRecordDTO::getRefuelingCode).collect(Collectors.toList());
            LambdaQueryWrapper wrappers = Wrappers.lambdaQuery(GovOilConsumption.class).in(GovOilConsumption::getRefuelingCode, codes);
            List<GovOilConsumption> GovOilConsumptionList = govOilConsumptionMapper.selectList(wrappers);

            if (!CollectionUtils.isEmpty(GovOilConsumptionList)) {

                Map<String, GovOilConsumption> GovOilConsumptionMap = GovOilConsumptionList.stream().
                        collect(Collectors.toMap(GovOilConsumption::getRefuelingCode, Function.identity()));

                for (CarRefuelingRecordDTO recordDTO : govGpsDevicePageList) {
                    GovOilConsumption govOilConsumption = GovOilConsumptionMap.get(recordDTO.getRefuelingCode());
                    if (govOilConsumption != null) {
                        recordDTO.setGpsMileage(govOilConsumption.getGpsMileage());
                        recordDTO.setGpsOilConsumption(govOilConsumption.getGpsOilConsumption());
                        recordDTO.setDashboardOilConsumption(govOilConsumption.getDashboardOilConsumption());
                    }

                }
            }
            govGpsDevicePageList.forEach(recordDTO -> {
                recordDTO.setManageCarTypeName(ManageCarTypeEnum.getDesByCode(recordDTO.getManageCarType()));
            });
        }


        return new PageDTO<>(reqDTO.getPage(), reqDTO.getPageSize(), Integer.parseInt(String.valueOf(pageList.getTotal())), govGpsDevicePageList);
    }

    @Override
    public void listPageExport(HttpServletRequest request, HttpServletResponse response, CarRefuelingRecordPageReqDTO dto) {

        List<Column> columnModes = new ArrayList<>();
        columnModes.add(new Column("refuelingCode", "加油单号", (short) 7000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("vehicleLicense", "车牌号", (short) 4000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("approvalStatusValue", "工单状态", (short) 4000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("addFee", "加油金额", (short) 4000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("addValue", "加油升数", (short) 4000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("dashboardMileage", "仪表盘里程", (short) 4000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("payTypeValue", "支付方式", (short) 4000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("refuelingTime", "加油时间", (short) 4000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("gpsMileage", "GPS行驶里程", (short) 4000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("gpsOilConsumption", "GPS百公里油耗", (short) 4000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("warnTypeValue", "异常提示", (short) 4000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("refuelingAddress", "加油地址", (short) 4000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("cooperationOrgStatusDesc", "是否合作机构", (short) 4000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("orgName", "合作机构名称", (short) 4000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("userName", "驾驶员", (short) 4000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("vehicleBelongDeptName", "车辆所有人", (short) 4000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("vehicleUseDeptName", "车辆使用人", (short) 4000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("vehicleUseStructName", "车辆使用部门", (short) 4000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("vehicleManageDeptName", "管理部门", (short) 4000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("createName", "创建人", (short) 4000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("createTime", "创建时间", (short) 4000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("updateTime", "更新时间", (short) 4000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("vehicleVin", "车架号", (short) 4000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));

        dto.setPage(1);
        dto.setPageSize(5000);

        List<CarRefuelingRecordDTO> result = dto.getExportEmptyFile() != null && dto.getExportEmptyFile() ?
                Lists.newArrayList() : listPage(dto).getResult();

        List<Map<String, String>> maps = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(result) && !Objects.equals(dto.getLoginDataPermType(), GovDataPermTypeEnum.SELF.getCode())) {
            maps = result.stream().map(item -> {
                HashMap<String, String> map = new HashMap<>();
                map.put("refuelingCode", item.getRefuelingCode());
                map.put("vehicleLicense", item.getVehicleLicense());
                map.put("approvalStatusValue", item.getApprovalStatusValue());

                String addFee = item.getAddFee() != null ? item.getAddFee().toString() : "0";
                map.put("addFee", addFee);

                String addValue = item.getAddValue() != null ? item.getAddValue().toString() : "0";
                map.put("addValue", addValue);

                String dashboardMileage = item.getDashboardMileage() != null ? item.getDashboardMileage().toString() : "0";
                map.put("dashboardMileage", dashboardMileage);
                map.put("payTypeValue", item.getPayTypeValue());

                String refuelingTime = item.getRefuelingTime() != null ?
                        DateUtils.format(item.getRefuelingTime(), DateUtils.TIME_FORMAT) : StringUtils.EMPTY;
                map.put("refuelingTime", refuelingTime);

                String gpsMileage = item.getGpsMileage() != null ? item.getGpsMileage().toString() : "0";
                map.put("gpsMileage", gpsMileage);

                String gpsOilConsumption = item.getDashboardOilConsumption() != null ?
                        item.getGpsOilConsumption().toString() : "0";
                map.put("gpsOilConsumption", gpsOilConsumption);

                String warnTypeValue = StringUtils.EMPTY;
                if (!CollectionUtils.isEmpty(item.getWarnTypeValue())) {
                    warnTypeValue = item.getWarnTypeValue().stream().collect(Collectors.joining(","));
                }
                map.put("warnTypeValue", warnTypeValue);

                map.put("refuelingAddress", item.getRefuelingAddress());
                map.put("cooperationOrgStatusDesc", item.getCooperationOrgStatusDesc());
                map.put("orgName", item.getOrgName());
                map.put("userName", item.getUserName());
                map.put("vehicleBelongDeptName", item.getVehicleBelongDeptName());
                map.put("vehicleUseDeptName", item.getVehicleUseDeptName());
                map.put("vehicleUseStructName", item.getVehicleUseStructName());
                map.put("vehicleManageDeptName", item.getVehicleManageDeptName());
                map.put("createName", item.getCreateName());

                String createTime = item.getCreateTime() != null ?
                        DateUtils.format(item.getCreateTime(), DateUtils.TIME_FORMAT) : StringUtils.EMPTY;
                map.put("createTime", createTime);

                String updateTime = item.getUpdateTime() != null ?
                        DateUtils.format(item.getUpdateTime(), DateUtils.TIME_FORMAT) : StringUtils.EMPTY;
                map.put("updateTime", updateTime);
                map.put("vehicleVin", item.getVehicleVin());
                return map;
            }).collect(Collectors.toList());
        }




        ExSheet exSheet = new ExSheet(columnModes, maps, "加油记录");

        EasyExcelUtil.exportExcel(request, response, "加油记录.xlsx", "加油记录.xlsx", Collections.singletonList(exSheet));

    }


    @Override
    public CarRefuelingRecordDetailDTO detail(AppCarRefuelingRecordReqDTO dto) {
                CarRefuelingRecordDetailDTO carRefuelingRecordDetailDTO = null;
        GovCarRefuelingRecord carRefuelingRecord = null;
        if (dto.getId() != null) {
            carRefuelingRecord = govCarRefuelingRecordMapper.selectByPrimaryKey(dto.getId());

        } else if (StringUtils.isNotBlank(dto.getRefuelingCode()) || StringUtils.isNotBlank(dto.getBusinessNo())) {
            String code = StringUtils.isNotBlank(dto.getRefuelingCode()) ? dto.getRefuelingCode() : dto.getBusinessNo();
            carRefuelingRecord = queryCarRefuelingRecordByCode(code);
        }
        if (carRefuelingRecord != null) {
            carRefuelingRecordDetailDTO = BeanUtil.copyObject(carRefuelingRecord, CarRefuelingRecordDetailDTO.class);

            GovVehicleBaseInfo carInfoByVehicleVin =
                    govVehicleBaseInfoMapper.getCarInfoByVehicleVin(carRefuelingRecord.getVehicleVin(), null, dto.getLoginCompanyId());
            if (carInfoByVehicleVin != null) {
                carRefuelingRecordDetailDTO.setVehicleModel(carInfoByVehicleVin.getVehicleSeriesName());
            }

            OilConsumptionDTO oilConsumptionDTO =
                    getDTOByOilCByCode(carRefuelingRecord.getRefuelingCode(), Byte.valueOf(carRefuelingRecord.getStatus().toString()));
            // 首次加油，累计油耗真是第一次加油油量
            if (carRefuelingRecord.getWarnType() != null
                    && OilConsumptionEnum.WarnTypeEnum.FIRST_ADD_REFUEL.getCode().toString().equals(carRefuelingRecord.getWarnType())) {
                oilConsumptionDTO = oilConsumptionDTO == null ? new OilConsumptionDTO() : oilConsumptionDTO;
                oilConsumptionDTO.setOilConsumption(carRefuelingRecord.getAddValue());
            }
            carRefuelingRecordDetailDTO.setOilConsumptionDTO(oilConsumptionDTO);
        }
        return carRefuelingRecordDetailDTO;
    }

    private GovCarRefuelingRecord queryCarRefuelingRecordByCode(String code) {
        List<GovCarRefuelingRecord> carRefuelingRecords = govCarRefuelingRecordMapper.selectByRefuelingCode(code);
        return !CollectionUtils.isEmpty(carRefuelingRecords) ? carRefuelingRecords.get(0) : null;
    }

    private OilConsumptionDTO getDTOByOilCByCode(String code, Byte status) {
        return BeanUtil.copyObject(getByOilCByCode(code, status), OilConsumptionDTO.class);
    }

    private GovOilConsumption getByOilCByCode(String code, Byte status) {
        Wrapper wrapper= Wrappers.lambdaQuery(GovOilConsumption.class)
        .eq(GovOilConsumption::getRefuelingCode, code).eq(GovOilConsumption::getStatus, status);

        List<GovOilConsumption> oilConsumptions = govOilConsumptionMapper.selectList(wrapper);
        return !CollectionUtils.isEmpty(oilConsumptions) ? oilConsumptions.get(0) : null;
    }

    @Override
    public PageDTO<GovOrgInfoListRespDTO> gasStationsList(GasStationsReqDTO reqDTO) {
        return null;
    }

    public GovCarRefuelingRecord getByPrimaryKey(Integer id) {
        return govCarRefuelingRecordMapper.selectByPrimaryKey(id);
    }

    /**
     * 确认提交
     * @param reqDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void toSave(AppOilConsumptionReqBaseDTO reqDTO, GovCarRefuelingRecord carRefuelingRecord) {
        Integer result = 0;
        // 真正保存将油耗记录更新为有效
        // 判断加油记录id是否有值，有值说明是在提示油耗异常后确定提交，此时更新已保存的加油记录和油耗记录状态
        GovCarRefuelingRecord oldCarRefuelingRecord = govCarRefuelingRecordMapper.selectByPrimaryKey(reqDTO.getId());
        if (oldCarRefuelingRecord != null) {
            reqDTO.setVehicleLicense(oldCarRefuelingRecord.getVehicleLicense());

            Wrapper recordLambdaQueryWrapperwrapper = Wrappers.lambdaQuery(GovCarRefuelingRecord.class).
                    eq(GovCarRefuelingRecord::getId,oldCarRefuelingRecord.getId());
            govCarRefuelingRecordMapper.update(carRefuelingRecord, recordLambdaQueryWrapperwrapper);

            GovOilConsumption oldOilConsumption = getByOilCByCode(oldCarRefuelingRecord.getRefuelingCode(), (byte) 0);
            if (oldOilConsumption != null) {
                GovOilConsumption oilConsumption = new GovOilConsumption();
                oilConsumption.setStatus( 1);

                Wrapper oilConsumptionLambdaQueryWrapper = Wrappers.lambdaQuery(GovOilConsumption.class).
                        eq(GovOilConsumption::getId,oldOilConsumption.getId());
                govOilConsumptionMapper.update(oilConsumption, oilConsumptionLambdaQueryWrapper);
            }
        }
    }

    /**
     * 审批撤销
     * @param dto
     * @return
     */
    @Override
    public String withdrawal(AppOilConsumptionReqBaseDTO dto) {
        GovCarRefuelingRecord carRefuelingRecord = new GovCarRefuelingRecord();
        carRefuelingRecord.setUpdateId(dto.getLoginUserId());
        carRefuelingRecord.setUpdateName(dto.getLoginUserName());
        carRefuelingRecord.setApprovalStatus(Integer.valueOf(BpmProcessInstanceResultEnum.CANCEL.getResult()));
        carRefuelingRecord.setCancelReason(dto.getCancelReason());

        Wrapper carRefuelingRecordWrapper = Wrappers.lambdaQuery(GovCarRefuelingRecord.class).
                eq(GovCarRefuelingRecord::getId, dto.getId());
        govCarRefuelingRecordMapper.update(carRefuelingRecord, carRefuelingRecordWrapper);
        return null;
    }


    public void approveResult(BpmMessageSendApproveResultDTO approveResultDTO) {
        log.info("加油审批结果:{}", JSON.toJSONString(approveResultDTO));
        String businessNo = approveResultDTO.getBusinessNo();

        GovCarRefuelingRecord record = queryCarRefuelingRecordByCode(businessNo);
        if (record == null) {
            log.warn("加油审批结果-找不到加油记录");
            return;
        }

        record.setApprovalStatus(Integer.valueOf(approveResultDTO.getResult()));
        record.setApprovalTime(approveResultDTO.getApproverTime());
        LambdaQueryWrapper<GovCarRefuelingRecord> wrapper = Wrappers.lambdaQuery(GovCarRefuelingRecord.class)
                .eq(GovCarRefuelingRecord::getRefuelingCode, businessNo);
        govCarRefuelingRecordMapper.update(record, wrapper);

    }

    @Override
    public GovCarRefuelingRecord selectLastRecord(AppCarRefuelingRecordReqDTO dto) {
        return govCarRefuelingRecordMapper.selectLastRecord(dto);
    }

    @Override
    public Date selectMaxRefuelingTime(AppCarRefuelingRecordReqDTO dto) {
        return govCarRefuelingRecordMapper.selectMaxRefuelingTime(dto);
    }

    @Override
    public BigDecimal getMaxDashboardMileage(AppCarRefuelingRecordReqDTO dto) {
        return govCarRefuelingRecordMapper.getMaxDashboardMileage(dto.getLoginCompanyId(), dto.getVehicleLicense());
    }
}




