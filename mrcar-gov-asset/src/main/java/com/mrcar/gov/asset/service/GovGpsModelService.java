package com.mrcar.gov.asset.service;

import com.mrcar.gov.asset.domain.GovGpsManufact;
import com.mrcar.gov.asset.domain.GovGpsModel;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【gov_gps_model(设备型号表)】的数据库操作Service
* @createDate 2024-11-08 21:19:16
*/
public interface GovGpsModelService extends IService<GovGpsModel> {
    GovGpsModel getByIdFromRedisCache(Integer id);
    GovGpsModel getByIdThrowException(Integer id);


    GovGpsModel getByModelNameFromDb(String modelName);
    GovGpsModel getByModelNameFromCache(String manufactName);
    GovGpsModel getByModelNameThrowException(String modelName);

    List<GovGpsModel> getAllGovGpsModelList();
}
