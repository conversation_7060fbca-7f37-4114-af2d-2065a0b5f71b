package com.mrcar.gov.asset.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.izu.framework.exception.ApiException;
import com.izu.framework.resp.InfoCode;
import com.mrcar.gov.asset.common.AssetFileTypeEnum;
import com.mrcar.gov.asset.domain.GovApproveReply;
import com.mrcar.gov.asset.domain.GovTransactionApply;
import com.mrcar.gov.asset.domain.GovVehiclePlan;
import com.mrcar.gov.asset.mapper.GovApproveReplyMapper;
import com.mrcar.gov.asset.service.*;
import com.mrcar.gov.common.constant.asset.VehicleStatusEnum;
import com.mrcar.gov.common.constant.business.GovTransactionApplyEnum;
import com.mrcar.gov.common.dto.business.request.DisposalReplyReqDTO;
import com.mrcar.gov.common.dto.business.request.GovUpLoadDisposalReplyReqDTO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【gov_approve_reply(车辆批复表)】的数据库操作Service实现
 * @createDate 2024-11-22 14:57:59
 */
@Service
public class GovApproveReplyServiceImpl extends ServiceImpl<GovApproveReplyMapper, GovApproveReply>
        implements GovApproveReplyService {

    @Resource
    private GovVehiclePlanService govVehiclePlanService;

    @Resource
    private GovAssetFileService govFileService;

    @Resource
    private GovVehicleDisposalDetailService govVehicleDisposalDetailService;

    @Resource
    private GovTransactionApplyService govTransactionApplyService;

    @Resource
    private GovVehicleBaseInfoService govVehicleBaseInfoService;

    @Override
    public Boolean upLoadDisposalReply(GovUpLoadDisposalReplyReqDTO upLoadDisposalReplyReqDTO) {
        String applyNo = upLoadDisposalReplyReqDTO.getApplyNo();
        LambdaQueryWrapper<GovTransactionApply> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(GovTransactionApply::getApplyNo, applyNo);
        GovTransactionApply approveReply = govTransactionApplyService.getOne(queryWrapper);
        if (approveReply == null) {
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "业务申请单不存在");
        }
        List<DisposalReplyReqDTO> disposalReplyList = upLoadDisposalReplyReqDTO.getDisposalReplyList();
        Map<String, DisposalReplyReqDTO> disposalReplyMap = disposalReplyList.stream().collect(Collectors.toMap(DisposalReplyReqDTO::getVehicleNo, Function.identity(), (v1, v2) -> v1));
        LambdaQueryWrapper<GovVehiclePlan> vehiclePlanLambdaQueryWrapper = new LambdaQueryWrapper<>();
        vehiclePlanLambdaQueryWrapper.in(GovVehiclePlan::getApplyNo, applyNo);
        Optional<List<GovVehiclePlan>> vehiclePlanList = Optional.ofNullable(govVehiclePlanService.list(vehiclePlanLambdaQueryWrapper));
        List<GovApproveReply> rows = new java.util.ArrayList<>();
        for (GovVehiclePlan vehiclePlan : vehiclePlanList.orElse(Lists.newArrayList())) {
            GovApproveReply govApproveReply = new GovApproveReply();
            govApproveReply.setApplyNo(applyNo);
            govApproveReply.setVehiclePlanId(vehiclePlan.getId());
            DisposalReplyReqDTO disposalReplyReqDTO = disposalReplyMap.get(vehiclePlan.getVehicleNo());
            if(disposalReplyReqDTO !=null){
                govApproveReply.setDisposalMethod(disposalReplyReqDTO.getDisposalMethod());
                if(Objects.equals(disposalReplyReqDTO.getDisposalMethod(), GovTransactionApplyEnum.DisposalMethodEnum.ALLOCATION.getCode())){
                    govApproveReply.setTransferInStructCode(disposalReplyReqDTO.getTransferInStructCode());
                    govApproveReply.setTransferInStructName(disposalReplyReqDTO.getTransferInStructName());
                }

            }
            govApproveReply.setVehicleNum(vehiclePlan.getVehicleNum());
            govApproveReply.setCompanyId(upLoadDisposalReplyReqDTO.getLoginCompanyId());
            rows.add(govApproveReply);
        }
        saveBatch(rows);
        govFileService.saveVehicleFile(applyNo, AssetFileTypeEnum.BUSINESS_TRANSACTION_REPLY.getCode(), upLoadDisposalReplyReqDTO.getReplyFileList(), upLoadDisposalReplyReqDTO.getLoginCompanyId());
        //修改状态为已完成
        govTransactionApplyService.updateDisposalStatus(applyNo, GovTransactionApplyEnum.ApplyStatusEnum.COMPLETED.getCode(),upLoadDisposalReplyReqDTO.getReplyRemark(), upLoadDisposalReplyReqDTO.getLoginUserCode(), upLoadDisposalReplyReqDTO.getLoginUserName());
        //插入明细
        govVehicleDisposalDetailService.saveDisposal(disposalReplyList, upLoadDisposalReplyReqDTO.getLoginCompanyId(), applyNo, upLoadDisposalReplyReqDTO.getLoginUserCode(), upLoadDisposalReplyReqDTO.getLoginUserName());
        // 状态已完成时 更新车辆状态
        List<String> transFerVehicleNos = disposalReplyList.stream().filter(o -> Objects.equals(o.getDisposalMethod(), GovTransactionApplyEnum.DisposalMethodEnum.ALLOCATION.getCode())).map(o -> o.getVehicleNo()).collect(Collectors.toList());
        List<String> notTransFerVehicleNos = disposalReplyList.stream().filter(o -> !Objects.equals(o.getDisposalMethod(), GovTransactionApplyEnum.DisposalMethodEnum.ALLOCATION.getCode())).map(o -> o.getVehicleNo()).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(transFerVehicleNos)) {
            govVehicleBaseInfoService.batchUpdateVehicleStatus(transFerVehicleNos, VehicleStatusEnum.TRANSFER.getCode(), upLoadDisposalReplyReqDTO.getLoginUserCode(), upLoadDisposalReplyReqDTO.getLoginUserName());
        }
        if (CollUtil.isNotEmpty(notTransFerVehicleNos)) {
            govVehicleBaseInfoService.batchUpdateVehicleStatus(notTransFerVehicleNos, VehicleStatusEnum.DISPOSAL.getCode(), upLoadDisposalReplyReqDTO.getLoginUserCode(), upLoadDisposalReplyReqDTO.getLoginUserName());
        }
        return Boolean.TRUE;
    }
}




