<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mrcar.gov.asset.mapper.GovMaintainEvaluationLabelMapper">

    <resultMap id="BaseResultMap" type="com.mrcar.gov.asset.domain.GovMaintainEvaluationLabel">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="evaluationId" column="evaluation_id" jdbcType="INTEGER"/>
            <result property="labelName" column="label_name" jdbcType="VARCHAR"/>
            <result property="satisfiedFlag" column="satisfied_flag" jdbcType="TINYINT"/>
            <result property="status" column="status" jdbcType="TINYINT"/>
            <result property="createCode" column="create_code" jdbcType="VARCHAR"/>
            <result property="createName" column="create_name" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateCode" column="update_code" jdbcType="VARCHAR"/>
            <result property="updateName" column="update_name" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,evaluation_id,label_name,
        satisfied_flag,status,create_code,
        create_name,create_time,update_code,
        update_name,update_time
    </sql>
</mapper>
