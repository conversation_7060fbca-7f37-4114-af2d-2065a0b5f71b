package com.mrcar.gov.common.enums.iot;


public enum CarStatusMongoFieldEnum {
    _id,
    carNo,
    deviceId,
    vehicleVin,
    workPattern,
    powerStatus,
    keyStatus,
    keyInsertionStatus,
    doorLeftFor,
    doorLeftBeh,
    doorRightFor,
    doorRightBeh,
    doorStatus,
    rearTailboxStatus,
    doorLockLeftFor,
    doorLockLeftBeh,
    doorLockRightFor,
    doorLockRightBeh,
    doorLockStatus,
    windowLeftFor,
    windowLeftBeh,
    windowRightFor,
    windowRightBeh,
    windowStatus,
    sunroofStatus,
    ligthLow,
    lightHead,
    lightWide,
    lightEmer,
    fogFor,
    fogBeh,
    lampStatus,
    gearsStatus,
    parkingStatus,
    batteryStatus,
    rotationRate,
    speed,
    totalMileage,
    totalVoltage,
    totalCurrent,
    remainingCapacity,
    remainingFuel,
    enduranceMileage,
    chargeStatus,
    signalStatus,
    createDate,
    businessLine,
    engineStatus;
}
