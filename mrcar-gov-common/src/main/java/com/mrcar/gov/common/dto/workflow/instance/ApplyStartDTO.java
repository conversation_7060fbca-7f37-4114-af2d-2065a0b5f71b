package com.mrcar.gov.common.dto.workflow.instance;

import com.mrcar.gov.common.dto.workflow.BaseDTO;
import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class ApplyStartDTO extends BaseDTO {

    /**
     * 业务类型
     * 参照 {@link com.mrcar.gov.common.dto.workflow.enums.ModelEnum.BusinessTypeEnum}
     * 必须提供，不能为空
     */
    @NotNull(message = "业务类型不能为空")
    private Byte businessType;

    /**
     * 业务单据号
     * 必须提供，不能为空
     */
    @NotBlank(message = "业务单据号不能为空")
    private String businessNo;

    /**
     * 变量实例，json字符串格式
     * 用于业务规则判断时使用
     */
    private String variables;

}
