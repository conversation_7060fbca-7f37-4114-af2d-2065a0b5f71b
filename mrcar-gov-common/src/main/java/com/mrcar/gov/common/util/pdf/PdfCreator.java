package com.mrcar.gov.common.util.pdf;

import com.mrcar.gov.common.util.pdf.api.PdfCreatorBuilder;
import com.mrcar.gov.common.util.pdf.layout.DefaultRenderFactory;
import com.mrcar.gov.common.util.pdf.model.PageElement;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;

import java.io.Closeable;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.util.List;

public class PdfCreator implements Closeable {

    private PDDocument document;
    private List<PageElement> pages;

    public static PdfCreatorBuilder builder() {
        return new PdfCreatorBuilder();
    }

    public PdfCreator(PDDocument document,
                      List<PageElement> pages) {
        this.document = document;
        this.pages = pages;
    }

    /**
     * 输出pdf文件
     */
    public void export(File output) throws IOException {
        DefaultRenderFactory factory = new DefaultRenderFactory();
        for (PageElement root : pages) {
            // create pdf page
            PDPage page = new PDPage(root.pageSize());
            // add page
            this.document.addPage(page);
            // content stream
            try (PDPageContentStream stream =
                         new PDPageContentStream(document, page)) {
                // render
                factory.render(root, stream);
            }
        }
        this.document.save(output);
    }

    public void export(OutputStream outputStream) throws IOException {
        DefaultRenderFactory factory = new DefaultRenderFactory();
        for (PageElement root : pages) {
            // create pdf page
            PDPage page = new PDPage(root.pageSize());
            // add page
            this.document.addPage(page);
            // content stream
            try (PDPageContentStream stream =
                         new PDPageContentStream(document, page)) {
                // render
                factory.render(root, stream);
            }
        }
        this.document.save(outputStream);
    }

    public PDDocument getDocument() throws IOException{
        DefaultRenderFactory factory = new DefaultRenderFactory();
        for (PageElement root : pages) {
            // create pdf page
            PDPage page = new PDPage(root.pageSize());
            // add page
            this.document.addPage(page);
            // content stream
            try (PDPageContentStream stream =
                         new PDPageContentStream(document, page)) {
                // render
                factory.render(root, stream);
            }
        }
        return document;
    }

    @Override
    public void close() throws IOException {
        if (document != null) {
            document.close();
        }
    }

}
