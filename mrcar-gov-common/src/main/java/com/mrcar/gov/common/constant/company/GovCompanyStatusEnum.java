package com.mrcar.gov.common.constant.company;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/11/16 10:31
 */
@AllArgsConstructor
@Getter
public enum GovCompanyStatusEnum {
   // 0：已停用;  1：已开通;3：未开通；
   STOP(0, "禁用"),
    NORMAL(1, "启用"),
    //NOT_OPEN(3, "未开通")
    ;

    private final Integer code;
    private final String desc;

    public static GovCompanyStatusEnum of(int code) {
        for (GovCompanyStatusEnum item : GovCompanyStatusEnum.values()) {
            if (item.code == code) {
                return item;
            }
        }
        return null;
    }

    public static String getDescByCode(Integer code) {
        GovCompanyStatusEnum enu =  of(code);
        if(Objects.isNull(enu)){
            return "";
        }
        return enu.desc;
    }
}
