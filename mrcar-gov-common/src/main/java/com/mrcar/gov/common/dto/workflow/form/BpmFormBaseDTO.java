package com.mrcar.gov.common.dto.workflow.form;

import com.mrcar.gov.common.dto.workflow.BaseDTO;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import javax.validation.constraints.NotNull;

/**
 * 动态表单
 * Base VO，提供给添加、修改、详细的子 VO 使用
 */
@Data
public class BpmFormBaseDTO extends BaseDTO {

    /**
     * 表单名称
     */
    @NotNull(message = "表单名称不能为空")
    @Length(max = 60, message = "表单名称最多输入60个字符")
    private String name;

    /**
     * 表单状态
     */
    @NotNull(message = "表单状态不能为空")
    private Byte status;

    /**
     * 备注
     */
    private String remark;

}
