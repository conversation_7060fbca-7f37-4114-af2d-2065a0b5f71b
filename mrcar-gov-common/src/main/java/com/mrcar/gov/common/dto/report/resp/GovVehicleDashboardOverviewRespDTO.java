package com.mrcar.gov.common.dto.report.resp;

import lombok.Data;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/12/18 10:29
 */
@Data
public class GovVehicleDashboardOverviewRespDTO {

    /**
     * 车辆总数
     */
    private Integer vehicleTotalNum;
    /**
     * 行程单量
     */
    private BigDecimal orderCount;
    /**
     * 行程单量(万次)
     */
    private String orderCountTenThousand;

    /**
     * 行驶里程KM
     */
    private BigDecimal totalMillage;
    /**
     * 行驶里程(万KM)
     */
    private String totalMillageTenThousand;

    /**
     * 日均服务时长(小时)
     */
    private BigDecimal totalHour;

    /**
     * 日均服务时长（天）
     */
    private String totalDay;
    /**
     * 车均单量
     */
    private BigDecimal averageOrderCount;

    /**
     * 车均单量（万次）
     */
    private String averageOrderCountTenThousand;

    /**
     * 车均里程（KM）
     */
    private BigDecimal averageTotalMillage;

    /**
     * 车均里程（万KM）
     */
    private String averageTotalMillageTenThousand;

    /**
     * 单车日服务时长（小时）
     */
    private BigDecimal averageTotalHour;

    /**
     * 单车日服务时长（天）
     */
    private String averageTotalDay;

    /**
     * 总运行费用（元）
     */
    private BigDecimal totalFee;

    /**
     * 车均运行费用（元）
     */
    private BigDecimal averageTotalFee;

    /**
     * 总运行费用(万元)
     */
    private String totalFeeTenThousand;

    /**
     * 车均运行费用(万元)
     */
    private String averageTotalFeeTenThousand;

    public void setOrderCount(BigDecimal orderCount) {
        this.orderCount = orderCount;
        if(Objects.nonNull(orderCount)){
            if(orderCount.compareTo(new BigDecimal("10000"))>=0){
                this.orderCountTenThousand = orderCount.divide(new BigDecimal("10000"),2, RoundingMode.HALF_UP) + "万次";
            }else{
                this.orderCountTenThousand = orderCount + "次";
            }
        }
    }

    public void setTotalMillage(BigDecimal totalMillage) {
        this.totalMillage = totalMillage;
        if(Objects.nonNull(totalMillage)){
            if(totalMillage.compareTo(new BigDecimal("10000"))>=0){
                this.totalMillageTenThousand = totalMillage.divide(new BigDecimal("10000"),2, RoundingMode.HALF_UP) + "万km";
            }else{
                this.totalMillageTenThousand = totalMillage.setScale(2, RoundingMode.HALF_UP)  + "km";
            }
        }
    }


    public void setTotalHour(BigDecimal totalHour) {
        this.totalHour = totalHour;
        if(Objects.nonNull(totalHour)){
            if(totalHour.compareTo(new BigDecimal("240000")) >= 0){
                this.totalDay = totalHour.divide(new BigDecimal("240000"),2, RoundingMode.HALF_UP)+ "万天";
            }else if(totalHour.compareTo(new BigDecimal("24")) >= 0){
                this.totalDay = totalHour.divide(new BigDecimal("24"),2, RoundingMode.HALF_UP) + "天";
            }else{
                this.totalDay = totalHour.setScale(2, RoundingMode.HALF_UP)  + "小时";
            }
            
        }
    }

    public void setAverageOrderCount(BigDecimal averageOrderCount) {
        this.averageOrderCount = averageOrderCount;
        if(Objects.nonNull(averageOrderCount)){
            if(averageOrderCount.compareTo(new BigDecimal("10000"))>=0){
                this.averageOrderCountTenThousand = averageOrderCount.divide(new BigDecimal("10000"),2, RoundingMode.HALF_UP) + "万次";
            }else{
                this.averageOrderCountTenThousand = averageOrderCount.setScale(1, RoundingMode.HALF_UP)  + "次";
            }

        }
    }

    public void setAverageTotalMillage(BigDecimal averageTotalMillage) {
        this.averageTotalMillage = averageTotalMillage;
        if(Objects.nonNull(averageTotalMillage)){
            if(averageTotalMillage.compareTo(new BigDecimal("10000"))>=0){
                this.averageTotalMillageTenThousand = averageTotalMillage.divide(new BigDecimal("10000"),2, RoundingMode.HALF_UP) + "万km";
            }else{
                this.averageTotalMillageTenThousand = averageTotalMillage.setScale(2, RoundingMode.HALF_UP)  + "km";
            }
        }
    }

    public void setAverageTotalHour(BigDecimal averageTotalHour) {
        this.averageTotalHour = averageTotalHour;
        if(Objects.nonNull(averageTotalHour)){
            if(averageTotalHour.compareTo(new BigDecimal("240000")) >= 0){
                this.averageTotalDay = averageTotalHour.divide(new BigDecimal("240000"),2, RoundingMode.HALF_UP)+ "万天";
            }else if(averageTotalHour.compareTo(new BigDecimal("24")) >= 0){
                this.averageTotalDay = averageTotalHour.divide(new BigDecimal("24"),2, RoundingMode.HALF_UP) + "天";
            }else{
                this.averageTotalDay = averageTotalHour.setScale(2, RoundingMode.HALF_UP)  + "小时";
            }
        }
    }


    public void setTotalFee(BigDecimal totalFee) {
        this.totalFee = totalFee;
        if(Objects.nonNull(totalFee)){
            if(totalFee.compareTo(new BigDecimal("10000"))>=0){
                this.totalFeeTenThousand = totalFee.divide(new BigDecimal("10000"),2, RoundingMode.HALF_UP) + "万元";
            }else{
                this.totalFeeTenThousand = totalFee.setScale(2, RoundingMode.HALF_UP) + "元";
            }
        }
    }

    public void setAverageTotalFee(BigDecimal averageTotalFee) {
        this.averageTotalFee = averageTotalFee;
        if(Objects.nonNull(averageTotalFee)){
            if(averageTotalFee.compareTo(new BigDecimal("10000"))>=0){
                this.averageTotalFeeTenThousand = averageTotalFee.divide(new BigDecimal("10000"),2, RoundingMode.HALF_UP) + "万元";
            }else{
                this.averageTotalFeeTenThousand = averageTotalFee.setScale(2, RoundingMode.HALF_UP)  + "元";
            }
        }
    }

}
