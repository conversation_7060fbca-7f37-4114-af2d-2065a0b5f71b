package com.mrcar.gov.common.constant.asset;

/**
 * 收费类别枚举
 * <AUTHOR>
 * @date 2025/1/19 12:22
 */
public enum MaintainChargeCategoryEnum {

    //收费类别：1-标准工时计费(standard_labor)，2-自定义工时计费(custom_labor)，3-材料费(parts)
    STANDARD_LABOR(1, "标准工时"),
    CUSTOM_LABOR(2, "自定义工时"),
    PARTS(3, "配件费");

    private final Integer code;
    private final String name;

    MaintainChargeCategoryEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

   public static String getDescName(Integer code) {
        for (MaintainChargeCategoryEnum maintainChargeCategory : MaintainChargeCategoryEnum.values()) {
            if (maintainChargeCategory.getCode().equals(code)) {
                return maintainChargeCategory.getName();
            }
        }
        return "";
   }
}
