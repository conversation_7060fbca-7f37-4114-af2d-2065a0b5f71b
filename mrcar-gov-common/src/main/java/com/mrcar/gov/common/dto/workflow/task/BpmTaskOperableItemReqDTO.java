package com.mrcar.gov.common.dto.workflow.task;

import com.mrcar.gov.common.dto.workflow.BaseDTO;
import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 任务支持的操作项 Request VO
 */
@Data
public class BpmTaskOperableItemReqDTO extends BaseDTO {

    /**
     * 任务编号
     */
    @NotBlank(message = "任务编号不能为空")
    private String id;

    /**
     * 业务类型
     */
    @NotNull(message = "业务类型不能为空")
    private Byte businessType;
}
