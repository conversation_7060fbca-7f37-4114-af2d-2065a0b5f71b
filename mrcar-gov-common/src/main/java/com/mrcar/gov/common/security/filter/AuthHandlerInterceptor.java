package com.mrcar.gov.common.security.filter;

import com.izu.framework.exception.ApiException;
import com.izu.framework.resp.InfoCode;
import com.mrcar.gov.common.security.SecurityUtil;
import com.mrcar.gov.common.security.annotation.Anonymous;
import com.mrcar.gov.common.security.annotation.Auth;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashSet;

/**
 * 权限验证拦截器.
 * <AUTHOR>
 */

@Slf4j
@Component
public class AuthHandlerInterceptor implements HandlerInterceptor {

    @Value("${token.expire:5}")
    private Integer tokenExpire;

    private static final String AUTHORIZATION = "Authorization";

    private final AntPathMatcher pathMatcher = new AntPathMatcher();

    // 定义需要排除的路径
    private final HashSet<String> exclude = new HashSet<>();

    @PostConstruct
    public void init() {
        this.exclude.add("/captcha/**"); // 验证码
    }

    /**
     * 进入controller前拦截.
     * @param request
     * @param response
     * @param handler
     * @return 是否成功
     * @throws Exception
     */
    @Override
    public boolean preHandle(HttpServletRequest request,
                             HttpServletResponse response,
                             Object handler) throws Exception {

        if (handler instanceof HandlerMethod) {
            HandlerMethod hm = (HandlerMethod) handler;
            String token = request.getHeader(AUTHORIZATION);
            Anonymous ma = AnnotationUtils.findAnnotation(hm.getMethod(), Anonymous.class);
            Anonymous ca = AnnotationUtils.findAnnotation(hm.getBeanType(), Anonymous.class);
            if (ma != null || ca != null) {
                return true;
            }
            // 过滤路径
            String path = request.getRequestURI();
            if (this.exclude.stream().anyMatch(s -> pathMatcher.match(s, path))) {
                return true;
            }
            // 匿名登录
            Auth auth = AnnotationUtils.findAnnotation(hm.getMethod(), Auth.class);
            if (auth != null && !auth.login()) {
                return true;
            }
            auth = AnnotationUtils.findAnnotation(hm.getBeanType(), Auth.class);
            if (auth != null && !auth.login()) {
                return true;
            }
             //验证登录状态
            if (StringUtils.isBlank(token)) {
                throw new ApiException(InfoCode.HTTP_UNAUTHORIZED);
            }
            if (!SecurityUtil.verify(token)) {
                throw new ApiException(InfoCode.HTTP_UNAUTHORIZED);
            }

        }
        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request,
                           HttpServletResponse response,
                           Object handler,
                           ModelAndView modelAndView) throws Exception {
        // 清除session上下文
        SecurityUtil.clean();
    }
}
