package com.mrcar.gov.common.constant.business;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/12/30 13:53
 */
@AllArgsConstructor
@Getter
public enum GovMsgPushStatusEnum {
    // 失败
    FAIL(0, "失败"),
    // 成功
    SUCCESS(1, "成功"),
    // 推送中
    PUSHING(2, "推送中");

    private Integer code;
    private String desc;

    public static String getDescByCode(Integer pushStatus) {
        for (GovMsgPushStatusEnum statusEnum : GovMsgPushStatusEnum.values()) {
            if (Objects.equals( statusEnum.getCode(), pushStatus)) {
                return statusEnum.getDesc();
            }
        }
        return "";
    }
}
