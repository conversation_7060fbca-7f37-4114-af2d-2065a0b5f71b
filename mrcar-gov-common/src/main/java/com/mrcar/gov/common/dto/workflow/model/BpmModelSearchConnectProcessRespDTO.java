package com.mrcar.gov.common.dto.workflow.model;

import lombok.Data;

/**
 * 管理后台 - 流程模型的创建 关联流程下拉框返回参数
 * @description: 用于返回流程关联下拉框中的流程信息。
 * @date 2024/9/3 10:40
 */
@Data
public class BpmModelSearchConnectProcessRespDTO {

    /**
     * 流程id
     * 示例值: 34cf0ff9-7124-11ed-9ce3-3e703374bdb3
     */
    private String processId;

    /**
     * 流程名称
     * 示例值: 中铁建审批1
     */
    private String processName;
}
