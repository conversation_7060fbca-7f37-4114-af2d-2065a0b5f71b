package com.mrcar.gov.common.constant.asset;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;
import java.util.stream.Stream;

/**
 * Author:  wangM
 * Date:  2025/1/17 15:59
 */
@Getter
@AllArgsConstructor
public enum AccidentFileEnum {
    ACCIDENT_IDENT_CERT(1, "事故认定书"),
    ACCIDENT_PICTURE(2, "事故图片"),;

    private final Integer code;
    private final String desc;

    public static String getFileDesc(Integer code) {
        return Stream.of(values())
                .filter(e -> Objects.equals(e.getCode(), code))
                .map(AccidentFileEnum::getDesc).findFirst().orElse("");
    }
}
