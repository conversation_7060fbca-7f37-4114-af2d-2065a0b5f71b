package com.mrcar.gov.common.util.pdf.model;

import com.mrcar.gov.common.util.pdf.layout.MetricValue;
import com.mrcar.gov.common.util.pdf.layout.Point;
import com.mrcar.gov.common.util.pdf.layout.RoundMetric;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.vandeseer.easytable.structure.cell.AbstractCell;

import java.io.IOException;

public abstract class TableCellElement extends AbstractSingleElement {

    protected TableCellElement(BlockElement parent) {
        super(parent, MetricValue.adaptive(), MetricValue.adaptive(), RoundMetric.empty());
    }

    @Override
    protected MetricValue doRefreshAdaptiveWidthMetric() {
        return MetricValue.adaptive();
    }

    @Override
    protected MetricValue doRefreshAdaptiveHeightMetric() {
        return MetricValue.adaptive();
    }

    @Override
    public void render(Point point, PDPageContentStream stream) throws IOException { }

    @Override
    public abstract String name();

    protected abstract AbstractCell buildCell();

}
