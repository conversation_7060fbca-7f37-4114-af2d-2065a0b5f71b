package com.mrcar.gov.common.service.api;

import com.izu.framework.resp.RestResp;
import com.mrcar.gov.common.constant.business.GovMsgSceneEnum;
import com.mrcar.gov.common.dto.msg.req.GovMessageTrackingSenderReqDTO;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 埋点消息发送接口
 *
 * <AUTHOR> on 2025/1/1 15:41
 */
public interface MessageTrackingSender {

    /**
     * 发送埋点消息
     *
     * <p>此方法用于异步发送埋点消息。调用此方法时，需指定消息的场景（通过{@link GovMsgSceneEnum}枚举指定）。</p>
     * <p>方法的返回值仅用于参数校验，并不代表消息已成功推送。调用成功后，将返回一个批次编码。</p>
     *
     * @param pushParam 包含消息发送所需参数的封装对象
     * @return 包含批次编码的响应对象（仅用于参数校验结果）
     */
    RestResp<String> sendMessageTracking(MessageTrackingParam pushParam);

    void sendMessageTrackingLog(List<GovMessageTrackingSenderReqDTO> collect);

    /**
     * 埋点消息参数封装类
     */
    @Data
    public static class MessageTrackingParam {

        /**
         * 指定消息场景
         *
         * <p>必传参数。通过此字段指定消息的场景，使用{@link GovMsgSceneEnum}枚举类。</p>
         */
        private GovMsgSceneEnum scene;

        /**
         * 场景中的扩展参数
         *
         * <p>可选参数。此参数用于替换消息模板中的占位符（如${key}）。</p>
         * <p>如果模板中的占位符数量和提供的扩展参数数量不一致，则消息发送将失败。</p>
         */
        private Map<String, String> extendParamMap;

        /**
         * 消息接收人编码集合
         *
         * <p>必传参数。包含消息接收人的编码集合，这些编码对应于员工表中的用户编码。</p>
         */
        private Set<String> receiverCodeSet;

        /**
         * 推送批次号
         *
         * <p>可选参数。用于同一消息的多批次推送场景。</p>
         * <p>首次推送后，系统将返回此批次号。后续推送时，需带上此参数以确保推送的唯一性。</p>
         * <p>这个参数第一期可暂不实现</p>
         */
        private String pushBatchNo;

        private Integer companyId;

        private Set<String> mobile;
    }
}
