package com.mrcar.gov.common.constant.order;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum GovPublicCarCommandSourceEnum {

    SOCIAL_VEHICLE(0, "社会化租赁");

    private final int code;
    private final String description;

    public static String getByCode(int code) {
        for (GovPublicCarCommandSourceEnum type : values()) {
            if (type.code == code) {
                return type.getDescription();
            }
        }
        return "";
    }
}