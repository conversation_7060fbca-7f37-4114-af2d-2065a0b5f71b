package com.mrcar.gov.common.dto.report.resp;

import com.mrcar.gov.common.dto.bi.resp.AssetDistributionRespDTO;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/12/18 10:41
 */
@Data
public class VehicleFuelTypeAssetDistributionDTO extends VehicleAssetDistributionDTO{

    /**
     * 新能源比例
     */
    private String energyPercentage;
    /**
     * 油车比例
     */
    private String oilPercentage;

    public static VehicleFuelTypeAssetDistributionDTO fromAssetDistributionRespDTO(AssetDistributionRespDTO dto){
        VehicleFuelTypeAssetDistributionDTO assetDistributionDTO = new VehicleFuelTypeAssetDistributionDTO();
//        assetDistributionDTO.setAssetType(dto.getAssetType());
        assetDistributionDTO.setAssetTypeStr(dto.getAssetTypeStr());
        assetDistributionDTO.setTotalCount(dto.getTotal());
        assetDistributionDTO.setEnergyPercentage(dto.getEnergyPercentage());
        assetDistributionDTO.setOilPercentage(dto.getOilPercentage());
        return assetDistributionDTO;
    }


}
