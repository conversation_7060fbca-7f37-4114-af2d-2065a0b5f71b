package com.mrcar.gov.common.util.pdf.model;

import com.mrcar.gov.common.util.pdf.layout.DisplayImage;
import org.vandeseer.easytable.structure.cell.AbstractCell;
import org.vandeseer.easytable.structure.cell.ImageCell;

public class TableImageCellElement extends TableCellElement {

    private static final String NODE_NAME = "Table-Cell-Image";

    private final DisplayImage image;

    protected TableImageCellElement(BlockElement parent,
                                    DisplayImage image) {
        super(parent);
        this.image = image;
    }

    public static TableImageCellElement create(BlockElement parent,
                                               DisplayImage image) {
        return new TableImageCellElement(parent, image);
    }

    @Override
    public String name() {
        return NODE_NAME;
    }

    @Override
    protected AbstractCell buildCell() {
        return ImageCell.builder()
                .image(this.image.image())
                .build();
    }
}
