package com.mrcar.gov.common.constant.order;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum GovPublicCarCommandTypeEnum {

    UNLOCK(0, "开锁"),
    LOCK(1, "关锁"),
    CUT_OFF_POWER(2, "断油断电"),
    RESTORE_POWER(3, "供油供电"),
    DOUBLE_HORN(4, "双闪鸣笛");

    private final int code;
    private final String description;

    public static String getByCode(int code) {
        for (GovPublicCarCommandTypeEnum type : values()) {
            if (type.code == code) {
                return type.description;
            }
        }
        return "";
    }
}