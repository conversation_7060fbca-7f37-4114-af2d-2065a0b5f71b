package com.mrcar.gov.common.constant.user;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.data.mongodb.core.aggregation.ArrayOperators;

import java.util.Objects;

/**
 * 单位性质枚举类
 * <AUTHOR>
 * @date 2024/11/7 15:01
 */
@AllArgsConstructor
@Getter
public enum GovAttributeTypeEnum {

    PARTY_GOVERNMENT_ORGANS(1, "党政机关"),

    PUBLIC_INSTITUTIONS(2, "参公事业单位"),

    INSTITUTIONS(3, "事业单位"),

    government_INSTITUTIONS(4, "团体组织");


    private final Integer code;

    private final String desc;

    // 根据 code 获取枚举实例
    public static GovAttributeTypeEnum getByCode(int code) {
        for (GovAttributeTypeEnum status : values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        return null;
    }

    // 根据 code 获取文本描述
    public static String getDesByCode(Integer code) {
        for (GovAttributeTypeEnum status : values()) {
            if (Objects.equals(status.getCode(), code)) {
                return status.getDesc();
            }
        }
       return "";
    }
}
