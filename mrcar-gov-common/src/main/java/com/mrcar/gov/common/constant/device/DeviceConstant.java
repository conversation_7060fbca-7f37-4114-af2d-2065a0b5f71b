package com.mrcar.gov.common.constant.device;

public interface DeviceConstant {
	
	
	/**
	 * mongo数据库集合
	 * 轨迹历史
	 */
	String MONGO_DB_GOV_VEHICLE_LOCATION="gov_vehicle_location";
	
	
	/**
	 * mongo数据库集合
	 * 状态历史
	 */
	String MONGO_DB_GOV_VEHICLE_STATUS="gov_vehicle_status";
	

    /**
     * 设备最后一个点位
     */
    String REDIS_KEY_VEHICLE_LATEST_POSITION = "mrcar:device:latest:position:%s";

    /**
     * 设备最后一个状态
     */
    String REDIS_KEY_VEHICLE_LATEST_STATUS = "mrcar:device:latest:status:%s";

    /**
     * GPS点位Topic
     */
    String MQ_TOPIC_GPS_POSITION = "mrcar-iot-gps-location";

    /**
     * Redis Null值
     */
    String REDIS_NULL_VALUE = "NaN";

}
