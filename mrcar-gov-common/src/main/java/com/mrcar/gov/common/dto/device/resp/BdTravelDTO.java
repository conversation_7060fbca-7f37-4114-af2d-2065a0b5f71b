package com.mrcar.gov.common.dto.device.resp;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.mrcar.gov.common.dto.iot.TravelDTO;

/**
 * @ClassName BdTravelDTO
 * @Description TODO
 * <AUTHOR>
 * @Date 2019/11/21 19:36
 * @Version 1.0
 */
public class BdTravelDTO extends TravelDTO{

    //起点经度
    private BigDecimal startLng;
    //起点纬度
    private BigDecimal startLat;
    //起点地址
    private String startAddRess;
    //起点时间
    private Date    startCreateDate;

    //终点经度
    private BigDecimal endtLng;
    //纬度终点
    private BigDecimal endLat;
    //终点地址
    private String endAddRess;
    //终点时间
    private Date    endCreateDate;

    //距离 单位(公里)
    private BigDecimal distance;

    //行程坐标对集合
    private List<BdCarLocationsDetailDTO> pointList;

    public BigDecimal getStartLng() {
        return startLng;
    }

    public void setStartLng(BigDecimal startLng) {
        this.startLng = startLng;
    }

    public BigDecimal getStartLat() {
        return startLat;
    }

    public void setStartLat(BigDecimal startLat) {
        this.startLat = startLat;
    }

    public String getStartAddRess() {
        return startAddRess;
    }

    public void setStartAddRess(String startAddRess) {
        this.startAddRess = startAddRess;
    }

    public BigDecimal getEndtLng() {
        return endtLng;
    }

    public void setEndtLng(BigDecimal endtLng) {
        this.endtLng = endtLng;
    }

    public BigDecimal getEndLat() {
        return endLat;
    }

    public void setEndLat(BigDecimal endLat) {
        this.endLat = endLat;
    }

    public String getEndAddRess() {
        return endAddRess;
    }

    public void setEndAddRess(String endAddRess) {
        this.endAddRess = endAddRess;
    }

    public BigDecimal getDistance() {
        return distance;
    }

    public void setDistance(BigDecimal distance) {
        this.distance = distance;
    }

    public List<BdCarLocationsDetailDTO> getPointList() {
        return pointList;
    }

    public void setPointList(List<BdCarLocationsDetailDTO> pointList) {
        this.pointList = pointList;
    }


    public Date getStartCreateDate() {
        return startCreateDate;
    }

    public void setStartCreateDate(Date startCreateDate) {
        this.startCreateDate = startCreateDate;
    }


    public Date getEndCreateDate() {
        return endCreateDate;
    }

    public void setEndCreateDate(Date endCreateDate) {
        this.endCreateDate = endCreateDate;
    }


}
