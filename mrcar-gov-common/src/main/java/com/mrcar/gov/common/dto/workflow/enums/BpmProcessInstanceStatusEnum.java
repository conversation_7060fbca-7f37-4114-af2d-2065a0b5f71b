package com.mrcar.gov.common.dto.workflow.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 流程实例的状态
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum BpmProcessInstanceStatusEnum {

    RUNNING((byte)1, "进行中"),
    FINISH((byte)2, "已完成");

    /**
     * 状态
     */
    private final Byte status;
    /**
     * 描述
     */
    private final String desc;

    public static BpmProcessInstanceStatusEnum getEnum(byte type){
        for(BpmProcessInstanceStatusEnum b : BpmProcessInstanceStatusEnum.values()){
            if(b.getStatus().equals(type)){
                return b;
            }
        }
        return BpmProcessInstanceStatusEnum.RUNNING;
    }

}
