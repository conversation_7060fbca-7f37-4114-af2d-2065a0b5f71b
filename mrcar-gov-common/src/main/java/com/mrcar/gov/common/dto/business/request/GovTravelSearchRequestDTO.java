package com.mrcar.gov.common.dto.business.request;

import com.mrcar.gov.common.dto.PageParamDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class GovTravelSearchRequestDTO extends PageParamDTO implements Serializable {

    /**
     * 车牌号
     */
    private String vehicleLicense;

    /**
     * 车架号
     */
    private String vehicleVin;

    /**
     * 车辆编码
     */
    private String vehicleSerialNo;

    /**
     * 起点开始时间
     */
    private String startPointStartDate;

    /**
     * 起点结束时间
     */
    private String startPointEndDate;

    /**
     * 终点开始时间
     */
    private String endPointStartDate;

    /**
     * 终点结束时间
     */
    private String endPointEndDate;

    /**
     * SIM卡号
     */
    private String simNo;

    /**
     * 设备类型
     */
    private Integer deviceType;

    /**
     * 行程编号
     */
    private String travelSerialNo;

    /**
     * 车辆编号集合
     */
    private List<String> vehicleSerialNoList;

    /**
     * 车架号集合集合
     */
    private List<String> vehicleVinList;

    /**
     * 车辆所有人(分时租赁时存分时租赁使用单位)
     */
    private String vehicleBelongDeptCode;

    /**
     * 车辆使用单位
     */
    private String vehicleUseDeptCode;

    /**
     * 车辆使用部门
     */
    private String vehicleUseStructCode;

    /**
     * 管车类型；1-机关事务管理局；2-财政部门
     */
    private Integer manageCarType;

    /**
     * 车辆管理单位
     */
    private String vehicleManageDeptCode;

    /**
     * 邮箱
     */
    private String email;

}
