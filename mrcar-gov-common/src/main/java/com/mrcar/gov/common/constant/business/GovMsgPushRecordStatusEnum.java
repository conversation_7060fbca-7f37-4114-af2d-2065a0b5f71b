package com.mrcar.gov.common.constant.business;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/12/30 13:41
 */
@AllArgsConstructor
@Getter
public enum GovMsgPushRecordStatusEnum {
    // 删除
    DELETE(0, "删除"),
    // 正常
    NORMAL(1, "正常");

    private final Integer code;

    private final String desc;

    public static String getDescByCode(Integer recordStatus) {
        for (GovMsgPushRecordStatusEnum recordStatusEnum : GovMsgPushRecordStatusEnum.values()) {
            if (Objects.equals( recordStatusEnum.getCode(), recordStatus)) {
                return recordStatusEnum.getDesc();
            }
        }
        return "";
    }
}
