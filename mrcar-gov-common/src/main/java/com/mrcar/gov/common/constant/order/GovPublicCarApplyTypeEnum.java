package com.mrcar.gov.common.constant.order;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum GovPublicCarApplyTypeEnum {

    // 申请单类型
    PUBLIC_CAR(1, "公务用车"),
    SOCIAL_RENT(2, "社会租赁"),
    ;

    private final Integer code;
    private final String name;

    public static String getName(Integer code) {
        return Arrays.stream(values()).filter(e -> e.getCode().equals(code)).findFirst().map(GovPublicCarApplyTypeEnum::getName).orElse(null);
    }
}