package com.mrcar.gov.common.dto.workflow.group;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import javax.validation.constraints.NotNull;

/**
 * 管理后台 - 用户组更新 Request VO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BpmUserGroupUpdateReqDTO extends BpmUserGroupBaseDTO {

    /**
     * 编号
     * 示例值: 1024
     */
    @NotNull(message = "编号不能为空")
    private Long id;

}
