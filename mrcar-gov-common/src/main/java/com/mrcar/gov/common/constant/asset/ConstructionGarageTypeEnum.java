package com.mrcar.gov.common.constant.asset;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum ConstructionGarageTypeEnum {

    // 维修机构类型；1-定点维修机构；2-外部维修机构
    FIXED_POINT(1, "定点维修机构"), // 定点维修机构
    EXTERNAL(2, "外部维修机构"),  // 外部维修机构
    ;

    private final Integer code;
    private final String name;

    public static String getName(Integer code) {
        return Arrays.stream(values()).filter(e -> e.getCode().equals(code)).findFirst().map(ConstructionGarageTypeEnum::getName).orElse(null);
    }
}