package com.mrcar.gov.common.dto.config;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class GovCompanyConfigItemValueDTO{
    /**
     * 值配置项code
     */
    private String itemCode;

    /**
     * 值名称
     */
    private String itemName;

    /**
     * 属部门ID
     */
    private Integer structId;

    /**
     * 属部门ID
     */
    private String structCode;

    /**
     * 值配置项code
     * use_car_apply_driver_type,用车申请驾驶员类型
     * when_use_car_dispatch_way,用车时的调度方式
     * rental_way_item,租赁方式
     * day_driver_type,驾驶员类型
     * day_dispatch_way,调度方式
     * day_use_car_max_days,最大用车天数
     * hour_driver_type,驾驶员类型
     * hour_dispatch_way,调度方式
     * hour_use_car_max_days,最大用车天数
     * return_fence_check,还车围栏校验
     * use_car_duration_filed,用车计费时长统计字段
     */
    private String businessConfigCode;

    /**
     * 类型 1:开关, 2:下拉框, 3:文本框, 4:其他
     */
    private Integer type;

    /**
     * 备注，用于值的内容展示
     */
    private String remark;

    /**
     * 是否必填 0:非必填 1:必填
     */
    private Integer isRequired;

    /**
     * 单选/多选 适用于下拉框 0:单选  1:多选
     */
    private Integer selectType;

    /**
     * 默认值，仅适用于新增的标准类型，NULL表示没有默认值
     */
    private String defaultValue;

    /**
     * 值类型 1:字符串, 2:数组, 3: jsonObject
     */
    private Integer dataType;

    /**
     * 数字最大值，适用于文本框且是数字格式
     */
    private BigDecimal maxValue;

    /**
     * 数字最小值，适用于文本框且是数字格式
     */
    private BigDecimal minValue;

    /**
     * 所有可能的值，JSON格式：开关/单选：[{"value":"0","label":"否"},{"value":"1","label":"是"}], 下拉框：[{"value":"","label":""},{"value":"","label":""}], 文本框:"", 其他：自定义类型JSON
     */
    private Object possibleValues;

    /**
     * 选择的值
     */
    private String configValue;

    /**
     * 业务线 单选/多选 0:单选  1:多选
     */
    private Integer businessSelectType;
}



