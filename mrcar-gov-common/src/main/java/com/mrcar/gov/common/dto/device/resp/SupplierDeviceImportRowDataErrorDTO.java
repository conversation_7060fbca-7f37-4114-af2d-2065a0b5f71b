package com.mrcar.gov.common.dto.device.resp;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

@Data
public class SupplierDeviceImportRowDataErrorDTO {
    /**
     * 序号
     */
    @ExcelIgnore
    private Integer rowDataIndex;
    @ExcelProperty({"设备编号"})
    @ColumnWidth(20)
    private String deviceNo;

    @ExcelProperty({"SIM卡号"})
    @ColumnWidth(20)
    private String simNo;

    @ExcelProperty({"厂商名称"})
    @ColumnWidth(20)
    private String manufactName;

    @ExcelProperty({"型号名称"})
    @ColumnWidth(20)
    private String modelName;

    @ExcelProperty({"车架号"})
    @ColumnWidth(20)
    private String vehicleVin;

    @ExcelProperty({"供应商"})
    @ColumnWidth(20)
    private String supplierName;

    @ExcelProperty({"失败原因"})
    @ColumnWidth(20)
    private String errorMsg;



}
