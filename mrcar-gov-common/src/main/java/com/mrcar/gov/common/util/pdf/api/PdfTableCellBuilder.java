package com.mrcar.gov.common.util.pdf.api;

import java.io.File;

public class PdfTableCellBuilder {

    public enum CellType {
        TEXT,
        IMAGE
    }

    CellType type;

    String text;
    File image;

    private PdfTableCellBuilder() { }

    private PdfTableCellBuilder(String text) {
        this.type = CellType.TEXT;
        this.text = text;
    }

    private PdfTableCellBuilder(File source) {
        this.type = CellType.IMAGE;
        this.image = source;
    }

    public static PdfTableCellBuilder text(String value) {
        return new PdfTableCellBuilder(value);
    }

    public static PdfTableCellBuilder image(File file) {
        return new PdfTableCellBuilder(file);
    }

}
