package com.mrcar.gov.common.dto.workflow.process;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import java.util.Date;

/**
 * 管理后台 - 流程定义的分页的每一项响应数据传输对象
 * 提供了流程定义的表单名字和部署时间等信息。
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BpmProcessDefinitionPageItemRespDTO extends BpmProcessDefinitionRespDTO {

    /**
     * 表单名字
     * 示例值: 请假表单
     */
    private String formName;

    /**
     * 部署时间
     */
    private Date deploymentTime;

}
