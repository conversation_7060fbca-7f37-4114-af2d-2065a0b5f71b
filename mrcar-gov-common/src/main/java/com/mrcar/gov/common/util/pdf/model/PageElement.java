package com.mrcar.gov.common.util.pdf.model;

import com.mrcar.gov.common.util.pdf.layout.RoundMetric;
import org.apache.pdfbox.pdmodel.common.PDRectangle;

import java.util.Collection;

public interface PageElement extends IElement {

    /**
     * 页面大小
     */
    PDRectangle pageSize();

    RoundMetric padding();

    /**
     * 行元素
     */
    Collection<RowElement> rows();

    void append(RowElement child);

    static PageElement create(PDRectangle rectangle) {
        return new DefaultPageElement(rectangle);
    }

    static PageElement create(PDRectangle rectangle,
                              RoundMetric padding) {
        return new DefaultPageElement(rectangle, padding);
    }

}
