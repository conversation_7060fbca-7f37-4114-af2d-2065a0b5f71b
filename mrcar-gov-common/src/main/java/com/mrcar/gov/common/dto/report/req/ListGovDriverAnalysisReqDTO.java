package com.mrcar.gov.common.dto.report.req;

import com.google.gson.annotations.Expose;
import com.mrcar.gov.common.dto.BaseDTO;
import com.mrcar.gov.common.dto.PageParamDTO;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import javax.validation.constraints.NotBlank;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/12/16 10:01
 */
@Data
public class ListGovDriverAnalysisReqDTO extends PageParamDTO {

    /**
     * 查询开始日期
     */
    private Date startDate ;// = LocalDate.of(2024, 11, 27);

    /**
     * 查询结束日期
     */
    private Date endDate ;//LocalDate.of(2024, 11, 28);

    /**
     * 单位性质
     */
    private Integer structAttributeType;


    /**
     * 编制类型 1: 聘用;2：编外
     */
    private Integer preparationType;

    /**
     * 驾驶员姓名
     */
    private String driverUserName;
    /**
     * 左侧树选中部门编码
     */
    private String selectStructCode;

    @Expose(serialize = false)
    private List<String> deptCodeList;


    public void setStartDate(Date startDate) {
        if(Objects.nonNull(startDate)){
            this.startDate = startDate;
        }
    }

    public void setEndDate(Date endDate) {
        if(Objects.nonNull(endDate)){
            this.endDate = endDate;
        }
    }

    public void setStructAttributeType(Integer structAttributeType) {
        if(Objects.nonNull(structAttributeType)){
            this.structAttributeType = structAttributeType;
        }

    }

    public void setPreparationType(Integer preparationType) {
        if(Objects.nonNull(preparationType)){
            this.preparationType = preparationType;
        }
    }

    public void setDriverUserName(String driverUserName) {
        if(StringUtils.isNotBlank(driverUserName)){
            this.driverUserName = driverUserName;
        }
    }
}
