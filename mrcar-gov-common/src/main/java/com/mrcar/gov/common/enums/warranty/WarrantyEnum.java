package com.mrcar.gov.common.enums.warranty;

import org.apache.commons.lang3.math.NumberUtils;

/**
 * 保单枚举
 */
public class WarrantyEnum {


    public enum WarrantyTypeEnum {
        TRAFFIC_ACCIDENT(  1, "交强险"),
        COMMERCIAL_INSURANCE(  2, "商业险"),
        NON_AUTO_INSURANCE(  3, "非车险"),

        ;

        private final Integer code;

        private final String name;

        WarrantyTypeEnum(Integer code, String name) {
            this.code = code;
            this.name = name;
        }

        public Integer getCode() {
            return code;
        }

        public String getName() {
            return name;
        }


        public static String getNameByCode(Integer code) {
            for (WarrantyTypeEnum e : WarrantyTypeEnum.values()) {
                if (e.getCode().equals(code)) {
                    return e.getName();
                }
            }
            return "";
        }

        public static Integer getCodeByName(String name) {
            for (WarrantyTypeEnum e : WarrantyTypeEnum.values()) {
                if (e.getName().equals(name)) {
                    return e.getCode();
                }
            }
            return NumberUtils.INTEGER_MINUS_ONE;
        }
    }

    public enum WarrantyStateEnum {
        UNINSURED(  1, "未起保"),
        IN_SECURITY(  2, "保障中"),
        EXPIRED(  3, "已过期"),

        ;

        private final Integer code;

        private final String name;

        WarrantyStateEnum(Integer code, String name) {
            this.code = code;
            this.name = name;
        }

        public Integer getCode() {
            return code;
        }

        public String getName() {
            return name;
        }


        public static String getNameByCode(Integer code) {
            for (WarrantyStateEnum e : WarrantyStateEnum.values()) {
                if (e.getCode().equals(code)) {
                    return e.getName();
                }
            }
            return "";
        }
    }
}
