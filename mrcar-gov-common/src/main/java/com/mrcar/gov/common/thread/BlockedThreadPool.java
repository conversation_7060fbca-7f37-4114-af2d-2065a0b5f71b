package com.mrcar.gov.common.thread;

import java.util.concurrent.TimeUnit;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.Semaphore;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 阻塞线程池.
 *
 * <AUTHOR>
 */
public final class BlockedThreadPool extends ThreadPoolExecutor {

    /**
     * 信号量.
     */
    private Semaphore semaphore;

    /**
     * 计数器.
     */
    private static final AtomicInteger COUNTER = new AtomicInteger(0);

    private static final String NAME = "btp-";

    /**
     * 构造函数.
     * @param poolSize 线程池大小
     */
    private BlockedThreadPool(int poolSize) {
        this(poolSize, poolSize * 10, NAME);
    }


    /**
     * 构造函数.
     * @param poolSize 线程池大小
     * @param queueSize 队列大小
     * @param name 线程池名称
     */
    private BlockedThreadPool(int poolSize, int queueSize, String name) {
        super(poolSize, poolSize, 60L, TimeUnit.SECONDS, new LinkedBlockingQueue<Runnable>(), r -> {
            Thread thread = new Thread(r);
            thread.setName(name + COUNTER.getAndIncrement());
            return thread;
        });
        semaphore = new Semaphore(poolSize + queueSize);
    }

    /**
     * 创建阻塞线程池.
     * @param poolSize 线程池大小
     * @return 阻塞线程池
     */
    public static BlockedThreadPool newBlockedThreadPool(int poolSize) {
        return new BlockedThreadPool(poolSize);
    }

    /**
     * 创建阻塞线程池.
     * @param poolSize 线程池大小
     * @param queueSize 队列大小
     * @return 阻塞线程池
     */
    public static BlockedThreadPool newBlockedThreadPool(int poolSize, int queueSize) {
        return new BlockedThreadPool(poolSize, queueSize, NAME);
    }
    /**
     * 创建阻塞线程池.
     * @param poolSize 线程池大小
     * @param queueSize 队列大小
     * @param name 线程池名称
     * @return 阻塞线程池
     */
    public static BlockedThreadPool newBlockedThreadPool(int poolSize, int queueSize, String name) {
        return new BlockedThreadPool(poolSize, queueSize, name);
    }

    /**
     * 线程执行前先获取信号量.
     * @param runnable 线程
     */
    @Override
    public void execute(Runnable runnable) {
        try {
            semaphore.acquire();
            super.execute(runnable);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 线程执行完毕后释放信号量.
     * @param r 线程
     * @param t 异常
     */
    protected void afterExecute(Runnable r, Throwable t) {
        super.afterExecute(r, t);
        semaphore.release();
    }
}
