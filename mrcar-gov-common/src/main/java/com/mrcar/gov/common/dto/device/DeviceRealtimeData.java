package com.mrcar.gov.common.dto.device;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 设备实时数据.
 */
@Data
public class DeviceRealtimeData implements Serializable {

    /**
     * 上报时间
     */
    private Date timestamp;

    /**
     * 设备唯一编号
     */
    private String deviceSysNo;

    /**
     * 设备编号
     */
    private String deviceNo;

    /**
     * SIM卡号
     */
    private String simNo;

    /**
     * 设备类型；1：有线；2：无线；3：OBD;4:车机；6：视频
     */
    private Integer deviceType;

    /**
     * 车辆编码
     */
    private String vehicleNo;

    /**
     * 车架号
     */
    private String vehicleVin;

    /**
     * 车牌号
     */
    private String vehicleLicense;

    /**
     * 所属公司id
     */
    private Integer companyId;

    /**
     * 经度 高德
     */
    private BigDecimal longitude;

    /**
     * 纬度 高德
     */
    private BigDecimal latitude;

    /**
     * 经度 百度
     */
    private BigDecimal lngBaidu;

    /**
     * 纬度 百度
     */
    private BigDecimal latBaidu;

    /**
     * 方向角  0-359，正北为 0，顺时针
     */
    private Integer direction;

    /**
     * 速度  1/10km/h
     */
    private Integer speed;

    /**
     * 海拔高度  单位为米（m）
     */
    private Integer elevation;

    /**
     * GPS解算时所⽤到的信号最强的四颗卫星的接收载噪⽐的平均值，精确到1Db 取值范围：0~50Db
     */
    private Integer snr;

}
