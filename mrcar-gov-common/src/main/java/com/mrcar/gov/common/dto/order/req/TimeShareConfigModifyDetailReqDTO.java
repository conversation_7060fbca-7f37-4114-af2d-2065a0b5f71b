package com.mrcar.gov.common.dto.order.req;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.mrcar.gov.common.dto.BaseDTO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Author: mengshuang
 * @Date: 2025/4/10 16:19
 * @Param: 保存 修改接口
 * @Return:
 * @Description:
 **/
@Data
public class TimeShareConfigModifyDetailReqDTO {
    /**
     * 对应配置主表主键id
     */
    private Integer configId;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 驾驶类型：1-自驾，2-驾驶员
     */
    private Integer drivingType;

    /**
     * 里程费(元/公里)
     */
    private BigDecimal mileageRent;

    /**
     * 时长费(元/分钟)
     */
    private BigDecimal durationRent;


}
