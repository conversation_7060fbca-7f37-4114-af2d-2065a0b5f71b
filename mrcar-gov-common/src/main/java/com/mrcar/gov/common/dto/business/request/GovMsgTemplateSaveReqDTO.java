package com.mrcar.gov.common.dto.business.request;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;

/**
 * 消息模板表
 */
@Data
public class GovMsgTemplateSaveReqDTO {




    /**
     * 模板名称
     * <p>
     * 字段不能为空，最大字符限制50
     * </p>
     */
    @NotNull(message = "模板名称不能为空")
    @Length(max = 50, message = "模板名称不能超过50个字符")
    private String templateName;

    /**
     * 模板内容
     */
    @NotNull(message = "模板内容不能为空")
    @Length(max = 200, message = "模板内容不能超过200个字符")
    private String templateContent;

    /**
     * 1公文公告;2系统消息;3用车通知;4审批通知;5监控报警;6车务消息;7维保通知;
     */
    @NotNull(message = "模板模块不能为空")
    private Integer msgModule;

    /**
     * 模板状态 0:停用 1:启用
     */
    @NotNull(message = "模板状态不能为空")
    private Integer templateStatus;

    /**
     * 推送方式列表，多个使用英文逗号分割 1短信 2站内信
     */
    @NotNull(message = "推送方式列表不能为空")
    @Length(max = 20, message = "推送方式列表不能超过20个字符")
    private String pushTypeList;

    @NotNull(message = "业务场景不能为空")
    private Integer msgScene;


    //=====================================登录态获取的字段-开始===========================================================
    /**
     * 消息模版所属企业
     * <p>
     * 登录态获取-前端无需传入
     * </p>
     */
    private Integer companyId;

    /**
     * 创建人编码
     * <p>
     * 登录态获取-前端无需传入
     * </p>
     */
    private String createCode;

    /**
     * 创建人名称
     * <p>
     * 登录态获取-前端无需传入
     * </p>
     */
    private String createName;

    /**
     * 更新人编码
     * <p>
     * 登录态获取-前端无需传入
     * </p>
     */
    private String updateCode;

    /**
     * 更新人名称
     * <p>
     * 登录态获取-前端无需传入
     * </p>
     */
    private String updateName;

    //=====================================登录态获取的字段-结束===========================================================


}