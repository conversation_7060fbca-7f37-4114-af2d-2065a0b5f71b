package com.mrcar.gov.common.constant.asset;

import lombok.Getter;

/**
 * 车辆处置状态枚举
 * <AUTHOR>
 * @date 2024/11/21 11:25
 */
@Getter
public enum DisposalStatusEnum {
    //1.待办理、2.待确认、3.已办理、4.取消办理、5待修改 6 审批中
    DISPOSAL_1(1, "待办理"),
    //DISPOSAL_2(2, "待确认"),
    DISPOSAL_3(3, "已办理"),
    DISPOSAL_4(4, "取消办理"),
    DISPOSAL_5(5, "待修改"),
    DISPOSAL_6(6, "审批中"),
    ;
    private Integer code;
    private String desc;

    DisposalStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDescByCode(Integer code) {
        for (DisposalStatusEnum disposalStatusEnum : DisposalStatusEnum.values()) {
            if (disposalStatusEnum.getCode().equals(code)) {
                return disposalStatusEnum.getDesc();
            }
        }
        return "";
    }

}
