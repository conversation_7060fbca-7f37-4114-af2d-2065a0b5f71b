package com.mrcar.gov.common.dto.asset.maintenance.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.mrcar.gov.common.constant.asset.AccidentIsCloseEnum;
import com.mrcar.gov.common.constant.asset.AccidentResponsibilityEnum;
import com.mrcar.gov.common.constant.asset.AccidentSeverityEnum;
import com.mrcar.gov.common.constant.asset.ConstructionGarageTypeEnum;
import com.mrcar.gov.common.constant.user.ManageCarTypeEnum;
import com.mrcar.gov.common.dto.asset.maintenance.req.VehicleAccidentFileDTO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * Author:  wangM
 * Date:  2025/1/13 18:06
 */
@Data
public class VehicleAccidentDetailResDTO {
    /**
     * 主键ID
     */
    private Long id;
    /**
     * 事故单号
     */
    private String accidentNo;
    /**
     * 车牌号
     */
    private String vehicleLicense;
    /**
     * 管车类型；1-机关事务部门；2-财政部门（快照信息）
     */
    private Integer manageCarType;
    /**
     * 管车类型描述
     */
    private String manageCarTypeStr;
    /**
     * 维保服务机构编码（维修厂）定点维修机构时存在
     */
    private String constructionGarageNo;
    /**
     * 维保服务机构名称
     */
    private String constructionGarageName;
    /**
     * 维保服务机构电话
     */
    private String constructionGaragePhone;

    /**
     * 维保服务机构地址
     */
    private String constructionGarageAddress;
    /**
     * 保险公司机构编码
     */
    private String insuranceGarageNo;
    /**
     * 保险公司机构名称
     */
    private String insuranceGarageName;
    /**
     * 事故责任 1 全责 2 主责 3 同责 4 次责 5 无责
     */
    private Integer accidentResponsibility;
    /**
     * 事故责任 1 全责 2 主责 3 同责 4 次责 5 无责
     */
    private String accidentResponsibilityStr;
    /**
     * 事故程度 1 特大事故 2 重大事故 3 一般事故 4 轻微事故
     */
    private Integer accidentDegree;
    /**
     * 事故程度
     */
    private String accidentDegreeStr;
    /**
     * 事故时间
     */
    private Date accidentTime;
    /**
     * 事故地点
     */
    private String accidentPlace;
    /**
     * 损失金额
     */
    private BigDecimal lossAmount;
    /**
     * 驾驶员编码（对应的用户表编码）
     */
    private String driverCode;
    /**
     * 司机姓名
     */
    private String driverName;
    /**
     * 驾驶员手机号
     */
    private String driverMobile;
    /**
     * 驾驶员单位
     */
    private String driverDeptName;
    /**
     * 驾驶员部门
     */
    private String driverStructName;
    /**
     * 是否结案 1 是 2 否
     */
    private Integer isClosed;
    private String  isClosedName;
    /**
     * 赔偿金额
     */
    private BigDecimal compensateAmount;

    /**
     * 1-定点维修机构；2-外部维修机构
     */
    private Integer constructionGarageType;

    private String constructionGarageTypeName;
    /**
     * 车辆编码
     */
    private String vehicleNo;
    /**
     * 车辆品牌id-快照信息
     */
    private Integer vehicleBrandId;

    /**
     * 车辆品牌-快照信息
     */
    private String vehicleBrandName;
    /**
     * 车型code码-快照信息
     */
    private Integer vehicleModelId;

    /**
     * 车型-快照信息
     */
    private String vehicleModelName;
    /**
     * 车辆所有人
     */
    private String vehicleBelongDeptName;
    /**
     * 车辆使用人
     */
    private String vehicleUseDeptName;
    /**
     * 车辆使用单位
     */
    private String vehicleUseStructName;
    /**
     * 创建人名称
     */
    private String createdName;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改人名称
     */
    private String updatedName;
    /**
     * 最后更新时间
     */
    private Date updateTime;
    /**
     * 车架号-快照信息
     */
    private String vehicleVin;
    /**
     * 登记日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date vehicleRegisterTime;
    /**
     * 事故说明
     */
    private String accidentDescription;

    /**
     * 责任认定
     */
    private String confirmResponsibility;

    /**
     * 达成协议
     */
    private String reachAgreement;
    /**
     * 车系-快照信息
     */
    private String vehicleSeriesName;
    /**
     * 上次维保日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date lastMaintenanceDate;
    /**
     * 事故认定书
     */
    private List<VehicleAccidentFileDTO> vehicleAccidentLetterFileList;
    /**
     * 事故图片
     */
    private List<VehicleAccidentFileDTO> vehicleAccidentPicFileList;


    public String getManageCarTypeStr() {
        return ManageCarTypeEnum.getDesByCode(manageCarType);
    }

    public String getAccidentDegreeStr() {
        return AccidentSeverityEnum.getSeverityDesc(accidentDegree);
    }

    public String getAccidentResponsibilityStr() {
        return AccidentResponsibilityEnum.getResponsibilityDesc(accidentResponsibility);
    }

    public String getConstructionGarageTypeName() {
        return ConstructionGarageTypeEnum.getName( constructionGarageType);
    }

    public String getIsClosedName() {
        return AccidentIsCloseEnum.getIsClosedName(isClosed);
    }
}
