package com.mrcar.gov.common.dto.asset.maintenance.resp;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import izu.org.apache.poi.ss.usermodel.HorizontalAlignment;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description:
 * @date 2025/1/11 8:28
 */
@HeadStyle(
        horizontalAlignment = HorizontalAlignment.CENTER,
        fillForegroundColor = 22,
        fillBackgroundColor = 22
)
@Data
public class MaintenancePCExportDTO {

    /**
     * 维保单号
     */
    @ExcelProperty({"维保单号"})
    @ColumnWidth(20)
    private String maintenanceNo;

    /**
     * 工单状态
     */
    @ExcelIgnore
    private Integer maintenanceStatus;
    /**
     * 工单状态（名称）
     */
    @ExcelProperty({"工单状态"})
    @ColumnWidth(10)
    private String maintenanceStatusStr;

    /**
     * 工单类型
     */
    @ExcelIgnore
    private Integer maintenanceType;
    /**
     * 工单类型（名称）
     */
    @ExcelProperty({"工单类型"})
    @ColumnWidth(10)
    private String maintenanceTypeStr;

    /**
     * 车牌号
     */
    @ExcelProperty({"车牌号"})
    @ColumnWidth(10)
    private String vehicleLicense;

    /**
     * 车架号
     */
    @ExcelProperty({"车架号"})
    @ColumnWidth(20)
    private String vehicleVin;

    /**
     * 车辆所有人
     */
    @ExcelProperty({"车辆所有人"})
    @ColumnWidth(15)
    private String vehicleBelongDeptName;
    /**
     * 车辆使用人
     */
    @ExcelProperty({"车辆使用人"})
    @ColumnWidth(15)
    private String vehicleUseDeptName;
    /**
     * 车辆使用部门
     */
    @ExcelProperty({"车辆使用部门"})
    @ColumnWidth(15)
    private String vehicleUseStructName;

    /**
     * 管车类型；1-机关事务部门；2-财政部门（快照信息）
     */
    @ExcelIgnore
    private Integer manageCarType;
    /**
     * 车辆管理部门（列表展示用）
     */
    @ExcelProperty({"管理部门类型"})
    @ColumnWidth(10)
    private String manageCarTypeStr;

    /**
     * 司机姓名
     */
    @ExcelIgnore
    private String driverName;

    /**
     * 驾驶员手机号
     */
    @ExcelIgnore
    private String driverMobile;

    /**
     * 驾驶员（列表展示用）
     */
    @ExcelProperty({"驾驶员"})
    @ColumnWidth(20)
    private String driver;

    /**
     * 车辆品牌
     */
    @ExcelProperty({"车辆品牌"})
    private String vehicleBrandName;
    /**
     * 车型
     */
    @ExcelProperty({"车型"})
    private String vehicleSeriesName;
    /**
     * 故障主诉
     */
    @ExcelProperty({"故障主诉"})
    @ColumnWidth(20)
    private String faultDesc;
    /**
     * 送修人姓名
     */
    @ExcelProperty({"送修人"})
    private String submitterName;
    /**
     * 送修人电话
     */
    @ExcelProperty({"送修人电话"})
    @ColumnWidth(15)
    private String submitterPhone;

    /**
     * 到厂时里程
     */
    @ExcelProperty({"到厂时里程"})
    private BigDecimal arrivalOdometerReading;
    /**
     * 到厂时间
     */
    @ExcelProperty({"到厂时间"})
    @ColumnWidth(20)
    private Date submissionTime;
    /**
     * 维修城市名称
     */
    @ExcelProperty({"维修城市"})
    private String constructionGarageCityName;

    /**
     * 维修厂类型
     */
    @ExcelIgnore
    private Integer constructionGarageType;
    /**
     * 维修厂类型（名称）
     */
    @ExcelProperty({"维修机构类型"})
    @ColumnWidth(15)
    private String constructionGarageTypeStr;

    /**
     * 维修厂名称
     */
    @ExcelProperty({"维修机构名称"})
    @ColumnWidth(20)
    private String constructionGarageName;

    /**
     * 发票类型
     */
    @ExcelIgnore
    private Integer invoiceType;
    /**
     * 发票类型(前端展示)
     */
    @ExcelProperty({"发票类型"})
    @ColumnWidth(15)
    private String invoiceTypeStr;


    /**
     * 税率
     */
    @ExcelIgnore
    private BigDecimal invoiceRate;

    /**
     * 税率 百分数形式
     */
    @ExcelProperty({"税率"})
    private String invoiceRateStr;

    /**
     * 维修明细
     */
    @ExcelProperty({"维修明细"})
    @ColumnWidth(50)
    @ContentStyle(wrapped = true)
    private String costDetail;

    /**
     * 实收工时费
     */
    @ExcelProperty({"实收工时费（元）"})
    private String laborCost;

    /**
     * 实收配件费
     */
    @ExcelProperty({"实收配件费（元）"})
    private String partsCost;

    /**
     * 实收合计
     */
    @ExcelProperty({"实收合计（元）"})
    private String actualCost;

    /**
     * 维修总价
     */
    @ExcelProperty({"维修总价（元）"})
    private String totalCost;

    /**
     * 不含税价
     */
    @ExcelProperty({"不含税价（元）"})
    private String totalPrice;


    /**
     * 维修开始时间
     */
    @ExcelProperty({"维修开始时间"})
    @ColumnWidth(20)
    private Date maintainStartTime;

    /**
     * 维修完成时间
     */
    @ExcelProperty({"维修完成时间"})
    @ColumnWidth(20)
    private Date maintainEndTime;

    /**
     * 出厂里程
     */
    @ExcelProperty({"出厂时里程"})
    private BigDecimal departureOdometerReading;

    /**
     * 维修完成后出厂接车时间
     */
    @ExcelProperty({"出厂时间"})
    @ColumnWidth(20)
    private Date pickupTime;

    /**
     * 到厂时间
     */
    @ExcelIgnore
    private Date arrivalContactTime;




}
