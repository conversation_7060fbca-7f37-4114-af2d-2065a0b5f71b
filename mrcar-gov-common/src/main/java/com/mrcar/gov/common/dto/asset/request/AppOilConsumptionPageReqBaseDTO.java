package com.mrcar.gov.common.dto.asset.request;

import com.mrcar.gov.common.dto.PageParamDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> dongxiya 2023/4/8 14:45
 */
@Data
public class AppOilConsumptionPageReqBaseDTO extends PageParamDTO {
    /**
     * 车牌号
     */
    @ApiModelProperty(value = "车牌号")
    private String vehicleLicense;
    @ApiModelProperty(value = "车架号", hidden = true)
    private String vehicleVin;
    @ApiModelProperty(value = "审批状态(1 待审批,2 审批通过,3 审批驳回,4 已撤回)")
    private String approvalStatus;
    @ApiModelProperty(value = "创建时间-开始")
    private String createBeginTime;
    @ApiModelProperty(value = "创建时间-结束")
    private String createEndTime;
}
