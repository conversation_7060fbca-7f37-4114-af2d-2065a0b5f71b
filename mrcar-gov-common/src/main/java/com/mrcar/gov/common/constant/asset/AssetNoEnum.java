package com.mrcar.gov.common.constant.asset;

import java.util.stream.Stream;

public enum AssetNoEnum {
	WEI_BAO("WBD","维保"),
	JIA_YOU("JY","加油"),
	CHU_XIAN("CX","出险"),
	NIAN_JIAN("NJ","年检"),
	WEI_ZHANG("WZ","违章"),
	CAR_REFUELING_RECORD("CRR","加油记录"),
	DRIVE_RECORD("DR","用车日志"),
	DRIVE_FEE("DF","车杂费"),
	PEI_JIAN("PJ","配件"),
	CHU_KU("CK","出库"),
	RU_KU("RK","入库"),
	MAINTAIN_BOOKING_ORDER("YY","维保预约订单"),
	ACCIDENT("SGD","事故"),
	WORK_BASE("REP","工时基数"),
	WORK_FACTOR("WGX","工时系数"),
	CONSTRUCTION_DETAIL("1","施工单明细"),
	MAINTAIN_EVALUATION_GRADE("2","维保评价分数"),
	CONSTRUCTION_ATTACHMENT("3","施工单附件"),
	CONSTRUCTION_OPERATION_RECORD("4","施工单操作记录"),
	CAR_ALLOCATION_ORDER("CLDB", "车辆调拨单前缀"),
	VIOLATION_SUPPLIER_BILL_NO("VSBN", "违章供应商账单号"),
	;


	AssetNoEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    private String code;
    private String name;

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public static AssetNoEnum getByValue(String code) {
        return Stream.of(values()).filter((c) -> c.code.endsWith(code) ).findFirst().orElse(null);
    }
}
