package com.mrcar.gov.common.config;

import com.mrcar.gov.common.eventbus.EventBusDispatcher;
import com.mrcar.gov.common.eventbus.listener.EventBusListener;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class EventBusConfig {


    /**
     * 创建EventBusDispatcher 事件总线.
     * @return eventBusDispatcher
     */
    @Bean
    public EventBusDispatcher eventBusDispatcher() {
        return new EventBusDispatcher();
    }

    /**
     * 创建EventBusListener 事件监听器.
     * @return eventBusListener
     */
    @Bean
    public EventBusListener eventBusListener() {
        return new EventBusListener();
    }
}
