package com.mrcar.gov.common.constant.asset;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/11/11 17:52
 */
@Getter
public enum PreparationTypeEnum {
    //编制类型码 1实有车辆 2编制内购买服务
    REAL_VEHICLE(1,"实有车辆"),
    PREPARATION_VEHICLE(2,"编制内购买服务");

    private final int code;
    private final String desc;

    PreparationTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static String getPreparationTypeDesc(int code) {
       return Arrays.stream(PreparationTypeEnum.values())
               .filter(preparationTypeEnum -> preparationTypeEnum.getCode() == code).findFirst()
               .map(PreparationTypeEnum::getDesc)
               .orElse("");
    }

    public static List<String> getDescList() {
        return Arrays.stream(PreparationTypeEnum.values()).map(PreparationTypeEnum::getDesc).collect(Collectors.toList());
    }

    // 根据desc获取code
    public static Integer getCodeByDesc(String desc) {
        for (PreparationTypeEnum value : PreparationTypeEnum.values()) {
            if (Objects.equals(value.getDesc(), desc)) {
                return value.getCode();
            }
        }
        return null;
    }



}
