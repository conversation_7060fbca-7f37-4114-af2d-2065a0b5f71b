package com.mrcar.gov.common.util.pdf.api;

import org.apache.pdfbox.pdmodel.common.PDRectangle;

import java.util.ArrayList;
import java.util.List;

public class PdfPageBuilder {

    List<RowLikeElementBuilder> elements = new ArrayList<>();

    PDRectangle rectangle;
    // 页面内边距
    float paddingTopBottom = 0;
    float paddingLeftRight = 0;

    private PdfPageBuilder(PDRectangle rectangle) {
        this.rectangle = rectangle;
    }

    public static PdfPageBuilder A4() {
        return new PdfPageBuilder(PDRectangle.A4);
    }

    /**
     * 百分比
     */
    public PdfPageBuilder padding(float topBottom, float leftRight) {
        this.paddingTopBottom = topBottom;
        this.paddingLeftRight = leftRight;
        return this;
    }

    public PdfPageBuilder append(RowLikeElementBuilder row) {
        this.elements.add(row);
        return this;
    }

}
