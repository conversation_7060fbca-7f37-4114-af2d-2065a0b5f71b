package com.mrcar.gov.common.constant.order;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum ApplyOperationTypeEnum {

    // 申请单操作日志 1.创建订单 2.取消调度 3.调度转派 4.调度完成 5.重新调度 6.审批撤回 7.审批通过 6.审批驳回
    CREATE_ORDER(1, "创建订单"),
    CANCEL_SCHEDULE(2, "取消调度"),
    REASSIGN_SCHEDULE(3, "调度转派"),
    SCHEDULE_COMPLETE(4, "调度完成"),
    RESCHEDULE(5, "重新调度"),
    WITHDRAW_APPROVAL(6, "审批撤回"),
    APPROVE(7, "审批通过"),
    REJECT_APPROVAL(8, "审批驳回"),
    ;

    private final Integer code;
    private final String name;

    public static String getName(Integer code) {
        return Arrays.stream(values()).filter(e -> e.getCode().equals(code)).findFirst().map(ApplyOperationTypeEnum::getName).orElse(null);
    }
}