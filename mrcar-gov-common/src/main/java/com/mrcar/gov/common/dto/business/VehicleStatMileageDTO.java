package com.mrcar.gov.common.dto.business;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 原iot统计车辆里程结果对象.
 */
@Data
public class VehicleStatMileageDTO {

    private Integer companyId;

    private String vehicleLicense;
    private String vehicleVin;
    private String vehicleNo;

    @ApiModelProperty(value = "行驶里程",example = "11.11")
    private String mileageTotal;//行驶里程
    private String travelDurationName;// 行驶时长
    /**
     * 行驶时长单位s
     */
    private Long travelDuration;
    /**
     * 当日行驶轨迹里程
     */
    private BigDecimal dayTravelMileage;
    private String mileageEnable;//有效里程
    private String date;
    /**
     * 耗油量
     */
    @ApiModelProperty(value = "耗油量",example = "11.11")
    private BigDecimal oil;

    /**
     * 订单日行驶里程
     */
    private BigDecimal orderMileage;

    /**
     * 车机最新总里程
     */
    private String tboxTotalMileage;

    /**
     * 车机当天行驶里程
     */
    private String tboxDayMileage;

    // 车辆单位部门字段

    /**
     * 车辆所有人(分时租赁时存分时租赁使用单位)
     */
    private String vehicleBelongDeptCode;

    /**
     * 车辆所有人(分时租赁时存分时租赁使用单位)
     */
    private String vehicleBelongDeptName;

    /**
     * 车辆使用单位
     */
    private String vehicleUseDeptCode;

    /**
     * 车辆使用单位
     */
    private String vehicleUseDeptName;

    /**
     * 车辆使用部门
     */
    private String vehicleUseStructCode;

    /**
     * 车辆使用部门
     */
    private String vehicleUseStructName;

    /**
     * 管车类型；1-机关事务管理局；2-财政部门
     */
    private Integer manageCarType;

    /**
     * 车辆管理单位
     */
    private String vehicleManageDeptCode;

    /**
     * 车辆管理单位
     */
    private String vehicleManageDeptName;

}
