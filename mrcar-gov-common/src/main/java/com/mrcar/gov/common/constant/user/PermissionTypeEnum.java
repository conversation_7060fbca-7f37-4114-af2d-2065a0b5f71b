package com.mrcar.gov.common.constant.user;

import java.util.Arrays;


public enum PermissionTypeEnum {

    MENU(0, "菜单"),
    BUTTON(1, "按钮"),
    //DATA_AREA(2, "数据区域"),
    DATA_AREA(2, "内容页"),
    LINK(3, "站外链接"),
    ;

    private Integer type;

    private String desc;

    PermissionTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static PermissionTypeEnum getByPermissionType(Integer permissionType){
        return Arrays.stream(PermissionTypeEnum.values()).filter(e->e.getType().equals(permissionType)).findAny().orElse(null);
    }

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }
    public static String getDescByType(Integer type){
        return Arrays.stream(PermissionTypeEnum.values()).filter(e->e.getType().equals(type)).findAny().map(e->e.getDesc()).orElse("");
    }


}
