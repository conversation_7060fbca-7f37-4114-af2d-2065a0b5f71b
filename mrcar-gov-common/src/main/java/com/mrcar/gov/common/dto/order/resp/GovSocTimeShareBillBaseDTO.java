package com.mrcar.gov.common.dto.order.resp;

import lombok.Data;
import java.math.BigDecimal;
import java.math.RoundingMode;


/**
 * <AUTHOR>
 */
@Data
public class GovSocTimeShareBillBaseDTO {

    /**
     * 账单日期
     */
    private String statDate;

    /**
     * 用车单位code
     */
    private String deptCode;

    /**
     * 用车单位名称
     */
    private String deptName;

    /**
     * 用车时长
     */
    private String totalDuration;

    /**
     * 里程数
     */
    private String totalMileage;

    /**
     * 费用合计
     */
    private  String totalAmount;

    /**
     * 高限额
     */
    private String limitAmount;


    /**
     * 账单金额
     */
    private String billAmount;

    /**
     * 供应商code
     */
    private String supplierCode;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 车牌号
     */
    private String vehicleLicense;


    public void setBillAmount(BigDecimal billAmount,String unit) {
        this.billAmount = billAmount.setScale(2, RoundingMode.HALF_UP) +unit;
    }

    public void setLimitAmount(BigDecimal limitAmount,String unit) {
        this.limitAmount = limitAmount.setScale(2, RoundingMode.HALF_UP)+unit;
    }



    public void setTotalAmount(BigDecimal totalAmount,String unit) {
        this.totalAmount = totalAmount.setScale(2, RoundingMode.HALF_UP)+unit;
    }


    public void setTotalMileage(BigDecimal totalMileage,String unit) {
        this.totalMileage = totalMileage.setScale(2, RoundingMode.HALF_UP)+unit;
    }

    public void setTotalDuration(Integer totalDuration) {
        int hours = totalDuration / 60;
        int minute = totalDuration % 60;
        String totalDurationStr;
        if (hours > 0) {
            totalDurationStr =hours + "小时" + String.format("%02d", minute) + "分钟";
        } else {
            totalDurationStr = minute + "分钟";
        }
        this.totalDuration = totalDurationStr;
    }


}
