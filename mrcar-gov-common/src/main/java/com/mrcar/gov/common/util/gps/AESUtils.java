package com.mrcar.gov.common.util.gps;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * AES加解密算法
 */
public class AESUtils {
	
	 protected static Logger log = LoggerFactory.getLogger(AESUtils.class);

	private static final String ALGORITHM = "AES/ECB/PKCS5Padding";

	/**
	 * 加密
	 * @param text
	 * @param key
	 * @return
	 * @throws Exception 
	 */
	public static String encrypt(String text, String key) throws Exception {
        try {
            SecretKeySpec secretKey = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), "AES");
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            cipher.init(Cipher.ENCRYPT_MODE, secretKey);
            byte[] encryptedTextBytes = cipher.doFinal(text.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(encryptedTextBytes);
        } catch (Exception e) {
            log.error("msg1=加密异常,,method=decrypt,,text={},,key={}",text,key);
            e.printStackTrace();
            throw e;
        }
    }

    /**
     * 解密
     * @param encryptedText
     * @param key
     * @return
     * @throws Exception 
     */
    public static String decrypt(String encryptedText, String key) throws Exception {
        try {
            SecretKeySpec secretKey = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), "AES");
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            cipher.init(Cipher.DECRYPT_MODE, secretKey);
            byte[] encryptedTextBytes = Base64.getDecoder().decode(encryptedText);
            byte[] decryptedTextBytes = cipher.doFinal(encryptedTextBytes);
            return new String(decryptedTextBytes, StandardCharsets.UTF_8);
        } catch (Exception e) {
        	log.error("msg1=解密异常,,method=decrypt,,encryptedText={},,key={}",encryptedText,key);
            e.printStackTrace();
            throw e;
        }
    }
    
    /**
	 * 基于HEX输出
	 * @param str
	 * @param key
	 * @return
	 * @throws Exception
	 */
	public static String encryptAESToHex(String str, String key) throws Exception {
		if (str == null || key == null) {
			return null;
		}
		Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
		cipher.init(Cipher.ENCRYPT_MODE, new SecretKeySpec(key.getBytes("utf-8"), "AES"));
		byte[] bytes = cipher.doFinal(str.getBytes("utf-8"));
		//.encode()
		return HexUtil.bytesToHex(bytes);
	}
	
}
