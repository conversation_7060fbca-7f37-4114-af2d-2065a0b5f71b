package com.mrcar.gov.common.dto.order.resp;

import com.mrcar.gov.common.constant.order.IsShowEnum;
import io.swagger.annotations.ApiModel;
import lombok.Data;


@ApiModel("订单详情按钮返回参数")
@Data
public class GovPublicApplyButtonRespDTO{

    /**
     * 是否展示撤回审核按钮 0:不展示 1:展示
     */
    private Integer showCancelApprovalButton = IsShowEnum.NO_SHOW.getCode();
    /**
     * 是否展示取消按钮 0:不展示 1:展示
     */
    private Integer showCancelButton = IsShowEnum.NO_SHOW.getCode();
    /**
     * 是否展示撤回按钮 0:不展示 1:展示
     */
    private Integer showWithdrawButton = IsShowEnum.NO_SHOW.getCode();
    /**
     * 是否展示开始行程按钮 0:不展示 1:展示
     */
    private Integer showStartTravelButton = IsShowEnum.NO_SHOW.getCode();

    /**
     * 是否展示取消行程按钮 0:不展示 1:展示
     */
    private Integer showCancelTravelButton = IsShowEnum.NO_SHOW.getCode();

    /**
     * 是否展示结束行程按钮 0:不展示 1:展示
     */
    private Integer showFinishTravelButton = IsShowEnum.NO_SHOW.getCode();


    /**
     * 是否展示强制结束行程按钮 0:不展示 1:展示
     */
    private Integer showForceFinishTravelButton = IsShowEnum.NO_SHOW.getCode();


    /**
     * 是否展示核实处理按钮 0:不展示 1:展示
     */
    private Integer showVerifyButton = IsShowEnum.NO_SHOW.getCode();

    /**
     * 是否展示调度按钮 0:不展示 1:展示
     */
    private Integer showScheduleButton = IsShowEnum.NO_SHOW.getCode();


    /**
     * 是否展示转派按钮 0:不展示 1:展示
     */
    private Integer showTransferButton = IsShowEnum.NO_SHOW.getCode();

    /**
     * 是否展示重新调度按钮 0:不展示 1:展示
     */
    private Integer showReScheduleButton = IsShowEnum.NO_SHOW.getCode();

    /**
     * 是否展示下载派车单按钮 0:不展示 1:展示
     */
    private Integer showDownLoadButton = IsShowEnum.NO_SHOW.getCode();
}
