package com.mrcar.gov.common.constant.asset;

/**
 * <AUTHOR>
 * @date 2024/12/26 22:02
 */
public enum DeviceSourceEnum {

    //设备来源 1:政府设备 2：社会设备
    GOV(1, "政府终端"),
    SOCIETY(2, "供应商终端");

    private final int code;
    private final String name;

    DeviceSourceEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
    public static String getByCode(int code) {
        for (DeviceSourceEnum value : values()) {
            if (value.getCode() == code) {
                return value.getName();
            }
        }
        return "";
    }
}
