package com.mrcar.gov.common.dto.device.req;

import com.mrcar.gov.common.dto.BaseDTO;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
@Data
public class GpsDeviceUpdateReqDTO {
    /**
     * 设备id
     */
    @NotNull
    private Integer id;    /**
     * 车辆编码
     */
    private String vehicleNo;
    /**
     * 备注
     */
    private String remarks;

    @Valid
    @NotNull
    private GpsDeviceBaseDTO baseGpsDeviceAttribute;

    /**
     * @ignore
     * 会话信息
     */
    private BaseDTO sesssionBaseDTO;

}
