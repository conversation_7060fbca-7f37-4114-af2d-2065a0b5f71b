package com.mrcar.gov.common.constant.asset;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;
import java.util.stream.Stream;

/**
 * Author:  wangM
 * Date:  2025/1/13 15:10
 * Description:  事故责任枚举
 */
@Getter
@AllArgsConstructor
public enum AccidentResponsibilityEnum {

    FULL_RESPONSIBILITY(1, "全责"),
    MAIN_RESPONSIBILITY(2, "主责"),
    EQUAL_RESPONSIBILITY(3, "同责"),
    SECONDARY_RESPONSIBILITY(4, "次责"),
    NO_RESPONSIBILITY(5, "无责");

    private final Integer code;
    private final String desc;
    public static String getResponsibilityDesc(Integer code) {
        return  Stream.of(values())
                .filter(e -> Objects.equals(e.getCode(), code))
                .map(AccidentResponsibilityEnum::getDesc).findFirst().orElse("");
    }
}
