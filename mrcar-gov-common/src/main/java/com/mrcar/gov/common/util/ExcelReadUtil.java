package com.mrcar.gov.common.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFDateUtil;
import org.apache.poi.ss.usermodel.Cell;

import java.text.DecimalFormat;
import java.text.SimpleDateFormat;

/**
 * <AUTHOR>
 * @date : 2023/2/18
 */
public class ExcelReadUtil {

    public static String getCellValue(Cell cell){
        String cellValue = "";
        if (cell==null){
            return "";
        }
        switch (cell.getCellType()) {
            case Cell.CELL_TYPE_NUMERIC:
                //如果为时间格式的内容
                if (org.apache.poi.ss.usermodel.DateUtil.isCellDateFormatted(cell)) {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    cellValue=sdf.format(HSSFDateUtil.getJavaDate(cell.getNumericCellValue())).toString();
                    break;
                } else {
                    double numericValue = cell.getNumericCellValue();
                    if (numericValue == Math.floor(numericValue)) {
                        cellValue = new DecimalFormat("#").format(cell.getNumericCellValue());
                    } else {
                        cellValue = String.valueOf(numericValue);
                    }
                }
                break;
            case Cell.CELL_TYPE_STRING:
                cellValue = cell.getRichStringCellValue().getString().trim();
                break;
            case Cell.CELL_TYPE_FORMULA:
                cellValue = cell.getCellFormula();
                break;
            case Cell.CELL_TYPE_BOOLEAN:
                cellValue = String.valueOf(cell.getBooleanCellValue()).trim();
                break;
            default:
                cellValue = "";
        }
        if(StringUtils.isBlank(cellValue)){
            cellValue = "";
        }
        return StringUtils.trim(cellValue);
    }

}
