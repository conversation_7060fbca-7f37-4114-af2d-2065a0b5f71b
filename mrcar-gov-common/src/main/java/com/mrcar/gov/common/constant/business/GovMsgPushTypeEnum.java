package com.mrcar.gov.common.constant.business;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/12/30 13:50
 */
@AllArgsConstructor
@Getter
public enum GovMsgPushTypeEnum {
    // 站内信
    IN_SITE(1, "站内信"), // 短信
    SMS(2, "短信"),
    ;

    private final Integer code;
    private final String desc;


    /**
     * 校验传入的逗号分隔的推送方式编码是否都是有效的推送方式。
     * 如果任一编码无效，返回 false。
     *
     * @param codes 逗号分隔的推送方式编码字符串
     * @return true 表示所有编码都是有效的推送方式，false 表示存在无效编码
     */
    public static boolean isValidCodes(String codes) {

        if (codes == null || codes.trim().isEmpty()) {
            return false;
        }


        Set<Integer> validCodes = Arrays.stream(values()).map(GovMsgPushTypeEnum::getCode).collect(Collectors.toSet());

        String[] codeArray = codes.split(",");

        for (String codeStr : codeArray) {
            try {
                Integer code = Integer.valueOf(codeStr.trim());

                if (!validCodes.contains(code)) {
                    return false;
                }
            } catch (NumberFormatException e) {
                return false;
            }
        }
        return true;
    }

    /**
     * 传入一个逗号分割的code字符串，返回对应逗号分割的desc字符串。
     *
     * @param codes 逗号分割的code字符串
     * @return 对应逗号分割的desc字符串
     */
    public static String getDescByCodes(String codes) {
        if (codes == null || codes.trim().isEmpty()) {
            return "";
        }

        return Arrays.stream(codes.split(",")).map(String::trim).filter(codeStr -> !codeStr.isEmpty()).map(codeStr -> {
                    try {
                        Integer code = Integer.valueOf(codeStr);
                        return Arrays.stream(values()).filter(type -> type.getCode().equals(code)).map(GovMsgPushTypeEnum::getDesc).findFirst().orElse("");
                    } catch (NumberFormatException e) {
                        // 如果转换失败或找不到对应的枚举项，则忽略该条目
                        return "";
                    }
                }).filter(desc -> !desc.isEmpty()) // 过滤掉未找到的描述
                .collect(Collectors.joining(","));
    }

    public static String getDescByCode(Integer pushType) {
        for (GovMsgPushTypeEnum type : values()) {
            if (Objects.equals(type.getCode(), pushType)) {
                return type.getDesc();
            }
        }
        return "";
    }
}
