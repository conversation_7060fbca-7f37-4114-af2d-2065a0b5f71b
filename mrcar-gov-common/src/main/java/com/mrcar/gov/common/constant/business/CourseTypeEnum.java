package com.mrcar.gov.common.constant.business;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/12/31 15:16
 */
@Getter
public enum CourseTypeEnum {
    //教程类型 1：图文教程 2：视频教程

    TEXT_COURSE(1,"图文教程"),
    VIDEO_COURSE(2,"视频教程");

    private Integer type;
    private String desc;

    CourseTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }
    public static String getDesc(Integer type) {
        for (CourseTypeEnum value : CourseTypeEnum.values()) {
            if (value.getType().equals(type)) {
                return value.getDesc();
            }
        }
        return "";
    }
}
