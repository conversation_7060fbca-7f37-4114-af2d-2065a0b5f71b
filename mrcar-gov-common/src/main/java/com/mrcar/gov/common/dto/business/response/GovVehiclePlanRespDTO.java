package com.mrcar.gov.common.dto.business.response;

import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 车辆拟购置表
 * <AUTHOR>
 * @TableName gov_vehicle_plan
 */
@Data
public class GovVehiclePlanRespDTO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 自增id
     */
    private Integer id;

    /**
     * 关联申请编码
     */
    private String applyNo;

    /**
     * 原车辆编码
     */
    private String vehicleNo;

    /**
     * 车牌号码
     */
    private String vehicleLicense;

    /**
     * 车架号
     */
    private String vehicleVin;

    /**
     * 使用性质
     */
    private Integer useAttributeId;

    /**
     * 使用性质
     */
    private String useAttributeName;

    /**
     * 车辆类型
     */
    private Integer vehicleType;

    /**
     * 车辆类型
     */
    private String vehicleTypeName;

    /**
     * 动力类型
     */
    private Integer fuelType;


    /**
     * 动力类型
     */
    private String fuelTypeName;

    /**
     * 价格
     */
    private BigDecimal vehicleBarePrice;

    /**
     * 排量
     */
    private String outputVolume;

    /**
     * 配备时填数量
     */
    private Integer vehicleNum;

    /**
     * 所属公司id
     */
    private Integer companyId;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 拟处置方式 1报废 2拍卖 3调拨 4损失核销 5厂家回收 6 对外捐赠
     */
    private Integer disposalMethod;


    /**
     * 拟处置方式 1报废 2拍卖 3调拨 4损失核销 5厂家回收 6 对外捐赠
     */
    private String disposalMethodName;

    /**
     * 拟调入单位编码
     */
    private String transferInStructCode;

    /**
     * 拟调入单位名称
     */
    private String transferInStructName;
}