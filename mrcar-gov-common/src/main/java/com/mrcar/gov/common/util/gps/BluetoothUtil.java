package com.mrcar.gov.common.util.gps;

import com.google.common.collect.Maps;

import java.util.BitSet;
import java.util.Map;

public class BluetoothUtil {

    final static String ENCRYPTION_FACTOR = "GmQdgUjRjgPEDiKNw8^3RTirKD51z8PxaCs62urHMMVuXZU7fpzG7&okU5EFRohR";
    final static String KEY = "kj$#a&f!^g,[s%f*";
    //final static String KEY = "~PEGLMf#Kg0z0sCf";

    /**
     * 获取加密后的字符串
     * @param s 开始位数
     * @param l 长度
     * @return
     */
    public static String encryptionStr(Integer s ,Integer l) throws Exception {
        String key = AESUtils.encryptAESToHex(ENCRYPTION_FACTOR.substring(s,s+l),KEY);
        System.out.println(key);
        //将密文转crc16
        return key.substring(0,6)+ getCrc16(key);
    }

    /**
     * 将密文转crc16
     * @param key
     * @return
     */
    public static String getCrc16(String key) {
        byte[] crc =key.getBytes();
        byte[] bb = new byte[2];
        CRC16.get_crc16(crc, crc.length, bb);
        String crcstr= Integer.toHexString((int)bb[0] & 0x000000ff) + Integer.toHexString((int)bb[1] & 0x000000ff);
        return crcstr;
    }
    
    
    
    
    /**解析蓝牙返回的数据
     * 
     * @param bluetoothData
     * @return Map
     */
    public static Map<String, Object> resolveBluetoothData(String bluetoothData) {
    	Map<String, Object> map = Maps.newHashMap();
    	try {
	    	byte [] bytes = ByteUtil.hexStringToByte(bluetoothData.toUpperCase());
	    	System.out.println(ByteUtil.bytesToHexString(bytes));
			map.put("result", bytes[0]);
			map.put("state", bytes[1]);
			map.put("power", bytes[2]);
			map.put("voltage", ByteUtil.bytes2Short(ByteUtil.subBytes(bytes, 3, 2))/100.00f);
			map.put("mileage", ByteUtil.bytes2Short(ByteUtil.subBytes(bytes, 5, 2)));
			map.put("totalMileage", ByteUtil.byte2Int(ByteUtil.subBytes(bytes, 7, 4)));
	
			BitSet statusBitSet = ByteUtil.byteArray2BitSet(ByteUtil.subBytes(bytes, 11, 4));
			map.put("acc", statusBitSet.get(31));
			map.put("door1", statusBitSet.get(31 - 1));
			map.put("door2", statusBitSet.get(31 - 2));
			map.put("door3", statusBitSet.get(31 - 3));
			map.put("door4", statusBitSet.get(31 - 4));
			map.put("door5", statusBitSet.get(31 - 5));
			map.put("poweron", statusBitSet.get(31 - 6));
			map.put("hightPower", statusBitSet.get(31 - 7));
			map.put("acCharge", statusBitSet.get(31 - 8));
			map.put("dcCharge", statusBitSet.get(31 - 9));
			map.put("headlight", statusBitSet.get(31 - 10));//大灯 0关 1开
			map.put("footBrake", statusBitSet.get(31 - 11));//脚刹 0释放 1踩下
			map.put("handBrake", statusBitSet.get(31 - 12));//手刹 0拉紧 1释放
			map.put("engine", statusBitSet.get(31 - 13));//1：电机/发动机启动 0：电机/发动机未启动
			map.put("time", ByteUtil.byte2Int(ByteUtil.subBytes(bytes, 15, 4)));
			
			if(bytes.length > 19) {
				byte gpsLen = bytes[19];
				String gps = new String(ByteUtil.subBytes(bytes, 20, gpsLen));
//				System.out.println(gps);
				
				String lon = gps.split(",")[0];
				String lat = gps.split(",")[1];
				
				double doublelat = Double.valueOf(lat.trim()) / 100;
				int intlat = (int) (Double.valueOf(lat.trim()) / 100);
				double blueLat = intlat + ((doublelat - intlat) * 100.00 / 60.00);

				double doublelon = Double.valueOf(lon.trim()) / 100;
				int intlon = (int) (Double.valueOf(lon.trim()) / 100);
				double blueLon = intlon + ((doublelon - intlon) * 100.00 / 60.00);
				double [] latlon = GPSUtil.wgs2gcj(blueLat, blueLon);
				map.put("lat", latlon[0]);
				map.put("lon", latlon[1]);
				
			}
    	} catch(Exception e) {
    		System.out.println("蓝牙数据解析异常...");
    	}
		return map;
    }
    
    public static void main(String[] args) {
        String bluetoothData = "00013d04f0005d00003812000000405b31e1961631313731372e37343736322c333135322e3437353531";
        System.out.println(resolveBluetoothData(bluetoothData));
        Map data = resolveBluetoothData(bluetoothData);
        double lat = Double.parseDouble(data.get("lat").toString());
        double lon = Double.parseDouble(data.get("lon").toString());
        double [] latlon = GPSUtil.wgs2gcj(lat, lon);
        System.out.println(latlon[0] + "," + latlon[1]);
        
    }
    
    


}
