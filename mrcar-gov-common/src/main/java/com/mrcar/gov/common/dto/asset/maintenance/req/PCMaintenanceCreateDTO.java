package com.mrcar.gov.common.dto.asset.maintenance.req;

import com.mrcar.gov.common.dto.BaseDTO;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2025/1/10 14:30
 */
@Data
public class PCMaintenanceCreateDTO extends BaseDTO {
    /**
     * 车辆编码
     */
    @NotEmpty(message = "请选择车辆信息")
    private String vehicleNo;
    /**
     * 维修厂类型 1-定点维修机构；2-外部维修机构
     */
    @NotNull(message = "维修厂类型不能为空")
    private Integer constructionGarageType;
    /**
     * 维保服务机构编码（维修厂）定点维修机构时存在
     */
    private String constructionGarageNo;

    /**
     * 维保服务机构名称
     */
    private String constructionGarageName;

    /**
     * 维保服务机构电话
     */
    private String constructionGaragePhone;

    /**
     * 维保服务机构地址
     */
    private String constructionGarageAddress;

    /**
     * 发票类型（ 1:增值税专用发票 2:增值税普通发票）维修厂快照
     */
    private Integer invoiceType;

    /**
     * 发票开具税率(维修厂快照)
     */
    private BigDecimal invoiceRate;
    /**
     * 故障主诉
     */
    @NotBlank(message = "故障主诉不能为空")
    @Length(max = 200, message = "故障主诉不能超过200个字符")
    private String faultDesc;
    /**
     * 送修人的编码
     */
    @NotEmpty(message = "送修人编码不能为空")
    private String submitterCode;

    /**
     * 送修人的姓名
     */
    @NotEmpty(message = "送修人姓名不能为空")
    private String submitterName;

    /**
     * 送修人的联系电话
     */
    @NotEmpty(message = "送修人电话不能为空")
    private String submitterPhone;

    /**
     * 车辆送修的时间
     */
    @NotNull(message = "送修时间不能为空")
    private Date submissionTime;

    /**
     * 车辆到厂时的仪表盘里程（公里，保留两位小数）
     */
    @NotNull(message = "到厂里程不能为空")
    private BigDecimal arrivalOdometerReading;

    /**
     * 维修服务机构给出的故障描述
     */
    @NotEmpty(message = "故障描述不能为空")
    @Length(max = 200, message = "故障描述不能超过200个字符")
    private String shopFaultDesc;
    /**
     *  维修前仪表盘照片URL
     */
    private String beforeDashboardPhotoUrl;
    /**
     *  维修前车牌照片URL
     */
    private String beforeVehicleLicensePhotoUrl;
    /**
     * 报价明细
     */
    @NotEmpty(message = "报价明细不能为空")
    private List<QuoteDetail> quoteDetailList;
    /**
     * 报价依据照片
     */
    private List<String> quoteBaseImgUrl;
    /**
     * 车辆视频
     */
    private List<String> vehicleVideoUrl;
    /**
     * 出厂接车人的编码
     */
    @NotEmpty(message = "接车人code不能为空")
    private String departureContactCode;

    /**
     * 出厂接车人的姓名
     */
    @NotEmpty(message = "接车人姓名不能为空")
    private String departureContactName;

    /**
     * 出厂接车人的联系电话
     */
    @NotEmpty(message = "接车人电话不能为空")
    private String departureContactPhone;

    /**
     * 维修完成后出厂接车时间
     */
    @NotNull(message = "接车时间不能为空")
    private Date pickupTime;
    /**
     * 出厂里程
     */
    @NotNull(message = "出厂里程不能为空")
    private BigDecimal departureOdometerReading;
    /**
     *  出厂仪表盘照片URL
     */
    private String afterDashboardPhotoUrl;
    /**
     *  发票照片URL
     */
    private String billPhotoUrl;
    /**
     *  维修明细
     */
    private String repairDetailsPhotoUrl;
}
