package com.mrcar.gov.common.dto.config;


import lombok.Data;
import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class GovCompanyConfigItemValueListDTO {
    /**
     * 分类code
     */
    private String categoryCode;

    /**
     * 分类名称
     */
    private String categoryName;

    /**
     * 排序字段
     */
    private Integer sort;

    /**
     * 业务环节
     */
    private String businessLink;


    /**
     * 业务编码
     */
    private String businessConfigCode;

    /**
     * 业务名称
     */
    private String businessConfigName;


    /**
     * 值配置项code
     */
    private String itemCode;

    /**
     * 值名称
     */
    private String itemName;


    /**
     *  类型 1:单选框, 2:开关,3:单选择器时间,4:文本框,5:多选框,6:下拉框,7:多时间选择器
     */
    private Integer type;

    /**
     * 备注，用于值的内容展示
     */
    private String remark;

    /**
     * 是否必填 0:非必填 1:必填
     */
    private Integer isRequired;

    /**
     * 单选/多选 适用于下拉框 0:单选  1:多选
     */
    private Integer selectType;

    /**
     * 默认值，仅适用于新增的标准类型，NULL表示没有默认值
     */
    private String defaultValue;

    /**
     * 值类型 1:字符串, 2:数组, 3: jsonObject
     */
    private Integer dataType;

    /**
     * 数字最大值，适用于文本框且是数字格式
     */
    private BigDecimal maxValue;

    /**
     * 数字最小值，适用于文本框且是数字格式
     */
    private BigDecimal minValue;

    /**
     * 所有可能的值，JSON格式：开关/单选：[{"value":"0","label":"否"},{"value":"1","label":"是"}], 下拉框：[{"value":"","label":""},{"value":"","label":""}], 文本框:"", 其他：自定义类型JSON
     */
    private Object possibleValues;

    /**
     * 选择的值
     */
    private String configValue;

    /**
     * 选择的值 展示
     */
    private String configLabel;


    private MergeInfo mergeInfo;

    @Data
    public static class MergeInfo {
        private  Integer rowspan;
        private  Integer colspan;
        public MergeInfo(Integer rowspan, Integer colspan) {
            this.rowspan = rowspan;
            this.colspan = colspan;
        }

    }

}



