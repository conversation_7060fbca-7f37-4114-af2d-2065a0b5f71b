package com.mrcar.gov.common.dto.workflow.model;

import com.mrcar.gov.common.dto.workflow.BaseDTO;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 管理后台 - 本单位流程创建 Request VO
 */
@Data
public class BpmModelCurrentUnitCreateReqDTO extends BaseDTO {

    /**
     * 流程标识
     * 示例值:
     */
    @NotBlank(message = "模型Id不能为空")
    private String modelId;

    /**
     * 流程标识
     * 示例值:
     */
    @NotNull(message = "部门Id不能为空")
    private Integer structId;

}
