package com.mrcar.gov.common.constant.order;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/8/18 10:23
 */
@AllArgsConstructor
@Getter
public enum GovPublicCarOrderOptCodeEnum {

    APPROVAL_WITHDRAW(1000, "审批撤回"),
    START_TRIP(1001, "开始行程"),
    CANCEL_TRIP(1002, "取消行程"),
    ORDER_SCHEDULE(1003, "订单调度"),
    ORDER_VERIFY(1004, "订单核实"),
    FORCE_FINISH_TRIP(1005, "强制结束行程"),
    END_TRIP(1006, "结束行程"),


    ;

    private int optCode;

    private String desc;
}
