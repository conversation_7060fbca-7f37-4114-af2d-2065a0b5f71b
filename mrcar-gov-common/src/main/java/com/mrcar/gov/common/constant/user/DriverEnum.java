package com.mrcar.gov.common.constant.user;

import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description: 司机相关枚举
 * @date 2024/11/9 13:30
 */
public class DriverEnum {

    /**
     * 1:无任务，2：有任务
     */
    @Getter
    public enum WorkingStatusEnum {
        /**
         * 无任务
         */
        HAVING_NO_WORKING(1, "无任务"),
        /**
         * 任务中
         */
        HAVING_WORKING(2, "任务中");

        private final Integer code;
        private final String desc;

        WorkingStatusEnum(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static String getDescByCode(Integer workingStatus) {
            for (WorkingStatusEnum enu : WorkingStatusEnum.values()){
                if(Objects.equals(workingStatus, enu.getCode())){
                    return enu.getDesc();
                }
            }
            return "";
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        public static WorkingStatusEnum getByCode(Integer code) {
            return Arrays.stream(WorkingStatusEnum.values()).filter(workingStatusEnum -> workingStatusEnum.getCode().equals(code)).findFirst().orElse(null);
        }

    }

    /**
     * 用户性别；1：男；2：女
     */
    @Getter
    public enum GenderEnum {

        MALE(1, "男"),

        FEMALE(2, "女");

        private final Integer code;
        private final String desc;

        GenderEnum(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static String getDescByCode(Integer gender) {
            for (GenderEnum enu : GenderEnum.values()){
                if(Objects.equals(gender, enu.getCode())){
                    return enu.getDesc();
                }
            }
            return "";

        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        public static GenderEnum getByCode(Integer code) {
            return Arrays.stream(GenderEnum.values()).filter(genderEnum -> genderEnum.getCode().equals(code)).findFirst().orElse(null);
        }


    }

    /**
     * 司机任职状态；1：正常；2：休假；3：未来用（服役）4：离职
     */
    @Getter
    public enum OfficeStatusEnum {

        NORMAL(1, "正常"),
        HAVE_HOLIDAY(2, "休假"),
        ACTIVE_SERVICE(3, "服役"),
        RESIGN(4, "离职");

        private final Integer code;
        private final String desc;

        OfficeStatusEnum(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static String getDescByCode(Integer officeStatus) {
            for (OfficeStatusEnum enu : OfficeStatusEnum.values()){
                if(Objects.equals(officeStatus, enu.getCode())){
                    return enu.getDesc();
                }
            }
            return "";

        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        public static OfficeStatusEnum getByCode(Integer code) {
            return Arrays.stream(OfficeStatusEnum.values()).filter(officeStatusEnum -> officeStatusEnum.getCode().equals(code)).findFirst().orElse(null);
        }
    }

    /**
     * 编制类型 1: 聘用;2：编外
     */
    @Getter
    public enum PreparationTypeEnum {

        EMPLOY(1, "聘用"),
        NON_STAFF(2, "编外");

        private final Integer code;
        private final String desc;

        PreparationTypeEnum(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static String getDescByCode(Integer preparationType) {
            for (PreparationTypeEnum enu : PreparationTypeEnum.values()){
                if(Objects.equals(preparationType, enu.getCode())){
                    return enu.getDesc();
                }
            }
            return "";
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        public static PreparationTypeEnum getByCode(Integer code) {
            return Arrays.stream(PreparationTypeEnum.values()).filter(preparationTypeEnum -> preparationTypeEnum.getCode().equals(code)).findFirst().orElse(null);
        }
    }


    /**
     * 政治面貌；1：群众；2：中共党员；3：中共预备党员；4：共青团员；5：民革党员；6：民盟盟员；7：民建会员；8：民进会员；9：农工党党员；10：致公党党员；11：九三学社社员；12：台盟盟员；13：无党派人士
     */
    @Getter
    public enum PoliticalStatusEnum {

        MASSES(1, "群众"),
        MCPC(2, "中共党员"),
        PMCPC(3, "中共预备党员"),
        PCYLC(4, "中青团员"),
        MRCCK(5, "民革党员"),
        MCDL(6, "民盟盟员"),
        MCDNCA(7, "民建会员"),
        MCAPD(8, "民进会员"),
        MCPWPD(9, "农工党党员"),
        MCZP(10, "致公党党员"),
        MJS(11, "九三学社社员"),
        MTDSGL(12, "台盟盟员"),
        NPP(13, "无党派人士");

        private final Integer code;
        private final String desc;

        PoliticalStatusEnum(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static String getDescByCode(Integer politicalStatus) {
            for (PoliticalStatusEnum enu : PoliticalStatusEnum.values()){
                if(Objects.equals(politicalStatus, enu.getCode())){
                    return enu.getDesc();
                }
            }
            return "";
        }

        public static Integer getCodeByDesc(String code) {
            for (PoliticalStatusEnum enu : PoliticalStatusEnum.values()){
                if(Objects.equals(code, enu.getDesc())){
                    return enu.getCode();
                }
            }
            return null;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        public static PoliticalStatusEnum getByCode(Integer code) {
            return Arrays.stream(PoliticalStatusEnum.values()).filter(politicalStatusEnum -> politicalStatusEnum.getCode().equals(code)).findFirst().orElse(null);
        }
    }


    /**
     * 文化程度 1：文盲；2：小学；3：初中；4：高中；5：技工学校；6：中专；7：大专；8：本科；9：研究生
     */
    @Getter
    public enum EducationStatusEnum {

        ILLITERATE(1, "文盲"),
        PRIMARY(2, "小学"),
        JUNIOR(3, "初中"),
        SENIOR(4, "高中"),
        TECHNICAL(5, "技工学校"),
        VOCATIONAL(6, "中专"),
        COLLEGE(7, "大专"),
        BACHELOR_DEGREE(8, "本科"),
        POSTGRADUATE(9,"研究生");
        private final Integer code;
        private final String desc;

        EducationStatusEnum(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static String getDescByCode(Integer educationStatus) {
            for (EducationStatusEnum enu : EducationStatusEnum.values()){
                if(Objects.equals(educationStatus, enu.getCode())){
                    return enu.getDesc();
                }
            }
            return "";
        }

        public static Integer getCodeByDesc(String desc) {
            for (EducationStatusEnum enu : EducationStatusEnum.values()){
                if(Objects.equals(desc, enu.getDesc())){
                    return enu.getCode();
                }
            }
            return null;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        public static EducationStatusEnum getByCode(Integer code) {
            return Arrays.stream(EducationStatusEnum.values()).filter(educationStatusEnum -> educationStatusEnum.getCode().equals(code)).findFirst().orElse(null);
        }
    }

    /**
     * 驾照状态 1:正常 2过期
     */
    @Getter
    public enum DriverLicenseStatusEnum {

        NORMAL(1, "正常"),
        EXPIRE(2, "过期");

        private final Integer code;
        private final String desc;

        DriverLicenseStatusEnum(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        public static DriverLicenseStatusEnum getByCode(Integer code) {
            return Arrays.stream(DriverLicenseStatusEnum.values()).filter(driverLicenseStatusEnum -> driverLicenseStatusEnum.getCode().equals(code)).findFirst().orElse(null);
        }
    }


}
