package com.mrcar.gov.common.util.pdf.model;

import org.vandeseer.easytable.structure.cell.AbstractCell;
import org.vandeseer.easytable.structure.cell.TextCell;

import java.util.List;

public class TableTextCellElement extends TableCellElement {

    private static final String NODE_NAME = "Table-Cell-Text";

    // 多行文本
    private final List<String> lines;

    protected TableTextCellElement(BlockElement parent,
                                   List<String> lines) {
        super(parent);
        this.lines = lines;
    }

    public static TableTextCellElement create(BlockElement parent,
                                              List<String> lines) {
        return new TableTextCellElement(parent, lines);
    }

    @Override
    public String name() {
        return NODE_NAME;
    }

    @Override
    protected AbstractCell buildCell() {
        return TextCell
                .builder()
                .text(String.join("\n", lines))
                .build();
    }
}
