package com.mrcar.gov.common.dto.business.request;

import com.mrcar.gov.common.dto.BaseDTO;
import com.mrcar.gov.common.dto.FileDTO;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 车辆 配备，更新处置调拨 申请查询请求DTO
 * <AUTHOR>
 */
@Data
public class GovTransactionApplyQueryDTO extends BaseDTO {
    private static final long serialVersionUID = 1L;



    private static final Integer PAGE_NUM = 1;
    private static final Integer PAGE_SIZE = 10;
    /**
     * 页码，从 1 开始
     */
    private Integer page = PAGE_NUM;
    /**
     * 每页条数
     */
    private Integer pageSize = PAGE_SIZE;


    /**
     * 业务申请编码
     */
    private String applyNo;

    /**
     * 申请类型 1 更新 2 配备 3 处置 4调拨
     */
    private Integer applyType;


    /**
     * 申请状态
     */
    private Integer applyStatus;


    /**
     * 申请单位编码
     */
    private String applyStructCode;

    /**
     * 车辆所属单位编码
     */
    @NotBlank(message = "申请单位编码不能为空")
    private String vehicleBelongDeptCode;

    /**
     * 车牌号码
     */
    private String vehicleLicense;

    /**
     * 车架号
     */
    private String vehicleVin;

    /**
     * 创建开始时间 yyyy-MM-dd
     */
    private String createStartDateStr;

    /**
     * 创建结束时间  yyyy-MM-dd
     */
    private String createEndDateStr;


    private String createStartDate;

    /**
     * 车辆所属单位编码列表
     */
    private List<String> vehicleBelongDeptCodeList;


    /**
     * 申请类型 1 更新 2 配备 3 处理及调拨
     */
    private Set<Integer> applyTypeSet;

}