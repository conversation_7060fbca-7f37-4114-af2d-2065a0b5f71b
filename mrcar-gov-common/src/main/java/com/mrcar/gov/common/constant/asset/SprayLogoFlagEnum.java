package com.mrcar.gov.common.constant.asset;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/11/17 11:39
 * 车辆喷涂标识枚举
 */
@AllArgsConstructor
@Getter
public enum SprayLogoFlagEnum {
    // 是
    YES(1, "是"),
    // 否
    NO(0, "否");

    private final Integer code;
    private final String desc;

    public static List<String> getDescList() {
        return Arrays.stream(SprayLogoFlagEnum.values()).map(SprayLogoFlagEnum::getDesc).collect(Collectors.toList());
    }

    public static Integer getCodeByDesc(String desc) {
        for (SprayLogoFlagEnum value : SprayLogoFlagEnum.values()) {
            if (Objects.equals(value.getDesc(), desc)) {
                return value.getCode();
            }
        }
        return null;
    }


}
