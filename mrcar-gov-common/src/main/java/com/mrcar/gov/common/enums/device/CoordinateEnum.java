package com.mrcar.gov.common.enums.device;

/**
 * @ClassName CoordinateEnum
 * @Description TODO
 * <AUTHOR>
 * @Date 2019/11/1 13:53
 * @Version 1.0
 */
public enum  CoordinateEnum {
    BAIDU("BAIDU","百度坐标系"),
    MARS("MARS","高德坐标系"),
    WGS84("WGS84","原生坐标系")
    ;


    public String code;
    public String msg;


    CoordinateEnum(String code, String msg)
    {
        this.code =code;
        this.msg = msg;
    }

    public static String getMsg(String code){
        for(CoordinateEnum e: CoordinateEnum.values()){
            if(e.getCode().equals(code)){
                return e.getMsg();
            }
        }
        return null;
    }

    public static CoordinateEnum getCoordinateEnum(String code){
        for(CoordinateEnum e: CoordinateEnum.values()){
            if(e.getCode().equals(code)){
                return e;
            }
        }
        return null;
    }


    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}
