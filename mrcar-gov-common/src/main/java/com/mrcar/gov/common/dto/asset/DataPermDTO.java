package com.mrcar.gov.common.dto.asset;

import com.mrcar.gov.common.dto.PageParamDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * 数据权限
 */
@Data
public class DataPermDTO extends PageParamDTO {
    @ApiModelProperty(value = "系统类型", hidden = true)
    private Byte systemType;
    @ApiModelProperty(value = "登录人企业id", hidden = true)
    private Integer companyId;
    @ApiModelProperty(value = "登录人企业code", hidden = true)
    private String companyCode;
    @ApiModelProperty(value = "登录人企业name", hidden = true)
    private String companyName;
    @ApiModelProperty(value = "登录人id", hidden = true)
    private Integer staffId;
    @ApiModelProperty(value = "登录人code", hidden = true)
    private String staffCode;
    @ApiModelProperty(value = "登录人name", hidden = true)
    private String staffName;
    @ApiModelProperty(value = "数据权限类型", hidden = true)
    private Byte dataPermType;
    @ApiModelProperty(value = "具体需要查询的权限编码集合", hidden = true)
    private Set<String> dataCodeSet;
    @ApiModelProperty(value = "登录人的权限不是空值", hidden = true)
    private Boolean dataPermIsNotNull;
    @ApiModelProperty(value = "所在城市编码")
    private String cityCode;
    @ApiModelProperty(value = "所属部门id")
    private Integer structId;
    @ApiModelProperty(value = "搜索条件-所属企业id")
    private List<Integer> companyIds;

    @ApiModelProperty(value = "登录人企业id", hidden = true)
    private Integer loginCompanyId;
}
