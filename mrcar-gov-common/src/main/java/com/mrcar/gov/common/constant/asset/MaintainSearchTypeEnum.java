package com.mrcar.gov.common.constant.asset;

import com.google.common.collect.Lists;
import lombok.Getter;

import java.util.List;

/**
 * 维修厂员工查询类型枚举
 *
 * <AUTHOR>
 * @date 2025/1/15 16:45
 */
@Getter
public enum MaintainSearchTypeEnum {
    //1:待到厂 2：维修待处理 3：待接车 4：已结束
    WAIT_FOR_FACTORY(1, "待到厂", Lists.newArrayList(MaintenanceStatusEnum.WAITING_FOR_FACTORY.getCode())),
    REPAIR_PENDING(2, "维修待处理", Lists.newArrayList(
            MaintenanceStatusEnum.QUOTE_APPROVAL_PENDING.getCode(),
            MaintenanceStatusEnum.ARRIVED_AT_FACTORY.getCode(),
            MaintenanceStatusEnum.UNDER_REPAIR.getCode(),
            MaintenanceStatusEnum.QUOTE_APPROVAL_REJECTED.getCode(),
            MaintenanceStatusEnum.ADDITIONAL_ITEM_APPROVAL_PENDING.getCode(),
            MaintenanceStatusEnum.ADDITIONAL_ITEM_APPROVAL_REJECTED.getCode())),
    WAIT_FOR_PICKUP(3, "待接车", Lists.newArrayList(MaintenanceStatusEnum.COMPLETED.getCode())),
    COMPLETED(4, "已结束", Lists.newArrayList(
            MaintenanceStatusEnum.VEHICLE_RECEIVED.getCode(),
            MaintenanceStatusEnum.DISCARDED.getCode()));

    private final Integer code;
    private final String name;

    private final List<Integer> statusList;

    MaintainSearchTypeEnum(Integer code, String name, List<Integer> statusList) {
        this.code = code;
        this.name = name;
        this.statusList = statusList;
    }

    public static String getName(Integer code) {
        for (MaintainSearchTypeEnum maintainSearchTypeEnum : MaintainSearchTypeEnum.values()) {
            if (maintainSearchTypeEnum.getCode().equals(code)) {
                return maintainSearchTypeEnum.getName();
            }
        }
        return "";
    }

    public static List<Integer> getStatusList(Integer code) {
        if (code == null) {
            return Lists.newArrayList();
        }
        for (MaintainSearchTypeEnum maintainSearchTypeEnum : MaintainSearchTypeEnum.values()) {
            if (maintainSearchTypeEnum.getCode().equals(code)) {
                return maintainSearchTypeEnum.getStatusList();
            }
        }
        return Lists.newArrayList();
    }
}
