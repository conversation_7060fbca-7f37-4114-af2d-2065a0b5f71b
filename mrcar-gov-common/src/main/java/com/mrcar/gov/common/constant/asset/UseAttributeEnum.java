package com.mrcar.gov.common.constant.asset;

import com.mrcar.gov.common.constant.user.GovAttributeTypeEnum;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/11/11 16:19
 */
@Getter
public enum UseAttributeEnum {
    //机要通信用车、应急保障用车、实物保障用车、调研用车、行政执法用车、接待用车、老干部服务用车、业务用车、执法执勤用车、特种专业技术用车
    COMMUNICATION_VEHICLE(1,"机要通信用车", 1),
    EMERGENCY_VEHICLE(2,"应急保障用车",1),
    PHYSICAL_VEHICLE(3,"实物保障用车",1),
    RESEARCH_VEHICLE(4,"调研用车",1),
    REGULATION_VEHICLE(5,"行政执法用车",1),
    RECEPTION_VEHICLE(6,"接待用车",1),
    OLD_AGE_VEHICLE(7,"老干部服务用车",1),
    BUSINESS_VEHICLE(8,"业务用车",1),
    POLICE_VEHICLE(9,"执法执勤用车",2),
    SPECIAL_VEHICLE(10,"特种专业技术用车",2);
    private final int code;
    private final String desc;
    //GovAttributeTypeEnum
    private final Integer attributeType;

    UseAttributeEnum(int code, String desc,Integer attributeType) {
        this.code = code;
        this.desc = desc;
        this.attributeType = attributeType;
    }

    public static String getDesc(int code) {
        for (UseAttributeEnum value : UseAttributeEnum.values()) {
            if (Objects.equals(value.getCode(), code)) {
                return value.getDesc();
            }
        }
        return "";
    }
    // 获取所有的描述列表
    public static List<String> getDescList() {
        List<String> descList = new ArrayList<>(UseAttributeEnum.values().length);
        for (int i = 0; i < UseAttributeEnum.values().length; i++) {
            descList.add(UseAttributeEnum.values()[i].getDesc());
        }
        return descList;
    }
    // 根据描述获取对应的code
    public static Integer getCodeByDesc(String desc) {
        for (UseAttributeEnum value : UseAttributeEnum.values()) {
            if (Objects.equals(value.getDesc(), desc)) {
                return value.getCode();
            }
        }
        return null;
    }
}
