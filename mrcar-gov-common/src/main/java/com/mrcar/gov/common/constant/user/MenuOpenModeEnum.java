package com.mrcar.gov.common.constant.user;

import java.util.Arrays;


public enum MenuOpenModeEnum {
    ENTERPRISE(1, "客户端"),
    APPLET(2, "小程序");
    private Integer code;

    private String desc;

    MenuOpenModeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static MenuOpenModeEnum getByCode(Integer menuOpen){
       return Arrays.stream(MenuOpenModeEnum.values()).filter(en->en.code.equals(menuOpen)).findAny().orElse(null);
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
