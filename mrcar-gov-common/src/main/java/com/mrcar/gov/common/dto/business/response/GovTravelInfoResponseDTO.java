package com.mrcar.gov.common.dto.business.response;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import izu.org.apache.poi.ss.usermodel.BorderStyle;
import izu.org.apache.poi.ss.usermodel.HorizontalAlignment;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 行程信息响应对象.
 *
 * <AUTHOR>
 * @date 2025-02-13
 */
@Data
@HeadStyle(
        fillForegroundColor = 22,
        fillBackgroundColor = 22
)
@ColumnWidth(20)
@ContentStyle(
        horizontalAlignment = HorizontalAlignment.CENTER,
        borderBottom = BorderStyle.THIN,
        borderLeft = BorderStyle.THIN,
        borderRight = BorderStyle.THIN,
        borderTop = BorderStyle.THIN,
        wrapped = true
)
public class GovTravelInfoResponseDTO implements Serializable {

    @ExcelIgnore
    private String id;

    /**
     * 车牌号
     */
    @ExcelProperty(
            value = {"车牌号"},
            order = 2
    )
    private String vehicleLicense;

    /**
     * 车辆编号
     */
    @ExcelIgnore
    private String vehicleNo;

    /**
     * 车架号
     */
    @ExcelProperty(
            value = {"车架号"},
            order = 3
    )
    private String vehicleVin;

    /**
     * 燃料类型
     */
    @ExcelIgnore
    private String fuelType;

    @ExcelIgnore
    private Integer deviceType;

    /**
     * SIM卡号
     */
    @ExcelProperty(
            value = {"SIM卡号"},
            order = 15
    )
    private String simNo;

    /**
     * 设备编号
     */
    @ExcelIgnore
    private String deviceNo;

    /**
     * 行程编号
     */
    @ExcelProperty(
            value = {"行程编号"},
            order = 1
    )
    private String travelSerialNo;

    @ExcelIgnore
    private String startTotalMile;

    @ExcelIgnore
    private String endTotalMile;

    @ExcelIgnore
    private BigDecimal fuel;

    /**
     * 百公里油耗(升/100公里)
     */
    @ExcelIgnore
    private String avgOil;

    /**
     * 当前行程耗油量(L)
     */
    @ExcelIgnore
    private String useOil;

    /**
     * 起点城市
     */
    @ExcelProperty(
            value = {"起点城市"},
            order = 4
    )
    private String startCity;

    /**
     * 起点时间
     */
    @ExcelProperty(
            value = {"起点时间"},
            order = 8
    )
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date startTime;

    /**
     * 起点位置
     */
    @ExcelProperty(
            value = {"起点位置"},
            order = 6
    )
    private String startPosition;

    @ExcelIgnore
    private String startLocation;

    /**
     * 终点城市
     */
    @ExcelProperty(
            value = {"终点城市"},
            order = 5
    )
    private String endCity;

    /**
     * 终点时间
     */
    @ExcelProperty(
            value = {"终点时间"},
            order = 9
    )
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date endTime;

    /**
     * 终点位置
     */
    @ExcelProperty(
            value = {"终点位置"},
            order = 7
    )
    private String endPosition;

    @ExcelIgnore
    private String endLocation;

    @ExcelProperty(
            value = {"行驶里程（公里）"},
            order = 12
    )
    private BigDecimal travelMile;

    /**
     * 行驶里程（位移向量算法：公里）
     */
    @ExcelIgnore
    private BigDecimal travelMileBak;

    /**
     * 行驶里程（里程表读数：公里）
     */
    @ExcelIgnore
    private String travelMileRead;

    @ExcelIgnore
    private String avgSpeed;

    @ExcelIgnore
    private String maxSpeed;

    @ExcelIgnore
    private String travelTag;

    @ExcelIgnore
    private Date createTime;

    @ExcelIgnore
    private Date updateTime;

    /**
     * 所属企业
     */
    @ExcelIgnore
    private String companyName;

    @ExcelIgnore
    @JsonFormat(
            pattern = "yyyy-MM-dd"
    )
    private Date statDate;

    /**
     * 行驶时长（时:分）
     */
    @ExcelProperty(
            value = {"行驶时长（时:分）"},
            order = 11
    )
    private String travelDurationName;

    /**
     * 设备类型
     */
    @ExcelProperty(
            value = {"设备类型"},
            order = 14
    )
    private String deviceTypeName;

    @ExcelIgnore
    private String manfactName;

    @ExcelIgnore
    private String modelName;

    /**
     * 车辆所有人(分时租赁时存分时租赁使用单位)
     */
    @ExcelIgnore
    private String vehicleBelongDeptCode;

    /**
     * 车辆所有人(分时租赁时存分时租赁使用单位)
     */
    @ExcelProperty(
            value = {"车辆所有人"},
            order = 10
    )
    private String vehicleBelongDeptName;

    /**
     * 车辆使用单位
     */
    @ExcelIgnore
    private String vehicleUseDeptCode;

    /**
     * 车辆使用单位
     */
    @ExcelProperty(
            value = {"车辆使用人"},
            order = 16
    )
    private String vehicleUseDeptName;

    /**
     * 车辆使用部门
     */
    @ExcelIgnore
    private String vehicleUseStructCode;

    /**
     * 车辆使用部门
     */
    @ExcelProperty(
            value = {"车辆使用部门"},
            order = 17
    )
    private String vehicleUseStructName;

    /**
     * 管车类型；1-机关事务管理局；2-财政部门
     */
    @ExcelIgnore
    private Integer manageCarType;

    /**
     * 管车类型；1-机关事务管理局；2-财政部门
     */
    @ExcelProperty(
            value = {"管理部门类型"},
            order = 18
    )
    private String manageCarTypeName;

    /**
     * 车辆管理单位
     */
    @ExcelIgnore
    private String vehicleManageDeptCode;

    /**
     * 车辆管理单位
     */
    @ExcelIgnore
    private String vehicleManageDeptName;


}
