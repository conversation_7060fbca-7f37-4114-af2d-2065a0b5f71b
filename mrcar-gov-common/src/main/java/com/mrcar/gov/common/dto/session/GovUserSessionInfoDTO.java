package com.mrcar.gov.common.dto.session;

import com.mrcar.gov.common.dto.AccountMenu;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 用户登录会话信息.
 *
 * <AUTHOR>
 * @date 2024-11-08
 */
@Data
public class GovUserSessionInfoDTO implements Serializable {

    /**
     * 账号基本信息
     */
    private AccountBaseInfo baseInfo;

    /**
     * 公司信息
     */
    private CompanyInfo companyInfo;

    /**
     * 角色列表
     */
    private List<AccountRole> roleList;

    /**
     * 菜单树供登录后初始化菜单（不包含按钮）
     */
    // private List<AccountMenu> menuTree;

    /**
     * APP菜单树
     */
    private List<AccountMenu> appMenuTree;

    /**
     * PC菜单树 （含按钮）
     */
    private List<AccountMenu> pcMenuTree;

    /**
     * 小程序按钮权限编码
     */
    private List<String> appButtonPermCodes;

    /**
     * PC按钮权限编码
     */
    private List<String> pcButtonPerCodes;

    /**
     * pc内容页编码
     */
    private List<String> pcContentPageCodes;

    /**
     * 数据权限
     */
    private AccountDataPerm dataPerm;

    /**
     * 机构信息
     */
    private OrgInfo orgInfo;


    private List<String> pcMenuUrlList;
    /**
     * 行政区编码
     */
    private String administrativeRegionCode;
    /**
     * 行政区名称
     */
    private String administrativeRegionName;
    /**
     * 行政区级别
     */
    private Integer administrativeLevel;
    /**
     * 行政区部门编码
     */
    private String administrativeStructCode;
    /**
     * 中心点 经度
     */
    private BigDecimal centerPointLongitude;
    /**
     * 中心点维度
     */
    private BigDecimal centerPointLatitude;

    /**
     * 父行政区编码
     */
    private String parentAdministrativeRegionCode;
}
