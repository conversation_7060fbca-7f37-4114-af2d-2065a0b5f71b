package com.mrcar.gov.common.dto.order.resp;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;


/**
 * <AUTHOR>
 */
@Data
public class GovSocTimeShareDailyListExportDTO {

    @ExcelProperty(value = {"账单单号"},index = 0)
    @ColumnWidth(20)
    private String dailyBillNo;

    @ExcelProperty(value = {"账单日期"},index = 1)
    @ColumnWidth(20)
    private String statDate;

    @ExcelProperty(value = {"管车单位"},index = 2)
    @ColumnWidth(20)
    private String deptName;

    @ExcelProperty(value = {"车辆"},index = 3)
    @ColumnWidth(20)
    private String vehicleLicense;

    @ExcelProperty(value = {"供应商"},index = 4)
    @ColumnWidth(20)
    private String supplierName;


    @ExcelProperty(value = {"用车时长"},index = 5)
    @ColumnWidth(20)
    private String totalDuration;


    @ExcelProperty(value = {"里程数(km)"},index = 6)
    @ColumnWidth(20)
    private String totalMileage;


    @ExcelProperty(value = {"费用合计(元)"},index = 7)
    @ColumnWidth(20)
    private  String totalAmount;

    /**
     * 日最高限额
     */
    @ExcelProperty(value = {"日最高限额(元)"},index = 8)
    @ColumnWidth(20)
    private String limitAmount;

    /**
     * 账单金额
     */
    @ExcelProperty(value = {"账单金额(元)"},index = 9)
    @ColumnWidth(20)
    private String billAmount;

}
