package com.mrcar.gov.common.constant.order;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 社会租赁费用枚举类
 */
@Getter
@AllArgsConstructor
public enum GovPublicSocVehicleFeeItemEnum {
    PARKING_FEE("FD001","停车费"),
    PASS_ROAD_FEE("FD002","过路过桥费"),
    HOTEL_FEE("FD003","住宿费"),
    MEALS_FEE("FD004","餐饮费"),
    WASH_CAR_FEE("FD005","洗车费"),
    ETC_FEE("FD006","ETC费"),
    RENT_FEE("FD007","租金"),
    OTHER_FEE("FD008","其他费用"),
    ;

    private final String code;
    private final String name;

    public static String getName(String code) {
        return Arrays.stream(values()).filter(e -> e.getCode().equals(code)).findFirst().map(GovPublicSocVehicleFeeItemEnum::getName).orElse(null);
    }

}
