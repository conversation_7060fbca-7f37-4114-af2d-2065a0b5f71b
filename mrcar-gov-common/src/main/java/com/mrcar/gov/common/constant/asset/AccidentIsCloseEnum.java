package com.mrcar.gov.common.constant.asset;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Author:  wangM
 * Date:  2025/1/19 14:07
 */
@Getter
@AllArgsConstructor
public enum AccidentIsCloseEnum {
    NO_CLOSE(1, "是"),
    CLOSE(2, "否");

    private final Integer code;
    private final String desc;

    public static String getIsClosedName(Integer code) {
        for (AccidentIsCloseEnum value : AccidentIsCloseEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getDesc();
            }
        }
        return null;
    }
}
