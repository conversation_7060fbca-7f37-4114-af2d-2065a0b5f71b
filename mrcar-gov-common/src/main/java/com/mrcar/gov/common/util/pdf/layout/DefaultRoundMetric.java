package com.mrcar.gov.common.util.pdf.layout;

public class DefaultRoundMetric implements RoundMetric {

    private final MetricValue _top;
    private final MetricValue _bottom;
    private final MetricValue _left;
    private final MetricValue _right;

    public DefaultRoundMetric(MetricValue top,
                              MetricValue bottom,
                              MetricValue left,
                              MetricValue right) {
        this._top = top;
        this._bottom = bottom;
        this._left = left;
        this._right = right;
    }

    @Override
    public MetricValue top() {
        return this._top;
    }

    @Override
    public MetricValue bottom() {
        return this._bottom;
    }

    @Override
    public MetricValue left() {
        return this._left;
    }

    @Override
    public MetricValue right() {
        return this._right;
    }

}
