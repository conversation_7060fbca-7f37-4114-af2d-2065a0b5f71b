package com.mrcar.gov.common.dto.workflow.instance;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * BPM 发送流程实例结束的消息体（包括审批通过、驳回、取消、退回）。
 * <p>
 * 该类用于对接业务侧系统，业务开发需要消费审批结果的MQ消息，消息的Topic是：WorkflowConstant.MQ_TOPIC_APPROVE_RESULT，消息的tag为业务类型（businessType）。
 * <p>
 * 返回的消息体为 BpmMessageSendApproveResultDTO，其中包含以下重要字段：
 * <ul>
 *     <li><b>businessType</b>：业务类型，用于标识审批的业务场景。</li>
 *     <li><b>result</b>：审批状态，指示审批结果。</li>
 *     <li><b>reason</b>：审批原因（可为空），提供审批结果的说明。</li>
 *     <li>其他与审批流程相关的信息，用于确定审批结果和执行相应的后续业务逻辑。</li>
 * </ul>
 * 请确保消费MQ消息后，根据 BpmMessageSendApproveResultDTO 提供的信息，执行相应的业务操作。
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BpmMessageSendApproveResultDTO {

    /**
     * 流程实例的编号
     */
    @NotEmpty(message = "流程实例的编号不能为空")
    private String processInstanceId;

    /**
     * 流程实例的名字
     */
    @NotEmpty(message = "流程实例的名字不能为空")
    private String processInstanceName;

    /**
     * 发起人ID
     */
    @NotNull(message = "发起人的用户id")
    private Long startUserId;

    /**
     * 发起人的昵称
     */
    @NotEmpty(message = "发起人的昵称不能为空")
    private String startUserNickname;

    /**
     * 业务类型
     * 参数参照 {@link com.mrcar.gov.common.dto.workflow.enums.ModelEnum.BusinessTypeEnum}
     */
    @NotNull(message = "业务类型不能为空")
    private Byte businessType;

    /**
     * 业务类型名称
     */
    private String businessTypeName;

    /**
     * 业务单据号
     */
    private String businessNo;

    /**
     * 审批状态
     * 参数参照 {@link com.mrcar.gov.common.dto.workflow.enums.BpmProcessInstanceResultEnum}
     */
    private Byte result;

    /**
     * 审批意见
     */
    private String reason;

    /**
     * 发起人部门名称
     */
    private String startUserDeptName;

    /**
     * 企业ID
     */
    private Integer companyId;

    /**
     * 审批人ID
     */
    private Integer approverId;

    /**
     * 审批人编码
     */
    private String approverCode;

    /**
     * 审批人姓名
     */
    private String approverName;

    /**
     * 审批时间
     */
    private Date approverTime;

    /**
     * 回退节点名称
     */
    private String backNodeName;

}
