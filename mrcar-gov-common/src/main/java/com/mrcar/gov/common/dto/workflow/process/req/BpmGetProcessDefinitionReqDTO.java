package com.mrcar.gov.common.dto.workflow.process.req;

import com.mrcar.gov.common.dto.workflow.BaseDTO;
import lombok.Data;
import javax.validation.constraints.NotEmpty;

/**
 * 请求 DTO - 获取流程定义
 * <p>
 * 该类用于请求获取流程定义的详细信息。
 * </p>
 *
 * <AUTHOR>
 * @date 2024/9/5 15:03
 */
@Data
public class BpmGetProcessDefinitionReqDTO extends BaseDTO {

    /**
     * 父级流程实例编号（发起下级流程时传入）
     * 示例值: 9f9f770d-69c0-11ef-8ea4-96b1e6fae18b
     */
    private String parentProcessInstanceId;

    /**
     * 流程定义编号
     * <p>必填字段</p>
     */
    @NotEmpty(message = "流程定义编号不能为空")
    private String processDefinitionId;

}
