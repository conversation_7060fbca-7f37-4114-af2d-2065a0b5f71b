package com.mrcar.gov.common.dto.report.resp;

import lombok.Data;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/12/18 10:29
 */
@Data
public class GovVehicleDashboardFeeRespDTO {
    /**
     * 合计费用
     */
    private BigDecimal totalFee;
    /**
     * 合计费用(万元)
     */
    private String totalFeeTenThousand;
    /**
     * 费用环比率。正标识上升，负标识下降
     */
    private String feeRatio;
    /**
     * 费用环比率增长.1正增长；0 无增长；-1 负增长
     */
    private Integer feeRatioGrowth;

    /**
     * 费用列表
     */
    private List<VehicleFeeDTO> vehicleFeeList;

    /**
     * 按日期统计-费用列表
     */
    private List<VehicleFeeStatisticsDTO> vehicleFeeStatisticsDTOList;

    public void setTotalFee(BigDecimal totalFee) {
        this.totalFee = totalFee;
        if(Objects.nonNull(totalFee)){
            if(totalFee.compareTo(new BigDecimal("10000"))>=0){
                this.totalFeeTenThousand = totalFee.divide(new BigDecimal("10000"),2, RoundingMode.HALF_UP) + "万元";
            }else{
                this.totalFeeTenThousand = totalFee + "元";
            }
        }


    }

}
