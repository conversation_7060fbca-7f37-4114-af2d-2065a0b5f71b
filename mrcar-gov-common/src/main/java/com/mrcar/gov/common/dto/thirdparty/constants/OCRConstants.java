package com.mrcar.gov.common.dto.thirdparty.constants;

public final class OCRConstants {

    private OCRConstants() {
        throw new IllegalStateException("OCRConstants.class");
    }

    public static final String SUCCESS = "success";

    private static final String APP_CODE = "APPCODE ";

    public static final String AUTHORIZATION_HEADER = "Authorization";

    public static final String IMG_PARAM = "image";

    public static final String CONFIGURE = "configure";

    public static final String TEMPLATE_LIST = "template_list";

    private static final String SIDE_PARAM = "{\"side\":\"%s\",\"ignore_quality\":True}";

    public static final String MULTI_CROP = "{\"multi_crop\": true}";

    public static String getAppCode(String appCode){
        return APP_CODE + appCode;
    }

    public static String getConfigure(String side){
        return String.format(SIDE_PARAM, side);
    }

    public static final String ALI_OCR = "aliOcr";
    /** 地址信息 */
    public static final String ADDRESS = "address";
    /**民族*/
    public static final String NATIONALITY = "nationality";
    /**身份证号、驾驶证号*/
    public static final String NUM = "num";
    /** 性别 */
    public static final String SEX = "sex";
    /**姓名*/
    public static final String NAME = "name";
    /**生日、出生日期*/
    public static final String BIRTH = "birth";
    /****驾驶证上出生日期获取ocr用 对外提供用 birth 字段****/
    public static final String BIRTH_DATE = "birth_date";
    /**有效期开始时间*/
    public static final String STARTDATE = "startDate";
    public static final String START_DATE = "start_date";
    /**有效期截止日期*/
    public static final String ENDDATE = "endDate";
    public static final String END_DATE = "end_date";
    /**签发机关*/
    public static final String ISSUE = "issue";
    /**初次领证时间*/
    public static final String ISSUEDATE = "issueDate";
    public static final String ISSUE_DATE = "issue_date";
    /**驾驶证准驾车型*/
    public static final String VEHICLETYPE = "vehicleType";
    public static final String VEHICLE_TYPE = "vehicle_type";
    /**地址*/
    public static final String ADDR = "addr";
    /**档案编号*/
    public static final String ARCHIVE_NO = "archive_no";
    public static final String ARCHIVENO = "archiveNo";
    /**车牌号*/
    public static final String PLATES = "plates";

    /** */
    public static final String VEHICLE_LICENSE = "vehicle_license";

    /**
     * ocr 接口剩余次数
     */
    public static final String ID_LICENSE_COUNT_KEY = "ocr_id_license_count";

    public static final String DRIVER_LICENSE_COUNT_KEY = "ocr_driver_license_count";

    public static final String CAR_LICENSE_COUNT_KEY = "ocr_car_license_count";

    public static final String VEHICLE_LICENSE_COUNT_KEY = "ocr_vehicle_license_count";

    public static final String TEMPLATE_COUNT_KEY = "ocr_template_license_count";

    public static final String BANK_CARD_COUNT_KEY = "ocr_bank_card_count";

    public static final String CAR_VIN_COUNT_KEY = "ocr_car_vin_count";

    public static final String GENERAL_COUNT_KEY = "ocr_general_count";

    public static final String INVOICE_COUNT_KEY = "ocr_invoice_count";

    public static final String BLICENSE_COUNT_KEY = "ocr_blicense_count";

    public static final String BANK_ACCOUNT_PERMIT_COUNT_KEY = "ocr_bank_account_permit_count_key_count";
}
