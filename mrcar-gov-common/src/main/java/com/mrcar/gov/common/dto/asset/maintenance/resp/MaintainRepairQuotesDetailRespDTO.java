package com.mrcar.gov.common.dto.asset.maintenance.resp;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/1/20 20:42
 */
@Data
public class MaintainRepairQuotesDetailRespDTO {

    /**
     * 关联的报价单ID
     */
    private Long quotedId;

    /**
     * 总价（元），由单价和数量计算得出
     */
    private BigDecimal totalPrice;

    /**
     * 实收费用（含税价，元）
     */
    private BigDecimal actualCost;

    /**
     * 实收工时费 含自定义工时费
     */
    private BigDecimal laborCost;

    /**
     * 实收配件费
     */
    private BigDecimal partsCost;
}
