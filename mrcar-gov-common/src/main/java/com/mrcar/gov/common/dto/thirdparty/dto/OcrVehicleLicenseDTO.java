package com.mrcar.gov.common.dto.thirdparty.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @date 2019-11-05 17:31
 */
@Data
public class OcrVehicleLicenseDTO {

    /**
     * 识别成功与否
     */
    private Boolean success;

    /**
     * 请求对应的唯一表示
     */
    @JSONField(name = "request_id")
    private String requestId;

    /**
     * 车牌号码
     */
    @JSONField(name = "plate_num")
    private String plateNum;

    /**
     * 车辆类型
     */
    @JSONField(name = "vehicle_type")
    private String vehicleType;

    /**
     * 所有人名称
     */
    private String owner;

    /**
     * 使用性质
     */
    @JSONField(name = "use_character")
    private String useCharacter;

    /**
     * 地址
     */
    private String addr;

    /**
     * 品牌型号
     */
    private String model;

    /**
     * 车辆识别代号
     */
    private String vin;

    /**
     * 发动机号码
     */
    @JSONField(name = "engine_num")
    private String engineNum;

    /**
     * 注册日期
     */
    @JSONField(name = "register_date")
    private String registerDate;

    /**
     * 发证日期
     */
    @JSONField(name = "issue_date")
    private String issueDate;

    /**
     * 签发机关
     */
    @JSONField(name = "issue_authority")
    private String issueAuthority;

    /**
     * 是否是复印件
     */
    @JSONField(name = "is_copy")
    private Boolean isCopy;

}
