package com.mrcar.gov.common.dto.workflow.copy;

import lombok.Data;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * ProviderPageParam - 分页参数传输对象
 *
 * <AUTHOR>
 * @date 2023/11/22 13:50
 */
@Data
public class ProviderPageParam extends ProviderDataPermDTO {

    private static final Integer PAGE_NUM = 1;
    private static final Integer PAGE_SIZE = 10;

    /**
     * 页码，从 1 开始
     */
    @NotNull(message = "页码不能为空")
    @Min(value = 1, message = "页码最小值为 1")
    private Integer pageNum = PAGE_NUM;

    /**
     * 每页条数，最大值为 100
     */
    @NotNull(message = "每页条数不能为空")
    @Min(value = 1, message = "页码最小值为 1")
    @Max(value = 100, message = "页码最大值为 100")
    private Integer pageSize = PAGE_SIZE;
}
