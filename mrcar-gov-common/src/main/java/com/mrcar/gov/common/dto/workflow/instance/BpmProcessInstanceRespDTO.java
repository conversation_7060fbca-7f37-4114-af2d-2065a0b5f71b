package com.mrcar.gov.common.dto.workflow.instance;

import com.mrcar.gov.common.dto.workflow.copy.CopyProcessSaveReqDTO;
import lombok.Data;
import java.util.Date;
import java.util.List;

/**
 * 管理后台 - 流程实例的 Response VO
 */
@Data
public class BpmProcessInstanceRespDTO {

    /**
     * 流程实例的编号
     * 示例值: 1024
     */
    private String id;

    /**
     * 流程名称
     * 示例值: 用车
     */
    private String name;

    /**
     * 流程分类
     * 参见 bpm_model_category 数据字典
     * 示例值: 1
     */
    private String category;

    /**
     * 流程实例的状态
     * 参见 bpm_process_instance_status
     * 示例值: 1
     */
    private Integer status;

    /**
     * 流程实例的结果
     * 参见 bpm_process_instance_result
     * 示例值: 2
     */
    private Integer result;

    /**
     * 提交时间
     */
    private Date createTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 提交的表单值
     */
    private String formVariables;

    /**
     * 业务的唯一标识
     * 示例值: 1
     * 例如说，请假申请的编号
     */
    private String businessKey;

    /**
     * 发起流程的用户
     */
    private User startUser;

    /**
     * 流程定义
     */
    private ProcessDefinition processDefinition;

    /**
     * 抄送人集合
     */
    private List<CopyProcessSaveReqDTO.CopyUser> copyUserList;

    /**
     * 用户信息
     */
    @Data
    public static class User {

        /**
         * 用户编号
         * 示例值: 1
         */
        private Long id;

        /**
         * 用户昵称
         * 示例值: hxc
         */
        private String nickname;

        /**
         * 部门编号
         * 示例值: 1
         */
        private Long deptId;

        /**
         * 部门名称
         * 示例值: 研发部
         */
        private String deptName;

    }

    /**
     * 流程定义信息
     */
    @Data
    public static class ProcessDefinition {

        /**
         * 编号
         * 示例值: 1024
         */
        private String id;

        /**
         * 表单类型
         * 参见 bpm_model_form_type 数据字典
         * 示例值: 1
         */
        private Integer formType;

        /**
         * 表单编号
         * 示例值: 1024
         * 在表单类型为 BpmModelFormTypeEnum.CUSTOM 时，必须非空
         */
        private Long formId;

        /**
         * 表单的配置
         * JSON 字符串。在表单类型为 BpmModelFormTypeEnum.CUSTOM 时，必须非空
         */
        private String formConf;

        /**
         * 表单项的数组
         * JSON 字符串的数组。在表单类型为 BpmModelFormTypeEnum.CUSTOM 时，必须非空
         */
        private String formFields;

        private List<String> formFieldsList;

        /**
         * 自定义表单的提交路径，使用 Vue 的路由地址
         * 示例值: /bpm/oa/leave/create
         * 在表单类型为 BpmModelFormTypeEnum.CUSTOM 时，必须非空
         */
        private String formCustomCreatePath;

        /**
         * 自定义表单的查看路径，使用 Vue 的路由地址
         * 示例值: /bpm/oa/leave/view
         * 在表单类型为 BpmModelFormTypeEnum.CUSTOM 时，必须非空
         */
        private String formCustomViewPath;

        /**
         * BPMN XML
         */
        private String bpmnXml;

    }

}
