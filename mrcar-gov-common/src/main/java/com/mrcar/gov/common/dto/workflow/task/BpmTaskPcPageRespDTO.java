package com.mrcar.gov.common.dto.workflow.task;

import com.izu.framework.response.PageDTO;
import com.mrcar.gov.common.dto.workflow.enums.BpmProcessInstanceResultEnum;
import com.mrcar.gov.common.dto.workflow.enums.ModelEnum;
import lombok.Data;
import java.util.Date;

/**
 * 管理后台 - 流程任务的 PC 端分页 Response VO
 */
@Data
public class BpmTaskPcPageRespDTO {

    /**
     * 处理中总数量
     * 示例值: 1
     */
    private Integer inProcessTotal;

    /**
     * 分页对象
     */
    PageDTO<BpmTaskPcDTO> pageDTO;

    @Data
    public static class BpmTaskPcDTO{
        /**
         * 流程任务编号
         * 示例值: 76a2d51a-6e08-11ed-b3e0-8a7d7a584401
         */
        private String taskId;

        /**
         * 流程实例编号
         * 示例值: 76a2d51a-6e08-11ed-b3e0-8a7d7a584401
         * 隐藏
         */
        private String processInstanceId;

        /**
         * 流程名称
         * 示例值: 内部用车申请
         */
        private String processInstanceName;

        /**
         * 办理状态
         * 示例值: 1
         */
        private Byte result;

        /**
         * 办理状态名称
         * 示例值: 处理中
         */
        private String resultStr;

        /**
         * 发起人
         * 示例值: 贺新春
         */
        private String creterName;

        /**
         * 当前审批任务
         * 示例值: 部门负责人
         */
        private String taskName;

        /**
         * 发起时间
         * 示例值: 2022-11-26 00:00:00
         */
        private Date createTime;

        /**
         * 取消按钮是否展示（true展示 false不展示）
         * 示例值: false
         */
        private boolean buttonCancel;

        /**
         * 单据编号
         * 示例值: M2022110100001
         */
        private String businessNo;

        /**
         * 业务类型
         * 示例值: 1
         */
        private Byte businessType;

        /**
         * 业务类型名称
         * 示例值: 内部用车申请
         */
        private String businessTypeName;

        public void setResult(Byte result) {
            this.result = result;
            this.resultStr = BpmProcessInstanceResultEnum.getEnum(result).getDesc();
        }

        public void setBusinessType(Byte businessType) {
            this.businessType = businessType;
            this.businessTypeName = ModelEnum.BusinessTypeEnum.getNameByCode(businessType);
        }
    }

}
