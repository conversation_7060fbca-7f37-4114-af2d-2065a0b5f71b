package com.mrcar.gov.common.constant.user;

import java.util.Objects;

/**
 * <AUTHOR>
 * @description: 角色类型;0-未知;1-系统角色;2-自定义角色
 * @date 2024/11/7 15:14
 */
public enum RoleTypeEnum {
    UNKNOWN(0, "未知"),
    SYSTEM(1, "系统角色"),
    CUSTOM(2, "自定义角色");

    private final Integer code;
    private final String desc;

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    RoleTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    public static String getDescByCode(Integer code) {
        for (RoleTypeEnum typeEnum : RoleTypeEnum.values()) {
            if (Objects.equals(code, typeEnum.getCode())) {
                return typeEnum.getDesc();
            }
        }
        return "";
    }
}
