package com.mrcar.gov.common.constant.order;

import com.google.common.collect.Lists;
import com.mrcar.gov.common.constant.asset.VehicleStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

@Getter
@AllArgsConstructor
public enum GovPublicCarRentTypeEnum {

    // 租赁方式 1:日租 2:分时
    DAILY(1, "日租"),
    TIME_SHARE(2, "分时");;

    private final Integer code;
    private final String name;

    public static String getName(Integer code) {
        return Arrays.stream(values()).filter(e -> e.getCode().equals(code)).findFirst().map(GovPublicCarRentTypeEnum::getName).orElse(null);
    }

    public static List<String> getDescList() {
        return Lists.newArrayList(DAILY.name, TIME_SHARE.name);
    }

    public static GovPublicCarRentTypeEnum getEnumByCode(Integer rentType) {
        return Arrays.stream(values()).filter(e -> e.getCode().equals(rentType)).findFirst().orElse(null);
    }

    public static Integer getCodeByDesc(String desc) {
        for (GovPublicCarRentTypeEnum govPublicCarRentTypeEnum : GovPublicCarRentTypeEnum.values()) {
            if (Objects.equals(govPublicCarRentTypeEnum.getName(), desc)) {
                return govPublicCarRentTypeEnum.getCode();
            }
        }
        return null;
    }
}