package com.mrcar.gov.common.dto.device.resp;

import java.math.BigDecimal;
import java.util.Date;

import com.mrcar.gov.common.enums.device.LocationFlagEnum;
import com.mrcar.gov.common.enums.device.RapidAccelerationEnum;
import com.mrcar.gov.common.enums.device.RapidBrakeEnum;
import com.mrcar.gov.common.enums.device.RapidTurningEnum;

/**
 * @ClassName BdCarLocationsDetailDTO
 * @Description TODO
 * <AUTHOR>
 * @Date 2019/11/21 19:34
 * @Version 1.0
 */
public class BdCarLocationsDetailDTO {
    //百度经度
    private BigDecimal lngBaidu;
    //百度纬度
    private BigDecimal latBaidu;
    //上报时间
    private Date createDate;
    //速度
    private BigDecimal speed;
    //方向角
    private Integer direction;

    /** 急刹⻋  */
    private Integer rapidBrake;
    private String rapidBrakeMsg;
    /** 急加速 */
    private Integer rapidAcceleration;
    private String rapidAccelerationMsg;
    /** 急转弯 */
    private Integer rapidTurning;
    private String rapidTurningMsg;

    //是否是停车点
    private Boolean  stopPoint = false;
    //停车时长
    private Integer  stopDuration=0;
    //停车时长详情
    private String   stopDurationDetail;

    //起点是否是停车点
    private Boolean  startStopPoint =  false;
    //终点是否是停车点
    private Boolean  endStopPoint =  false;

    private Integer locationFlag;
    private String locationFlagName;

    public BigDecimal getLngBaidu() {
        return lngBaidu;
    }

    public void setLngBaidu(BigDecimal lngBaidu) {
        this.lngBaidu = lngBaidu;
    }

    public BigDecimal getLatBaidu() {
        return latBaidu;
    }

    public void setLatBaidu(BigDecimal latBaidu) {
        this.latBaidu = latBaidu;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public BigDecimal getSpeed() {
        return speed;
    }

    public void setSpeed(BigDecimal speed) {
        this.speed = speed;
    }

    public Integer getDirection() {
        return direction;
    }

    public void setDirection(Integer direction) {
        this.direction = direction;
    }

    public Boolean getStopPoint() {
        return stopPoint;
    }

    public void setStopPoint(Boolean stopPoint) {
        this.stopPoint = stopPoint;
    }

    public Integer getStopDuration() {
        return stopDuration;
    }

    public void setStopDuration(Integer stopDuration) {
        this.stopDuration = stopDuration;
    }

    public Boolean getStartStopPoint() {
        return startStopPoint;
    }

    public void setStartStopPoint(Boolean startStopPoint) {
        this.startStopPoint = startStopPoint;
    }

    public Boolean getEndStopPoint() {
        return endStopPoint;
    }

    public void setEndStopPoint(Boolean endStopPoint) {
        this.endStopPoint = endStopPoint;
    }

    public String getStopDurationDetail() {
        return stopDurationDetail;
    }

    public void setStopDurationDetail(String stopDurationDetail) {
        this.stopDurationDetail = stopDurationDetail;
    }

    public Integer getRapidBrake() {
        return rapidBrake;
    }

    public void setRapidBrake(Integer rapidBrake) {
        this.rapidBrake = rapidBrake;
    }

    public Integer getRapidAcceleration() {
        return rapidAcceleration;
    }

    public void setRapidAcceleration(Integer rapidAcceleration) {
        this.rapidAcceleration = rapidAcceleration;
    }

    public Integer getRapidTurning() {
        return rapidTurning;
    }

    public void setRapidTurning(Integer rapidTurning) {
        this.rapidTurning = rapidTurning;
    }

    public String getRapidBrakeMsg() {
        return RapidBrakeEnum.getMsg(this.rapidBrake);
    }

    public void setRapidBrakeMsg(String rapidBrakeMsg) {
        this.rapidBrakeMsg = rapidBrakeMsg;
    }

    public String getRapidAccelerationMsg() {
        return RapidAccelerationEnum.getMsg(this.rapidAcceleration);
    }

    public void setRapidAccelerationMsg(String rapidAccelerationMsg) {
        this.rapidAccelerationMsg = rapidAccelerationMsg;
    }

    public String getRapidTurningMsg() {
        return RapidTurningEnum.getMsg(this.rapidTurning);
    }

    public void setRapidTurningMsg(String rapidTurningMsg) {
        this.rapidTurningMsg = rapidTurningMsg;
    }

    public Integer getLocationFlag() {
        return locationFlag;
    }

    public void setLocationFlag(Integer locationFlag) {
        this.locationFlag = locationFlag;
    }

    public String getLocationFlagName() {
        return LocationFlagEnum.getMsg(this.locationFlag);
    }

    public void setLocationFlagName(String locationFlagName) {
        this.locationFlagName = locationFlagName;
    }
}
