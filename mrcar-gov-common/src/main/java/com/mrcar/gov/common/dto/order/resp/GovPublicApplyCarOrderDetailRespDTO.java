package com.mrcar.gov.common.dto.order.resp;

import com.mrcar.gov.common.constant.order.IsShowEnum;
import com.mrcar.gov.common.dto.device.VehicleDeviceRealtimeStatus;
import com.mrcar.gov.common.dto.iot.resp.CarGpsFenceDTO;
import com.mrcar.gov.common.dto.iot.resp.OfficialVehicleWarnRecordDTO;
import com.mrcar.gov.common.dto.user.resp.GovUserDTO;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@ApiModel("APP订单详情返回参数")
@Data
public class GovPublicApplyCarOrderDetailRespDTO extends GovPublicApplyButtonRespDTO {

    /**
     * 车牌号
     */
    private String vehicleLicense;

    /**
     * 车辆编码
     */
    private String vehicleNo;

    /**
     * 车型
     */
    private String vehicleModelName;

    /**
     * 供应商code
     */
    private String supplierCode;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 调度类型：0-无需调度，1-调度
     */
    private Integer scheduleType;

    /**
     * 调度类型：0-无需调度，1-调度
     */
    private String scheduleTypeStr;

    /**
     * 车辆类型
     */
    private String vehicleTypeStr;

    /**
     * 车辆所有人
     */
    private String vehicleBelongDeptName;

    /**
     * 车辆所有人code
     */
    private String vehicleBelongDeptCode;

    /**
     * 车辆使用人
     */
    private String vehicleUseDeptName;

    /**
     * 分时租赁使用单位
     */
    private String vehicleDeptName;

    /**
     * 调度方式
     */
    private String scheduleStyle;


    /**
     * 核实状态 0:未核实 1:已核实 2:无需核实
     */
    private Integer verifyStatus;


    /**
     * 核实状态 0:未核实 1:已核实 2:无需核实
     */
    private String verifyStatusStr;


//    /**
//     * 下单围栏
//     */
//    private String fenceName;

    /**
     * 说明
     */
    private String tips;


    /**
     * 预计开始时间
     */
    private String expectedPickupTime;

    /**
     * 预计结束时间
     */
    private String expectedReturnTime;

    /**
     * 用车时间拼接 用于审批
     */
    private String expectUseTime;

    /**
     * 核实实际
     */
    private Date verifierTime;

    /**
     * 预计开始时间
     */
    private Date expectedPickupTimeDate;

    /**
     * 预计结束时间
     */
    private Date expectedReturnTimeDate;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 创建时间
     */
    private Date createTime;


    /**
     * 订单类型：1-办公用车，2-无任务用车
     */
    private Integer orderType;

    /**
     * 订单类型：1-办公用车，2-无任务用车
     */
    private String orderTypeStr;

    /**
     * 订单状态(10待审批  20待调度 30待出发 40用车中 50已完成 60已取消 70 审批撤回 80 审批驳回 90已结束)
     */
    private Integer orderStatus;

    /**
     * 订单状态(10待审批  20待调度 30待出发 40用车中 50已完成 60已取消 70 审批撤回 80 审批驳回 90已结束)
     */
    private String orderStatusStr;

    /**
     * 下单人信息 接口拼好返回
     */
    private String createUserInfo;

    /**
     * 下单人单位
     */
    private String createDeptName;

    /**
     * 乘车人信息 接口拼好返回 名称/部门/手机号
     */
    private List<String> passengerUserInfo;

    /**
     * 用车人列表
     */
    private List<GovUserDTO> userInfoList;


    /**
     * 乘车人
     */
    private String passenger;


    /**
     * 乘车人单位
     */
    private String passengerDeptName;

    /**
     * 乘车人部门
     */
    private String passengerStructName;

    /**
     * 司机信息 接口拼好返回
     */
    private String driverInfo;

    /**
     * 驾驶员code
     */
    private String driverUserCode;


    /**
     * 驾驶员名称 用于用车审批
     */
    private String driverUserName;

    /**
     * 驾驶员单位  用于用车审批
     */
    private String driverDeptName;

    /**
     * 驾驶员信息 用于核实的时候匹配下拉框
     */
    private GovUserDTO driverUserInfo;

    /**
     * 调度员信息 接口拼好返回
     */
    private String scheduleInfo;

    /**
     * 订单开始操作人 接口拼好返回
     */
    private String orderStartOperateInfo;

    /**
     * 订单结束操作人 接口拼好返回
     */
    private String orderEndOperateInfo;

    /**
     * 里程
     */
    private BigDecimal totalMileage;


    /**
     * 用车事由
     */
    private String carUseReason;

    /**
     * 用车备注
     */
    private String orderUserMemo;

    /**
     * 实际开始时间
     */
    private Date orderStartTime;

    /**
     * 实际结束时间
     */
    private Date orderEndTime;

    /**
     * 用车时长(小时)
     */
    private String userTime;

    /**
     * 车架号,获取围栏信息时使用
     */
    private String vehicleVin;

    /**
     * 是否展示围栏快照 0:不展示 1:展示
     */
    private Integer showSnapshotFence;

    /**
     * 是否展示行驶轨迹 0:不展示 1:展示
     */
    private Integer showTrail;

    /**
     * 报警记录
     */
    private OfficialVehicleWarnRecordDTO warnRecordDTO;

    /**
     * 实时围栏信息
     */
    private VehicleDeviceRealtimeStatus vehicleRealtimeStatusDTO;

    /**
     * 快照围栏信息
     */
    private List<CarGpsFenceDTO> gpsFenceList;

    /**
     * 车杂费单号
     */
    private String vehicleFeeCode;


    /**
     * 关联车杂费状态 0:未关联 1:已关联
     */
    private Integer relatedFeesStatus;

    /**
     * 费用合计
     */
    private String totalFee;

    /**
     * 租金
     */
    private String rentFee;

    /**
     * 高速费
     */
    private String passRoadFee;

    /**
     * 餐饮费
     */
    private String mealsFee;

    /**
     * 车杂费
     */
    private List<GovPublicVehicleFeeDTO> vehicleFeeList;

    /**
     * 是否展示撤回按钮 0:不展示 1:展示
     */
    private Integer showWithdrawButton = IsShowEnum.NO_SHOW.getCode();


    /**
     * 是否展示开始行程按钮 0:不展示 1:展示
     */
    private Integer showStartTravelButton = IsShowEnum.NO_SHOW.getCode();

    /**
     * 是否展示取消行程按钮 0:不展示 1:展示
     */
    private Integer showCancelTravelButton = IsShowEnum.NO_SHOW.getCode();

    /**
     * 是否展示结束行程按钮 0:不展示 1:展示
     */
    private Integer showFinishTravelButton = IsShowEnum.NO_SHOW.getCode();

    /**
     * 是否展示调度按钮 0:不展示 1:展示
     */
    private Integer showScheduleButton = IsShowEnum.NO_SHOW.getCode();

    /**
     * 是否展示重新调度按钮 0:不展示 1:展示
     */
    private Integer showReScheduleButton = IsShowEnum.NO_SHOW.getCode();

    /**
     * 是否展示审批通过按钮 0:不展示 1:展示
     */
    private Integer showPassButton = IsShowEnum.NO_SHOW.getCode();

    /**
     * 是否展示审批驳回按钮 0:不展示 1:展示
     */
    private Integer showNoPassButton = IsShowEnum.NO_SHOW.getCode();

    /**
     * 是否展示下载派车单按钮 0:不展示 1:展示
     */
    private Integer showDownLoadButton = IsShowEnum.NO_SHOW.getCode();

    /**
     * 车辆服务类型 （1 定点车 2 平台车）
     */
    private Integer vehicleServiceType;

    private String vehicleServiceTypeStr;

    /**
     * 车辆管理部门名称
     */
    private String vehicleManagementDeptName;

    /**
     * 车辆id
     */
    private Integer vehicleId;
}
