package com.mrcar.gov.common.util.pdf.api;

import com.mrcar.gov.common.util.pdf.layout.AlignmentType;

import java.util.ArrayList;
import java.util.List;

public class PdfTableBuilder implements RowLikeElementBuilder {

    float padding = 0;
    float border = 0;
    PdfFontBuilder font = null;
    int fontSize = 10;
    AlignmentType alignment = AlignmentType.LEFT;

    MetricBuilder[] columns;
    final List<PdfTableRowBuilder> rows = new ArrayList<>();

    public static PdfTableBuilder builder() {
        return new PdfTableBuilder();
    }

    // --------------- 边距 -------------

    public PdfTableBuilder padding(float padding) {
        this.padding = padding;
        return this;
    }

    public PdfTableBuilder border(float border) {
        this.border = border;
        return this;
    }

    // ------------ 字体 ---------------

    public PdfTableBuilder font(PdfFontBuilder font) {
        this.font = font;
        return this;
    }

    public PdfTableBuilder fontSize(int fontSize) {
        this.fontSize = fontSize;
        return this;
    }

    // ----------- 对齐方式 -------------

    public PdfTableBuilder alignLeft() {
        this.alignment = AlignmentType.LEFT;
        return this;
    }

    public PdfTableBuilder alignRight() {
        this.alignment = AlignmentType.RIGHT;
        return this;
    }

    public PdfTableBuilder alignCenter() {
        this.alignment = AlignmentType.CENTER;
        return this;
    }

    // ----------- 列 ---------------

    public PdfTableBuilder columns(int count) {
        MetricBuilder[] builders = new MetricBuilder[count];
        for (int i = 0; i < count; i++) {
            builders[i] = MetricBuilder.percentage((float) (1.0 / count));
        }
        return columns(builders);
    }

    public PdfTableBuilder columns(MetricBuilder[] columns) {
        this.columns = columns;
        return this;
    }

    public PdfTableBuilder columns(List<MetricBuilder> columns) {
        this.columns = columns.toArray(new MetricBuilder[0]);
        return this;
    }

    // ------------- 行 ----------------

    public PdfTableBuilder addRow(PdfTableRowBuilder row) {
        this.rows.add(row);
        return this;
    }

    public PdfTableBuilder addRows(List<PdfTableRowBuilder> rows) {
        this.rows.addAll(rows);
        return this;
    }

    @Override
    public ElementType builderType() {
        return ElementType.TABLE;
    }
}
