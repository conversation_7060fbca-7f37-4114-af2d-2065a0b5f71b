package com.mrcar.gov.common.dto.workflow.common;

import lombok.Data;

import java.util.Date;

@Data
public class CustomerStructInfoDTO {
    /**
     * 人员ID
     **/
    private Integer customerId;

    /**
     * 用户姓名
     **/
    private String customerName;

    /**
     * 姓名和手机号
     */
    private String nameAndMobile;

    /**
     * 用户姓名
     **/
    private String customerCode;
    /**
     * 用户手机号；也是登录名
     **/
    private String mobile;

    /** 员工所属部门id **/
    private Integer structId;

    /** 员工所属部门名称 **/
    private String structName;

    /** 员工上级id **/
    private Integer superiorId;

    /** 员工上级姓名 **/
    private String superiorName;

    /** 员工上级联系电话 **/
    private String superiorPhone;

    /** 部门所属城市编码 **/
    private String cityCode;

    /** 部门所属城市名称 **/
    private String cityName;
    /** 是否是初始管理员:1.是;0.不是; 后台初始化管理员时该字段为1;其他情况为0 **/
    //首页改版之后废除改字段。
    @Deprecated
    private Byte isInit;
    /** 用户状态 1 有效 4删除 */
    private Byte customerStatus;
    /** 创建人id **/
    private Integer createId;

    /** 创建人姓名 **/
    private String createName;

    /** 创建时间 **/
    private Date createTime;

    /** 修改人id **/
    private Integer updateId;

    /** 修改人姓名 **/
    private String updateName;

    /** 修改时间 **/
    private Date updateDate;

    /** 员工岗位 */
    private String position;

    /** 员工邮箱 */
    private String email;

    /** 员工工号(自定义客户编号) */
    private String customerNo;


}
