package com.mrcar.gov.common.security.annotation;


import java.lang.annotation.Target;
import java.lang.annotation.Retention;
import java.lang.annotation.ElementType;
import java.lang.annotation.Documented;
import java.lang.annotation.RetentionPolicy;

/**
 * 匿名访问不鉴权注解.
 *
 * <AUTHOR>
 */
@Documented
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface Anonymous {
}
