package com.mrcar.gov.common.config;

import org.bson.types.Decimal128;
import org.springframework.data.convert.ReadingConverter;
import org.springframework.data.convert.WritingConverter;
import org.springframework.core.convert.converter.Converter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/2/13 16:52
 */
@ReadingConverter
public class Decimal128ToBigDecimalConverter implements Converter<Decimal128, BigDecimal> {
    @Override
    public BigDecimal convert(Decimal128 source) {
        return source.bigDecimalValue();
    }
}
