package com.mrcar.gov.common.dto.order.req;

import com.mrcar.gov.common.dto.BaseDTO;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: mengshuang
 * @Date: 2025/4/10 16:19
 * @Param: 保存 修改接口
 * @Return:
 * @Description:
 **/
@Data
public class TimeShareConfigModifyReqDTO extends BaseDTO {
    /**
     * 自增主键,更新,删除必传
     */
    private Integer id;

    /**
     * (社会租赁)供应商code
     */
    private String supplierCode;

    /**
     * (社会租赁)供应商name
     */
    private String supplierName;

    /**
     * 车辆类型
     */
    private Integer vehicleType;

    /**
     * 车辆类型名称
     */
    private String vehicleTypeName;

    /**
     * 车辆品牌id
     */
    private Integer vehicleBrandId;

    /**
     * 车辆品牌
     */
    private String vehicleBrandName;

    /**
     * 车系code码
     */
    private Integer vehicleSeriesId;

    /**
     * 车系
     */
    private String vehicleSeriesName;

    /**
     * 是否配置多时段 0:未配置  1:已配置
     */
    @NotNull
    private Integer isTimePeriods;

    /**
     * 是否配置限额规则 0:未配置  1:已配置
     */
    @NotNull
    private Integer isLimitRule;

    /**
     * 当月最高限额
     */
    private BigDecimal monthLimit;

    /**
     * 当天最高限额
     */
    private BigDecimal dailyLimit;
    /**
     * 定价类型 0按车辆定价  1 按车系定价
     */
    private Integer priceType;

    /**
     * 分时价格信息
     */
    @NotNull
    List<TimeShareConfigModifyDetailReqDTO> detail;


}
