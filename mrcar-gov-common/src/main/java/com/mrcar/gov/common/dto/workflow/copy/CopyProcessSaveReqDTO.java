package com.mrcar.gov.common.dto.workflow.copy;

import lombok.Data;
import java.util.List;

/**
 * 抄送保存请求参数
 *
 * <AUTHOR>
 * @date 2024/9/3 15:03
 */
@Data
public class CopyProcessSaveReqDTO {

    /** 抄送人集合 */
    private List<CopyUser> copyUserList;

    /** 流程实例id */
    private String processInstanceId;

    /**
     * 抄送人信息内部类
     */
    @Data
    public static class CopyUser {

        /** 抄送人姓名 */
        private String copyUserName;

        /** 抄送人电话 */
        private String copyUserMobile;

        /** 抄送人Id */
        private Integer copyUserId;
    }
}
