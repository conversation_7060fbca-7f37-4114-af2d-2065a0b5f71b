package com.mrcar.gov.common.dto.order.resp;
import com.mrcar.gov.common.dto.iot.resp.CarGpsFenceDTO;
import com.mrcar.gov.common.dto.iot.resp.OfficialVehicleWarnRecordDTO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description: 对公公务用车订单列表
 * @date 2024/8/16 9:45
 */
@Data
public class GovPublicCarOrderDetailInfoRespDTO {

    /**
     * 订单基本信息
     */
    private GovPublicCarOrderInfoDTO orderInfo;

    /**
     * 用车信息
     */
    private GovPublicVehicleInfoDTO vehicleInfo;

    /**
     * 操作日志
     */
    private List<GovPublicCarOrderOperationLogDTO> operationLogList;

    /**
     * 订单报警记录
     */
    private OfficialVehicleWarnRecordDTO warnRecord;

    /**
     * 围栏信息
     */
    private List<CarGpsFenceDTO> gpsFenceList;
}
