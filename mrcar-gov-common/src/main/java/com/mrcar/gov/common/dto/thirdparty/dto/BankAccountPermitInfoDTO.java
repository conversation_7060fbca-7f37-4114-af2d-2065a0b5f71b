package com.mrcar.gov.common.dto.thirdparty.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

@Data
public class BankAccountPermitInfoDTO {


    /**
     * code
     */
    @JSONField(name ="code")
    private int code;
    /**
     * data
     */
    @JSONField(name ="data")
    private DataBean data;
    /**
     * requestId
     */
    @JSONField(name ="requestId")
    private String requestId;

    @Data
    public static class DataBean {
        /**
         * data
         */
        @JSONField(name ="data")
        private InnerDataBean data;
        /**
         * orgWidth
         */
        @JSONField(name ="orgWidth")
        private int orgWidth;
        /**
         * requestId
         */
        @JSONField(name ="requestId")
        private String requestId;
        /**
         * width
         */
        @JSONField(name ="width")
        private int width;
        /**
         * orgHeight
         */
        @JSONField(name ="orgHeight")
        private int orgHeight;
        /**
         * prismKeyvalueinfo
         */
        @JSONField(name ="prism_keyValueInfo")
        private List<PrismKeyValueInfoBean> prismKeyvalueinfo;
        /**
         * height
         */
        @JSONField(name ="height")
        private int height;


        @Data
        public static class InnerDataBean {
            /**
             * bankAccount
             */
            @JSONField(name ="bankAccount")
            private String bankAccount;
            /**
             * legalRepresentative
             */
            @JSONField(name ="legalRepresentative")
            private String legalRepresentative;
            /**
             * permitNumber
             */
            @JSONField(name ="permitNumber")
            private String permitNumber;
            /**
             * approvalNumber
             */
            @JSONField(name ="approvalNumber")
            private String approvalNumber;
            /**
             * title
             */
            @JSONField(name ="title")
            private String title;
            /**
             * depositaryBank
             */
            @JSONField(name ="depositaryBank")
            private String depositaryBank;
            /**
             * customerName
             */
            @JSONField(name ="customerName")
            private String customerName;
        }

        @Data
        public static class PrismKeyValueInfoBean {
            /**
             * valuePos
             */
            @JSONField(name ="valuePos")
            private List<ValuePosBean> valuePos;
            /**
             * keyProb
             */
            @JSONField(name ="keyProb")
            private int keyProb;
            /**
             * valueProb
             */
            @JSONField(name ="valueProb")
            private int valueProb;
            /**
             * value
             */
            @JSONField(name ="value")
            private String value;
            /**
             * key
             */
            @JSONField(name ="key")
            private String key;


            @Data
            public static class ValuePosBean {
                /**
                 * x
                 */
                @JSONField(name ="x")
                private int x;
                /**
                 * y
                 */
                @JSONField(name ="y")
                private int y;
            }
        }
    }
}
