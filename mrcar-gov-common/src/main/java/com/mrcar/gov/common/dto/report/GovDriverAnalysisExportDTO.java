package com.mrcar.gov.common.dto.report;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/12/16 10:01
 */
@HeadStyle
@HeadFontStyle
@Data
public class GovDriverAnalysisExportDTO {

    @ExcelIgnore
    private String driverUserCode;
    /**
     * 驾驶员用户名称
     */
    @ExcelProperty(value = "驾驶员姓名")
    @ColumnWidth(20)
    private String driverUserName;

    @ExcelIgnore
    private String driverDeptCode;
    /**
     * 驾驶员所属于单位名称
     */
    @ExcelProperty(value = "所属单位")
    @ColumnWidth(20)
    private String driverDeptName;


    /**
     * 订单数
     */
    @ExcelProperty(value = "订单数")
    @ContentStyle(dataFormat = 49)
    @ColumnWidth(20)
    private Integer orderNum;

    /**
     * 出车天数
     */
    @ExcelProperty(value = "出车天数")
    @ContentStyle(dataFormat = 49)
    @ColumnWidth(20)
    private Integer drivingDays;
    /**
     *
     */
    @ExcelProperty(value = "订单总里程（KM）")
    @ContentStyle(dataFormat = 49)
    @ColumnWidth(20)
    private Long orderTotalMileage;

    /**
     * 订单总时长（小时）
     */
    @ExcelProperty(value = "订单总时长（小时）")
    @ContentStyle(dataFormat = 49)
    @ColumnWidth(20)
    private Long orderTotalDuration;


    /**
     * 总车杂费用
     */
    @ExcelProperty(value = "总车杂费用")
    @ContentStyle(dataFormat = 49)
    @ColumnWidth(20)
    private BigDecimal totalFee;
    /**
     * 加油费
     */
    @ExcelProperty(value = "加油费")
    @ContentStyle(dataFormat = 49)
    @ColumnWidth(20)
    private BigDecimal oilFee;

    /**
     * 停车费
     */
    @ExcelProperty(value = "停车费")
    @ContentStyle(dataFormat = 49)
    @ColumnWidth(20)
    private BigDecimal parkingFee;
    /**
     * 过路过桥费
     */
    @ExcelProperty(value = "过路过桥费")
    @ContentStyle(dataFormat = 49)
    @ColumnWidth(20)
    private BigDecimal tollFee;
    /**
     * 其他费用
     */
    @ExcelProperty(value = "其他费用")
    @ContentStyle(dataFormat = 49)
    @ColumnWidth(20)
    private BigDecimal otherFee;



}
