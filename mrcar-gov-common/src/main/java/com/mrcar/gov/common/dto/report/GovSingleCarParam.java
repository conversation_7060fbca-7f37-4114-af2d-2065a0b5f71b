package com.mrcar.gov.common.dto.report;

import com.google.gson.annotations.Expose;
import com.mrcar.gov.common.dto.PageParamDTO;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

@Data
public class GovSingleCarParam extends PageParamDTO {
    @Expose(serialize = false)
    private Integer pageNum = 1;

    /**
     * 开始日期
     */
    private LocalDate startDate = LocalDate.of(2024, 11, 27);

    /**
     * 结束日期
     */
    private LocalDate endDate = LocalDate.of(2024, 11, 28);

    /**
     * 所属单位性质
     */
    private Integer structType;

    /**
     * 管理部门编码
     */
    private String manageDeptCode;

    /**
     * 车辆类型
     */
    private Integer vehicleType;

    /**
     * 车辆使用性质
     */
    private Integer vehicleUseType;

    /**
     * 车牌号
     */
    private String vehicleLicense;

    /**
     * 机构编码
     */
    private String structCode;

    /**
     * 是否生成汇总，导出时不生成
     * #ignore
     */
    private Boolean isGenerateSummary = true;

    /**
     * 管车部门类型
     */
    private Integer manageCarType;

    @Expose(serialize = false)
    private List<String> deptCodes;


    public void setPageNum(Integer pageNum){
        this.pageNum = pageNum;
        setPage(pageNum);
    }
}
