package com.mrcar.gov.common.constant.asset;

import java.util.regex.Pattern;

/**
 * <AUTHOR> dongxiya
 * @create 2022/12/29 15:23
 */
public class AssetConstant {
    /**
     * 车牌号正则
     */
    public static final Pattern vehicleLicensePattern = Pattern.compile("^(([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领测][A-Z](([0-9]{5}[ABCDEFGHJK])|([ABCDEFGHJK]([A-HJ-NP-Z0-9])[0-9]{4})))|([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领测]\\d{3}\\d{1,3}[领])|([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领测][A-Z][A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳使领]))$");

    /**
     * 去除小数点后末位零
     */
    public static final String replaceLastZero = "([0-9]+(\\.[0-9]*[1-9])?)\\.?0*";

    /**
     * 车牌号正则，用于匹配一个文本中的所有车牌号
     */
    public static final Pattern vehicleLicensePatternAll = Pattern.compile("(([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领测][A-Z](([0-9]{5}[ABCDEFGHJK])|([ABCDEFGHJK]([A-HJ-NP-Z0-9])[0-9]{4})))|([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领测]\\d{3}\\d{1,3}[领])|([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领测][A-Z][A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳使领]))");


    public static void main(String[] args) {
        //京A181382
        boolean 京A181382 = vehicleLicensePattern.matcher("京A218138").find();
        System.out.println(京A181382);
    }

}
