package com.mrcar.gov.common.dto.business.request;

import com.google.gson.annotations.Expose;
import com.mrcar.gov.common.dto.PageParamDTO;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/12/27 18:41
 */
@Data
public class GovMsgNoticeListReqDTO extends PageParamDTO {




    /**
     * 公告通知标题
     */
    private String noticeName;

    /**
     * 发布人编码
     */
    private String publisherCode;

    /**
     * 发布单位编码
     */
    private String publishDeptCode;

    /**
     * 发布开始时间
     */
    private Date publishStartTime;
    /**
     * 发布结束时间
     */
    private Date publishEndTime;

    @Expose(serialize = false)
    private Integer publishStatus;


    /**
     * 状态 0:删除 1:正常
     */
    private Integer noticeStatus;


}
