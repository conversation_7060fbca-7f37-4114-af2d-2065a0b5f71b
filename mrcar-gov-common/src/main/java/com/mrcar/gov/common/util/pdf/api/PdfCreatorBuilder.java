package com.mrcar.gov.common.util.pdf.api;

import com.google.common.collect.Lists;
import com.mrcar.gov.common.util.pdf.*;
import com.mrcar.gov.common.util.pdf.layout.DisplayFont;
import com.mrcar.gov.common.util.pdf.layout.DisplayImage;
import com.mrcar.gov.common.util.pdf.layout.MetricValue;
import com.mrcar.gov.common.util.pdf.layout.RoundMetric;
import com.mrcar.gov.common.util.pdf.model.*;
import org.apache.pdfbox.pdmodel.PDDocument;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

public class PdfCreatorBuilder {

    List<PdfPageBuilder> pages = new ArrayList<>();

    public PdfCreatorBuilder addPage(PdfPageBuilder page) {
        this.pages.add(page);
        return this;
    }

    // 转换为element对象
    public PdfCreator build() {
        List<PageElement> list = new ArrayList<>();
        PDDocument document = new PDDocument();
        for (PdfPageBuilder page : pages) {
            // page
            PageElement root = PageElement.create(page.rectangle,
                    RoundMetric.percentage(page.paddingTopBottom, page.paddingLeftRight));
            // span
            for (RowLikeElementBuilder row : page.elements) {
                // table
                if (row.builderType() ==
                        RowLikeElementBuilder.ElementType.TABLE) {
                    PdfTableBuilder tableBuilder = (PdfTableBuilder) row;
                    // font
                    PdfFontBuilder font = tableBuilder.font;
                    // columns
                    MetricBuilder[] columns = tableBuilder.columns;
                    MetricValue[] metrics = new MetricValue[columns.length];
                    for (int i = 0; i < columns.length; i++) {
                        MetricBuilder column = columns[i];
                        metrics[i] = MetricValue.create(column.type, column.value);
                    }
                    // table
                    TableElement table =
                            TableElement.create(root,
                                    tableBuilder.padding,
                                    tableBuilder.border,
                                    DisplayFont.create(font.source, document, tableBuilder.fontSize),
                                    tableBuilder.alignment,
                                    metrics);
                    root.append(table);
                    // rows
                    for (PdfTableRowBuilder rowBuilder : tableBuilder.rows) {
                        TableRowElement tableRow = TableRowElement.create(table);
                        table.append(tableRow);
                        for (PdfTableCellBuilder cellBuilder : rowBuilder.cells) {
                            switch (cellBuilder.type) {
                                case TEXT:
                                {
                                    TableTextCellElement tableCell =
                                            TableTextCellElement.create(tableRow,
                                                    Collections.singletonList(cellBuilder.text));
                                    tableRow.append(tableCell);
                                    break;
                                }
                                case IMAGE:
                                {
                                    TableImageCellElement tableCell =
                                            TableImageCellElement.create(tableRow,
                                                    DisplayImage.create(cellBuilder.image, document));
                                    tableRow.append(tableCell);
                                    break;
                                }
                            }
                        }
                    }
                } else {
                    // span
                    PdfSpanBuilder spanBuilder = (PdfSpanBuilder) row;
                    SpanElement spanElement =
                            SpanElement.create(root,
                                    MetricValue.create(spanBuilder.height.type, spanBuilder.height.value),
                                    RoundMetric.absolute(spanBuilder.marginTopBottom, spanBuilder.marginLeftRight));
                    root.append(spanElement);
                    // div
                    for (PdfDivBuilder element : spanBuilder.elements) {
                        DivElement divElement = DivElement.create(spanElement,
                                MetricValue.create(element.width.type, element.width.value),
                                MetricValue.adaptive());
                        spanElement.append(divElement);
                        LeafLikeElementBuilder elementBuilder = element.element;
                        // text
                        switch (elementBuilder.builderType()) {
                            case TEXT:
                            {
                                PdfTextBuilder textBuilder = (PdfTextBuilder) elementBuilder;
                                String text = textBuilder.text;
                                List<String> textList = Lists.newArrayList();
                                if(text != null && text.length() > 0){
                                    if(text.contains("\r\n")){
                                        String[] textArr = text.split("\r\n");
                                        textList.addAll(Arrays.asList(textArr));
                                    }else{
                                        textList.add(text);
                                    }
                                }
                                TextElement textElement =
                                        TextElement.create(divElement,
                                                MetricValue.full(),
                                                MetricValue.adaptive(),
                                                textList,
                                                5,
                                                DisplayFont.create(textBuilder.font.source, document, textBuilder.fontSize),
                                                textBuilder.alignment);
                                divElement.append(textElement);
                                break;
                            }
                            default:
                                break;
                        }
                    }
                }
            }
            list.add(root);
        }
        return new PdfCreator(document, list);
    }

}
