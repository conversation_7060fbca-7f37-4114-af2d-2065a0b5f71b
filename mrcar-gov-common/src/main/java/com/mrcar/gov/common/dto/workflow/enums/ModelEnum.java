package com.mrcar.gov.common.dto.workflow.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import java.util.Arrays;
import java.util.Objects;

public class ModelEnum {

    @Getter
    public enum CategoryEnum{

        DEFAULT("1", "业务流程"),
        OA("2", "自定义流程");

        private String code;
        private String name;

        CategoryEnum(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public static CategoryEnum codeOf(String code) {
            for(CategoryEnum e : CategoryEnum.values() ) {
                if (e.code.equals(code)) {
                    return e;
                }
            }
            return null;
        }

        public static String getNameByCode(String code) {
            if (StringUtils.isBlank(code)) {
                return "";
            }
            for (CategoryEnum e : CategoryEnum.values()) {
                if (code.equals(e.code)) {
                    return e.name;
                }
            }
            return "";
        }
    }

    @Getter
    public enum BusinessTypeEnum{

        // -----------业务流程-业务类型---------------
        CAR_USE_APPROVAL((byte)1, "用车审批", CategoryEnum.DEFAULT.getCode(), true, true, true, false),
        MAINTENANCE_APPROVAL((byte)2, "维保审批", CategoryEnum.DEFAULT.getCode(), true, true, true, false),
        ACCIDENT_APPROVAL((byte)3, "事故审批", CategoryEnum.DEFAULT.getCode(), true, false, true, true),
        REFUELING_APPROVAL((byte)4, "加油审批", CategoryEnum.DEFAULT.getCode(), true, true, true, false),
        INSURANCE_APPROVAL((byte)5, "保险审批", CategoryEnum.DEFAULT.getCode(), true, true, true, false),
        DRIVER_FEE_APPROVAL((byte)6, "车杂费审批", CategoryEnum.DEFAULT.getCode(), true, true, true, false),
        DOCUMENT_APPROVAL((byte)7, "发文审批", CategoryEnum.DEFAULT.getCode(), true, true, true, false),
        EQUIPMENT_UPDATE_APPROVAL((byte)8, "配备审批", CategoryEnum.DEFAULT.getCode(), true, true, true, false),
        DISPOSAL_ALLOCATION_APPROVAL((byte)9, "处置审批", CategoryEnum.DEFAULT.getCode(), true, true, true, false),
        DISPOSAL_ALLOCATION_HANDLING_APPROVAL((byte)10, "处置办理审批", CategoryEnum.DEFAULT.getCode(), true, true, true, false),
        CAR_SOCIAL_RENT_APPROVAL((byte)11, "社会租赁", CategoryEnum.DEFAULT.getCode(), true, true, true, false),
        UPDATE_APPROVAL((byte)12, "更新审批", CategoryEnum.DEFAULT.getCode(), true, true, true, false),
        ALLOCATION_APPROVAL((byte)13, "调拨审批", CategoryEnum.DEFAULT.getCode(), true, true, true, false),
        ALLOCATION_HANDLING_APPROVAL((byte)14, "调拨办理审批", CategoryEnum.DEFAULT.getCode(), true, true, true, false),
        QUOTATION_APPROVAL((byte)15, "报价审批", CategoryEnum.DEFAULT.getCode(), true, true, true, false),

        // -----------自定义流程（OA）-业务类型---------------
        GENERAL((byte)24, "通用事项审批", CategoryEnum.OA.getCode(),true,true,true,false);

        private Byte code;
        private String name;

        //流程分类
        private String category;

        // 是否支持通过
        private Boolean approve;
        //是否支持不通过"
        private Boolean reject;
        //是否支持转办
        private Boolean transfer;
        //是否支持回退
        private Boolean back;

        BusinessTypeEnum(Byte code, String name,String category, Boolean approve, Boolean reject, Boolean transfer, Boolean back) {
            this.code = code;
            this.name = name;
            this.category =category;
            this.approve = approve;
            this.reject = reject;
            this.transfer = transfer;
            this.back = back;
        }

        public static BusinessTypeEnum codeOf(Byte code) {
            for(BusinessTypeEnum e : BusinessTypeEnum.values() ) {
                if (e.code.equals(code)) {
                    return e;
                }
            }
            return null;
        }

        public static String getNameByCode(Byte code) {
            if (code == null) {
                return "";
            }
            for (BusinessTypeEnum e : BusinessTypeEnum.values()) {
                if (code.equals(e.code)) {
                    return e.name;
                }
            }
            return "";
        }

        public static BusinessTypeEnum getDefault(Byte code) {
            return Arrays.stream(BusinessTypeEnum.values()).filter(e-> Objects.equals(CategoryEnum.DEFAULT.getCode(),e.getCategory())).filter(e->Objects.equals(code,e.getCode())).findFirst().orElse(null);
        }
    }

    @Getter
    @Deprecated
    public enum BusinessTypeDefaultEnum{

        // -----------业务流程-业务类型---------------
        CAR_USE_APPROVAL((byte)1, "用车审批"),
        MAINTENANCE_APPROVAL((byte)2, "维保审批"),
        ACCIDENT_APPROVAL((byte)3, "事故审批"),
        REFUELING_APPROVAL((byte)4, "加油审批"),
        INSURANCE_APPROVAL((byte)5, "保险审批"),
        DRIVER_FEE_APPROVAL((byte)6, "车杂费审批"),
        DOCUMENT_APPROVAL((byte)7, "发文审批"),
        EQUIPMENT_UPDATE_APPROVAL((byte)8, "配备更新审批"),
        DISPOSAL_ALLOCATION_APPROVAL((byte)9, "处置/调拨审批"),
        DISPOSAL_ALLOCATION_HANDLING_APPROVAL((byte)10, "处置/调拨办理审批"),
        CAR_SOCIAL_RENT_APPROVAL((byte)11, "社会租赁"),
        ;

        private Byte code;
        private String name;

        BusinessTypeDefaultEnum(Byte code, String name) {
            this.code = code;
            this.name = name;
        }

        public static BusinessTypeDefaultEnum codeOf(Byte code) {
            for(BusinessTypeDefaultEnum e : BusinessTypeDefaultEnum.values() ) {
                if (e.code.equals(code)) {
                    return e;
                }
            }
            return null;
        }

        public static String getNameByCode(Byte code) {
            if (code == null) {
                return "";
            }
            for (BusinessTypeDefaultEnum e : BusinessTypeDefaultEnum.values()) {
                if (code.equals(e.code)) {
                    return e.name;
                }
            }
            return "";
        }
    }

    @Getter
    @Deprecated
    public enum BusinessTypeOAEnum{

        // -----------自定义流程（OA）-业务类型---------------
        GENERAL((byte)24, "通用事项审批");

        private Byte code;
        private String name;

        BusinessTypeOAEnum(Byte code, String name) {
            this.code = code;
            this.name = name;
        }

        public static BusinessTypeOAEnum codeOf(Byte code) {
            for(BusinessTypeOAEnum e : BusinessTypeOAEnum.values() ) {
                if (e.code.equals(code)) {
                    return e;
                }
            }
            return null;
        }

        public static String getNameByCode(Byte code) {
            if (code == null) {
                return "";
            }
            for (BusinessTypeOAEnum e : BusinessTypeOAEnum.values()) {
                if (code.equals(e.code)) {
                    return e.name;
                }
            }
            return "";
        }
    }

}
