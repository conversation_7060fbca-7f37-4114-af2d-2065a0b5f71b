package com.mrcar.gov.common.enums.device;

import lombok.Getter;
import lombok.Setter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @description: 开关锁类型 1 网络开关锁 2 蓝牙开关锁
 * @date 2025/6/23 18:11
 */
public enum GovPublicCarLockTypeEnum {
    NETWORK_LOCK(1,"网络开关锁"),
    BLUE_LOCK(2,"蓝牙开关锁");
    

    @Getter
    @Setter
    private Integer code;
    @Getter
    @Setter
    private String  msg;

    GovPublicCarLockTypeEnum(Integer code, String msg){
        this.code = code;
        this.msg = msg;
    }

    public static GovPublicCarLockTypeEnum getEnumByCode(Integer code) {
        return Arrays.stream(GovPublicCarLockTypeEnum.values())
                .filter(govPublicCarLockTypeEnum -> govPublicCarLockTypeEnum.code.equals(code))
                .findFirst()
                .orElse(null);
    }

    public static String getEnumMsgByCode(Integer code) {
        return Arrays.stream(GovPublicCarLockTypeEnum.values())
                .filter(govPublicCarLockTypeEnum -> govPublicCarLockTypeEnum.code.equals(code))
                .map(GovPublicCarLockTypeEnum::getMsg)
                .findFirst()
                .orElse("");
    }
}
