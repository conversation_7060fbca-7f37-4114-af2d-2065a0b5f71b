package com.mrcar.gov.common.util.pdf.model;

import com.mrcar.gov.common.util.pdf.layout.MetricValue;
import com.mrcar.gov.common.util.pdf.layout.RoundMetric;

public class DivElement extends AbstractBlockElement {

    private static final String NODE_NAME = "Div";

    protected DivElement(RowElement parent,
                         MetricValue width,
                         MetricValue height,
                         RoundMetric margin) {
        super(parent, width, height, margin);
    }

    public static DivElement create(RowElement row,
                                    MetricValue width,
                                    MetricValue height) {
        return new DivElement(row, width, height, RoundMetric.empty());
    }

    public static DivElement create(RowElement row,
                                    MetricValue width,
                                    MetricValue height,
                                    RoundMetric margin) {
        return new DivElement(row, width, height, margin);
    }

    @Override
    public String name() {
        return NODE_NAME;
    }

}
