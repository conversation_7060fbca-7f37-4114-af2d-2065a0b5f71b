package com.mrcar.gov.common.dto.workflow.process.req;

import com.mrcar.gov.common.dto.workflow.BaseDTO;
import lombok.Data;
import javax.validation.constraints.NotEmpty;

/**
 * 请求 DTO - 获取所有流程实例详情
 * <p>
 * 该类用于请求获取所有流程实例的详细信息。
 * </p>
 *
 * <AUTHOR>
 * @date 2024/9/5 16:46
 */
@Data
public class AppBpmListAllProcessInstanceDetailReqDTO extends BaseDTO {

    /**
     * 流程实例编号
     * <p>必填字段</p>
     */
    @NotEmpty(message = "流程实例编号不能为空")
    private String processInstanceId;

}
