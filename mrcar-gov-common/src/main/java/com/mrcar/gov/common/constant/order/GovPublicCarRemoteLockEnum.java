package com.mrcar.gov.common.constant.order;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;


/**
 * 远程开关锁 枚举
 */
@Getter
@AllArgsConstructor
public enum GovPublicCarRemoteLockEnum {

    // 远程开关锁 1:开启 2:关闭
    LOCK(0, "关闭"),

    OPEN(1, "开启");

    private final Integer code;
    private final String name;

    public static String getName(Integer code) {
        return Arrays.stream(values()).filter(e -> e.getCode().equals(code)).findFirst().map(GovPublicCarRemoteLockEnum::getName).orElse(null);
    }

    public static List<String> getDescList() {
        return Lists.newArrayList(OPEN.name, LOCK.name);
    }

    public static GovPublicCarRemoteLockEnum getEnumByCode(Integer rentType) {
        return Arrays.stream(values()).filter(e -> e.getCode().equals(rentType)).findFirst().orElse(null);
    }

    public static Integer getCodeByDesc(String desc) {
        for (GovPublicCarRemoteLockEnum govPublicCarRemoteLockEnum : GovPublicCarRemoteLockEnum.values()) {
            if (Objects.equals(govPublicCarRemoteLockEnum.getName(), desc)) {
                return govPublicCarRemoteLockEnum.getCode();
            }
        }
        return null;
    }

}