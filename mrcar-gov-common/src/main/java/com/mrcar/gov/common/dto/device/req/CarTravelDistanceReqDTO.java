package com.mrcar.gov.common.dto.device.req;

import java.util.List;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import lombok.Data;

/**
 * <AUTHOR> ON 2023/2/15.
 */
@Data
public class CarTravelDistanceReqDTO {
	
	
	/**
     * 车辆唯一编号
     */
    @NotEmpty(message = "车辆唯一编号不能为空")
    private String vehicleNo;
    
	/**
     * 设备编码集合
     * 上层根据vehicleNo查询下绑定所有设备deviceNo
     * 否则iov层有引入ass模块依赖太重
     */
    @NotNull(message = "设备编码集合不能为空")
    private List<String> deviceNoList;
    
    /**
     * 车牌号
     */
    @NotEmpty(message = "车牌号不能为空")
    private String carNo;
    
    /**
     * SIM卡号
     */
    @NotEmpty(message = "SIM卡号不能为空")
    private String simNo;
    
    /**
     * 开始时间
     */
    @NotEmpty(message = "开始时间不能为空")
    private String beginCreateDate;
    /**
     * 结束时间
     */
    @NotEmpty(message = "结束时间不能为空")
    private String endCreateDate;
    
}
