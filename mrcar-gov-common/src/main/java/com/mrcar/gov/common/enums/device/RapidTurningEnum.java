package com.mrcar.gov.common.enums.device;

public enum RapidTurningEnum {
    NOT_SUPPORT(0,"不支持"),
    NORMAL(1,"没发生急转弯"),
    WARNING(2,"已发生急转弯");;



    public Integer code;
    public String msg;


    RapidTurningEnum(Integer code, String msg)
    {
        this.code =code;
        this.msg = msg;
    }

    public static String getMsg(Integer code){
        if(code==null) return null;
        for(RapidTurningEnum e: RapidTurningEnum.values()){
            if(code == e.getCode()){
                return e.getMsg();
            }
        }
        return null;
    }


    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}
