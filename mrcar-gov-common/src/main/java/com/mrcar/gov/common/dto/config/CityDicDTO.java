package com.mrcar.gov.common.dto.config;

import lombok.Data;

import java.io.Serializable;

/**
 * 城市字典数据.
 *
 * <AUTHOR>
 * @date 2024-11-15
 */
@Data
public class CityDicDTO implements Serializable {

    /**
     * ID
     */
    private Integer id;

    /**
     * 城市编码
     */
    private Integer cityCode;

    /**
     * 高德编码
     */
    private String amapCode;

    /**
     * 百度编码
     */
    private String baiduCode;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 城市拼音
     */
    private String citySpell;

    /**
     * 首字母
     */
    private String firstLetter;

    /**
     * 省份编码
     */
    private Integer provinceCode;

    /**
     * 邮编
     */
    private String zipCode;

    /**
     * 高德中心点经纬度坐标
     */
    private String coordinatePointAmap;

    /**
     * 百度中心点经纬度坐标
     */
    private String coordinatePointBaidu;

}
