package com.mrcar.gov.common.dto.thirdparty.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> on 2019/11/26.
 */
@Data
public class OcrRequestDTO {
    /**
     * 用户ID
     */
    private long userId;
    /**
     * 图片地址
     */
    @NotBlank(message = "图片地址不能为空")
    private String photoUrl;
    /**
     * 正反面
     */
    private int side;
    /**
     * OCR识别类型
     */
    @NotNull(message = "OCR识别类型不能为空")
    private Integer type;
}
