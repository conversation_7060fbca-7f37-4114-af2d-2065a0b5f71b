package com.mrcar.gov.common.util.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.util.excel.ExSheet;
import com.izu.framework.web.util.excel.ExcelTool;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpMethod;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;

@Slf4j
public class EasyExcelUtil {
    /**
     * 获取Excel单元格的索引
     *
     * @param obj        JavaBean对象
     * @param fieldValue JavaBean字段值
     * @return
     */
    public static Integer getCellIndex(Object obj, String fieldValue) {
        try {
            Field declaredField = obj.getClass().getDeclaredField(fieldValue);
            ExcelProperty annotation = declaredField.getAnnotation(ExcelProperty.class);
            if (annotation == null) {
                return null;
            }
            return annotation.index();
        } catch (NoSuchFieldException e) {
            log.error("error:", e);
        }
        return null;
    }

    public static InputStream getExcelInputStream(final String excelUrl, AtomicReference<String> atomicReferenceFilePath) {
        String LOGTAG = "importStaff";
        if (excelUrl.toLowerCase().startsWith("http")) {//--->>>URL地址
            final String baseDir = System.getProperty("java.io.tmpdir") + File.separator + "upload";
            final String excelFileName = excelUrl.substring(excelUrl.lastIndexOf("/") + 1);
            final String storeFilePath = baseDir + File.separator + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + "_" + excelFileName;
            atomicReferenceFilePath.set(storeFilePath);
            Map<String, String> httpHeader = new HashMap<String, String>(2);
            httpHeader.put("Referer", "https://prd-third.izuche.com");//有防盗链机制，必须加上Referer
            boolean downloadOK = BaseHttpClient.download(BaseHttpClient.HttpMethod.GET, excelUrl, null, httpHeader, storeFilePath);
            if (downloadOK) {
                File storeFile = new File(storeFilePath);
                try {
                    return new FileInputStream(storeFile);
                } catch (FileNotFoundException e) {
                    log.error(LOGTAG + "文件下载失败", e);
                    return null;
                }
            } else {
                log.error(LOGTAG + "文件下载失败");
                return null;
            }
        } else if (excelUrl.toLowerCase().startsWith("file:")) {//--->>>本地文件路径，方便开发调试
            final String storeFilePath = excelUrl.substring(5);
            log.info(LOGTAG + "存储保存路径=" + storeFilePath);
            if (!storeFilePath.toLowerCase().endsWith("xls") && !storeFilePath.toLowerCase().endsWith("xlsx")) {
                log.error(LOGTAG + "不是Excel文件");
                return null;
            }
            File storeFile = new File(storeFilePath);
            if (storeFile.exists() == false) {
                log.error(LOGTAG + "文件不存在");
                return null;
            }
            if (storeFile.isFile() == false) {
                log.error(LOGTAG + "文件不存在");
                return null;
            }
            try {
                return new FileInputStream(storeFile);
            } catch (FileNotFoundException e) {
                log.error(LOGTAG + "文件不存在", e);
                return null;
            }
        } else {
            log.error(LOGTAG + "不支持此类型的URL！");
            return null;
        }
    }

    public static void exportExcel(HttpServletRequest request, HttpServletResponse response, String excelFilenameCn, String excelFilenameEn, List<ExSheet> exSheets) {
        if (exSheets == null || exSheets.size() == 0) {
            return;
        }
        //A 默认的Excel文件名
        if (StringUtils.isBlank(excelFilenameCn)) {
            excelFilenameCn = "导出数据文件_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xlsx";
        }
        if (StringUtils.isBlank(excelFilenameEn)) {
            excelFilenameEn = "ExportedData_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xlsx";
        }
        try {
            //B1 根据文件名，获知Excel文件类型。（注：采用POI生成不同类型的Excel时，其性能不一样。xls：生成速度快，但文件比较大；xlsx：生成速度慢，但文件比较小）
            final String excelType = excelFilenameCn.substring(excelFilenameCn.trim().lastIndexOf(".") + 1).toLowerCase();//扩展名
            //B2 决定文件名(需要对不同浏览器进行兼容，尤其是Firefox)
            String filename = excelFilenameCn;//中文文件名
            String ua = request.getHeader("User-Agent");
            if (StringUtils.isEmpty(ua)) {//如果没有UA，则采用英文文件名
                filename = excelFilenameEn;
            } else if (ua.toLowerCase().indexOf("firefox") != -1) {//UA是Firefox
                if (HttpMethod.POST.name().equalsIgnoreCase(request.getMethod()) && request.getContentType() != null && request.getContentType().toLowerCase().indexOf("application/json") >= 0) {
                    filename = URLEncoder.encode(filename, "UTF8");
                } else {
                    filename = new String(filename.getBytes("UTF8"), "ISO8859-1");
                }
            } else if (ua.toLowerCase().indexOf("chrome") != -1) {//UA是Chrome
                filename = URLEncoder.encode(filename, "UTF8");
            } else if (ua.toLowerCase().indexOf("safari") != -1) {//UA是safari
                filename = URLEncoder.encode(filename, "UTF8");
            } else {//UA是IE、Chrome或其它
                filename = URLEncoder.encode(filename, "UTF8");
            }
            response.setHeader("Content-Disposition", "attachment; filename=\"" + filename + "\"");
            response.setHeader("Download-Filename", URLEncoder.encode(excelFilenameCn, "UTF8"));//这是和前端约定的自定义响应头
            if ("xls".equalsIgnoreCase(excelType)) {
                response.setContentType("application/vnd.ms-excel;charset=gbk");
            } else if ("xlsx".equalsIgnoreCase(excelType)) {
                response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=gbk");
            }
            response.setCharacterEncoding("gbk");
            //B3 输出EXCEL
            ExcelTool.export(exSheets, response.getOutputStream(), excelType);
        } catch (Exception e) {
        }
    }
}

