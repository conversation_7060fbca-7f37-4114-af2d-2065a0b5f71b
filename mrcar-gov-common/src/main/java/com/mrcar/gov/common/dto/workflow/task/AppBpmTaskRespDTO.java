package com.mrcar.gov.common.dto.workflow.task;

import com.mrcar.gov.common.dto.workflow.enums.BpmProcessInstanceResultEnum;
import lombok.Data;
import lombok.ToString;
import java.util.Date;

/**
 * AppBpmTask 响应 DTO
 * 返回任务的详细信息
 *
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
public class AppBpmTaskRespDTO {

    /**
     * 任务定义的标识
     */
    private String definitionKey;

    /**
     * 审核的用户信息
     */
    private User assigneeUser;

    /**
     * 流程实例编号
     */
    private String processInstanceId;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 持续时间
     */
    private Long durationInMillis;

    /**
     * 任务结果
     * 参见 bpm_process_instance_result
     */
    private Byte result;

    /**
     * 任务结果名称
     */
    private String resultStr;

    /**
     * 审批建议
     */
    private String reason;

    /**
     * 任务编号
     */
    private String id;

    /**
     * 任务名字
     */
    private String name;

    /**
     * 接收时间
     */
    private Date claimTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 激活状态
     * 参见 SuspensionState 枚举
     */
    private Integer suspensionState;

    public void setResult(Byte result) {
        this.result = result;
        this.resultStr = BpmProcessInstanceResultEnum.getEnum(result).getDesc();
    }

    /**
     * 用户信息
     */
    @Data
    public static class User {

        /**
         * 用户编号
         */
        private Long id;

        /**
         * 用户昵称
         */
        private String nickname;

        /**
         * 用户手机号
         */
        private String mobile;

        /**
         * 部门编号
         */
        private Long deptId;

        /**
         * 部门名称
         */
        private String deptName;

        /**
         * 图像
         */
        private String headIcon;
    }
}
