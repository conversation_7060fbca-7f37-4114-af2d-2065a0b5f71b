package com.mrcar.gov.common.constant.business;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/12/30 13:52
 */
@AllArgsConstructor
@Getter
public enum GovMsgPushChannelEnum {
    PC(1, "PC端"),
    H5(2, "H5"),
    APP(3, "APP");

    private Integer code;

    private String desc;

    public static String getDescByCode(Integer pushChannel) {
        for (GovMsgPushChannelEnum value : GovMsgPushChannelEnum.values()) {
            if (Objects.equals(value.getCode(), pushChannel)) {
                return value.getDesc();
            }
        }
        return "";
    }
}
