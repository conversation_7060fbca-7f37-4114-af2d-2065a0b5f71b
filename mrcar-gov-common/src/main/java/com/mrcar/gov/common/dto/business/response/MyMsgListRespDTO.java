package com.mrcar.gov.common.dto.business.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.mrcar.gov.common.dto.business.request.GovMsgFileDTO;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.springframework.web.util.HtmlUtils;

import java.util.Date;
import java.util.List;

/**
 * 我的消息列表请求参数
 *
 * <AUTHOR> on  2024/12/30 21:23
 */
@Data
public class MyMsgListRespDTO {

    /**
     * 消息记录ID
     */
    private String id;


    /**
     * 消息模块 GovMsgModuleEnum
     * <pre>
     * 1 - 公文公告 (GOVERNMENT_NOTICE, 关联 GovMsgTypeEnum.NOTICE)
     * 2 - 系统消息 (SYSTEM_MESSAGE, 关联 GovMsgTypeEnum.NOTICE)
     * 3 - 用车通知 (VEHICLE_NOTICE, 关联 GovMsgTypeEnum.TEMPLATE_MSG)
     * 4 - 审批通知 (APPROVAL_NOTICE, 关联 GovMsgTypeEnum.TEMPLATE_MSG)
     * 5 - 监控报警 (MONITOR_ALARM, 关联 GovMsgTypeEnum.TEMPLATE_MSG)
     * 6 - 车务消息 (VEHICLE_SERVICE, 关联 GovMsgTypeEnum.TEMPLATE_MSG)
     * 7 - 维保通知 (MAINTENANCE_NOTICE, 关联 GovMsgTypeEnum.TEMPLATE_MSG)
     * </pre>
     */
    private Integer msgModule;


    /**
     * msgType 消息类型 1-模版消息  2-公告消息
     */
    private Integer msgType;


    /**
     * 消息对应模块的图标
     */
    private String msgModuleIcon;


    /**
     * 模板或公告名称
     */
    private String msgName;


    /**
     * 消息内容
     * 公告消息是摘要
     */
    private String msgContent;
    /**
     * 无格式消息内容
     */
    private String msgContentWithoutFormat;
    /**
     * 推送时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date pushTime;

    /**
     * 发布单位名称
     */
    private String publishDeptName;

    /**
     * 读取状态 0未读 1已读 2默认
     */
    private Integer readStatus;

    /**
     * 附件地址列表
     */
    private List<GovMsgFileDTO> attachmentUrlList;
    /**
     * 记录编号
     */
    private String recordNo;
    /**
     * 发送批次号
     */
    private String batchNo;
    /**
     * 模板或公告编码
     */
    private String msgNo;

    public String getMsgContent(){
        if(StringUtils.isBlank(msgContent)){
            return "";
        }
        return HtmlUtils.htmlUnescape(msgContent);
    }

    public String getMsgContentWithoutFormat(){
        if(StringUtils.isBlank(msgContent)){
            return "";
        }
        String content = HtmlUtils.htmlUnescape(msgContent);
        return Jsoup.parse(content).text();
    }
}
