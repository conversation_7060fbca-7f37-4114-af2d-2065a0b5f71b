package com.mrcar.gov.common.dto.order.resp;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class GovPublicCarCommandRespDTO {

    /**
     * id
     */
    private Long id;

    /**
     * 业务单号
     */
    private String businessNo;

    /**
     * 指令编号/日志编号
     */
    private String commandNo;

    /**
     * 车牌号码
     */
    private String vehicleLicense;

    /**
     * 车系
     */
    private String vehicleSeriesName;

    /**
     * 设备编号
     */
    private String deviceNo;

    /**
     * 设备厂商名称
     */
    private String manufactName;

    /**
     * 设备型号名称
     */
    private String modelName;

    /**
     * 指令类型 0:开锁 1:关锁 2:断油断电 3:供油供电 4:双闪鸣笛
     */
    private Integer commandType;


    /**
     * 指令类型 0:开锁 1:关锁 2:断油断电 3:供油供电 4:双闪鸣笛
     */
    private String commandTypeStr;

    /**
     * 指令下发时间
     */
    private Date commandDispatchTime;

    /**
     * 指令完成时间
     */
    private Date commandCompleteTime;

    /**
     * 执行结果 0:失败 1:成功
     */
    private Integer commandResult;

    /**
     * 执行结果 0:失败 1:成功
     */
    private String commandResultStr;

    /**
     * 失败原因
     */
    private String failReason;

    /**
     * 操作来源 0:社会化用车
     */
    private Integer operateSource;

    /**
     * 操作来源 0:社会化用车
     */
    private String operateSourceStr;

    /**
     * 企业名称
     */
    private String companyName;

    /**
     * 操作人名称
     */
    private String operatorName;

    /**
     * 操作人手机号
     */
    private String operatorMobile;

    /**
     * 单位名称
     */
    private String deptName;

    /**
     * 指令类型
     */
    private Integer requestType;

    /**
     * 指令类型名称
     */
    private String requestTypeStr;
}
