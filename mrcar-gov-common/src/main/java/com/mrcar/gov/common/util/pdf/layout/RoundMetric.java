package com.mrcar.gov.common.util.pdf.layout;

public interface RoundMetric {

    MetricValue top();

    MetricValue bottom();

    MetricValue left();

    MetricValue right();

    static RoundMetric empty() {
        return new DefaultRoundMetric(
                MetricValue.create(MetricType.ABSOLUTE, 0),
                MetricValue.create(MetricType.ABSOLUTE, 0),
                MetricValue.create(MetricType.ABSOLUTE, 0),
                MetricValue.create(MetricType.ABSOLUTE, 0));
    }

    static RoundMetric percentage(float topAndBottom,
                                  float leftAndRight) {
        return new DefaultRoundMetric(
                MetricValue.create(MetricType.PERCENTAGE, topAndBottom),
                MetricValue.create(MetricType.PERCENTAGE, topAndBottom),
                MetricValue.create(MetricType.PERCENTAGE, leftAndRight),
                MetricValue.create(MetricType.PERCENTAGE, leftAndRight));
    }

    static RoundMetric absolute(float topAndBottom,
                                float leftAndRight) {
        return new DefaultRoundMetric(
                MetricValue.create(MetricType.ABSOLUTE, topAndBottom),
                MetricValue.create(MetricType.ABSOLUTE, topAndBottom),
                MetricValue.create(MetricType.ABSOLUTE, leftAndRight),
                MetricValue.create(MetricType.ABSOLUTE, leftAndRight));
    }

    static RoundMetric create(MetricValue top,
                              MetricValue bottom,
                              MetricValue left,
                              MetricValue right) {
        return new DefaultRoundMetric(top, bottom, left, right);
    }
}
