package com.mrcar.gov.common.constant.asset;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 年检状态
 */
public enum VehicleAnnualInspectionStatusEnum {
    //1 未年检 2 已年检 3 已逾期
    NOT_ANNUAL_INSPECTION(1,"未年检"),
    ANNUAL_INSPECTION(2,"已年检"),
    OVERDUE(3,"已逾期")

    ;

    private final int code;
    private final String desc;

    VehicleAnnualInspectionStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
    public static String getDesc(int code) {
        for (VehicleAnnualInspectionStatusEnum vehicleStatusEnum : VehicleAnnualInspectionStatusEnum.values()) {
            if (vehicleStatusEnum.getCode() == code) {
                return vehicleStatusEnum.getDesc();
            }
        }
        return "";
    }

    // 使用list 返回所有描述信息
    public static List<String> getDescList() {
        return Arrays.stream(VehicleAnnualInspectionStatusEnum.values()).map(VehicleAnnualInspectionStatusEnum::getDesc).collect(Collectors.toList());
    }

    // 根据描述获取code
    public static Integer getCodeByDesc(String desc) {
        for (VehicleAnnualInspectionStatusEnum vehicleStatusEnum : VehicleAnnualInspectionStatusEnum.values()) {
            if (Objects.equals(vehicleStatusEnum.getDesc(), desc)) {
                return vehicleStatusEnum.getCode();
            }
        }
        return null;
    }




}
