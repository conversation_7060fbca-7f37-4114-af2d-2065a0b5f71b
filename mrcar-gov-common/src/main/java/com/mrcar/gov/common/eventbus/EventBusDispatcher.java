package com.mrcar.gov.common.eventbus;

import com.google.common.eventbus.AsyncEventBus;
import com.mrcar.gov.common.thread.BlockedThreadPool;

/**
 * EventBus事件分发器.
 * eventBusDispatcher.register(new MallHandler()
 *      @Subscribe
 *     public void handler(TestEvent te){
 *         System.out.println(te);
 *     }
 * );
 * eventBusDispatcher.dispatcher(new TestEvent());
 *
 * <AUTHOR>
 */
public class EventBusDispatcher {

    private final AsyncEventBus asyncEventBus;


    /**
     * 构造函数.
     */
    public EventBusDispatcher() {
        asyncEventBus = new AsyncEventBus(BlockedThreadPool.newBlockedThreadPool(Runtime.getRuntime().availableProcessors()));
    }

    /**
     * 触发事件.
     * @param event
     */
    public void dispatcher(Event event) {
        asyncEventBus.post(event);
    }

    /**
     * 注册处理器.
     * @param eventBusHandler
     */
    public void register(EventBusHandler eventBusHandler) {
        asyncEventBus.register(eventBusHandler);
    }

    /**
     * 注销处理器.
     * @param eventBusHandler
     */
    public void unregister(EventBusHandler eventBusHandler) {
        asyncEventBus.unregister(eventBusHandler);
    }

}
