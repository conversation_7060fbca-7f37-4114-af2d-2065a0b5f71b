package com.mrcar.gov.common.dto.device.resp;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.mrcar.gov.common.enums.device.GovGpsDeviceEnum;
import lombok.Data;

import java.util.Date;

@Data
public class SupplierDeviceHistoryListExportRespDTO {
    /**
     * 设备编号
     */
    @ExcelProperty(value = {"设备号"}, index = 0)
    @ColumnWidth(25)
    private String deviceNo;

    /**
     * SIM卡号
     */
    @ExcelProperty(value = {"SIM卡号"}, index = 1)
    @ColumnWidth(25)
    private String simNo;

    /**
     * 供应商名称
     */
    @ExcelProperty(value = {"供应商"}, index = 2)
    @ColumnWidth(25)
    private String supplierServiceName;

    /**
     * 设备类型详情
     */
    @ExcelProperty(value = {"设备类型"}, index = 3)
    @ColumnWidth(25)
    private String deviceTypeMsg;


    /**
     * 设备所属型号名称
     */
    @ExcelProperty(value = {"设备品牌/型号"}, index = 4)
    @ColumnWidth(25)
    private String modelName;

    /**
     * 历史绑定次数
     */
    @ExcelProperty(value = {"绑定次数"}, index = 5)
    @ColumnWidth(25)
    private Long historyBindTimes;

    /**
     * 设备所属厂商名称
     */
    @ExcelProperty(value = {"设备厂商"}, index = 6)
    @ColumnWidth(25)
    private String manufactName;

    /**
     * 车牌号
     */
    @ExcelProperty(value = {"车牌号"}, index = 7)
    @ColumnWidth(25)
    private String vehicleLicense;

    /**
     * 车架号
     */
    @ExcelProperty(value = {"车架号"}, index = 8)
    @ColumnWidth(25)
    private String vehicleVin;

    /**
     * 发动机号
     */
    @ExcelProperty(value = {"发动机号"}, index = 9)
    @ColumnWidth(25)
    private String engineNum;

    /**
     * 车系
     */
    @ExcelProperty(value = {"车辆型号"}, index = 10)
    @ColumnWidth(25)
    private String vehicleModel;

    /**
     * 车辆品牌
     */
    @ExcelProperty(value = {"车辆品牌"}, index = 11)
    @ColumnWidth(25)
    private String vehicleBrand;

    /**
     * 绑定时间
     */
    @ExcelProperty(value = {"绑定时间"}, index = 12)
    @ColumnWidth(25)
    private Date bindTime;

    /**
     * 解绑时间
     */
    @ExcelProperty(value = {"解绑时间"}, index = 13)
    @ColumnWidth(25)
    private Date ubindTime;

    /**
     * 绑定人员姓名
     */
    @ExcelProperty(value = {"绑定人"}, index = 14)
    @ColumnWidth(25)
    private String bindName;

    /**
     * 最新绑定状态详情
     */
    @ExcelProperty(value = {"状态"}, index = 15)
    @ColumnWidth(25)
    private String bindStatusMsg;


    /**
     * 绑定状态 1：已绑定，2：解绑
     */
    @ExcelIgnore
    private Integer bindStatus;
    /**
     * 设备类型code
     */
    @ExcelIgnore
    private Integer deviceType;




    public String getDeviceTypeMsg() {
        return GovGpsDeviceEnum.DeviceTypeEnum.getEnumMsgByCode(this.deviceType);
    }

    public String getBindStatusMsg() {
        return GovGpsDeviceEnum.DeviceBindStatusEnum.getEnumMsgByCode(this.bindStatus);
    }
}
