package com.mrcar.gov.common.dto.workflow.copy;

import lombok.Data;
import java.util.Date;
import java.util.List;

/**
 * 抄送列表请求DTO
 *
 * <AUTHOR>
 * @date 2024/9/3 15:03
 */
@Data
public class ProviderCopyProcessReqDTO extends ProviderPageParam {

    /** 流程编号 */
    private String processInstanceId;

    /** 流程名称 */
    private String processName;

    /** 抄送人id */
    private Integer copyUserId;

    /** 抄送人名称 */
    private String copyUserName;

    /** 流程发起时间-查询开始时间 */
    private Date processStartBeginDate;

    /** 流程发起时间-查询结束时间 */
    private Date processStartEndDate;

    /** 抄送时间-查询开始时间 */
    private Date copyBeginDate;

    /** 抄送时间-查询结束时间 */
    private Date copyEndDate;

    /** 发起抄送人id-创建抄送的人 */
    private Integer copyCreatorId;

    /** 发起抄送人名称-创建抄送的人 */
    private String copyCreatorName;

    /** 所属企业列表 */
    private List<Integer> companyIds;

    /** 所属企业 */
    private Integer companyId;

    /** 抄送人集合 */
    private List<CopyUser> copyUserList;

    /**
     * 抄送人信息内部类
     */
    @Data
    public static class CopyUser {

        /** 抄送人姓名 */
        private String copyUserName;

        /** 抄送人电话 */
        private String copyUserMobile;

        /** 抄送人Id */
        private Integer copyUserId;
    }
}
