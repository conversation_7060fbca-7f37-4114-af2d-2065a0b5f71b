package com.mrcar.gov.common.util.pdf.layout;

public class Point {

    private final float _x;
    private final float _y;

    private Point(float x, float y) {
        this._x = x;
        this._y = y;
    }

    public static Point of(float x, float y) {
        return new Point(x, y);
    }

    public float x() {
        return this._x;
    }

    public float y() {
        return this._y;
    }

    @Override
    public String toString() {
        return "Point{" +
                "_x=" + _x +
                ", _y=" + _y +
                '}';
    }
}
