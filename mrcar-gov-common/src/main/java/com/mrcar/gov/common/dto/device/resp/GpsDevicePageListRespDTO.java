package com.mrcar.gov.common.dto.device.resp;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.mrcar.gov.common.enums.device.GovGpsDeviceEnum;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
public class GpsDevicePageListRespDTO {
    /**
     * 主键id
     */
    private Integer id;
    /**
     * 系统编码
     */
    private String deviceSysNo;
    /**
     * 设备编号
     */
    private String deviceNo;
    /**
     * SIM卡号
     */
    private String simNo;
    /**
     * 设备类型code
     */
    private Integer deviceType;
    /**
     * 设备所属厂商ID
     */
    private Integer manufactId;
    /**
     * 设备所属厂商名称
     */
    private String manufactName;
    /**
     * 设备所属型号ID
     */
    private Integer modelId;
    /**
     * 设备所属型号名称
     */
    private String modelName;
    /**
     * 最新绑定状态code
     */
    private Integer bindStatus;
    /**
     * 最新绑定时间
     */
    private Date bindTime;
    /**
     * 首次绑定时间
     */
    private Date firstBindTime;
    /**
     * 车辆编码
     */
    private String vehicleNo;
    /**
     * 备注
     */
    private String remarks;
    /**
     * 企业id
     */
    private Integer companyId;
    /**
     * 企业名称
     */
    private String companyName;
    /**
     * 删除状态 1：删除；0：未删
     */
    private Integer deleteStatus;
    /**
     * 创建人编码
     */
    private String createCode;
    /**
     * 创建人名称
     */
    private String createName;
    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 修改人编码
     */
    private String updateCode;
    /**
     * 修改人名称
     */
    private String updateName;
    /**
     * 修改时间
     */
    private Date updateDate;

    /**
     * 车牌号
     */
    private String vehicleLicense;
    /**
     * 车架号
     */
    private String vehicleVin;

    /**
     * 发动机号
     */
    private String engineNum;

    /**
     * 车系
     */
    private String vehicleModel;

    /**
     * 车辆品牌
     */
    private String vehicleBrand;

    /**
     * 设备类型详情
     */
    private String deviceTypeMsg;
    /**
     * 最新绑定状态详情
     */
    private String bindStatusMsg;


    private boolean editBtn = true;
    private boolean deleteBtn = true;

    /**
     * 设备来源类型 1:政府设备 2：社会设备
     */
    private Integer deviceSource;

    /**
     * 供应商编码
     */
    private String supplierServiceCode;

    /**
     * 供应商名称
     */
    private String supplierServiceName;

    public String getDeviceTypeMsg() {
        return GovGpsDeviceEnum.DeviceTypeEnum.getEnumMsgByCode(this.deviceType);
    }


    public String getBindStatusMsg() {
        return GovGpsDeviceEnum.DeviceBindStatusEnum.getEnumMsgByCode(this.bindStatus);
    }
}
