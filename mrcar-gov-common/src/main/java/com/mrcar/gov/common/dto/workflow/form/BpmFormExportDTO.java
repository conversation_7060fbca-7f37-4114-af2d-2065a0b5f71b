package com.mrcar.gov.common.dto.workflow.form;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class BpmFormExportDTO implements Serializable {

    /**
     * 流程实例名称
     */
    private String processName;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private String createDate;

    /**
     * 数据行
     */
    private List<RowEntry> rows;

    @Data
    public static class RowEntry {

        private String key;

        private String value;

    }

}
