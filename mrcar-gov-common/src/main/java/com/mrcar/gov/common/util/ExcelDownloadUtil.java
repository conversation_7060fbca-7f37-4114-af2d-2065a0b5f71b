package com.mrcar.gov.common.util;

import com.izu.framework.exception.ApiException;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.mrcar.gov.common.constant.RestErrorCode;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.nio.file.Files;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/11/14 15:35
 */
@Slf4j
public class ExcelDownloadUtil {

    public static InputStream downloadFile(String urlString) {
        File tmpFile = null;
        try {
            // 使用HttpURLConnection下载文件
            URL url = new URL(urlString);
            java.net.HttpURLConnection connection = (java.net.HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            // 获取输入流
            InputStream inputStream = connection.getInputStream();
            tmpFile = Files.createTempFile(System.currentTimeMillis() + "", ".xlsx").toFile();

            // 创建本地文件输出流
            FileOutputStream fileOutputStream = new FileOutputStream(tmpFile);
            // 读取并写入数据
            byte[] buffer = new byte[1024];
            int length;
            while ((length = inputStream.read(buffer)) != -1) {
                fileOutputStream.write(buffer, 0, length);
            }
            // 关闭输入流和输出流
            inputStream.close();
            fileOutputStream.close();

            return new FileInputStream(tmpFile);
        } catch (IOException e) {
            log.info("excel地址：{}下载失败", urlString, e);
            throw new ApiException(RestErrorCode.EXCEL_DOWNLOAD_ERROR);
        }finally {
            if(Objects.nonNull(tmpFile)){
                tmpFile.delete();
            }
        }
    }

}
