package com.mrcar.gov.common.enums.device;

/**
 * <AUTHOR>
 */

public enum FenceTypeEnum {
    /**
     * 圆形
     */
    ROUND(1,"圆形"),
    /**
     * 多边形
     */
    POLYGON(2,"多边形"),
    /**
     * 行政区划（省市）
     */
    REGION(3,"行政区划"),

    province(4,"省份"),

    city(5,"城市"),

    county(6,"区县")
    ;

    private Integer code;

    private String name;

    FenceTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static String getNameByCode(Integer code){
        for(FenceTypeEnum e: FenceTypeEnum.values()){
            if(e.getCode().equals(code)){
                return e.getName();
            }
        }
        return "";
    }
}
