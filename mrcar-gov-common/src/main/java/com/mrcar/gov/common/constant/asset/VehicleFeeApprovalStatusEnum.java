package com.mrcar.gov.common.constant.asset;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public enum VehicleFeeApprovalStatusEnum {

    WAIT_AUTIT(0,"待审核"),
    AUTIT_PASS(1,"审核通过"),
    AUTIT_REFUSE(2,"审核拒绝"),
    AUTIT_CANCEL(3,"审核撤销");

    private final int code;
    private final String desc;

    VehicleFeeApprovalStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
    public static String getDesc(int code) {
        for (VehicleFeeApprovalStatusEnum vehicleFeeApprovalStatusEnum : VehicleFeeApprovalStatusEnum.values()) {
            if (vehicleFeeApprovalStatusEnum.getCode() == code) {
                return vehicleFeeApprovalStatusEnum.getDesc();
            }
        }
        return "";
    }

    // 使用list 返回所有描述信息
    public static List<String> getDescList() {
        return Arrays.stream(VehicleFeeApprovalStatusEnum.values()).map(VehicleFeeApprovalStatusEnum::getDesc).collect(Collectors.toList());
    }

    // 根据描述获取code
    public static Integer getCodeByDesc(String desc) {
        for (VehicleFeeApprovalStatusEnum vehicleStatusEnum : VehicleFeeApprovalStatusEnum.values()) {
            if (Objects.equals(vehicleStatusEnum.getDesc(), desc)) {
                return vehicleStatusEnum.getCode();
            }
        }
        return null;
    }
}
