package com.mrcar.gov.common.dto.business.response;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import izu.org.apache.poi.ss.usermodel.BorderStyle;
import izu.org.apache.poi.ss.usermodel.HorizontalAlignment;
import lombok.Data;

import java.math.BigDecimal;

@ApiModel("车辆使用情况表单")
@Data
@HeadStyle(
        fillForegroundColor = 22,
        fillBackgroundColor = 22
)
@ColumnWidth(20)
@ContentStyle(
        horizontalAlignment = HorizontalAlignment.CENTER,
        borderBottom = BorderStyle.THIN,
        borderLeft = BorderStyle.THIN,
        borderRight = BorderStyle.THIN,
        borderTop = BorderStyle.THIN,
        wrapped = true
)
public class StatisticsVehicleUsageDTO {

    /**
     * 车架号 vehicle_vin
     */
    @ExcelProperty(
            value = {"车架号"},
            order = 2
    )
    private String vehicleVin;

    /**
     * 车牌号 vehicle_license
     */
    @ExcelProperty(
            value = {"车牌号"},
            order = 1
    )
    private String vehicleLicense;

    /**
     * 行驶总里程 total_mileage
     */
    @ExcelProperty(
            value = {"行程明细汇总里程(km)"},
            order = 8
    )
    private BigDecimal totalMileage;

    /**
     * 历史总里程 his_total_mileage
     */
    @ExcelProperty(
            value = {"仪表盘里程数(km)"},
            order = 6
    )
    private BigDecimal hisTotalMileage;

    /**
     * 仪表盘行驶里程(km)
     */
    @ExcelProperty(
            value = {"仪表盘行驶里程(km)"},
            order = 7
    )
    private BigDecimal dayTotalMileage;

    /**
     * 违章总数量 violation_count
     */
    @ExcelIgnore
    private Integer violationCount;

    /**
     * 维保总次数（维修+保养） maintain_count
     */
    @ExcelIgnore
    private Integer maintainCount;

    /**
     * 年检总次数 year_check_count
     */
    @ExcelIgnore
    private Integer yearCheckCount;

    /**
     * 车辆型号 vehicle_model
     */
    @ExcelProperty(
            value = {"车型"},
            order = 3
    )
    private String vehicleModel;

    /**
     * 当天行驶轨迹里程 day_travel_mileage
     */
    @ExcelProperty(
            value = {"轨迹总里程(km)"},
            order = 9
    )
    private BigDecimal dayTravelMileage;

    /**
     * 当天行驶的时长 day_travel_duration
     */
    @ExcelProperty(
            value = {"行驶时长(时:分)"},
            order = 5
    )
    private String dayTravelDurationString;

    /**
     * 当天行驶的时长 day_travel_duration
     */
    @ExcelIgnore
    private Integer dayTravelDuration;

    /**
     * 当前行驶油耗L day_travel_oil
     */
    @ExcelIgnore
    private String dayTravelOil;

    @ExcelIgnore
    private BigDecimal useOil;

    /**
     * 企业ID
     */
    @ExcelIgnore
    private Integer companyId;

    /**
     * 企业名称
     */
    @ExcelIgnore
    private String companyName;

    /**
     * 车辆所有人
     */
    @ExcelIgnore
    private String vehicleBelongDeptCode;

    /**
     * 车辆所有人
     */
    @ExcelProperty(
            value = {"车辆所有人"},
            order = 4
    )
    private String vehicleBelongDeptName;

    /**
     * 车辆使用人-单位
     */
    @ExcelIgnore
    private String vehicleUseDeptCode;

    /**
     * 车辆使用人-单位
     */
    @ExcelProperty(
            value = {"车辆使用人"},
            order = 11
    )
    private String vehicleUseDeptName;

    /**
     * 车辆使用单位-内部部门
     */
    @ExcelIgnore
    private String vehicleUseStructCode;

    /**
     * 车辆使用单位-内部部门
     */
    @ExcelProperty(
            value = {"车辆使用部门"},
            order = 12
    )
    private String vehicleUseStructName;

    /**
     * 车辆管理单位
     */
    @ExcelIgnore
    private String vehicleManageDeptCode;

    /**
     * 车辆管理单位
     */
    @ExcelIgnore
    private String vehicleManageDeptName;

    /**
     * 管车类型；1-机关事务管理局；2-财政部门
     */
    @ExcelIgnore
    private Integer manageCarType;

    /**
     * 管理部门类型
     */
    @ExcelProperty(
            value = {"管理部门类型"},
            order = 10
    )
    private String manageCarTypeName;


}
