package com.mrcar.gov.common.util;

import com.izu.framework.exception.ApiException;
import com.izu.framework.resp.InfoCode;

/**
 * <AUTHOR>
 * @date 2025/1/2 20:25
 */
public class CheckConditionUtil {

    public static void check(boolean condition, String message) {
        if (!condition) {
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID,message);
        }
    }

    public static void check(boolean condition, InfoCode infoCode,String message) {
        if (!condition) {
            throw new ApiException(infoCode,message);
        }
    }
}
