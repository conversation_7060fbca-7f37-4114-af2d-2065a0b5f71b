package com.mrcar.gov.common.dto.workflow.task;

import lombok.Data;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 任务支持的操作项 Response VO
 */
@Data
public class BpmTaskOperableItemRespDTO {

    /**
     * 任务编号
     */
    @NotBlank(message = "任务编号不能为空")
    private String id;

    /**
     * 是否支持通过
     */
    private boolean approve = true;

    /**
     * 是否支持不通过
     */
    private boolean reject = true;

    /**
     * 是否支持转办
     */
    private boolean transfer = true;

    /**
     * 是否支持回退
     */
    private boolean back = false;

    /**
     * 可回退的历史任务节点（包含开始节点）
     */
    private List<BpmHistoricTaskInstanceRespDTO> historicTaskNodeList;
}
