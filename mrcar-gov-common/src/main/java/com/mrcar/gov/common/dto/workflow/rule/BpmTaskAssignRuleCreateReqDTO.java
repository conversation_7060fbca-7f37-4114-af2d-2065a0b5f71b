package com.mrcar.gov.common.dto.workflow.rule;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import javax.validation.constraints.NotEmpty;

/**
 * 管理后台 - 流程任务分配规则的创建 Request VO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BpmTaskAssignRuleCreateReqDTO extends BpmTaskAssignRuleBaseDTO {

    /**
     * 流程模型的编号
     * 必填
     * 示例值: 1024
     */
    @NotEmpty(message = "流程模型的编号不能为空")
    private String modelId;

    /**
     * 流程任务定义的编号
     * 必填
     * 示例值: 2048
     */
    @NotEmpty(message = "流程任务定义的编号不能为空")
    private String taskDefinitionKey;

    /**
     * 流程id
     * 必填
     * 示例值: 2048
     */
    private String processDefinitionId;

}
