package com.mrcar.gov.common.dto.workflow.model;

import com.mrcar.gov.common.dto.workflow.BaseDTO;
import lombok.Data;

/**
 * 管理后台 - 流程模型的创建 关联流程下拉框入参
 * @description: 用于传递当前流程的 ID，进行流程关联的搜索。
 * @date 2024/9/3 10:40
 */
@Data
public class BpmModelSearchConnectProcessReqDTO extends BaseDTO {

    /**
     * 当前流程id
     * 示例值: 34cf0ff9-7124-11ed-9ce3-3e703374bdb3
     */
    private String processId;
}
