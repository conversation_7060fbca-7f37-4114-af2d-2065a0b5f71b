package com.mrcar.gov.common.dto.device.resp;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class LbsLocationDetailDTO extends LocationsDetailDTO {
    /**
     * 是否疲劳驾驶的标志位
     */
    private <PERSON>ole<PERSON> tired;

    public Boolean getTired() {
        return tired;
    }

    public void setTired(<PERSON><PERSON><PERSON> tired) {
        this.tired = tired;
    }

    @Override
    public String toString() {
        return super.toString();
    }
}
