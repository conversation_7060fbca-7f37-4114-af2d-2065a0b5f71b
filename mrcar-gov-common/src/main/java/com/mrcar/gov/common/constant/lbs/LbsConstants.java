package com.mrcar.gov.common.constant.lbs;

import java.math.BigDecimal;

public class LbsConstants {
	
	public static final byte YES = 1;
    public static final byte NO = 0;

    public static final String ALLCOUNTRY  = "11111";


    public static final String ENV_DEV  = "dev";
    public static final String ENV_TEST  = "test";
    public static final String ENV_PRE   = "pre";
    public static final String ENV_ONLINE  = "online";
    /**
     * 查询长租业务信息接口
     */
    public static final String LONG_RENT_ORDER_URL="/erp/info/bussMessage.json";

    /**
     * 停车点计算时间阈值  3分钟，180秒的时间阈值
     */
    public static final int LBS_STOP_DURTAION = 180;

    /**
     * 停车点计算速度阈值 速度阈值 km/h
     */
    public static final int LBS_STOP_SPEED = 5;
    /**
     * 行程判断依据，停车超过30分钟
     */
    public static final int LBS_SUPER_STOP_DURATION = 1800;
    /**
     * 中间行程最小间隔时长30S
     */
    public static final int LBS_TRAVEL_DURATION = 30;
    /**
     * 中间行程最小移动距离100M
     */
    public static final int LBS_TRAVEL_DISTANCE = 100;

    public static final String LBS_ES_INDEX = "lbs_travel";
    /**
     * 两个定位点间的最高平均速度
     */
    public static final BigDecimal LBS_MAX_AVG_SPEED = new BigDecimal(150);
    /**
     * 定位最高瞬时速度
     */
    public static final BigDecimal LBS_MAX_SUDDENLY_SPEED = new BigDecimal(200);
    /**
     * 里程表读数和里程计算的最大差值
     */
    public static final BigDecimal LBS_READ_CAL_MAX = new BigDecimal(5);
    /**
     * 最大单日行程数
     */
    public static final int MAX_TRAVEL = 1000000;

    public static final int PER_MINUTES = 30;

    public static final String LBS_DELAY_MSG_TOPIC = "lbs_delay_topic";

    public static final String LBS_LOW_SPEED_TAG = "LOWSPEED";

    public static final String LBS_LOW_SPEED_CONSUMER_GROUP = "low_speed_group";

}
