package com.mrcar.gov.common.util.pdf.layout;

import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.font.PDFont;
import org.apache.pdfbox.pdmodel.font.PDType0Font;

import java.io.ByteArrayInputStream;
import java.io.IOException;

public class DisplayFont {

    private final PDType0Font _font;
    private final int _size;

    private DisplayFont(PDType0Font font, int size) {
        _font = font;
        _size = size;
    }

    public static DisplayFont create(byte[] source,
                                     PDDocument document,
                                     int size) {
        try {
            PDType0Font font = PDType0Font.load(document, new ByteArrayInputStream(source));
            return new DisplayFont(font, size);
        } catch (IOException e) {
            throw new IllegalArgumentException("Cannot open font file", e);
        }
    }

    public PDFont font() {
        return this._font;
    }

    public int size() {
        return this._size;
    }

}
