package com.mrcar.gov.common.util;

import com.izu.framework.web.util.VinUtil;
import com.mrcar.gov.common.constant.asset.AssetConstant;
import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR> dongxiya 2023/4/20 20:21
 */
public class VerifyUtil {

    private final static String TEST_VEHICLE_VIN_PREFIX = "CCCCCCC";

    /**
     * 校验车牌号
     */
    public static boolean checkVehicleLicense(String vehicleLicense) {
        if (StringUtils.isNotBlank(vehicleLicense)) {
            return AssetConstant.vehicleLicensePattern.matcher(vehicleLicense).matches();
        }
        return false;
    }


    /**
     * 校验车架号
     * @param vehicleVin
     * @return
     */
    public static boolean checkVehicleVin(String vehicleVin) {
        if (StringUtils.isNotBlank(vehicleVin)) {
            return vehicleVin.startsWith(TEST_VEHICLE_VIN_PREFIX) || VinUtil.check(vehicleVin);
        }
        return false;
    }


}
