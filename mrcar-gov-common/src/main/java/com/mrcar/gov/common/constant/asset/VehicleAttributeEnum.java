package com.mrcar.gov.common.constant.asset;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/11/11 17:53
 */
@Getter
public enum VehicleAttributeEnum {
    //车辆性质码 1.分散管理、2.集中管理
    SPREAD_MANAGEMENT(1,"分散管理"),
    COLLECTIVE_MANAGEMENT(2,"集中管理");

    private final int code;
    private final String desc;

    VehicleAttributeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }



    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDesc(int code) {
        for(VehicleAttributeEnum vehicleAttribute : VehicleAttributeEnum.values()) {
            if(vehicleAttribute.getCode() == code) {
                return vehicleAttribute.getDesc();
            }
        }
        return "";
    }

    public static List<String> getDescList() {
        return Arrays.stream(VehicleAttributeEnum.values()).map(VehicleAttributeEnum::getDesc).collect(Collectors.toList());
    }

    public static Integer getCodeByDesc(String desc) {
        for (VehicleAttributeEnum value : VehicleAttributeEnum.values()) {
            if (Objects.equals(value.getDesc(), desc)) {
                return value.getCode();
            }
        }
        return null;

    }
}
