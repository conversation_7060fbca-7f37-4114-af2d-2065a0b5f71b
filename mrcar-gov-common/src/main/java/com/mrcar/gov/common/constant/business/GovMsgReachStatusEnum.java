package com.mrcar.gov.common.constant.business;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/12/30 13:56
 */
@AllArgsConstructor
@Getter
public enum GovMsgReachStatusEnum {


    //未触达、已触达

    UNREACHED(0, "未触达"),

    REACHED(1, "已触达"),
    ;


    private final Integer code;

    private final String desc;

    public static String getDescByCode(Integer reachStatus) {
        for (GovMsgReachStatusEnum value : GovMsgReachStatusEnum.values()) {
            if (Objects.equals(value.getCode(), reachStatus)) {
                return value.getDesc();
            }
        }
        return "";
    }
}
