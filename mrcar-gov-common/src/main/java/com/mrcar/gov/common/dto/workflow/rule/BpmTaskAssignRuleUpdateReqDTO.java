package com.mrcar.gov.common.dto.workflow.rule;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import javax.validation.constraints.NotNull;

/**
 * 管理后台 - 流程任务分配规则的更新 Request VO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BpmTaskAssignRuleUpdateReqDTO extends BpmTaskAssignRuleBaseDTO {

    /**
     * 任务分配规则的编号
     * 必填
     * 示例值: 1024
     */
    @NotNull(message = "任务分配规则的编号不能为空")
    private Long id;

}
