package com.mrcar.gov.common.dto.business.response;

import com.mrcar.gov.common.constant.business.GovMsgModuleEnum;
import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * @Description: 消息中心未读消息数
 * @author: 牛子联
 * @Date: 2022/9/21
 **/
@Data
@ApiModel("底部未读消息")
public class GovMsgUnreadNumberRespDTO {


    /**
     * 未读消息数
     */
    private Integer msgUnreadNumber;


    /**
     * 1公文公告;2系统消息;3用车通知;4审批通知;5监控报警;6车务消息;7维保通知; -1:全部未读数量
     */
    private Integer msgModule;


    /**
     * 公文公告描述
     */
    private String msgModuleDesc;


}
