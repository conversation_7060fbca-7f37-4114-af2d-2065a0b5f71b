package com.mrcar.gov.common.constant.lbs;

/**
 * <AUTHOR> ON 2021/7/26.
 */
public enum TravelTagEnum {
    DIS_CITY("跨城","终点城市 与 起点城市不是同一个"),
    TAFFIC_JAM("堵车","平均速度在20km以下"),
    TIRED("疲劳驾驶","行驶时长大于等于4小时"),
    LONG_ROAD("长途行驶","行驶里程大于400公里"),
    HIGH_SPEED("高速行驶","定位上报点中的速度多次（暂定10次）大于等于80公里/小时 且小于120公里/小时"),
    SUPER_HIGH_SPEED("超速行驶","定位上报点中的速度多次（暂定10次）大于等于120公里/小时"),
    NIGHT_DRIVE("夜间行驶","行程的起点时间在晚上10点-12点（含10点，不含12点0分）"),
    BEFORE_DAWN_DRIVE("凌晨行驶","行程的起点时间在早上0点-4点（含0点，不含4点0分）"),
    ;
    private String tag;
    private String des;

    TravelTagEnum(String tag, String des) {
        this.tag = tag;
        this.des = des;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public String getDes() {
        return des;
    }

    public void setDes(String des) {
        this.des = des;
    }
}
