package com.mrcar.gov.common.dto.order.resp;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: mengshuang
 * @Date: 2025/4/10 18:08
 * @Param:
 * @Return:
 * @Description:
 **/
@Data
public class TimeShareConfigDetailRespDTO {
    /**
     * 自增主键
     */
    private Integer id;

    /**
     * 对应配置主表主键id
     */
    private Integer configId;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 驾驶类型：1-自驾，2-驾驶员
     */
    private Integer drivingType;

    /**
     * 里程费(元/公里)
     */
    private BigDecimal mileageRent;

    /**
     * 时长费(元/分钟)
     */
    private BigDecimal durationRent;

    /**
     * 创建人编码
     */
    private String createCode;

    /**
     * 创建人
     */
    private String createName;

    /**
     * 创建时间
     */
    private Date createTime;
}
