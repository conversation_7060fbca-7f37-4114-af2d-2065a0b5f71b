package com.mrcar.gov.common.dto.business.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.mrcar.gov.common.dto.asset.request.VehicleApplyBtnDTO;
import lombok.Data;

import java.util.Date;

/**
 * 处置明细列表
 *
 * <AUTHOR>
 * @date 2024/11/21 9:38
 */
@Data
public class GovDisposalApplyDetailListDTO extends VehicleApplyBtnDTO {
    /**
     * 序号id
     */
    private Integer id;

    /**
     * 处置编码
     */
    private String disposalNo;

    /**
     * 申请编码
     */
    private String applyNo;

    /**
     * 原车辆编码
     */
    private String vehicleNo;


    /**
     * 车牌号
     */
    private String vehicleLicense;

    /**
     * 车架号
     */
    private String vehicleVin;

    /**
     * 车辆类型
     */
    private Integer vehicleType;

     /**
     * 车辆类型
     */
    private String vehicleTypeName;

    /**
     * 申请单位编码
     */
    private String applyStructCode;

    /**
     * 申请单位名称
     */
    private String applyStructName;

    /**
     * 车辆所属单位编码
     */
    private String vehicleBelongDeptCode;

    /**
     * 车辆所属单位名称
     */
    private String vehicleBelongDeptName;

    /**
     * 处置方式 1报废 2拍卖 3调拨 4损失核销 5厂家回收 6 对外捐赠
     */
    private Integer disposalMethod;
    /**
     * 处置方式名称 1报废 2拍卖 3调拨 4损失核销 5厂家回收 6 对外捐赠
     */
    private String disposalMethodName;

    /**
     * 填调入单位
     */
    private String transferInStructCode;

    /**
     * 填调入单位
     */
    private String transferInStructName;

    /**
     * 处置状态
     */
    private Integer disposalStatus;

    /**
     * 处置状态名成
     */
    private String disposalStatusName;

    /**
     * 填处置调拨时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date disposalTime;


}
