package com.mrcar.gov.common.dto.business.request;

import com.mrcar.gov.common.dto.BaseDTO;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/27 18:41
 */
@Data
public class GovMsgNoticePublishReqDTO extends BaseDTO {

    /**
     * 公告通知编号
     */
    private String noticeNo;

    @NotNull(message = "消息分类不能为空")
    private Integer msgModule;

    /**
     * 通知分类 1公文公告 2系统通知
     */
    @NotNull(message = "消息分类不能为空")
    private Integer noticeType;


    /**
     * 公告通知标题
     */
    @NotBlank(message = "公告标题不能为空")
    private String noticeName;
    /**
     * 发布单位编码
     */
    @NotBlank(message = "发布单位编码不能为空")
    private String publishDeptCode;
    /**
     * 内容
     */
    @NotBlank(message = "正文不能为空")
    private String noticeContent;
    /**
     * 推送部门编码列表
     */
    private List<String> pushDeptCodeList;

    /**
     * 推送部门模式 1:全部 2指定部门
     */
    @NotBlank(message = "推送部门模式不能为空")
    private Integer pushDeptModel;
    /**
     * 附件地址列表
     */
    private List<GovMsgFileDTO> attachmentUrlList;


}
