package com.mrcar.gov.common.constant.order;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum GovPublicCarCommandResultEnum {

    FAIL(0, "失败"),
    SUCCESS(1, "成功");

    private final int code;
    private final String description;

    public static String getByCode(int code) {
        for (GovPublicCarCommandResultEnum type : values()) {
            if (type.code == code) {
                return type.getDescription();
            }
        }
        return "";
    }
}