package com.mrcar.gov.common.dto.business.response;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/12/31 15:15
 */
@Data
public class GovCourseRespDTO {

    /**
     * 主键id
     */
    private Integer id;

    /**
     * 教程类型 1：图文教程 2：视频教程
     */
    private Integer courseType;

    /**
     * 教程类型 1：图文教程 2：视频教程
     */
    private String courseTypeName;

    /**
     * 教程标题
     */
    private String courseTitle;

    /**
     * 教程简介
     */
    private String courseIntroduction;

    /**
     * 教程文件名称
     */
    private String fileName;

    /**
     * 教程文件url
     */
    private String courseUrl;

    /**
     * 创建人
     */
    private String createName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 修改人
     */
    private String updateName;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 是否删除 1：正常 2：删除
     */
    private Integer valid;
}
