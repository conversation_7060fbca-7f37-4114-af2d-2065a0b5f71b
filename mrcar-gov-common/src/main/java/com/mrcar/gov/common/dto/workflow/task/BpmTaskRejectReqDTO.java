package com.mrcar.gov.common.dto.workflow.task;

import com.mrcar.gov.common.dto.workflow.BaseDTO;
import lombok.Data;
import javax.validation.constraints.NotEmpty;

/**
 * 不通过流程任务的 Request VO
 */
@Data
public class BpmTaskRejectReqDTO extends BaseDTO {

    /**
     * 任务编号
     */
    @NotEmpty(message = "任务编号不能为空")
    private String id;

    /**
     * 审批意见
     */
    @NotEmpty(message = "审批意见不能为空")
    private String reason;
}
