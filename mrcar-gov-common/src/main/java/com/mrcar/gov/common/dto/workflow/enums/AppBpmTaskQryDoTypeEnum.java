package com.mrcar.gov.common.dto.workflow.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/9/6 18:48
 */
@AllArgsConstructor
@Getter
public enum AppBpmTaskQryDoTypeEnum {

   // 办理类型（1待办理 2已办理 3我发起的 4 抄送给我的
    TASK_TODO((byte)1, "待办"),
    TASK_DONE((byte)2, "已办"),
    TASK_MYSELF_START((byte)3, "我发起的"),
    TASK_COPY_MYSELF((byte)4, "抄送给我的");

    private Byte doType;

    private String desc;

}
