package com.mrcar.gov.common.util;


import com.mrcar.gov.common.dto.device.resp.GovVehicleStatusDTO;
import com.mrcar.gov.common.dto.tbox.TBoxStatusDTO;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 跟进发动机引擎的点火熄火状态计算剩余油量
 * <AUTHOR> ON 2023/4/27.
 */
public class LbsFuelUtil {

    private static final Logger logger = LoggerFactory.getLogger(LbsFuelUtil.class);

    public static int calculateUseFuel(List<GovVehicleStatusDTO> list){
        return cal(erfen(filter(list)));
    }

    /**
     * 跟进引擎状态进行拆分101010
     * @param list
     * @return
     */
    public static List<GovVehicleStatusDTO> filter(List<GovVehicleStatusDTO> list){
        list = list.stream().filter(carStatusDTO -> carStatusDTO.getRemainingFuel() != null
                && !"-1".equals(carStatusDTO.getRemainingFuel().toString()))
                .sorted(Comparator.comparing(GovVehicleStatusDTO::getCreateDate))
                .collect(Collectors.toList());
        Iterator<GovVehicleStatusDTO> iterator = list.iterator();
        GovVehicleStatusDTO before = null;
        while (iterator.hasNext()){
            GovVehicleStatusDTO now = iterator.next();
            if(before == null){
                if(now.getEngineStatus() == null || now.getEngineStatus() == 0){
                    iterator.remove();
                    continue;
                }else{
                    logger.warn("起始引擎状态为1的油耗,上报时间={}",now.getCreateDate());
                }
                before = now;
                continue;
            }
            if(before.getEngineStatus().equals(now.getEngineStatus()) && iterator.hasNext()){
               iterator.remove();
            }else{
                before = now;
            }
        }
        return list;
    }

    public static List<List<GovVehicleStatusDTO>> erfen(List<GovVehicleStatusDTO> list){
        //二分拆分
        List<List<GovVehicleStatusDTO>> erfen = new ArrayList<>();

        for (int i = 1;i <= list.size();i++){
            if(i % 2 == 0){
                List<GovVehicleStatusDTO> dtoList = new ArrayList<>();
                dtoList.add(list.get(i-1));
                dtoList.add(list.get(i-2));
                erfen.add(dtoList);
            }else if(i == list.size()){
                //单数最后一个判断
                List<GovVehicleStatusDTO> dtoList = new ArrayList<>();
                dtoList.add(list.get(i-1));
                dtoList.add(list.get(i-2));
                erfen.add(dtoList);
            }
        }
        return erfen;
    }

    public static int cal(List<List<GovVehicleStatusDTO>> lists){
        List<Integer> useFuelList = lists.stream().map(list -> {
            GovVehicleStatusDTO carStatusDTO0 = list.get(0);
            GovVehicleStatusDTO carStatusDTO1 = list.get(1);
            int useFuel = carStatusDTO1.getRemainingFuel() - carStatusDTO0.getRemainingFuel();
            return useFuel;
        }).collect(Collectors.toList());
        int useFuel = useFuelList.stream().mapToInt(i->i).sum();
        return useFuel;
    }

    /**
     * 判断开始和结束点前后三十分钟内，是否存在剩余油量超过80的数据
     */
    public static boolean isExists(List<GovVehicleStatusDTO> list){
        list = list.stream().filter(carStatusDTO -> carStatusDTO.getRemainingFuel() != null
                        && !"-1".equals(carStatusDTO.getRemainingFuel().toString()))
                .sorted(Comparator.comparing(GovVehicleStatusDTO::getCreateDate))
                .collect(Collectors.toList());
        List<GovVehicleStatusDTO> list1 = list.stream().limit(30).collect(Collectors.toList());
        List<GovVehicleStatusDTO> list2 = list.stream().sorted(Comparator.comparing(GovVehicleStatusDTO::getCreateDate).reversed()).limit(30).collect(Collectors.toList());
        if(list1.stream().anyMatch(carStatusDTO -> carStatusDTO.getRemainingFuel() >= 80)
                && list2.stream().anyMatch(carStatusDTO -> carStatusDTO.getRemainingFuel() >= 80)){
            return true;
        }
        return false;
    }
}
