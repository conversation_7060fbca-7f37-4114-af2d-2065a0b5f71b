package com.mrcar.gov.common.dto.workflow.model;

import com.mrcar.gov.common.dto.workflow.enums.BpmModelCategoryEnum;
import com.mrcar.gov.common.dto.workflow.enums.BpmModelFormTypeEnum;
import com.mrcar.gov.common.dto.workflow.enums.ModelEnum;
import lombok.Data;
import javax.validation.constraints.NotEmpty;

/**
 * 流程模型 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响文档生成
 */
@Data
public class BpmModelBaseDTO {

    /**
     * 流程标识
     * 示例值: process_yudao
     */
    @NotEmpty(message = "流程标识不能为空")
    private String key;

    /**
     * 流程名称
     * 示例值: 用车
     */
    @NotEmpty(message = "流程名称不能为空")
    private String name;

    /**
     * 流程描述
     * 示例值: 我是描述
     */
    private String description;

    /**
     * 流程分类
     * 参见 bpm_model_category 数据字典
     * 示例值: 1
     */
    @NotEmpty(message = "流程分类不能为空")
    private String category;

    /**
     * 流程分类名称
     */
    private String categoryStr;

    /**
     * 业务类型
     */
    private Byte businessType;

    /**
     * 业务类型名称
     */
    private String businessTypeStr;

    /**
     * 表单类型
     * 参见 bpm_model_form_type 数据字典
     * 示例值: 1
     */
    private Integer formType;

    /**
     * 表单编号
     * 示例值: 1024
     * 在表单类型为 {@link BpmModelFormTypeEnum#CUSTOM} 时，必须非空
     */
    private Long formId;

    /**
     * 自定义表单的提交路径，使用 Vue 的路由地址
     * 示例值: /bpm/oa/leave/create
     * 在表单类型为 {@link BpmModelFormTypeEnum#CUSTOM} 时，必须非空
     */
    private String formCustomCreatePath;

    /**
     * 自定义表单的查看路径，使用 Vue 的路由地址
     * 示例值: /bpm/oa/leave/view
     * 在表单类型为 {@link BpmModelFormTypeEnum#CUSTOM} 时，必须非空
     */
    private String formCustomViewPath;



    /**
     * 部门单位Id
     */
    private Integer structId;


    public void setCategory(String category) {
        this.category = category;
        this.categoryStr = BpmModelCategoryEnum.getEnum(category).getDesc();
    }

    public void setBusinessType(Byte businessType) {
        this.businessType = businessType;
        this.businessTypeStr = ModelEnum.BusinessTypeEnum.getNameByCode(businessType);
    }
}
