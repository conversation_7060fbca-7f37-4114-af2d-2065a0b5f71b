package com.mrcar.gov.common.dto.config;

import lombok.Data;

import java.io.Serializable;

/**
 * 行政区信息返回对象.
 *
 * <AUTHOR>
 * @date 2024-02-27
 */
@Data
public class DistrictRespDTO implements Serializable {

    /**
     * 行政区编码
     */
    private int adcode;

    /**
     * 行政区级别.
     * 1 - 省级
     * 2 - 市级
     * 3 - 区县
     */
    private int level;

    /**
     * 行政区名称
     */
    private String name;

    /**
     * 对应省份信息(如存在)
     */
    private DistrictRespDTO province;

    /**
     * 对应城市信息(如存在)
     */
    private DistrictRespDTO city;

}
