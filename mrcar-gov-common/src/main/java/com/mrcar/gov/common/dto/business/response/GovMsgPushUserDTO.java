package com.mrcar.gov.common.dto.business.response;

import com.mrcar.gov.common.constant.user.GovUserStatusEnum;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/28 08:42
 */
@Data
public class GovMsgPushUserDTO {

    /**
     * 用户编码
     */
    private String userCode;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 用户手机号
     */
    private String mobile;



    /**
     * 人员状态；1：正常；0：停用
     */
    private Integer userStatus;
    /**
     * 人员状态描述
     */
    private String userStatusDesc;


    /**
     * 用户邮箱
     */
    private String email;


    /**
     * 员工所属部门
     */
    private String belongStructCode;

    private String belongStructName;

    /**
     * 所属单位
     */
    private String belongDeptCode;

    private String belongDeptName;

    /**
     * 企业id
     */
    private Integer companyId;

    /**
     * 服务商id 维修厂 保险公司
     */
    private String serviceId;
    /**
     * 角色编码
     */
    private List<String> roleCodeList;
    /**
     * 角色名称列表
     */
    private String roleNameListStr;



    public void setUserStatus(Integer userStatus){
        this.userStatus = userStatus;
        this.userStatusDesc = GovUserStatusEnum.getDescByCode(userStatus);
    }

}
