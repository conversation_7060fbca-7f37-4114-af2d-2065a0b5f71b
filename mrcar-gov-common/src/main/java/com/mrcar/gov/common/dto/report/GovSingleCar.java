package com.mrcar.gov.common.dto.report;

import com.mrcar.gov.common.constant.user.ManageCarTypeEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Objects;

@Data
public class GovSingleCar {
    /**
     * 车牌号
     */
    private String vehicleLicense;

    /**
     * 订单数
     */
    private Integer orderNum = 0;

    /**
     * 无任务订单数
     */
    private Integer noTaskOrderNum = 0;

    /**
     * 无任务订单比例
     */
    private BigDecimal noTaskOrderRatio = BigDecimal.ZERO;

    /**
     * 无任务订单比例，百分比展示
     */
    private String noTaskOrderRatioStr = "0%";

    /**
     * 行驶里程（KM）
     */
    private BigDecimal driveDistance = BigDecimal.ZERO;

    /**
     * 行驶时长（小时）
     */
    private BigDecimal driveTime = BigDecimal.ZERO;

    /**
     * 车辆使用率
     */
    private BigDecimal vehicleUseRate = BigDecimal.ZERO;

    /**
     * 车辆使用率，百分比展示
     */
    private String vehicleUseRateStr = "0%";

    /**
     * 使用时长率
     */
    private BigDecimal driveTimeRate = BigDecimal.ZERO;

    /**
     * 使用时长率,因为最大值为300%,将其缩放到100%以内
     */
    private BigDecimal driveTimeRateDisplay;

    /**
     * 使用时长率，百分比展示
     */
    private String driveTimeRateStr = "0%";

    /**
     * 运行总费用
     */
    private BigDecimal totalFee;

    /**
     * 百公里费用
     */
    private BigDecimal hundredKMFee;

    /**
     * 加油费
     */
    private BigDecimal oilFee;

    /**
     * 加油量
     */
    private BigDecimal oilQuantity;

    /**
     * 过路过桥费
     */
    private BigDecimal tollFee;

    /**
     * 保险费
     */
    private BigDecimal insuranceFee;

    /**
     * 维修保养费
     */
    private BigDecimal maintainFee;

    /**
     * 年检费
     */
    private BigDecimal annualInspectionFee;

    /**
     * 其他费用
     */
    private BigDecimal otherFee;

    /**
     * 车辆类型
     */
    private String vehicleTypeStr;

    /**
     * 使用性质
     */
    private String vehicleUseTypeStr;

    /**
     * 是否自主品牌
     */
    private String selfBrandStr;

    /**
     * 动力类型
     */
    private String powerTypeStr;

    /**
     * 注册时间
     */
    private String registerTimeStr;

    /**
     * 车辆所有人
     */
    private String ownerName;

    /**
     * 管理部门
     */
    private String manageDeptName;
    /**
     * 管理部门类型
     */
    private Integer manageCarType;
    /**
     * 管理部门类型-文字描述
     */
    private String manageCarTypeStr;
    /**
     * 车辆编码
     */
    private String vehicleNo;

    /**
     * 洗车费
     */
    private BigDecimal carWashFee;

    public void setManageCarType(Integer manageCarType){
        this.manageCarType = manageCarType;
        this.manageCarTypeStr = ManageCarTypeEnum.getDesByCode(manageCarType);
    }


    public BigDecimal getTotalFee() {
        BigDecimal sum = BigDecimal.ZERO;
        if(Objects.nonNull(oilFee)){
            sum = sum.add(oilFee);
        }
        if(Objects.nonNull(tollFee)){
            sum = sum.add(tollFee);
        }
        if(Objects.nonNull(insuranceFee)){
            sum = sum.add(insuranceFee);
        }
        if(Objects.nonNull(maintainFee)){
            sum = sum.add(maintainFee);
        }
        if(Objects.nonNull(annualInspectionFee)){
            sum = sum.add(annualInspectionFee);
        }
        if(Objects.nonNull(otherFee)){
            sum = sum.add(otherFee);
        }
        if(Objects.nonNull(carWashFee)){
            sum = sum.add(carWashFee);
        }
        return sum;
    }

    /**
     * 对各个字段初始化默认值
     */
    public void intValue(){
        this.orderNum = 0;
        this.noTaskOrderNum = 0;
        this.noTaskOrderRatio = BigDecimal.ZERO;
        this.noTaskOrderRatioStr = "";
        this.driveDistance = BigDecimal.ZERO;
        this.driveTime = BigDecimal.ZERO;
        this.vehicleUseRate = BigDecimal.ZERO;
        this.vehicleUseRateStr = "";
        this.driveTimeRate = BigDecimal.ZERO;
        this.driveTimeRateDisplay = BigDecimal.ZERO;
        this.driveTimeRateStr = "";
        this.totalFee = BigDecimal.ZERO;
        this.hundredKMFee = BigDecimal.ZERO;
        this.oilFee = BigDecimal.ZERO;
        this.oilQuantity = BigDecimal.ZERO;
        this.tollFee = BigDecimal.ZERO;
        this.insuranceFee = BigDecimal.ZERO;
        this.maintainFee = BigDecimal.ZERO;
        this.annualInspectionFee = BigDecimal.ZERO;
        this.otherFee = BigDecimal.ZERO;
        this.vehicleTypeStr = "";
        this.vehicleUseTypeStr = "";
        this.selfBrandStr = "";
        this.powerTypeStr = "";
        this.registerTimeStr = "";
        this.ownerName = "";
        this.manageDeptName = "";
    }
}
