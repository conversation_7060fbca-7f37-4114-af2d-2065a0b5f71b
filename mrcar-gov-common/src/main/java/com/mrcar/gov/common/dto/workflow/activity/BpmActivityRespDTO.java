package com.mrcar.gov.common.dto.workflow.activity;

import lombok.Data;
import java.util.Date;

/**
 * 管理后台 - 流程活动的响应数据传输对象 (Response VO)
 *
 * 该类用于表示流程活动的基本信息，包括活动标识、活动类型、开始时间、结束时间等。
 */
@Data
public class BpmActivityRespDTO {

    /**
     * 流程活动的标识
     *
     * 例如："1024"
     */
    private String key;

    /**
     * 流程活动的类型
     *
     * 例如："StartEvent"
     */
    private String type;

    /**
     * 流程活动的开始时间
     */
    private Date startTime;

    /**
     * 流程活动的结束时间
     */
    private Date endTime;

    /**
     * 关联的流程任务的编号
     *
     * 仅在某些类型（如 UserTask）时有效，例如："2048"
     */
    private String taskId;

}
