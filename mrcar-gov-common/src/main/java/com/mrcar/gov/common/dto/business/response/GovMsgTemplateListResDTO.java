package com.mrcar.gov.common.dto.business.response;

import com.mrcar.gov.common.constant.business.GovMsgModuleEnum;
import com.mrcar.gov.common.constant.business.GovMsgPushTypeEnum;
import com.mrcar.gov.common.constant.business.GovMsgTemplateStatusEnum;
import com.mrcar.gov.common.constant.business.GovMsgSceneEnum;
import lombok.Data;

import java.util.Date;

/**
 * Author:  wangM
 * Date:  2024/12/27 15:23
 * DESC:消息模板列表响应DTO
 */
@Data
public class GovMsgTemplateListResDTO {

    /**
     * 模版ID
     */
    private Integer id;

    /**
     * 模板编码
     */
    private String templateNo;
    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 模板内容
     */
    private String templateContent;
    /**
     * 1公文公告;2系统消息;3用车通知;4审批通知;5监控报警;6车务消息;7维保通知;
     */
    private Integer msgModule;

    /**
     * 模板类型名称
     */
    private String msgModuleDesc;
    /**
     * 模板状态 0:停用 1:启用
     */
    private Integer templateStatus;
    /**
     * 模板状态名称 0:停用 1:启用
     */
    private String templateStatusName;

    /**
     * 推送方式列表，多个使用逗号分割 1短信 2站内信
     */
    private String pushTypeList;

    /**
     * 推送方式列表名称，多个使用逗号分割 1短信 2站内信
     */
    private String pushTypeListDesc;

    /**
     * 创建时间
     */
    private Date createTime;


    /**
     * 更新人名称
     */
    private String updateName;

    /**
     * 更新时间
     */
    private Date updateTime;


    private Integer msgScene;

    private String msgSceneDesc;


    public String getMsgModuleDesc() {

        return GovMsgModuleEnum.getDescByCode(msgModule);
    }

    public String getTemplateStatusName() {

        return GovMsgTemplateStatusEnum.getDescByCode(templateStatus);

    }

    public String getPushTypeListDesc() {

        return GovMsgPushTypeEnum.getDescByCodes(pushTypeList);

    }

    public String getMsgSceneDesc() {
        return GovMsgSceneEnum.getDescByCode(msgScene);
    }
}
