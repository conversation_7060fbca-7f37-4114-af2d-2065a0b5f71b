package com.mrcar.gov.common.constant.business;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/12/30 13:41
 */
@AllArgsConstructor
@Getter
public enum GovMsgPushLogStatusEnum {
    // 删除
    DELETE(0, "删除"),
    // 正常
    NORMAL(1, "正常");

    private Integer code;

    private String desc;

    public static String getDescByCode(Integer logStatus) {
        for (GovMsgPushLogStatusEnum statusEnum : GovMsgPushLogStatusEnum.values()) {
            if (Objects.equals(statusEnum.getCode(), logStatus)) {
                return statusEnum.getDesc();
            }
        }
        return "";
    }
}
