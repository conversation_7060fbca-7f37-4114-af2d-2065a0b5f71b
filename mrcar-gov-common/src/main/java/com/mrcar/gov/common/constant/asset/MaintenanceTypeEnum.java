package com.mrcar.gov.common.constant.asset;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum MaintenanceTypeEnum {

    // 工单类型；1:事前下单 2：事后补录
    PRE_ORDER(1, "事前下单"),  // 事前下单
    POST_RECORD(2, "事后补录"); // 事后补录,
    ;

    private final Integer code;
    private final String name;

    public static String getName(Integer code) {
        return Arrays.stream(values()).filter(e -> e.getCode().equals(code)).findFirst().map(MaintenanceTypeEnum::getName).orElse(null);
    }
}