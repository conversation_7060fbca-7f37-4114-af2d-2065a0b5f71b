package com.mrcar.gov.common.dto.thirdparty.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @date 2019-11-05 17:31
 */
@Data
public class VehicleLicenseDTO {

    private static final Logger logger = LoggerFactory.getLogger(VehicleLicenseDTO.class);

    /**
     * 核定载客人数
     */
    @JSONField(name = "appproved_passenger_capacity")
    private String appprovedPassengerCapacity;

    /**
     * 核定载质量
     */
    @JSONField(name = "approved_load")
    private String approvedLoad;

    /**
     * 档案编号
     */
    @JSONField(name = "file_no")
    private String fileNo;

    /**
     * 总质量
     */
    @JSONField(name = "gross_mass")
    private String grossMass;

    /**
     * 校验记录
     */
    @JSONField(name = "inspection_record")
    private String inspectionRecord;

    /**
     * 外廓尺寸
     */
    @JSONField(name = "overall_dimension")
    private String overallDimension;

    /**
     * 准牵引总质量
     */
    @JSONField(name = "traction_mass")
    private String tractionMass;

    /**
     * 整备质量
     */
    @JSONField(name = "unladen_mass")
    private String unladenMass;

    /**
     * 号牌号码
     */
    @JSONField(name = "plate_num")
    private String plateNum;

    /**
     * 车辆类型
     */
    @JSONField(name = "vehicle_type")
    private String vehicleType;

    /**
     * 所有人名称
     */
    private String owner;

    /**
     * 使用性质
     */
    @JSONField(name = "use_character")
    private String useCharacter;

    /**
     * 地址
     */
    private String addr;

    /**
     * 品牌型号
     */
    private String model;

    /**
     * 车辆识别代号
     */
    private String vin;

    /**
     * 发动机号码
     */
    @JSONField(name = "engine_num")
    private String engineNum;

    /**
     * 注册日期
     */
    @JSONField(name = "register_date")
    private String registerDate;

    /**
     * 发证日期
     */
    @JSONField(name = "issue_date")
    private String issueDate;

    /**
     * 响应是否成功
     */
    private boolean success;

    /**
     * 签发机关
     */
    @JSONField(name = "issue_authority")
    private String issueAuthority;
}
