package com.mrcar.gov.common.constant.asset;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.stream.Stream;

@Getter
public enum VehicleFeeEnum {
    FUEL_COST(1, "加油费"),
    INSURANCE_COST(2, "保险费"),
    MAINTENANCE_COST(3, "维修保养费"),
    TOLL_COST(4, "过路过桥费"),
    CAR_WASH_COST(5, "洗车费"),
    OTHER_COST(6, "其他费用");

    private final int code;
    private final String description;

    VehicleFeeEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据代码获取枚举实例。
     *
     * @param code 费用类型的代码。
     * @return 对应的枚举实例，如果未找到则返回 null。
     */
    public static VehicleFeeEnum fromCode(int code) {
        for (VehicleFeeEnum type : VehicleFeeEnum.values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        return null;
    }
}
