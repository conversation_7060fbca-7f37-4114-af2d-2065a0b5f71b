package com.mrcar.gov.common.dto.workflow.rule;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 管理后台 - 流程任务分配规则的 Response VO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BpmTaskAssignRuleRespDTO extends BpmTaskAssignRuleBaseDTO {

    /**
     * 任务分配规则的编号
     * 必填
     * 示例值: 1024
     */
    private Long id;

    /**
     * 流程模型的编号
     * 必填
     * 示例值: 2048
     */
    private String modelId;

    /**
     * 流程定义的编号
     * 必填
     * 示例值: 4096
     */
    private String processDefinitionId;

    /**
     * 流程任务定义的编号
     * 必填
     * 示例值: 2048
     */
    private String taskDefinitionKey;

    /**
     * 流程任务定义的名字
     * 必填
     * 示例值: 关注用车
     */
    private String taskDefinitionName;

}
