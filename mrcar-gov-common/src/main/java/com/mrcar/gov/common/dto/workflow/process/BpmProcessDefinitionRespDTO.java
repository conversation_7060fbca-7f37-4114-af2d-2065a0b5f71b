package com.mrcar.gov.common.dto.workflow.process;

import com.mrcar.gov.common.dto.workflow.enums.BpmModelCategoryEnum;
import com.mrcar.gov.common.dto.workflow.enums.BpmModelFormTypeEnum;
import lombok.Data;
import javax.validation.constraints.NotEmpty;
import java.util.Date;
import java.util.List;

/**
 * 管理后台 - 流程定义 Response VO
 */
@Data
public class BpmProcessDefinitionRespDTO {

    /**
     * 编号
     * 必填
     * 示例值: 1024
     */
    private String id;

    /**
     * 版本
     * 必填
     * 示例值: 1
     */
    private Integer version;

    /**
     * 流程名称
     * 必填
     * 示例值: 用车
     */
    @NotEmpty(message = "流程名称不能为空")
    private String name;

    /**
     * 流程描述
     * 示例值: 我是描述
     */
    private String description;

    /**
     * 流程分类
     * 参见 bpm_model_category 数据字典
     * 示例值: 1
     */
    @NotEmpty(message = "流程分类不能为空")
    private String category;
    private String categoryStr;

    /**
     * 表单类型
     * 参见 bpm_model_form_type 数据字典
     * 示例值: 1
     */
    private Integer formType;

    /**
     * 表单编号
     * 示例值: 1024
     * 在表单类型为 {@link BpmModelFormTypeEnum#CUSTOM} 时，必须非空
     */
    private Long formId;

    /**
     * 表单的配置
     * 必填
     * JSON 字符串。在表单类型为 {@link BpmModelFormTypeEnum#CUSTOM} 时，必须非空
     */
    private String formConf;

    /**
     * 表单项的数组
     * 必填
     * JSON 字符串的数组。在表单类型为 {@link BpmModelFormTypeEnum#CUSTOM} 时，必须非空
     */
    private String formFields;
    private List<String> formFieldsList; // 方便前端使用

    /**
     * 自定义表单的提交路径，使用 Vue 的路由地址
     * 示例值: /bpm/oa/leave/create
     * 在表单类型为 {@link BpmModelFormTypeEnum#CUSTOM} 时，必须非空
     */
    private String formCustomCreatePath;

    /**
     * 自定义表单的查看路径，使用 Vue 的路由地址
     * 示例值: /bpm/oa/leave/view
     * 在表单类型为 {@link BpmModelFormTypeEnum#CUSTOM} 时，必须非空
     */
    private String formCustomViewPath;

    /**
     * 中断状态
     * 必填
     * 示例值: 1
     * 参见 SuspensionState 枚举
     */
    private Integer suspensionState;

    /**
     * 关联子流程名称
     * 示例值: 中铁建审批
     */
    private String subProcessStr;

    /**
     * 关联父流程名称
     * 示例值: 中铁建审批
     */
    private String parentProcessStr;

    /**
     * 流程定义创建时间
     */
    private Date modelCreateTime;

    public void setCategory(String category) {
        this.category = category;
        this.categoryStr = BpmModelCategoryEnum.getEnum(category).getDesc();
    }
}
