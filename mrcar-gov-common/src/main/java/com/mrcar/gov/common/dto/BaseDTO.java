package com.mrcar.gov.common.dto;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.gson.annotations.Expose;
import com.mrcar.gov.common.constant.user.GovUserTypeEnum;
import com.mrcar.gov.common.dto.session.GovUserSessionInfoDTO;
import com.mrcar.gov.common.dto.session.OrgInfo;
import lombok.Data;

import java.util.Objects;
import java.util.Set;

/**
 * 公共的用户信息
 */
@Data
public class BaseDTO {

    // 当前登录用户ID
    @Expose(serialize = false)
    private Integer loginUserId;

    // 当前登录人编码
    @Expose(serialize = false)
    private String loginUserCode;

    // 当前登录用户名
    @Expose(serialize = false)
    private String loginUserName;

    // 当前登录手机号
    @Expose(serialize = false)
    private String loginUserMobile;

    // 当前登录企业ID
    @Expose(serialize = false)
    private Integer loginCompanyId;

    // 当前登录企业code
//    @Expose(serialize = false)
//    private String loginCompanyCode;

    // 当前登录企业名
    @Expose(serialize = false)
    private String loginCompanyName;

    // 当前登录用户所属长租客户 code
    @Expose(serialize = false)
    private String loginCustomerCode;

    // 数据权限类型
    @Expose(serialize = false)
    private Integer loginDataPermType;

    // 具体需要查询的权限编码集合
    @Expose(serialize = false)
    private Set<String> dataCodeSet;

    // 当前登录系统;1-运营平台;2-客户平台
    @Expose(serialize = false)
    private Byte loginSystemType;

    /**
     * 员工所属部门ID
     */
    @Expose(serialize = false)
    private Integer belongStructId;

    /**
     * 员工所属部门编码
     **/
    @Expose(serialize = false)
    private String loginUserBelongStructCode;

    /**
     * 员工所属部门名称
     **/
    @Expose(serialize = false)
    private String loginUserBelongStructName;

    /**
     * 员工所属单位id
     */
    @Expose(serialize = false)
    private Integer loginUserBelongDeptId;

    /**
     * 员工所属单位编码
     */
    @Expose(serialize = false)
    private String loginUserBelongDeptCode;

    /**
     * 员工所属单位名称
     */
    @Expose(serialize = false)
    private String loginUserBelongDeptName;

    /**
     * 登录用户类型
     */
    @Expose(serialize = false)
    private Integer loginUserType;


    /**
     * 登录人所属服务机构编号
     */
    @Expose(serialize = false)
    private String loginOrgNo;

    /**
     * 登录人所属服务机构名称
     */
    @Expose(serialize = false)
    private String loginOrgName;

    /**
     * 登录人所属服务机构类型（1维保 2保险 3加油 4租赁）
     */
    @Expose(serialize = false)
    private Integer loginOrgType;



}
