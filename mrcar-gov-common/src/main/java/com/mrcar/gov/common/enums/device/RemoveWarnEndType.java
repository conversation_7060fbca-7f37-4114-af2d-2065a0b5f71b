package com.mrcar.gov.common.enums.device;

import lombok.Data;

public enum RemoveWarnEndType {
    LOCATION_REPORT(1,"收到定位"),
    DEVICE_INFO_REPORT(2,"设备上报报文");

    public int code;
    public String msg;


    RemoveWarnEndType(int code, String msg)
    {
        this.code =code;
        this.msg = msg;
    }

    public static String getMsg(Integer code){
        if(code==null)
        {
            return null;
        }
        for(RemoveWarnEndType e: RemoveWarnEndType.values()){
            if(e.getCode() == code){
                return e.getMsg();
            }
        }
        return null;
    }


    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}
