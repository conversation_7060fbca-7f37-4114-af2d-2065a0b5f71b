package com.mrcar.gov.common.util;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.NameValuePair;
import org.apache.http.client.CookieStore;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ByteArrayEntity;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.BasicCookieStore;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.impl.cookie.BasicClientCookie;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.*;
import java.util.Map.Entry;

/**
 * @ClassName HttpUtils
 * @Description HttpClient工具类
 * <AUTHOR>
 * @Date 2019/7/3 13:23
 * @Version 1.0
 */
public class HttpUtils {

    private static final Logger logger = LoggerFactory.getLogger(HttpUtils.class);

    private static PoolingHttpClientConnectionManager connMgr;
    private static RequestConfig requestConfig;
    private static final int MAX_TIMEOUT = 20000;
    private static final int READ_TIMEOUT = 20000;
    private static final int CONNECT_TIMEOUT = 5000;
    private static CloseableHttpClient httpClient=null;
    private final static Object syncLock = new Object();

    static {
        // 设置连接池
        connMgr = new PoolingHttpClientConnectionManager();
        // 设置连接池大小
        connMgr.setMaxTotal(500);

        connMgr.setDefaultMaxPerRoute(300);

        RequestConfig.Builder configBuilder = RequestConfig.custom();
        // 设置连接超时
        configBuilder.setConnectTimeout(CONNECT_TIMEOUT);
        // 设置读取超时
        configBuilder.setSocketTimeout(READ_TIMEOUT);
        // 设置从连接池获取连接实例的超时
        configBuilder.setConnectionRequestTimeout(MAX_TIMEOUT);
        // 在提交请求之前 测试连接是否可用
        configBuilder.setStaleConnectionCheckEnabled(true);
        requestConfig = configBuilder.build();
//		requestConfig = RequestConfig.custom().setCookieSpec(CookieSpecs.STANDARD_STRICT).build();
    }

    public static CloseableHttpClient getHttpClient(){
        if(httpClient == null){
            synchronized (syncLock){
                if(httpClient == null){
                    CookieStore cookieStore = new BasicCookieStore();
                    BasicClientCookie cookie = new BasicClientCookie("sessionID", "######");
                    cookie.setDomain("#####");
                    cookie.setPath("/");
                    cookieStore.addCookie(cookie);
                    httpClient = HttpClients.custom().setConnectionManager(connMgr).setDefaultCookieStore(cookieStore).setDefaultRequestConfig(requestConfig).build();
                }
            }
        }
        return httpClient;
    }

    public static String doPost(String url, Map<String, Object> headers, Map<String, Object> params)
            throws IOException {
        String response = null;
        response = doPostMuti(url, headers, params);

        return response;
    }

    public static String doPost(String url, Map<String, Object> params) throws IOException {
        String response = null;
        response = doPostMuti(url, null, params);
        return response;
    }

    public static String doGet(String url)
            throws IOException {
        CloseableHttpClient httpclient = getHttpClient();
        String result = null;
        CloseableHttpResponse response = null;
        HttpEntity httpEntity = null;

        HttpGet httpGet = new HttpGet(url);
        try {
            response = httpclient.execute(httpGet);
            if (response != null) {
                httpEntity = response.getEntity();
                result = EntityUtils.toString(httpEntity, "utf-8");
                EntityUtils.consume(httpEntity);
                logger.info("返回数据：" + result);
            } else {
                logger.info("response==null");
            }

        } finally {
            if (httpEntity != null) {
                EntityUtils.consume(httpEntity);
            }
            if (response != null) {
                response.close();
            }
            if(httpGet != null){
                httpGet.releaseConnection();
            }
        }
        return result;
    }

    public static String doJson(String url,JSONObject json)
            throws IOException {
        CloseableHttpClient httpclient = getHttpClient();
        CloseableHttpResponse response = null;
        HttpPost post = new HttpPost(url);
        HttpEntity httpEntity = null;
        String result  = null;
        try {
            StringEntity s = new StringEntity(json.toString(), "UTF-8");// 解决中文乱码问题
            s.setContentEncoding("UTF-8");
            s.setContentType("application/json");//发送json数据需要设置contentType
            post.setEntity(s);
            HttpResponse res = httpclient.execute(post);
            if(res.getStatusLine().getStatusCode() == HttpStatus.SC_OK){
                httpEntity = res.getEntity();
                result = EntityUtils.toString(httpEntity,"utf-8");// 返回json格式：
                EntityUtils.consume(httpEntity);
            }
        } catch (Exception e) {
            logger.error("json 请求工具出错",e);
        }finally {
            if (response != null) {
                response.close();
            }
            if(post != null){
                post.releaseConnection();
            }
        }
        return result;
    }


    public static String doJsonList(String url,List<JSONObject> json)
            throws IOException {
        CloseableHttpClient httpclient = getHttpClient();
        CloseableHttpResponse response = null;
        HttpPost post = new HttpPost(url);
        HttpEntity httpEntity = null;
        String result  = null;
        try {
            StringEntity s = new StringEntity(json.toString(), "UTF-8");// 解决中文乱码问题
            s.setContentEncoding("UTF-8");
            s.setContentType("application/json");//发送json数据需要设置contentType
            post.setEntity(s);
            HttpResponse res = httpclient.execute(post);
            if(res.getStatusLine().getStatusCode() == HttpStatus.SC_OK){
                httpEntity = res.getEntity();
                result = EntityUtils.toString(httpEntity,"utf-8");// 返回json格式：
                EntityUtils.consume(httpEntity);
            }
        } catch (Exception e) {
            logger.error("json 请求工具出错",e);
        }finally {
            if (response != null) {
                response.close();
            }
            if(post != null){
                post.releaseConnection();
            }
        }
        return result;
    }

    private static String doPostMuti(String url, Map<String, Object> headers, Map<String, Object> params)
            throws IOException {
        CloseableHttpClient httpclient = getHttpClient();
        String result = null;
        CloseableHttpResponse response = null;
        HttpEntity httpEntity = null;
        List<NameValuePair> nvps = new ArrayList<NameValuePair>();

        if (params != null) {
            Set<Entry<String, Object>> entrys = params.entrySet();
            Iterator<Entry<String, Object>> its = entrys.iterator();
            Entry<String, Object> entry = null;

            while (its.hasNext()) {
                entry = its.next();
                nvps.add(new BasicNameValuePair(entry.getKey(), entry.getValue()!=null?String.valueOf(entry.getValue()):""));
            }
        }

        HttpPost httpPost = new HttpPost(url);
        httpPost.setConfig(requestConfig);

        if (headers != null) {
            Set<Entry<String, Object>> entrys = headers.entrySet();
            Iterator<Entry<String, Object>> its = entrys.iterator();
            Entry<String, Object> entry = null;

            while (its.hasNext()) {
                entry = its.next();
                httpPost.addHeader(entry.getKey(), String.valueOf(entry.getValue()));
            }
        }

        httpPost.setEntity(new UrlEncodedFormEntity(nvps, "utf-8"));

        try {
            response = doPostExecute(httpclient, httpPost);
            if (response != null) {
                httpEntity = response.getEntity();
                result = EntityUtils.toString(httpEntity, "utf-8");
                EntityUtils.consume(httpEntity);
                logger.debug("返回数据：" + result);
            } else {
                logger.info("response==null");
            }

        } finally {
            if (httpEntity != null) {
                EntityUtils.consume(httpEntity);
            }
            if (response != null) {
                response.close();
            }
            if(httpPost != null){
                httpPost.releaseConnection();
            }
        }
        return result;
    }

    private static CloseableHttpResponse doPostExecute(CloseableHttpClient httpclient, HttpPost httpPost)
            throws IOException {
        CloseableHttpResponse response = null;
        response = httpclient.execute(httpPost);
        String reasonPhrase = response.getStatusLine().getReasonPhrase();
        if (response.getStatusLine().getStatusCode() != HttpStatus.SC_OK) {
            logger.info("当前响应的statusLine={} reasonPhrase={}",response.getStatusLine().getStatusCode(),reasonPhrase);
//			throw new IOException(reasonPhrase);
        }
        return response;
    }

    /**
     * post请求
     *
     * @param url
     * @param headers
     * @param json
     * @return
     */
    public static String doPost(String url, Map<String, String> headers, String json) throws IOException {
        String response = null;
        response = doPostJsonMuti(url, headers, json);
        return response;
    }

    private static String doPostJsonMuti(String url, Map<String, String> headers, String json) throws IOException {
        CloseableHttpClient httpclient = getHttpClient();
        String result = null;
        CloseableHttpResponse response = null;
        HttpEntity httpEntity = null;
        HttpPost httpPost = new HttpPost(url);
        httpPost.setConfig(requestConfig);
        if (headers != null) {
            Set<Entry<String, String>> entrys = headers.entrySet();
            Iterator<Entry<String, String>> its = entrys.iterator();
            Entry<String, String> entry = null;

            while (its.hasNext()) {
                entry = its.next();
                httpPost.addHeader(entry.getKey(), entry.getValue());
            }
        }

        StringEntity stringEntity = new StringEntity(json.toString(), "UTF-8");// 解决中文乱码问题
        stringEntity.setContentEncoding("UTF-8");
        stringEntity.setContentType("application/json");
        httpPost.setEntity(stringEntity);
        try {
            response = doPostExecute(httpclient, httpPost);
            if (response != null) {
                httpEntity = response.getEntity();
                result = EntityUtils.toString(httpEntity, "utf-8");
                EntityUtils.consume(httpEntity);
                logger.info("返回数据：" + result);
            } else {
                logger.info("response==null");
            }

        } finally {
            if (httpEntity != null) {
                EntityUtils.consume(httpEntity);
            }
            if (response != null) {
                response.close();
            }
            if(httpPost != null){
                httpPost.releaseConnection();
            }
        }
        return result;
    }

    public static byte[] doPostProtobuf(String url ,byte[] content) throws IOException {
        CloseableHttpClient httpclient = getHttpClient();
        byte[] result = null;
        CloseableHttpResponse response = null;
        HttpEntity httpEntity = null;
        HttpPost httpPost = new HttpPost(url);
        httpPost.setConfig(requestConfig);

        httpPost.setHeader("Content-Type", "application/x-www-form-urlencoded;charset=utf-8");
        httpPost.setEntity(new ByteArrayEntity(content));
        try {
            response = doPostExecute(httpclient, httpPost);
            if (response != null) {
                httpEntity = response.getEntity();
                httpEntity.getContent();
                result = EntityUtils.toByteArray(httpEntity);
                EntityUtils.consume(httpEntity);
//				logger.info("返回数据：" + result);
            } else {
                logger.info("response==null");
            }

        } finally {
            if (httpEntity != null) {
                EntityUtils.consume(httpEntity);
            }
            if (response != null) {
                response.close();
            }
            if(httpPost != null){
                httpPost.releaseConnection();
            }
        }
        return result;
    }

    public static int OK =  0 ;
    public static int FAIL = 1;


    public static JSONObject doPost(String url,JSONObject json){
        JSONObject response = null;
        try
        {
            String result = doJson(url, json);
            return JSON.parseObject(result);
        }catch (Exception e)
        {
            e.printStackTrace();
        }
        return response;
    }

    public static JSONObject doGetReturnJSON(String url){
        JSONObject response = null;
        try
        {
            String result = doGet(url);
            return JSON.parseObject(result);
        }catch (Exception e)
        {
            e.printStackTrace();
        }
        return response;
    }

}
