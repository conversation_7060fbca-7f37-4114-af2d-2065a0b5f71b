package com.mrcar.gov.common.constant.asset;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public enum VehicleFeeDetailItemEnum {
    PARKING_FEE("FD001","停车费",1),
    PASS_ROAD_FEE("FD002","过路过桥费",2),
    HOTEL_FEE("FD003","住宿费",3),
    MEALS_FEE("FD004","餐饮费",4),
    WASH_CAR_FEE("FD005","洗车费",5),
    TEMP_RENT_FEE("FD006","临时租赁费",6),
    ETC_FEE("FD007","ETC费",7),
    COMMUNICATION_FEE("FD008","通讯费",8);

    private final String code;
    private final String desc;
    private final Integer sort;

    VehicleFeeDetailItemEnum(String code, String desc,Integer sort) {
        this.code = code;
        this.desc = desc;
        this.sort = sort;
    }



    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public Integer getSort() { return sort;
    }

    public static String getDesc(String code) {
        for(VehicleFeeDetailItemEnum vehicleFeeDetailItem : VehicleFeeDetailItemEnum.values()) {
            if(vehicleFeeDetailItem.getCode() .equals(code) ) {
                return vehicleFeeDetailItem.getDesc();
            }
        }
        return "";
    }

    public static VehicleFeeDetailItemEnum getEnumByCode(String code) {
        for(VehicleFeeDetailItemEnum vehicleFeeDetailItem : VehicleFeeDetailItemEnum.values()) {
            if(vehicleFeeDetailItem.getCode() .equals(code) ) {
                return vehicleFeeDetailItem;
            }
        }
        return null;
    }

    public static List<String> getDescList() {
        return Arrays.stream(VehicleFeeDetailItemEnum.values()).map(VehicleFeeDetailItemEnum::getDesc).collect(Collectors.toList());
    }

    public static String getCodeByDesc(String desc) {
        for (VehicleFeeDetailItemEnum value : VehicleFeeDetailItemEnum.values()) {
            if (Objects.equals(value.getDesc(), desc)) {
                return value.getCode();
            }
        }
        return null;

    }
}
