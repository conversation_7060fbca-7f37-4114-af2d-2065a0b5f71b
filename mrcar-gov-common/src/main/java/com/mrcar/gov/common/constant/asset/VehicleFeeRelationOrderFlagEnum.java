package com.mrcar.gov.common.constant.asset;


public enum VehicleFeeRelationOrderFlagEnum {

    RELATION_NO(0,"未关联"),
    RELATION_YES(1,"已关联");

    private final int code;
    private final String desc;

    VehicleFeeRelationOrderFlagEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
    public static String getDesc(int code) {
        for (VehicleFeeRelationOrderFlagEnum vehicleFeeRelationOrderFlagEnum : VehicleFeeRelationOrderFlagEnum.values()) {
            if (vehicleFeeRelationOrderFlagEnum.getCode() == code) {
                return vehicleFeeRelationOrderFlagEnum.getDesc();
            }
        }
        return "";
    }

}
