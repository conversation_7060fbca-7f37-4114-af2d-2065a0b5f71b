package com.mrcar.gov.common.constant.asset;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @description: 司机车辆绑定关系枚举
 * @date 2024/11/9 13:30
 */
public enum GovVehicleDriverRelationEnum {
    NORMAL(1, "绑定"),
    EXPIRE(2, "解绑");

    private final Integer code;
    private final String desc;

    GovVehicleDriverRelationEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static GovVehicleDriverRelationEnum getByCode(Integer code) {
        return Arrays.stream(GovVehicleDriverRelationEnum.values()).filter(govVehicleDriverRelationEnum -> govVehicleDriverRelationEnum.getCode().equals(code)).findFirst().orElse(null);
    }
}
