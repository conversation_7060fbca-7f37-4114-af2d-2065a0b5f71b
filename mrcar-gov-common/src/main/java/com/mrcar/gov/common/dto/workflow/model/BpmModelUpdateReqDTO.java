package com.mrcar.gov.common.dto.workflow.model;

import com.mrcar.gov.common.dto.workflow.BaseDTO;
import com.mrcar.gov.common.dto.workflow.enums.BpmModelFormTypeEnum;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;

/**
 * 管理后台 - 流程模型的更新 Request VO
 */
@Data
public class BpmModelUpdateReqDTO extends BaseDTO {

    /**
     * 编号
     * 示例值: 1024
     */
    @NotEmpty(message = "编号不能为空")
    private String id;

    /**
     * 流程名称
     * 示例值: 用车
     */
    @Length(max = 200, message = "流程名称最多输入200个字符")
    private String name;

    /**
     * 流程描述
     * 示例值: 我是描述
     */
    @Length(max = 200, message = "流程描述最多输入200个字符")
    private String description;

    /**
     * 流程分类
     * 参见 ModelEnum 枚举
     * 示例值: 1
     */
    private String category;

    /**
     * 业务类型
     */
    private Byte businessType;

    /**
     * BPMN XML
     */
    private String bpmnXml;

    /**
     * 表单类型
     * 参见 BpmModelFormTypeEnum 枚举
     * 示例值: 1
     */
    private Integer formType;

    /**
     * 表单编号
     * 示例值: 1024
     * 在表单类型为 {@link BpmModelFormTypeEnum#CUSTOM} 时，必须非空
     */
    private Long formId;

    /**
     * 自定义表单的提交路径，使用 Vue 的路由地址
     * 示例值: /bpm/oa/leave/create
     * 在表单类型为 {@link BpmModelFormTypeEnum#CUSTOM} 时，必须非空
     */
    private String formCustomCreatePath;

    /**
     * 自定义表单的查看路径，使用 Vue 的路由地址
     * 示例值: /bpm/oa/leave/view
     * 在表单类型为 {@link BpmModelFormTypeEnum#CUSTOM} 时，必须非空
     */
    private String formCustomViewPath;

    /**
     * 批量审批开关
     */
    private Boolean batchApproval;

    /**
     * 是否关联上级
     * 示例值: 0：否 1：是
     */
    private int connectSwitch;

    /**
     * 关联流程id
     * 示例值: 8b4b9fe9-68d0-11ef-8ea4-96b1e6fae18b
     */
    private String processId;

    /**
     * 关联流程名称
     * 示例值: 中铁建审批
     */
    private String processName;


    /**
     * 部门Id
     */
    private Integer structId;
}
