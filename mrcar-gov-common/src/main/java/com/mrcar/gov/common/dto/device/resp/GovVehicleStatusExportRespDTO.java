package com.mrcar.gov.common.dto.device.resp;

import java.math.BigDecimal;
import java.util.Date;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;

import lombok.Data;

@Data
public class GovVehicleStatusExportRespDTO {
	
	//保留为了匹配车牌唯一标识
	@ExcelIgnore
	 private String id;
	/**
     * 车牌号
     */
	@ExcelProperty(value = {"车牌号"}, index = 0)
    @ColumnWidth(25)
    private String vehicleLicense;
	@ExcelProperty(value = {"动力状态"}, index = 1)
    @ColumnWidth(25)
    private String powerStatusMsg;
	@ExcelProperty(value = {"钥匙状态"}, index = 2)
    @ColumnWidth(25)
    private String keyStatusMsg;
	@ExcelProperty(value = {"钥匙插入状态"}, index = 3)
    @ColumnWidth(25)
    private String keyInsertionStatusMsg;
	@ExcelProperty(value = {"剩余油量/电量"}, index = 4)
    @ColumnWidth(25)
    private Integer remainingFuel;
	@ExcelProperty(value = {"续航里程"}, index = 5)
    @ColumnWidth(25)
    private Integer enduranceMileage;
	@ExcelProperty(value = {"总里程"}, index = 6)
    @ColumnWidth(25)
    private Integer totalMileage;
	@ExcelProperty(value = {"档位"}, index = 7)
    @ColumnWidth(25)
    private String gearsStatusMsg;
	@ExcelProperty(value = {"电瓶电压"}, index = 8)
    @ColumnWidth(25)
    private BigDecimal batteryStatus;
    @ExcelProperty(value = {"车速"}, index = 9)
    @ColumnWidth(25)
    private Integer speed;
    @ExcelProperty(value = {"驻车制动"}, index = 10)
    @ColumnWidth(25)
    private String parkingStatusMsg;
    @ExcelProperty(value = {"车门状态"}, index = 11)
    @ColumnWidth(25)
    private String doorStatusMsg;
    @ExcelProperty(value = {"车门锁状态"}, index = 12)
    @ColumnWidth(25)
    private String doorLockStatusMsg;
    @ExcelProperty(value = {"车窗状态"}, index = 13)
    @ColumnWidth(25)
    private String windowStatusMsg;
    @ExcelProperty(value = {"后尾箱状态"}, index = 14)
    @ColumnWidth(25)
    private String rearTailboxStatusMsg;
    @ExcelProperty(value = {"车灯状态"}, index = 15)
    @ColumnWidth(25)
    private String lampStatusMsg;
    @ExcelProperty(value = {"天窗状态"}, index = 16)
    @ColumnWidth(25)
    private String sunroofStatusMsg;
    @ExcelProperty(value = {"上报时间"}, index = 17)
    @ColumnWidth(25)
    private Date createDate;
    
}
