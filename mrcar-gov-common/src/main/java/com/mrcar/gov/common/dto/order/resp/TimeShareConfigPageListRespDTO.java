package com.mrcar.gov.common.dto.order.resp;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Author: mengshuang
 * @Date: 2025/4/10 18:08
 * @Param:
 * @Return:
 * @Description:
 **/
@Data
public class TimeShareConfigPageListRespDTO {
    /**
     * 自增主键
     */
    private Integer id;


    /**
     * (社会租赁)供应商name
     */
    private String supplierName;

    /**
     * 定价类型 0按车辆定价  1 按车系定价
     */
    private Integer priceType;


    /**
     * 车辆类型
     */
    private String priceTypeName;

    /**
     * 车辆类型名称
     */
    private String vehicleTypeName;


    /**
     * 车辆品牌
     */
    private String vehicleBrandName;


    /**
     * 车系
     */
    private String vehicleSeriesName;


    /**
     * 带驾里程费
     */
    private String otherDrivingMilesFee;

    /**
     * 自驾里程费
     */
    private String selfDrivingMilesFee;


    /**
     * 带驾时长费
     */
    private String otherDrivingTimeFee;

    /**
     * 自驾时长费
     */
    private String selfDrivingTimeFee;


    /**
     * 带驾里程费明细信息
     */
    List<TimeShareConfigDetailRespDTO> otherDrivingtimeShareConfigDetailList;

    /**
     * 自驾里程费明细信息
     */
    List<TimeShareConfigDetailRespDTO> selfDrivingtimeShareConfigDetailList;



    /**
     * 创建人
     */
    private String createName;

    /**
     * 创建时间
     */
    private Date createTime;


    /**
     * 修改人
     */
    private String updateName;

    /**
     * 更新时间
     */
    private Date updateTime;


    public void setPriceType(Integer priceType) {
        this.priceType = priceType;
        // 根据 priceType 的值设置 priceTypeName
        if (priceType == 0) {
            this.priceTypeName = "按车辆类型定价";
        } else if (priceType == 1) {
            this.priceTypeName = "按车系定价"; // 示例: 如果是按车系定价，设置为 2
        } else {
            this.priceTypeName = null; // 或者设置为一个默认值
        }
    }


}
