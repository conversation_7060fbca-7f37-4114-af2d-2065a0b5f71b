package com.mrcar.gov.common.constant.config;

import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;


@Deprecated
public enum BussinessTypeDictionary {
    BUSSINESS_INSIDE_VEHICLE(1, "内部用车", "Internal", EnterpriseConfigEnum.EnterpriseConfigGroupEnum.USE_CAR_TYPE.getCode(), 1),
    BUSSINESS_OUTSIDE_VEHICLE(2, "商务用车", "Business", EnterpriseConfigEnum.EnterpriseConfigGroupEnum.USE_CAR_TYPE.getCode(), 2, Boolean.TRUE),
    BUSSINESS_INSIDE_AUDIT(3, "内部用车审批", "内部用车审批"),
    BUSSINESS_SELF_HELP_AUDIT(11, "自助取还审批", "自助取还审批"),
    BUSSINESS_INSIDE_SEND_BILL(9, "发送账单", "内部用车发送账单", EnterpriseConfigEnum.EnterpriseConfigGroupEnum.INTERNAL_CAR.getCode(), 1),
    BUSSINESS_AUTH(4, "实名认证（收费）", "实名认证（收费）"),
    BUSSINESS_FACE_CLOCK(5, "人脸识别打卡", "人脸识别打卡", EnterpriseConfigEnum.EnterpriseConfigGroupEnum.BASIC_FUNCTION.getCode(), 3),
    BUSSINESS_OUTSIDE_CLOCK(6, "外勤审批", "外勤审批"),
    BUSSINESS_OIL_AUDIT(7, "加油审批", "加油审批"),
    BUSSINESS_VIOLATION(8, "自有车辆违章查询", "自有车辆违章查询", EnterpriseConfigEnum.EnterpriseConfigGroupEnum.BASIC_FUNCTION.getCode(), 5),
    BUSSINESS_SEND_SMS(10, "短信通知", "短信通知", EnterpriseConfigEnum.EnterpriseConfigGroupEnum.BASIC_FUNCTION.getCode(), 1, Boolean.TRUE),
    BUSSINESS_PRIVATECAR(12, "私车公用", "PrivateCar", EnterpriseConfigEnum.EnterpriseConfigGroupEnum.USE_CAR_TYPE.getCode(), 3),
    ENTER_MILEAGE(13, "录入里程数", "录入里程数", EnterpriseConfigEnum.EnterpriseConfigGroupEnum.INTERNAL_CAR.getCode(), 2),
    BUSSINESS_SO(14, "零散用车", "零散用车", EnterpriseConfigEnum.EnterpriseConfigGroupEnum.USE_CAR_TYPE.getCode(), 4),
    BUSSINESS_FACE_CAR_CONTROL(15, "人脸识别-车机控制", "人脸识别-车机控制", EnterpriseConfigEnum.EnterpriseConfigGroupEnum.BASIC_FUNCTION.getCode(), 4),
    BUSSINESS_PRIVATECAR_ORDER_REMARK(16, "下单备注必填", "下单备注必填", EnterpriseConfigEnum.EnterpriseConfigGroupEnum.PRIVATECAR_FUNCTION.getCode(), 1),
    ALLOWED_VEHICLE_NUM(17, "自有车辆数上限", "自有车辆数上限", EnterpriseConfigEnum.EnterpriseConfigGroupEnum.BASIC_FUNCTION.getCode(), 20, Boolean.TRUE, "100"),
    OCR_CAR_REFUELING(18, "OCR识别-加油小票", "OCR识别-加油小票", EnterpriseConfigEnum.EnterpriseConfigGroupEnum.BASIC_FUNCTION.getCode(), 7),
    MILEAGE_CORRECT(19,"里程纠偏","里程纠偏", EnterpriseConfigEnum.EnterpriseConfigGroupEnum.PRIVATECAR_FUNCTION.getCode(), 2),
    SET_TYPE(20,"设备类型","设备类型", EnterpriseConfigEnum.EnterpriseConfigGroupEnum.PRIVATECAR_FUNCTION.getCode(), 3),


    OVERDUE_DAYS(21,"账期天数","设备类型", EnterpriseConfigEnum.EnterpriseConfigGroupEnum.LS.getCode(), 1),
    PAYMENT_DAY(22,"账单生成时间","设备类型", EnterpriseConfigEnum.EnterpriseConfigGroupEnum.LS.getCode(), 2),
    INVOICE_TYPE(23,"劳务公司给首汽开票类型","设备类型", EnterpriseConfigEnum.EnterpriseConfigGroupEnum.LS.getCode(), 3),

    SELF_OPERATE_FENCE(24, "还车围栏", "还车围栏", EnterpriseConfigEnum.EnterpriseConfigGroupEnum.SELF_CAR.getCode(), 1),
    SELF_OPERATE_POWER(25, "断油断电", "断油断电", EnterpriseConfigEnum.EnterpriseConfigGroupEnum.SELF_CAR.getCode(), 2),
    SELF_OPERATE_LOCK(26, "开关锁", "开关锁", EnterpriseConfigEnum.EnterpriseConfigGroupEnum.SELF_CAR.getCode(), 3),
    SELF_OPERATE_REPEAT(27, "允许重复下单", "允许重复下单", EnterpriseConfigEnum.EnterpriseConfigGroupEnum.SELF_CAR.getCode(), 4),
    SERVICE_DAY_MAX(28, "最大用车天数", "整日最大用车天数", EnterpriseConfigEnum.EnterpriseConfigGroupEnum.INTERNAL_CAR.getCode(), 3,Boolean.TRUE,"30"),
    ORDER_REMARKS(29, "下单备注必填", "下单备注必填", EnterpriseConfigEnum.EnterpriseConfigGroupEnum.GENERAL_CONFIGURATION.getCode(), 1),
    TIME_OUT_CANCEL(30, "超时取消时长", "超时取消时长", EnterpriseConfigEnum.EnterpriseConfigGroupEnum.INTERNAL_CAR.getCode(), 4),
    SHOW_DEPARTMENT_NAME(31, "展示部门名称", "展示部门名称", EnterpriseConfigEnum.EnterpriseConfigGroupEnum.BASIC_FUNCTION.getCode(), 2),

    // 内部用车开关锁配置
    INNER_OPERATE_LOCK(32, "开关锁", "开关锁", EnterpriseConfigEnum.EnterpriseConfigGroupEnum.INTERNAL_CAR.getCode(), 5),

    //维保审批配置
    MAINTENANCE_APPROVAL_SELF(33, "自主审批", "维保审批配置", EnterpriseConfigEnum.EnterpriseConfigGroupEnum.BASIC_FUNCTION.getCode(), 21),
    MAINTENANCE_APPROVAL_HOSTING_SELF(34, "托管审批+自主审批", "维保审批配置", EnterpriseConfigEnum.EnterpriseConfigGroupEnum.BASIC_FUNCTION.getCode(), 22),
    //预留字段
    MAINTENANCE_APPROVAL_HOSTING(35, "托管审批", "维保审批配置", EnterpriseConfigEnum.EnterpriseConfigGroupEnum.BASIC_FUNCTION.getCode(), 23),
    MAINTENANCE_APPROVAL_SELF_HOSTING(36, "自主审批+托管审批", "维保审批配置", EnterpriseConfigEnum.EnterpriseConfigGroupEnum.BASIC_FUNCTION.getCode(), 24),
    ACCIDENT_APPROVAL_SELF(37, "自主审批", "维保审批配置", EnterpriseConfigEnum.EnterpriseConfigGroupEnum.BASIC_FUNCTION.getCode(), 25),
    ACCIDENT_APPROVAL_HOSTING_SELF(38, "托管审批+自主审批", "维保审批配置", EnterpriseConfigEnum.EnterpriseConfigGroupEnum.BASIC_FUNCTION.getCode(), 26),
    //预留字段
    ACCIDENT_APPROVAL_HOSTING(39, "托管审批", "维保审批配置", EnterpriseConfigEnum.EnterpriseConfigGroupEnum.BASIC_FUNCTION.getCode(), 27),
    ACCIDENT_APPROVAL_SELF_HOSTING(40, "自主审批+托管审批", "维保审批配置", EnterpriseConfigEnum.EnterpriseConfigGroupEnum.BASIC_FUNCTION.getCode(), 28),
    ;

    private final Integer value;

    private final String text;

    private final String enName;

    private Byte enterpriseConfigGroup;

    private Integer sort;
    // 是否默认勾选
    private final Boolean checked;

    private String businessValue;


    BussinessTypeDictionary(Integer value, String text, String enName){
        this.value = value;
        this.text  = text;
        this.enName = enName;
        this.checked = Boolean.FALSE;
    }

    BussinessTypeDictionary(Integer value, String text, String enName, Byte enterpriseConfigGroup, Integer sort) {
        this.value = value;
        this.text = text;
        this.enName = enName;
        this.enterpriseConfigGroup = enterpriseConfigGroup;
        this.sort = sort;
        this.checked = Boolean.FALSE;
    }

    BussinessTypeDictionary(Integer value, String text, String enName, Byte enterpriseConfigGroup, Integer sort, Boolean checked) {
        this.value = value;
        this.text = text;
        this.enName = enName;
        this.enterpriseConfigGroup = enterpriseConfigGroup;
        this.sort = sort;
        this.checked = checked;
    }

    BussinessTypeDictionary(Integer value, String text, String enName, Byte enterpriseConfigGroup, Integer sort, Boolean checked, String businessValue) {
        this.value = value;
        this.text = text;
        this.enName = enName;
        this.enterpriseConfigGroup = enterpriseConfigGroup;
        this.sort = sort;
        this.checked = checked;
        this.businessValue = businessValue;
    }

    public Integer value() {
        return this.value;
    }
    public String text() {
        return this.text;
    }

    public Integer getValue() {
        return value;
    }

    public String getText() {
        return text;
    }

    public String getEnName() {
        return enName;
    }

    public Byte getEnterpriseConfigGroup() {
        return enterpriseConfigGroup;
    }

    public Integer getSort() {
        return sort;
    }

    public Boolean getChecked() {
        return checked;
    }

    public String getBusinessValue() {
        return businessValue;
    }

    public static BussinessTypeDictionary getBussinessByCode(Integer bizType) {
        if (null == bizType){
            return null;
        }
        for (final BussinessTypeDictionary type : BussinessTypeDictionary.values()) {
            if (Objects.equals(type.value(), bizType)) {
                return type;
            }
        }
        return null;
    }

    public static BussinessTypeDictionary getByValue(int value) {
        return Stream.of(values()).filter((c) -> c.value == value).findFirst().orElse(null);
    }

    public static List<BussinessTypeDictionary> getBusinessTypeDictionaryByEnterpriseConfigGroup(Byte enterpriseConfigGroup) {
        return Arrays.stream(values()).filter(e -> enterpriseConfigGroup.equals(e.getEnterpriseConfigGroup())).sorted(Comparator.comparingInt(BussinessTypeDictionary::getSort)).collect(Collectors.toList());
    }
    public static List<BussinessTypeDictionary> getBusinessTypeDictionaryByEnterpriseConfigGroupAndChecked(Byte enterpriseConfigGroup) {
        return Arrays.stream(values()).filter(e -> enterpriseConfigGroup.equals(e.getEnterpriseConfigGroup()) && e.getChecked()).sorted(Comparator.comparingInt(BussinessTypeDictionary::getSort)).collect(Collectors.toList());
    }

}
