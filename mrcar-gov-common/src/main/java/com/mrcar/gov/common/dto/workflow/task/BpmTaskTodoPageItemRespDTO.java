package com.mrcar.gov.common.dto.workflow.task;

import com.mrcar.gov.common.dto.workflow.enums.BpmProcessInstanceResultEnum;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import java.util.Date;

/**
 * 流程任务的 Running 进行中的分页项 Response VO
 */
@Data
public class BpmTaskTodoPageItemRespDTO {

    /**
     * 任务编号
     */
    private String id;

    /**
     * 任务名字
     */
    private String name;

    /**
     * 接收时间
     */
    private Date claimTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 激活状态
     * 参见 SuspensionState 枚举
     */
    private Integer suspensionState;

    /**
     * 任务结果
     */
    private Byte result = BpmProcessInstanceResultEnum.PROCESS.getResult(); // 待办任务-状态一定是处理中的

    /**
     * 任务结果名称
     */
    private String resultStr = BpmProcessInstanceResultEnum.PROCESS.getDesc(); // 待办任务-状态一定是处理中的

    /**
     * 所属流程实例
     */
    private ProcessInstance processInstance;

    /**
     * 流程实例
     */
    @Data
    public static class ProcessInstance {

        /**
         * 流程实例编号
         */
        private String id;

        /**
         * 流程实例名称
         */
        private String name;

        /**
         * 发起人的用户编号
         */
        private Long startUserId;

        /**
         * 发起人的用户昵称
         */
        private String startUserNickname;

        /**
         * 流程定义的编号
         */
        private String processDefinitionId;

        /**
         * 发起人的部门编号
         */
        private Integer startDeptId;

        /**
         * 发起人的部门名称
         */
        private String startDeptName;

        /**
         * 单据编号
         */
        private String businessNo;

        /**
         * 业务类型
         */
        private Byte businessType;

        /**
         * 业务类型名称
         */
        private String businessTypeName;

        public String getBusinessNo() {
            if (StringUtils.isEmpty(businessNo)) {
                return "";
            }
            return businessNo;
        }
    }
}
