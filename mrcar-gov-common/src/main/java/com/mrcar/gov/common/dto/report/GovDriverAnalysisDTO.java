package com.mrcar.gov.common.dto.report;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/12/16 10:01
 */
@Data
public class GovDriverAnalysisDTO {

    private Integer driverId;

    /**
     * 驾驶员所属单位编码
     */
    private String driverDeptCode;
    /**
     * 驾驶员所属于单位名称
     */
    private String driverDeptName;
    /**
     * 驾驶员用户编码
     */
    private String driverUserCode;
    /**
     * 驾驶员用户名称
     */
    private String driverUserName;

    /**
     * 订单数
     */
    private Integer orderNum = 0;

    /**
     * 出车天数
     */
    private Integer drivingDays = 0;
    /**
     * 订单总里程（KM）
     */
    private BigDecimal orderTotalMileage = BigDecimal.ZERO;

    /**
     * 订单总时长（小时）
     */
    private BigDecimal orderTotalDuration = BigDecimal.ZERO;


    /**
     * 总车杂费用
     */
    private BigDecimal totalFee = BigDecimal.ZERO;
    /**
     * 加油费
     */
    private BigDecimal oilFee = BigDecimal.ZERO;

    /**
     * 停车费
     */
    private BigDecimal parkingFee = BigDecimal.ZERO;
    /**
     * 过路过桥费
     */
    private BigDecimal tollFee = BigDecimal.ZERO;
    /**
     * 其他费用
     */
    private BigDecimal otherFee = BigDecimal.ZERO;



    public void addOrderNum(Integer orderNum){
        if(Objects.nonNull(orderNum)){
            this.orderNum += orderNum;
        }
    };
    public void addDrivingDays(Integer drivingDays){
        if(Objects.nonNull(drivingDays)){
            this.drivingDays += drivingDays;
        }
    }
    public void addOrderTotalMileage(BigDecimal orderTotalMileage){
        if(Objects.nonNull(orderTotalMileage)){
            this.orderTotalMileage.add(orderTotalMileage) ;
        }
    }
    public void addOrderTotalDuration(BigDecimal orderTotalDuration){
        if(Objects.nonNull(orderTotalDuration)){
            this.orderTotalDuration.add(orderTotalDuration);
        }
    }
    public void addTotalFee(BigDecimal totalFee){
        if(Objects.nonNull(totalFee)){
            this.totalFee = this.totalFee.add(totalFee);
        }
    }
    public void addOilFee(BigDecimal oilFee){
        if(Objects.nonNull(oilFee)){
            this.oilFee = this.oilFee.add(oilFee);
        }
    }
    public void addParkingFee(BigDecimal parkingFee){
        if(Objects.nonNull(parkingFee)){
            this.parkingFee = this.parkingFee.add(parkingFee);
        }
    }
    public void addTollFee(BigDecimal totalFee){
        if(Objects.nonNull(totalFee)){
            this.tollFee = this.tollFee.add(totalFee);
        }
    }
    public void addOtherFee(BigDecimal otherFee){
        if(Objects.nonNull(otherFee)){
            this.otherFee = this.otherFee.add(otherFee);
        }
    }

}
