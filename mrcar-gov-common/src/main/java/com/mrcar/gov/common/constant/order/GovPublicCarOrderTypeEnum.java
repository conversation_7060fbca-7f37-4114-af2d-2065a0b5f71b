package com.mrcar.gov.common.constant.order;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum GovPublicCarOrderTypeEnum {

    // 订单类型：1-办公用车，2-无任务用车
    PUBLIC_USE_CAR_TYPE(1, "办公用车"),
    NO_TASK_USE_CAR_TYPE(2, "紧急用车"),
    ;

    private final Integer code;
    private final String name;

    public static String getName(Integer code) {
        return Arrays.stream(values()).filter(e -> e.getCode().equals(code)).findFirst().map(GovPublicCarOrderTypeEnum::getName).orElse(null);
    }
}