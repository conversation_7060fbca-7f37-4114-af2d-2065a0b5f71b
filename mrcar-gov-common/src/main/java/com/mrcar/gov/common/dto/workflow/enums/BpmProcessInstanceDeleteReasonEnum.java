package com.mrcar.gov.common.dto.workflow.enums;

import cn.hutool.core.text.CharSequenceUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 流程实例的删除原因
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum BpmProcessInstanceDeleteReasonEnum {

    // 修改文案时，需要注意 isRejectReason 方法
    REJECT_TASK("不通过任务，原因：{}"),
    CANCEL_TASK("主动取消任务，原因：{}"),
    BACK_TASK("退回至{}，原因：{}"),

    // ========== 流程任务的独有原因 ==========
    // 多实例满足 condition 而结束时，其它任务实例任务会被取消，对应的删除原因是 MI_END
    MULTI_TASK_END("系统自动取消，原因：多任务审批操作已由其他人审批完成，无需处理。"),
    ;
    private final String reason;
    private static final String REASON_CHANGE = "Change parent activity";
    private static final String REASON_MI_END = "MI_END";

    /**
     * 格式化理由
     *
     * @param args 参数
     * @return 理由
     */
    public String format(Object... args) {
        return CharSequenceUtil.format(reason, args);
    }

    // ========== 逻辑 ==========

    public static boolean isRejectReason(String reason) {
        return CharSequenceUtil.startWith(reason, "不通过任务，原因：");
    }

    /**
     * 将 Flowable 的删除原因，翻译成对应的中文原因
     *
     * @param reason 原始原因
     * @return 原因
     */
    public static String translateReason(String reason) {
        if (CharSequenceUtil.isEmpty(reason)) {
            return reason;
        }
        if (reason.startsWith(REASON_CHANGE)){
            return MULTI_TASK_END.getReason();
        }
        if (REASON_MI_END.equals(reason)) {
            return MULTI_TASK_END.getReason();
        }else {
            return reason;
        }
    }

}
