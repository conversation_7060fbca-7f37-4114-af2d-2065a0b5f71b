package com.mrcar.gov.common.constant.config;

/**
 * <AUTHOR> dongxiya 2023/4/7 14:22
 */
public class BaseEnum {
    /**
     * 一级分类枚举
     */
    public enum FirstLevelEnum{

        DRIVER_FEE((byte)1, "车杂费细项"),
        CAR_REFUELING_RECORD_OCR_PATTERN((byte)2, "加油记录ocr识别正则"),
        CAR_REFUELING_RECORD_OIL_CONSUMPTION_THRESHOLD((byte)3, "加油记录百公里油耗阈值"),
        SELF_HELP_ORDER_QUESTION_SUBMIT((byte)4,"自助取还问题反馈")
        ;

        /**
         * 枚举code code
         */
        private final Byte code;

        /**
         * 枚举名称 name
         */
        private final String name;

        FirstLevelEnum(Byte code, String name) {
            this.code = code;
            this.name = name;
        }

        public Byte getCode() {
            return code;
        }

        public String getName() {
            return name;
        }
    }
}
