package com.mrcar.gov.common.constant.user;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 用户添加来源
 * <AUTHOR>
 * @date 2024/11/25 11:42
 */
@AllArgsConstructor
@Getter
public enum GovUserAddSourceEnum {
    // 用户添加
    USER_ADD(1, "用户添加"),
    // 机构添加
    ORG_ADD(3, "机构添加"),
    // 司机添加
    DRIVER_ADD(4, "司机添加"),
    SUPPLIER_DRIVER_ADD(5, "供应商司机添加"),
    ORG_USER_ADD(6, "司机添加"),
    ;

    private Integer code;

    private String desc;

}
