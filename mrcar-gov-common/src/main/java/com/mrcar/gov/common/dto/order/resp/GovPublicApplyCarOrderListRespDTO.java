package com.mrcar.gov.common.dto.order.resp;

import io.swagger.annotations.ApiModel;
import lombok.Data;


@ApiModel("订单列表返回参数")
@Data
public class GovPublicApplyCarOrderListRespDTO extends GovPublicApplyButtonRespDTO {

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 订单类型：1-办公用车，2-无任务用车
     */
    private Integer orderType;

    /**
     * 订单类型：1-办公用车，2-无任务用车
     */
    private String orderTypeStr;

    /**
     * 订单状态(10待审批  20待调度 30待出发 40用车中 50已完成 60已取消 70 审批撤回 80 审批驳回 90已结束)
     */
    private Integer orderStatus;

    /**
     * 订单状态(10待审批  20待调度 30待出发 40用车中 50已完成 60已取消 70 审批撤回 80 审批驳回 90已结束)
     */
    private String orderStatusStr;

    /**
     * 乘车人信息 接口拼好返回
     */
    private String passengerUserInfo;

    /**
     * 驾驶员信息 接口拼好返回
     */
    private String driverInfo;

    /**
     * 车牌号
     */
    private String vehicleLicense;

    /**
     * 品牌
     */
    private String vehicleBrandName;

    /**
     * 车型
     */
    private String vehicleModelName;

    /**
     * 出发地
     */
    private String estimatedDepartureLocation;

    /**
     * 目的地
     */
    private String estimatedDestinationLocation;

    /**
     * 预计开始时间
     */
    private String expectedPickupTime;

    /**
     * 预计结束时间
     */
    private String expectedReturnTime;


    /**
     * 预计开始时间
     */
    private String expectedPickupTimeForVehicle;

    /**
     * 预计结束时间
     */
    private String expectedReturnTimeForVehicle;

    /**
     * 车辆所属单位
     */
    private String vehicleBelongDeptName;

    /**
     * 车辆所属单位code
     */
    private String vehicleBelongDeptCode;

    /**
     * 核实状态 核实状态 0:未核实 1:已核实 2:无需核实
     */
    private Integer verifyStatus;

    /**
     * 核实状态 核实状态 0:未核实 1:已核实 2:无需核实
     */
    private String verifyStatusStr;

    /**
     * 车辆类型
     */
    private Integer vehicleType;

    /**
     * 车辆类型
     */
    private String vehicleTypeStr;

    /**
     * 用车事由，描述用户为何需要使用车辆
     */
    private String carUseReason;


    /**
     * 调度类型：0-无需调度，1-调度
     */
    private Integer scheduleType;
}
