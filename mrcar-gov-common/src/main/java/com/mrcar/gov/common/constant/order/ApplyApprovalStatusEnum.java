package com.mrcar.gov.common.constant.order;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum ApplyApprovalStatusEnum {

    //申请单审核状态
    PENDING(1, "待审核"),
    APPROVED(2, "审核通过"),
    NOT_REQUIRED(3, "无需审核"),
    REJECTED(4, "审核驳回"),
    WITHDRAWN(5, "审核撤回"),
    ;

    private final Integer code;
    private final String name;

    public static String getName(Integer code) {
        return Arrays.stream(values()).filter(e -> e.getCode().equals(code)).findFirst().map(ApplyApprovalStatusEnum::getName).orElse(null);
    }
}