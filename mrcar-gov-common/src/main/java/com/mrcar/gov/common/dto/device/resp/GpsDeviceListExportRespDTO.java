package com.mrcar.gov.common.dto.device.resp;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.mrcar.gov.common.enums.device.GovGpsDeviceEnum;
import lombok.Data;

import java.util.Date;

@Data
public class GpsDeviceListExportRespDTO {
    /**
     * 设备编号
     */
    @ExcelProperty(value = {"设备号"}, index = 0)
    @ColumnWidth(25)
    private String deviceNo;
    /**
     * SIM卡号
     */
    @ExcelProperty(value = {"SIM卡号"}, index = 1)
    @ColumnWidth(25)
    private String simNo;
    /**
     * 设备所属厂商名称
     */
    @ExcelProperty(value = {"设备厂商"}, index = 2)
    @ColumnWidth(25)
    private String manufactName;
    /**
     * 设备所属型号名称
     */
    @ExcelProperty(value = {"设备型号"}, index = 3)
    @ColumnWidth(25)
    private String modelName;
    /**
     * 设备类型详情
     */
    @ExcelProperty(value = {"设备类型"}, index = 4)
    @ColumnWidth(25)
    private String deviceTypeMsg;
    /**
     * 车牌号
     */
    @ExcelProperty(value = {"车牌号"}, index = 5)
    @ColumnWidth(25)
    private String vehicleLicense;

    /**
     * 车架号
     */
    @ExcelProperty(value = {"车架号"}, index = 6)
    @ColumnWidth(25)
    private String vehicleVin;

    /**
     * 最新绑定时间
     */
    @ExcelProperty(value = {"绑定时间"}, index = 7)
    @ColumnWidth(25)
    private Date bindTime;

    /**
     * 设备类型code
     */
    @ExcelIgnore
    private Integer deviceType;


    public String getDeviceTypeMsg() {
        return GovGpsDeviceEnum.DeviceTypeEnum.getEnumMsgByCode(this.deviceType);
    }


}
