package com.mrcar.gov.common.dto.order.resp;

import com.mrcar.gov.common.dto.device.resp.BdTravelDTO;
import com.mrcar.gov.common.dto.iot.resp.CarGpsFenceDTO;
import com.mrcar.gov.common.dto.iot.resp.OfficialVehicleWarnRecordDTO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;


@Data
public class GovPublicCarOrderDetailDTO {

    /**
     * 行程单号(方便前端从台账入口进入 展示tab)
     */
    private String orderNo;

    /**
     * 订单状态
     */
    private Integer orderStatus;

    /**
     * 预计目的地的纬度
     */
    private BigDecimal estimatedDestinationLatitude;

    /**
     * 预计目的地的经度
     */
    private BigDecimal estimatedDestinationLongitude;

    /**
     * 用车信息
     */
    private GovPublicVehicleInfoDTO vehicleInfo;

    /**
     * 分时日纬度用车详情
     */
    private List<GovPublicTimeShareFeeDTO> timeShareFeeList;

    /**
     * 操作日志
     */
    private List<GovPublicCarOrderOperationLogDTO> operationLogList;

    /**
     * 订单报警记录
     */
    private OfficialVehicleWarnRecordDTO warnRecord;

    /**
     * 围栏信息
     */
    private List<CarGpsFenceDTO> gpsFenceList;

    /**
     * 驾车路线规划
     */
    private BdTravelDTO bdTravelDTO;

    /**
     * 车辆服务类型 （1 定点车 2 平台车）
     */
    private Integer vehicleServiceType;

    /**
     * 车辆服务类型 （1 定点车 2 平台车）
     */
    private String vehicleServiceTypeStr;

    /**
     * 车辆管理单位名称
     */
    private String vehicleManagementDeptName;
}