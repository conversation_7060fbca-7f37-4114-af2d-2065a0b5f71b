package com.mrcar.gov.common.dto.thirdparty.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

@Data
public class InvoiceDTO {

    /**
     * codes
     */
    @JSONField(name ="codes")
    private List<CodesBean> codes;
    /**
     * data
     */
    @JSONField(name ="data")
    private DataBean data;
    /**
     * orgWidth
     */
    @JSONField(name ="orgWidth")
    private int orgWidth;
    /**
     * width
     */
    @JSONField(name ="width")
    private int width;
    /**
     * angle
     */
    @JSONField(name ="angle")
    private int angle;
    /**
     * orgHeight
     */
    @JSONField(name ="orgHeight")
    private int orgHeight;
    /**
     * prismKeyvalueinfo
     */
    @JSONField(name ="prism_keyValueInfo")
    private List<PrismKeyValueInfoBean> prismKeyvalueinfo;
    /**
     * height
     */
    @JSONField(name ="height")
    private int height;
    /**
     * sid
     */
    @JSONField(name ="sid")
    private String sid;


    @Data
    public static class DataBean {
        /**
         * 开票日期
         */
        @JSONField(name ="开票日期")
        private String invoiceDate;
        /**
         * 联次
         */
        @JSONField(name ="联次")
        private String formType;
        /**
         * 不含税金额
         */
        @JSONField(name ="不含税金额")
        private String invoiceAmountPreTax;
        /**
         * 特殊标识信息
         */
        @JSONField(name ="特殊标识信息")
        private String specialTag;
        /**
         * 受票方名称
         */
        @JSONField(name ="受票方名称")
        private String purchaserName;
        /**
         * 销售方税号
         */
        @JSONField(name ="销售方税号")
        private String sellerTaxNumber;
        /**
         * 备注
         */
        @JSONField(name ="备注")
        private String remarks;
        /**
         * 校验码
         */
        @JSONField(name ="校验码")
        private String checkCode;
        /**
         * 发票号码
         */
        @JSONField(name ="发票号码")
        private String invoiceNumber;
        /**
         * 发票金额
         */
        @JSONField(name ="发票金额")
        private String totalAmount;
        /**
         * 机打发票代码
         */
        @JSONField(name ="机打发票代码")
        private String printedInvoiceCode;
        @JSONField(name ="销售方地址、电话")
        private String sellerContactInfo;
        /**
         * 发票详单
         */
        @JSONField(name ="发票详单")
        private List<InvoiceDetailDTO> invoiceDetails;
        /**
         * 复核人
         */
        @JSONField(name ="复核人")
        private String reviewer;
        @JSONField(name ="销售方开户行、账号")
        private String sellerBankAccountInfo;
        /**
         * 发票类型
         */
        @JSONField(name ="发票类型")
        private String invoiceType;
        /**
         * 机器编码
         */
        @JSONField(name ="机器编码")
        private String machineCode;
        /**
         * 发票代码
         */
        @JSONField(name ="发票代码")
        private String invoiceCode;
        /**
         * 受票方税号
         */
        @JSONField(name ="受票方税号")
        private String purchaserTaxNumber;
        /**
         * 开票人
         */
        @JSONField(name ="开票人")
        private String drawer;
        /**
         * 销售方名称
         */
        @JSONField(name ="销售方名称")
        private String sellerName;
        /**
         * 机打发票号码
         */
        @JSONField(name ="机打发票号码")
        private String printedInvoiceNumber;
        /**
         * 发票代码解析
         */
        @JSONField(name ="发票代码解析")
        private InvoiceCodeDTO invoiceCodeDetail;
        @JSONField(name ="受票方开户行、账号")
        private String purchaserBankAccountInfo;
        /**
         * 大写金额
         */
        @JSONField(name ="大写金额")
        private String totalAmountInWords;
        /**
         * 发票税额
         */
        @JSONField(name ="发票税额")
        private String invoiceTax;
        @JSONField(name ="受票方地址、电话")
        private String purchaserContactInfo;
        /**
         * 密码区
         */
        @JSONField(name ="密码区")
        private String passwordArea;
        /**
         * 收款人
         */
        @JSONField(name ="收款人")
        private String recipient;
        /**
         * 标题
         */
        @JSONField(name ="标题")
        private String title;


        @Data
        public static class InvoiceCodeDTO {
            /**
             * 文字版
             */
            @JSONField(name ="文字版")
            private String code;
            /**
             * 印刷批次
             */
            @JSONField(name ="印刷批次")
            private String batchNo;
            /**
             * 年份
             */
            @JSONField(name ="年份")
            private String year;
            /**
             * 联次
             */
            @JSONField(name ="联次")
            private String formType;
            /**
             * 金额版
             */
            @JSONField(name ="金额版")
            private String code2;
            /**
             * 行政区划代码
             */
            @JSONField(name ="行政区划代码")
            private String areaCode;
        }

        @Data
        public static class InvoiceDetailDTO {
            /**
             * 税额
             */
            @JSONField(name ="税额")
            private String tax;
            /**
             * 单位
             */
            @JSONField(name ="单位")
            private String unit;
            /**
             * 数量
             */
            @JSONField(name ="数量")
            private String quantity;
            /**
             * 税率
             */
            @JSONField(name ="税率")
            private String taxRate;
            /**
             * 规格型号
             */
            @JSONField(name ="规格型号")
            private String specification;
            /**
             * 单价
             */
            @JSONField(name ="单价")
            private String unitPrice;
            @JSONField(name ="货物或应税劳务、服务名称")
            private String itemName;
            /**
             * 金额
             */
            @JSONField(name ="金额")
            private String amount;
        }
    }

    @Data
    public static class CodesBean {
        /**
         * data
         */
        @JSONField(name ="data")
        private String data;
        /**
         * type
         */
        @JSONField(name ="type")
        private String type;
        /**
         * points
         */
        @JSONField(name ="points")
        private List<PointsBean> points;


        @Data
        public static class PointsBean {
            /**
             * x
             */
            @JSONField(name ="x")
            private int x;
            /**
             * y
             */
            @JSONField(name ="y")
            private int y;
        }
    }

    @Data
    public static class PrismKeyValueInfoBean {
        /**
         * valuePos
         */
        @JSONField(name ="valuePos")
        private List<ValuePosBean> valuePos;
        /**
         * keyProb
         */
        @JSONField(name ="keyProb")
        private int keyProb;
        /**
         * valueProb
         */
        @JSONField(name ="valueProb")
        private int valueProb;
        /**
         * value
         */
        @JSONField(name ="value")
        private String value;
        /**
         * key
         */
        @JSONField(name ="key")
        private String key;


        @Data
        public static class ValuePosBean {
            /**
             * x
             */
            @JSONField(name ="x")
            private int x;
            /**
             * y
             */
            @JSONField(name ="y")
            private int y;
        }
    }
}
