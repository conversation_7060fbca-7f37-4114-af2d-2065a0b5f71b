package com.mrcar.gov.common.dto.device.req;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import lombok.Data;

/**
 * <AUTHOR> ON 2023/2/15.
 */
@Data
public class CarLocationTrailReqDTO {
    /**
     * 设备类型
     * 1:有线;2:无线;4:车机;6:视频
     */
	@NotEmpty(message = "设备类型不能为空")
    private String deviceType;
    /**
     * 车牌号
     */
	@NotEmpty(message = "车牌号不能为空")
    private String carNo;
    /**
     * 开始时间
     */
    @NotEmpty(message = "开始时间不能为空")
    private String beginCreateDate;
    /**
     * 结束时间
     */
    @NotEmpty(message = "结束时间不能为空")
    private String endCreateDate;
    /**
     * 设备SIM卡号不能为空
     */
    @NotEmpty(message = "设备SIM卡号不能为空")
    private String simNo;
    /**
     * 设备号
     */
    @NotEmpty(message = "设备号不能为空")
    private String deviceNo;
    /**
     * 设备系统唯一编码
     */
    @NotEmpty(message = "设备系统唯一编码不能为空")
    private String deviceSysNo;
    /**
     * 车辆唯一编号
     */
    @NotEmpty(message = "车辆唯一编号不能为空")
    private String vehicleNo;
    /**
     * 车架号
     */
    @NotEmpty(message = "车架号不能为空")
    private String vehicleVin;
    /**
     * 商户号
     */
    private Integer companyId;
    /**
     * 是否抽样
     */
    private Boolean sampling;
    
    private Integer sample;
    /**
     * 是否去重
     */
    private Boolean distinct;
    /**
     * 是否保留停车点
     */
    private Boolean stopInfo;
    /**
     * 坐标系
     * BAIDU/MARS
     */
    private String coordinate;
    /**
     * 限制条数,为空则没有限制条数，条件内所有数据
     */
    private Integer limit;
    /**
     * 排序规则
     * 默认为ture,true:按时间升序;false:按时间降序
     */
    private Boolean sort =true;;
    
    
    
}
