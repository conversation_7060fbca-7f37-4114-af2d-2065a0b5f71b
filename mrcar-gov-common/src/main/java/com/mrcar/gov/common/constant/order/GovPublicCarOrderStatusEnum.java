package com.mrcar.gov.common.constant.order;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;


@Getter
@AllArgsConstructor
public enum GovPublicCarOrderStatusEnum {

    //订单状态 10待审批  20待调度 30待出发 40用车中 50已完成 60已取消 70 审批撤回 80 审批驳回
    PENDING_APPROVAL(10, "待审批"),
    PENDING_SCHEDULE(20, "待调度"),
    PENDING_DEPARTURE(30, "待出发"),
    IN_USE(40, "用车中"),
    COMPLETED(50, "已完成"),
    CANCELED(60, "已取消"),
    APPROVAL_REVOKED(70, "审批撤回"),
    APPROVAL_REJECTED(80, "审批驳回");

    private final Integer code;
    private final String name;

    public static String getName(Integer code) {
        return Arrays.stream(values()).filter(e -> e.getCode().equals(code)).findFirst().map(GovPublicCarOrderStatusEnum::getName).orElse(null);
    }

    public static GovPublicCarOrderStatusEnum getByValue(int value) {
        for (GovPublicCarOrderStatusEnum dateType : values()) {
            if (dateType.code == value) {
                return dateType;
            }
        }
        return null;
    }
}
