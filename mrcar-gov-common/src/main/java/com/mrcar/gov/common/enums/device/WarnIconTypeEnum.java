package com.mrcar.gov.common.enums.device;

/**
 * <AUTHOR>
 * @description: icon图标类型
 * @date 2023/11/8 14:14
 */
public enum WarnIconTypeEnum {

    CAR_IN_WARN(1,"车内报警","12,13,14,15,16,18,19,32"),
    DRIVER_BEHAVING_WARN(2,"驾驶行为报警","7,8,9,31,20,21,22"),

    MIX_WARN(3,"混合报警",""),
    CAR_PARK(4,"停车点",""),
    ;
    private Integer type;

    private String desc;

    private String warn;

    WarnIconTypeEnum(Integer type, String desc, String warn) {
        this.type = type;
        this.desc = desc;
        this.warn = warn;
    }

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public String getWarn() {
        return warn;
    }
}
