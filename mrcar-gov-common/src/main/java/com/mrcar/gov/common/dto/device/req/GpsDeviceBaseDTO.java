package com.mrcar.gov.common.dto.device.req;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Data
public class GpsDeviceBaseDTO {
    /**
     * 设备编号
     */
    @NotEmpty
    private String deviceNo;
    /**
     * SIM卡号
     */
    @NotEmpty
    private String simNo;
    /**
     * 设备厂商code
     */
    @NotNull
    private Integer manufactId;
    /**
     * 设备所属型号ID
     */
    @NotNull
    private Integer modelId;

    /**
     * 设备来源类型 1:政府设备 2：社会设备
     */
    @NotNull
    private Integer deviceSource;

    /**
     * 供应商编码
     */
    private String supplierServiceCode;

    /**
     * 供应商名称
     */
    private String supplierServiceName;
}
