package com.mrcar.gov.common.constant.asset;

import lombok.Getter;

@Getter
public enum VehicleRemindEnum {
    INSURANCE_PENDING(1, "保险待办"),
    MAINTENANCE_PENDING(2, "维保待办"),
    VEHICLE_INSPECTION_PENDING(3, "车检待办"),
    VIOLATION_PENDING(4, "违章待办");

    private final int code;
    private final String description;

    VehicleRemindEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据代码获取枚举实例。
     *
     * @param code 车务提醒类型的代码。
     * @return 对应的枚举实例，如果未找到则返回 null。
     */
    public static VehicleRemindEnum fromCode(int code) {
        for (VehicleRemindEnum type : values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        return INSURANCE_PENDING;
    }
}
