package com.mrcar.gov.common.dto.report.resp;

import com.mrcar.gov.common.dto.bi.resp.AssetDistributionRespDTO;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/12/18 10:41
 */
@Data
public class VehicleAssetDistributionDTO {
    /**
     * 资产类型
     */
    private Integer assetType;
    /**
     * 资产类型字符串
     */
    private String assetTypeStr;
    /**
     * 总计数量
     */
    private Integer totalCount;


    public static VehicleAssetDistributionDTO fromAssetDistributionRespDTO(AssetDistributionRespDTO dto){
        VehicleAssetDistributionDTO assetDistributionDTO = new VehicleAssetDistributionDTO();
//        assetDistributionDTO.setAssetType(dto.getAssetType());
        assetDistributionDTO.setAssetTypeStr(dto.getAssetTypeStr());
        assetDistributionDTO.setTotalCount(dto.getTotal());
        return assetDistributionDTO;
    }


}
