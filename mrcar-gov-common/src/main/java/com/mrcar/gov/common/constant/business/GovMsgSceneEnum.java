package com.mrcar.gov.common.constant.business;

import com.google.common.collect.Lists;
import com.mrcar.gov.common.dto.business.response.EnumResDTO;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/1/10 15:05
 * 消息业务场景
 */
@AllArgsConstructor
@Getter
public enum GovMsgSceneEnum {
    // 待调度-通知调度员
    WAIT_SCHEDULE_NOTICE_SCHEDULER(301,"待调度-通知调度员", GovMsgModuleEnum.VEHICLE_NOTICE),
    // 调度完成-通知下单人及主乘车人
    SCHEDULE_FINISH_NOTICE_ORDER_PERSON_AND_MAIN_PASSENGER(302, "调度完成-通知下单人及主乘车人", GovMsgModuleEnum.VEHICLE_NOTICE),
    // 重新调度-通知下单人及主乘车人
    RE_SCHEDULE_NOTICE_ORDER_PERSON_AND_MAIN_PASSENGER(303,"重新调度-通知下单人及主乘车人", GovMsgModuleEnum.VEHICLE_NOTICE),
    // 调度完成-通知驾驶员
    SCHEDULE_FINISH_NOTICE_DRIVER(304, "调度完成-通知驾驶员", GovMsgModuleEnum.VEHICLE_NOTICE),
    // 行程取消-通知驾驶员
    TRIP_CANCEL_NOTICE_DRIVER(305, "行程取消-通知驾驶员", GovMsgModuleEnum.VEHICLE_NOTICE),
    // 重新调度-信息变更 通知驾驶员
    RE_SCHEDULE_NOTICE_DRIVER(306, "重新调度-通知驾驶员", GovMsgModuleEnum.VEHICLE_NOTICE),

    // 待审批
    WAIT_APPROVE(401, "待审批", GovMsgModuleEnum.APPROVAL_NOTICE),
    // 审批通过
    APPROVE_PASS(402, "审批通过", GovMsgModuleEnum.APPROVAL_NOTICE),
    // 审批驳回
    APPROVE_REJECT(403, "审批驳回", GovMsgModuleEnum.APPROVAL_NOTICE),
    // 审批退回
    APPROVE_BACK(404, "审批退回", GovMsgModuleEnum.APPROVAL_NOTICE),

    // 维保提醒
    MAINTENANCE_REMIND(601, "维保提醒", GovMsgModuleEnum.VEHICLE_SERVICE),
    // 交强险提醒
    INSURANCE_REMIND(602, "交强险提醒", GovMsgModuleEnum.VEHICLE_SERVICE),
    // 商业险提醒
    BUSINESS_INSURANCE_REMIND(603, "商业险提醒", GovMsgModuleEnum.VEHICLE_SERVICE),
    // 年检提醒
    YEAR_CHECK_REMIND(604, "年检提醒", GovMsgModuleEnum.VEHICLE_SERVICE),

    // 维修竣工
    MAINTENANCE_FINISH(701, "维修竣工", GovMsgModuleEnum.MAINTENANCE_NOTICE),
    MAINTENANCE_RECEIVED(702, "接车完成", GovMsgModuleEnum.MAINTENANCE_NOTICE),

    ;


    private final Integer code;
    private final String desc;
    private final GovMsgModuleEnum msgModule;


    public static List<EnumResDTO> listMsgSceneByMsgModule(Integer msgModule) {
        GovMsgModuleEnum msgModuleEnum = GovMsgModuleEnum.getByCode(msgModule);
        if(Objects.isNull(msgModuleEnum)){
            return Lists.newArrayList();
        }
        return Arrays.stream(GovMsgSceneEnum.values())
                .filter(e -> e.getMsgModule().equals(msgModuleEnum))
                .map(e -> {
                    EnumResDTO dto = new EnumResDTO();
                    dto.setCode(e.getCode());
                    dto.setDesc(e.getDesc());
                    return dto;
                }).collect(Collectors.toList());
    }

    public static String getDescByCode(Integer businessScene) {
        for (GovMsgSceneEnum value : GovMsgSceneEnum.values()) {
            if (value.getCode().equals(businessScene)) {
                return value.getDesc();
            }
        }
        return "";
    }
}
