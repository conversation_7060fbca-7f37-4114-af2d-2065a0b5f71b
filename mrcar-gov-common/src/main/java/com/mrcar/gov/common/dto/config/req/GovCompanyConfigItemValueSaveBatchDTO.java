package com.mrcar.gov.common.dto.config.req;

import com.mrcar.gov.common.dto.BaseDTO;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class GovCompanyConfigItemValueSaveBatchDTO extends BaseDTO {

    /**
     * 属部门ID
     */
    @NotNull(message = "单位ID不能为空")
    private Integer structId;

    /**
     * 属部门ID
     */
    @NotBlank(message = "单位code不能为空")
    private String structCode;


    @NotEmpty(message = "值配置项不能为空")
    private List<GovCompanyConfigItemValueReqDTO> valueList;


}



