package com.mrcar.gov.common.constant.asset;

import com.google.common.collect.Lists;
import com.mrcar.gov.common.dto.DictionaryEnumDTO;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.data.mongodb.core.aggregation.ArrayOperators;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/11/13 20:53
 */
@Getter
@AllArgsConstructor
public enum VehicleReportVehicleTypeEnum {

    //车辆分类 轿车、越野车、商务车、大型客车、中型客车、其他小型客车、救护车、消防车、垃圾清运车、货车
    CAR(1, "轿车", Lists.newArrayList(VehicleTypeEnum.CAR)),
    SUV(2, "越野车", Lists.newArrayList(VehicleTypeEnum.SUV)),
    LARGE_CAR(4, "大型客车", Lists.newArrayList(VehicleTypeEnum.LARGE_CAR)),
    MIDDLE_CAR(5, "中型客车", Lists.newArrayList(VehicleTypeEnum.MIDDLE_CAR)),
    OTHER_SMALL_CAR(6, "其他小型客车", Lists.newArrayList(VehicleTypeEnum.OTHER_SMALL_CAR)),
    OTHER(99, "其他车型", Lists.newArrayList(VehicleTypeEnum.BUS,
    VehicleTypeEnum.AMBULANCE, VehicleTypeEnum.FIRE_CAR, VehicleTypeEnum.GARBAGE_CLEANING_CAR, VehicleTypeEnum.TRUCK)),
    ;

    private final Integer code;
    private final String desc;
    private final List<VehicleTypeEnum> mappingList;

    //        WHEN vehicle_type IN (1, 2, 4, 5, 6) THEN
//                CASE
//        WHEN vehicle_type = 1 THEN '轿车'
//        WHEN vehicle_type = 2 THEN '越野车'
//        WHEN vehicle_type = 4 THEN '大型客车'
//        WHEN vehicle_type = 5 THEN '中型客车'
//        WHEN vehicle_type = 6 THEN '其他小型客车'
//        END
//        ELSE '其他车型'

    public static List<DictionaryEnumDTO> convertToDicEnum(){
        List<DictionaryEnumDTO> list = new ArrayList<>(VehicleReportVehicleTypeEnum.values().length);
        for(VehicleReportVehicleTypeEnum enu : VehicleReportVehicleTypeEnum.values()){
            DictionaryEnumDTO dto = new DictionaryEnumDTO();
            dto.setCode(enu.getCode() + "");
            dto.setDesc(enu.getDesc());
            list.add(dto);
        }
        return list;
    }

    public static List<Integer> listMappingCode(Integer code){
        for(VehicleReportVehicleTypeEnum enu : VehicleReportVehicleTypeEnum.values()){
            if(Objects.equals(enu.getCode(), code)){
                return enu.getMappingList().stream().map(VehicleTypeEnum::getCode).collect(Collectors.toList());
            }
        }
        return Lists.newArrayList();
    }

}
