package com.mrcar.gov.common.util.pdf.api;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

public class PdfTableRowBuilder {

    List<PdfTableCellBuilder> cells = new ArrayList<>();

    public static PdfTableRowBuilder builder() {
        return new PdfTableRowBuilder();
    }

    public PdfTableRowBuilder addTextCell(String text) {
        this.cells.add(PdfTableCellBuilder.text(text));
        return this;
    }

    public PdfTableRowBuilder addImageCell(File file) {
        this.cells.add(PdfTableCellBuilder.image(file));
        return this;
    }

}
