package com.mrcar.gov.common.constant.business;


import lombok.Getter;

/**
 * <AUTHOR> on  2024/12/31 20:18
 */
@Getter
public enum GovMsgTemplateStatusEnum {


    DISABLED(0, "停用"),

    ENABLED(1, "启用");

    private final Integer code;

    private final String desc;


    GovMsgTemplateStatusEnum(Integer code, String description) {
        this.code = code;
        this.desc = description;
    }

    //根据code获取desc
    public static String getDescByCode(Integer code) {
        for (GovMsgTemplateStatusEnum value : GovMsgTemplateStatusEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getDesc();
            }
        }
        return null;
    }


}
