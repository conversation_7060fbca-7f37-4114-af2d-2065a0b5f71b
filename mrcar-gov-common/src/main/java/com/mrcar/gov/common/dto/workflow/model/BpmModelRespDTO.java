package com.mrcar.gov.common.dto.workflow.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import java.util.Date;

/**
 * 管理后台 - 流程模型的创建 Request VO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BpmModelRespDTO extends BpmModelBaseDTO {

    /**
     * 编号
     * 示例值: 1024
     */
    private String id;

    /**
     * BPMN XML
     */
    private String bpmnXml;

    /**
     * 创建时间
     */
    private Date createTime;

}
