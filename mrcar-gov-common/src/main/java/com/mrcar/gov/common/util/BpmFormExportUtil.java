package com.mrcar.gov.common.util;

import com.google.common.collect.Lists;
import com.mrcar.gov.common.dto.workflow.enums.OSSBucketEnum;
import com.mrcar.gov.common.dto.workflow.form.BpmFormExportDTO;
import com.mrcar.gov.common.dto.workflow.form.element.FormElementDTO;
import com.mrcar.gov.common.dto.workflow.process.resp.AppBpmProcessInstanceDetailRespDTO;
import com.mrcar.gov.common.dto.workflow.process.resp.AppBpmProcessInstanceRespDTO;
import com.mrcar.gov.common.dto.workflow.task.AppBpmTaskRespDTO;
import com.mrcar.gov.common.util.pdf.PdfCreator;
import com.mrcar.gov.common.util.pdf.api.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.*;
import java.nio.file.Files;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/9/12 16:19
 */
@Slf4j
public class BpmFormExportUtil {

    private static final String PDF_FONT_PATH = "/static/pdf_font.ttf";

    private static final String DATE_TIME_FORMAT_CHINA = "yyyy年MM月dd日 HH:mm:ss";

    private static final int TEXT_SPLIT_LENGTH = 12;
    private static String formatDate(Date date, String format){
        ZoneId zoneId = ZoneId.systemDefault();
        LocalDateTime localDateTime = date.toInstant().atZone(zoneId).toLocalDateTime();
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(format);
        return localDateTime.format(dateTimeFormatter);
    }

    public static List<BpmFormExportDTO> buildBpmFormExportDTO(List<AppBpmProcessInstanceDetailRespDTO> instanceDetailList) {
        if(collectionIsEmpty(instanceDetailList)){
            return Lists.newArrayList();
        }
        List<BpmFormExportDTO> bpmFormExportDTOList = Lists.newArrayListWithCapacity(instanceDetailList.size());
        for(AppBpmProcessInstanceDetailRespDTO instanceDetail : instanceDetailList){
            AppBpmProcessInstanceRespDTO processInstance = instanceDetail.getProcessInstance();
            List<AppBpmTaskRespDTO> bpmTasks = instanceDetail.getBpmTasks();

            BpmFormExportDTO bpmFormExportDTO = new BpmFormExportDTO();
            bpmFormExportDTO.setCreatorName(processInstance.getStartUser().getNickname());
            String processName = processInstance.getName();
            StringBuilder processNameBuilder = new StringBuilder();
            for (int i = 0; i < processName.length(); ){
                if(i + TEXT_SPLIT_LENGTH >= processName.length()){
                    processNameBuilder.append(processName.substring(i, processName.length() ));
                }else {
                    processNameBuilder.append(processName.substring(i, i + TEXT_SPLIT_LENGTH)).append("\r\n");
                }
                i += TEXT_SPLIT_LENGTH;
            }
            bpmFormExportDTO.setProcessName(processNameBuilder.toString());
            bpmFormExportDTO.setCreateDate(formatDate(processInstance.getCreateTime(), DATE_TIME_FORMAT_CHINA));
            List<FormElementDTO> formEleList = BpmFormUtil.extractFormValue(processInstance.getProcessDefinition().getFormFieldsList(), processInstance.getFormVariables());
            List<BpmFormExportDTO.RowEntry> rowEntries = new ArrayList<>(formEleList.size());
            for (FormElementDTO formEle : formEleList) {
                BpmFormExportDTO.RowEntry rowEntry = new BpmFormExportDTO.RowEntry();
                rowEntry.setKey(formEle.getName());
                rowEntry.setValue(formEle.getValue());
                rowEntries.add(rowEntry);
            }
            BpmFormExportDTO.RowEntry rowEntry = new BpmFormExportDTO.RowEntry();
            rowEntry.setKey("审批流程");
            StringBuilder valueBuild = new StringBuilder();
            for(int i = 0; i < bpmTasks.size(); i++){
                AppBpmTaskRespDTO task = bpmTasks.get(i);
                if(Objects.isNull(task.getAssigneeUser()) || Objects.isNull(task.getEndTime()) || StringUtils.isEmpty(task.getResultStr())){
                    continue;
                }
                valueBuild.append(task.getAssigneeUser().getNickname()).append(" ").append(task.getResultStr()).append(" ").append(formatDate(task.getEndTime(), DATE_TIME_FORMAT_CHINA));
                if(i != bpmTasks.size() - 1){
                    valueBuild.append("\r\n");
                }
            }
            rowEntry.setValue(valueBuild.toString());
            rowEntries.add(rowEntry);
            bpmFormExportDTO.setRows(rowEntries);
            bpmFormExportDTOList.add(bpmFormExportDTO);
        }
        return bpmFormExportDTOList;
    }

    public static PdfCreator builderPdfCreator(List<BpmFormExportDTO> forms, String exportUserName, String exportDate) throws IOException {
        // font
        PdfFontBuilder font = PdfFontBuilder.from(new ByteArrayInputStream(loadFontResource()));
        int fontSize = 12;
        // builder
        PdfCreatorBuilder builder = PdfCreator.builder();
        // page
        for (int i = 0; i < forms.size(); i++) {
            BpmFormExportDTO form = forms.get(i);
            List<PdfTableRowBuilder> rows = Lists.newArrayList();
            if(collectionIsNotEmpty(form.getRows())){
                rows = form.getRows().stream().map(s ->
                                PdfTableRowBuilder.builder()
                                        .addTextCell(s.getKey())
                                        .addTextCell(s.getValue()))
                        .collect(Collectors.toList());
            }
            PdfPageBuilder page =
                    PdfPageBuilder.A4()
                            .padding(0.05f, 0.1f)
                            .append(PdfSpanBuilder.builder()
                                    .margin(10, 0)
                                    .appendText(PdfTextBuilder.builder()
                                                    .text(form.getProcessName())
                                                    .alignLeft()
                                                    .font(font)
                                                    .fontSize(fontSize),
                                            MetricBuilder.percentage(0.3f))
                                    .appendText(PdfTextBuilder.builder()
                                                    .text("创建人:" + form.getCreatorName())
                                                    .alignLeft()
                                                    .font(font)
                                                    .fontSize(fontSize),
                                            MetricBuilder.percentage(0.3f))
                                    .appendText(PdfTextBuilder.builder()
                                                    .text("创建时间:" + form.getCreateDate())
                                                    .alignRight()
                                                    .font(font)
                                                    .fontSize(fontSize),
                                            MetricBuilder.percentage(0.4f)))
                            .append(PdfTableBuilder.builder()
                                    .border(1)
                                    .alignLeft()
                                    .padding(5)
                                    .columns(2)
                                    .font(font)
                                    .fontSize(fontSize)
                                    .addRows(rows));
            if(i == forms.size() - 1){
                page.append(exportTimeBuild(exportDate, font, fontSize))
                        .append(exportUserBuild(exportUserName, font, fontSize));
            }
            builder.addPage(page);
        }
        if(collectionIsEmpty(forms)){
            PdfPageBuilder page =
                    PdfPageBuilder.A4()
                            .padding(0.05f, 0.1f)
                            .append(exportTimeBuild(exportDate, font, fontSize))
                            .append(exportUserBuild(exportUserName, font, fontSize));
            builder.addPage(page);
        }
        return builder.build();
    }


    public static String savePdfToOss(PdfCreator creator, String fileName) throws IOException {
        File output = Files.createTempFile(null, ".pdf").toFile();
        try {
            creator.export(output);
            output.length();
            return uploadToOss(output, OSSBucketEnum.FILE, "workflow", fileName);
        } catch (Exception e){
            log.error("savePdfToOss exception", e);
        }finally {
            output.delete();
        }
        return "";
    }

    private static PdfSpanBuilder exportTimeBuild(String exportDate, PdfFontBuilder font,int fontSize){
        return PdfSpanBuilder.builder()
                .margin(10, 0)
                .appendText(PdfTextBuilder.builder()
                                .text(" ")
                                .alignLeft()
                                .font(font)
                                .fontSize(fontSize),
                        MetricBuilder.percentage(0.5f))
                .appendText(PdfTextBuilder.builder()
                                .text("导出时间：" + exportDate)
                                .alignLeft()
                                .font(font)
                                .fontSize(fontSize),
                        MetricBuilder.percentage(0.5f));
    }

    private static PdfSpanBuilder exportUserBuild(String exportUserName, PdfFontBuilder font,int fontSize){
        return PdfSpanBuilder.builder()
                .margin(10, 0)
                .appendText(PdfTextBuilder.builder()
                                .text(" ")
                                .alignLeft()
                                .font(font)
                                .fontSize(fontSize),
                        MetricBuilder.percentage(0.5f))
                .appendText(PdfTextBuilder.builder()
                                .text("导出人：" + exportUserName)
                                .alignLeft()
                                .font(font)
                                .fontSize(fontSize),
                        MetricBuilder.percentage(0.5f));
    }


    // 将文件上传到OSS存储中
    public static String uploadToOss(File output,
                                      OSSBucketEnum type,
                                      String prefix,
                                      String fileName) {
        // 文件路径
//        SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
////        String path = prefix + "/"
////                + format.format(new Date()) + "-"
////                + RandomStringUtil.genRandomString(6) + "." + suffix;
//        String path = prefix + "/" + format.format(new Date()) + "/" + fileName;
//        // 上传文件
//        Map<String, Object> params = new HashMap<>();
//        params.put("file", output);
//        params.put("type", type.getCode());
//        params.put("fileName", path);
//        params.put("business", "workflow");
//
//        String restUrl = new ThirdRestLocator().getRestUrl("/oss/uploadFile");
//        @SuppressWarnings("unchecked")
//        RestResponse<String> response = RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, restUrl, params, null, String.class);
        return "";
    }

    private static byte[] loadFontResource() throws IOException {
        ByteArrayOutputStream output = new ByteArrayOutputStream();
//        getClass()
        try (InputStream inStream = BpmFormExportUtil.class.getResourceAsStream(PDF_FONT_PATH)) {
            byte[] buffer = new byte[1024];
            int len;
            while ((len = inStream.read(buffer, 0, buffer.length)) != -1) {
                output.write(buffer, 0, len);
            }
        }
        return output.toByteArray();
    }

    private static Boolean collectionIsEmpty(Collection collection) {
        return collection == null || collection.isEmpty();
    }

    private static Boolean collectionIsNotEmpty(Collection collection) {
        return !collectionIsEmpty(collection);
    }

    private static Boolean strIsEmpty(String str) {
        return str == null || str.trim().isEmpty();
    }
}
