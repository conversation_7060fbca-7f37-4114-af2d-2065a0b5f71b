package com.mrcar.gov.common.dto;

import lombok.Data;

/**
 * 字典键值对数据传输对象
 *
 * 该类用于表示枚举类型的键值对，其中键表示枚举类型，值表示对应的名称。
 *
 * @param <K> 枚举类型的键
 * @param <V> 枚举类型的值
 */
@Data
public class DicKeyValueDTO<K,V>  {
    /**
     * 枚举类型的键
     */
    private K key;

    /**
     * 枚举类型的值
     */
    private V value;

    /**
     * 构造函数，用于初始化键和值。
     *
     * @param key 枚举类型的键
     * @param value 枚举类型的值
     */
    public DicKeyValueDTO(K key, V value){
        this.key=key;
        this.value=value;
    }
}
