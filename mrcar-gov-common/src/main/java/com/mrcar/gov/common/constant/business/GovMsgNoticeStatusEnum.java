package com.mrcar.gov.common.constant.business;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/12/28 09:05
 */
@Getter
@AllArgsConstructor
public enum GovMsgNoticeStatusEnum {

    // 0:删除 1:草稿 2:已发布;3推送中；4已推送；5推送失败
    DELETED(0, "删除"),

    DRAFT(1, "草稿"),

    PUBLISHED(2, "已发布"),

    // 推送中
    PUSHING(3, "推送中"),

    // 推送完成
    PUSHED(4, "已推送"),

    // 推送失败
    PUSH_FAIL(5, "推送失败"),

    ;


    private final Integer code;

    private final String desc;

    // 根据code 获取desc
    public static String getDescByCode(Integer code) {
        for (GovMsgNoticeStatusEnum value : GovMsgNoticeStatusEnum.values()) {
            if (Objects.equals(value.getCode(), code)) {
                return value.getDesc();
            }
        }
        return "";
    }



}
