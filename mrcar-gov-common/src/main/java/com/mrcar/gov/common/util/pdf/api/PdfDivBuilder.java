package com.mrcar.gov.common.util.pdf.api;

public class PdfDivBuilder {

    LeafLikeElementBuilder element;
    MetricBuilder width = MetricBuilder.adaptive();

    private PdfDivBuilder(LeafLikeElementBuilder element) {
        this.element = element;
    }

    private PdfDivBuilder(LeafLikeElementBuilder element,
                          MetricBuilder width) {
        this.element = element;
        this.width = width;
    }

    public static PdfDivBuilder create(LeafLikeElementBuilder element) {
        return new PdfDivBuilder(element);
    }

    public static PdfDivBuilder create(LeafLikeElementBuilder element,
                                       MetricBuilder width) {
        return new PdfDivBuilder(element, width);
    }

}
