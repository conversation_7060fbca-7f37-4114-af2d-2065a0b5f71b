package com.mrcar.gov.common.constant.asset;

/**
 * <AUTHOR>
 * @date 2024/12/26 21:35
 */
public enum DisposalTypeEnum {

    //处置类型：1.处置 ;2:调拨

    DISPOSAL(1, "处置"),
    ALLOCATION(2, "调拨");

    private final int code;
    private final String name;

    DisposalTypeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
