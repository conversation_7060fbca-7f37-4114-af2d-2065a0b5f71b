package com.mrcar.gov.common.dto.order.resp;

import lombok.Data;

import java.util.Date;

/**
 * 公务用车申请
 */
@Data
public class GovPublicCarApplyDetailDTO {

    /**
     * 申请单号
     */
    private String applyNo;

    /**
     * 预计开始时间
     */
    private Date expectedPickupTime;

    /**
     * 预计结束时间
     */
    private Date expectedReturnTime;

    /**
     * 车辆id
     */
    private Integer vehicleId;

    /**
     * 车辆编码
     */
    private String vehicleNo;

    /**
     * 车牌号
     */
    private String vehicleLicense;

    /**
     * 车架号
     */
    private String vehicleVin;

    /**
     * 车辆类型
     */
    private Integer vehicleType;

    /**
     * 员工id
     */
    private Integer driverId;

    /**
     * 员工code
     */
    private String driverCode;

    /**
     * 员工name
     */
    private String driverName;

    /**
     * 员工手机号
     */
    private String driverMobile;
}
