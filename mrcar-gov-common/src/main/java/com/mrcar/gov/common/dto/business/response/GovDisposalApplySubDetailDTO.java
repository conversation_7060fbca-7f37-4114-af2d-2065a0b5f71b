package com.mrcar.gov.common.dto.business.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.mrcar.gov.common.dto.FileDTO;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 处置及调拨明细详情
 *
 * <AUTHOR>
 * @date 2024/11/21 9:36
 */
@Data
public class GovDisposalApplySubDetailDTO {

    /**
     * 处置编码
     */
    private String disposalNo;

    /**
     * 关联申请编码
     */
    private String applyNo;

    /**
     * 原车辆编码
     */
    private String vehicleNo;

    /**
     * 车牌号
     */
    private String vehicleLicense;

    /**
     * 车架号
     */
    private String vehicleVin;

    /**
     * 车辆类型
     */
    private Integer vehicleType;

        /**
     * 车辆类型
     */
    private String vehicleTypeName;

    /**
     * 处置方式 1报废 2拍卖 3调拨 4损失核销 5厂家回收 6 对外捐赠
     */
    private Integer disposalMethod;
    /**
     * 处置方式名称 1报废 2拍卖 3调拨 4损失核销 5厂家回收 6 对外捐赠
     */
    private String disposalMethodName;

    /**
     * 填调入单位
     */
    private String transferInStructCode;

    /**
     * 填调入单位
     */
    private String transferInStructName;

    /**
     * 填处置调拨时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date disposalTime;

    /**
     * 处置状态
     */
    private Integer disposalStatus;

     /**
     * 处置状态名成
     */
    private String disposalStatusName;

    /**
     * 使用人部门
     */
    private String vehicleUseDeptCode;

    /**
     * 使用人部门名称
     */
    private String vehicleUseDeptName;

    /**
     * 使用单位部门
     */
    private String vehicleUseStructCode;

    /**
     * 使用单位名称
     */
    private String vehicleUseStructName;

    /**
     * 说明
     */
    private String disposalRemark;

    /**
     * 申请单位编码
     */
    private String applyStructCode;

    /**
     * 申请单位名称
     */
    private String applyStructName;

    /**
     * 车辆所属单位编码
     */
    private String vehicleBelongDeptCode;

    /**
     * 车辆所属单位名称
     */
    private String vehicleBelongDeptName;

    /**
     * 附件
     */
    private List<FileDTO> fileList;

    /**
     * 批复时间
     */
    private Date approveDate;

    /**
     * 审批id
     */
    private String approveId;
}
