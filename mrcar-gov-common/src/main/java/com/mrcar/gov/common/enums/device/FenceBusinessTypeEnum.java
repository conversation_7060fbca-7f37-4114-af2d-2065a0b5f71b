package com.mrcar.gov.common.enums.device;

import lombok.Getter;
import lombok.Setter;

import java.util.Arrays;

/**
 * 围栏业务类型.
 *
 * <AUTHOR>
 * @date 2024-08-17
 */
public enum FenceBusinessTypeEnum {

    SELLING_ALARM(1,"出栏报警围栏", "出栏报警"),
    ENTRY_ALARM(2,"入栏报警围栏", "入栏报警"),
    OVERSPEED(3,"超速报警围栏", "超速报警"),
    OFFICIAL_VEHICLE(4,"公务用车围栏", ""),
    ;

    @Getter
    @Setter
    private Integer code;
    @Getter
    @Setter
    private String  msg;

    @Getter
    private String  warnMsg;

    FenceBusinessTypeEnum(Integer code, String msg, String warnMsg){
        this.code = code;
        this.msg = msg;
        this.warnMsg = warnMsg;
    }

    public static FenceBusinessTypeEnum getEnumByCode(Integer code) {
        return Arrays.stream(FenceBusinessTypeEnum.values())
                .filter(fenceBusinessTypeEnum -> fenceBusinessTypeEnum.code.equals(code))
                .findFirst()
                .orElse(null);
    }

    public static String getEnumMsgByCode(Integer code) {
        return Arrays.stream(FenceBusinessTypeEnum.values())
                .filter(fenceBusinessTypeEnum -> fenceBusinessTypeEnum.code.equals(code))
                .map(FenceBusinessTypeEnum::getMsg)
                .findFirst()
                .orElse("");
    }

}
