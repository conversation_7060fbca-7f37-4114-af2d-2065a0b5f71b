package com.mrcar.gov.common.util.pdf.model;

import com.mrcar.gov.common.util.pdf.layout.*;
import org.apache.pdfbox.pdmodel.PDPageContentStream;

import java.io.IOException;
import java.util.List;

public class TextElement extends AbstractSingleElement {

    private static final String NODE_NAME = "Text";

    private final List<String> lines;
    private final float lineSpace;
    private final DisplayFont font;
    private final AlignmentType alignment;

    protected TextElement(BlockElement parent,
                          MetricValue width,
                          MetricValue height,
                          List<String> lines,
                          float lineSpace,
                          DisplayFont font,
                          AlignmentType alignment,
                          RoundMetric margin) {
        super(parent, height, width, margin);

        this.lines = lines;
        this.lineSpace = lineSpace;
        this.font = font;
        this.alignment = alignment;
    }

    public static TextElement create(BlockElement block,
                                     MetricValue width,
                                     MetricValue height,
                                     List<String> lines,
                                     float lineSpace,
                                     DisplayFont font,
                                     AlignmentType alignment) {
        return new TextElement(block,
                width,
                height,
                lines,
                lineSpace,
                font,
                alignment,
                RoundMetric.empty());
    }

    @Override
    protected MetricValue doRefreshAdaptiveWidthMetric() {
        try {
            float max = 0;
            for (String line : lines) {
                float width = this.font.font().getStringWidth(line) / 1000 * this.font.size();
                max = Math.max(max, width);
            }
            return MetricValue.create(MetricType.ABSOLUTE, max);
        } catch (IOException e) {
            throw new IllegalStateException("Cannot read font file", e);
        }
    }

    @Override
    protected MetricValue doRefreshAdaptiveHeightMetric() {
        float height = font.font().getFontDescriptor().getCapHeight() / 1000 * this.font.size();
        return MetricValue.create(MetricType.ABSOLUTE,
                height * this.lines.size() + (this.lines.size() - 1) * this.lineSpace);
    }

    @Override
    public void render(Point point, PDPageContentStream stream) throws IOException {
        // 渲染多行文字
        // 字体高度
        float fontHeight = font.font().getFontDescriptor().getCapHeight() / 1000 * font.size();
        // 去掉margin和padding的距离
        float leftX = point.x();
        float rightX = point.x() + _width.value();
        float topY = point.y() - fontHeight;
        float bottomY = point.y() - _height.value();
        // 对其方式
        if (this.alignment == AlignmentType.LEFT) {
            float x = leftX;
            for (int i = 0; i < lines.size(); i++) {
                float y = topY - i * (fontHeight + this.lineSpace);
                stream.beginText();
                stream.setFont(font.font(), font.size());
                stream.newLineAtOffset(x, y);
                stream.showText(lines.get(i));
                stream.endText();
            }
        } else if (this.alignment == AlignmentType.RIGHT) {
            for (int i = 0; i < lines.size(); i++) {
                float x = rightX - font.font().getStringWidth(lines.get(i)) / 1000 * font.size();
                float y = topY - i * (fontHeight + this.lineSpace);
                stream.beginText();
                stream.setFont(font.font(), font.size());
                stream.newLineAtOffset(x, y);
                stream.showText(lines.get(i));
                stream.endText();
            }
        } else if (this.alignment == AlignmentType.CENTER) {
            for (int i = 0; i < lines.size(); i++) {
                float x = ( leftX + rightX ) / 2 - ( font.font().getStringWidth(lines.get(i)) / 1000 * font.size() / 2 );
                float y = topY - i * (fontHeight + this.lineSpace);
                stream.beginText();
                stream.setFont(font.font(), font.size());
                stream.newLineAtOffset(x, y);
                stream.showText(lines.get(i));
                stream.endText();
            }
        }
    }

    @Override
    public String name() {
        return NODE_NAME;
    }
}
