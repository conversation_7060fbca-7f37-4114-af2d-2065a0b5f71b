package com.mrcar.gov.common.dto.workflow.process.resp;

import lombok.Data;
import java.util.List;

/**
 * 列出所有流程实例详情的响应 DTO
 * <p>
 * 该类包含父流程实例详情以及子流程实例详情列表。
 * </p>
 *
 * <AUTHOR>
 * @date 2024/9/5 16:47
 */
@Data
public class AppBpmListAllProcessInstanceDetailRespDTO {

    /**
     * 父流程实例详情
     */
    private AppBpmProcessInstanceDetailRespDTO parentProcessInstanceDetail;

    /**
     * 子流程实例详情列表
     */
    private List<AppBpmProcessInstanceDetailRespDTO> subProcessInstanceDetailList;
}
