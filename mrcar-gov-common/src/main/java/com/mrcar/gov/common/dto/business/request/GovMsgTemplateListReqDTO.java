package com.mrcar.gov.common.dto.business.request;

import com.mrcar.gov.common.dto.BaseDTO;
import com.mrcar.gov.common.dto.PageParamDTO;
import lombok.Data;

/**
 * Author:  wangM
 * Date:  2024/12/27 15:05
 * DESC:消息模板列表请求DTO
 */
@Data
public class GovMsgTemplateListReqDTO extends PageParamDTO {

    /**
     * 模板编码
     */
    private String templateNo;

    /**
     * 模板名称
     */
    private String templateName;
    /**
     * 模板类型 1公文公告;2系统消息;3用车通知;4审批通知;5监控报警;6车务消息;7维保通知;
     */
    private Integer msgModule;

    /**
     * 模板状态 0:停用 1:启用
     */
    private Integer templateStatus;

    /**
     * 推送方式 1短信 2站内信
     */
    private Integer pushType;


}
