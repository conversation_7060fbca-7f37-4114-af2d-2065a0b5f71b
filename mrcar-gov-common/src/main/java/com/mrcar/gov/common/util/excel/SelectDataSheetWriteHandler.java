package com.mrcar.gov.common.util.excel;

import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import izu.org.apache.poi.hssf.usermodel.HSSFDataValidation;
import izu.org.apache.poi.ss.usermodel.*;
import izu.org.apache.poi.ss.util.CellRangeAddressList;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023/5/26 18:47
 */
public class SelectDataSheetWriteHandler implements SheetWriteHandler {
    private Map<Integer, List<String>> selectMap;

    // 设置100列column

    private char[] alphabet = new char[]{'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L',
            'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'};

    public SelectDataSheetWriteHandler(Map<Integer, List<String>> selectMap) {
        this.selectMap = selectMap;
    }

    @Override
    public void beforeSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
    }

    @Override
    public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        if (selectMap == null || selectMap.size() == 0) {
            return;
        }
        Sheet sheet = writeSheetHolder.getSheet();
        CellStyle cellStyle = writeWorkbookHolder.getCachedWorkbook().createCellStyle();
        // 49为文本格式
        cellStyle.setDataFormat((short) 49);
        // i为列，一整列设置为文本格式 13 列设置为文本格式
        sheet.setDefaultColumnStyle(13, cellStyle);
        //设置下拉框
        DataValidationHelper helper = sheet.getDataValidationHelper();
        for (Map.Entry<Integer, List<String>> entry : selectMap.entrySet()) {
            if (entry == null || entry.getValue() == null || entry.getValue().isEmpty()) {
                continue;
            }
            // 定义sheet的名称
            String hiddenName = "hidden" + entry.getKey();
            // 创建一个隐藏的sheet 名称为 hidden
            Workbook workbook = writeWorkbookHolder.getWorkbook();
            Sheet hidden = workbook.createSheet(hiddenName);
            // 设置下拉单元格的首行、末行、首列、末列
            CellRangeAddressList rangeAddressList = new CellRangeAddressList(1, 5000, entry.getKey(), entry.getKey());
            int rowLen = entry.getValue().size();
            // 设置字典sheet页的值 每一列一个字典项
            for (int i = 0; i < rowLen; i++) {
                Row row = hidden.getRow(i);
                if (row == null) {
                    row = hidden.createRow(i);
                }
                row.createCell(entry.getKey()).setCellValue(entry.getValue().get(i));
            }
            String excelColumn = getExcelColumn(entry.getKey());
            // 下拉框数据来源 eg:字典sheet!$B1:$B2
            String refers = hiddenName + "!$" + excelColumn + "$1:$" + excelColumn + "$" + rowLen;
            // 将刚才设置的sheet引用到你的下拉列表中
            DataValidationConstraint constraint = helper.createFormulaListConstraint(refers);
            // 设置约束
            DataValidation validation = helper.createValidation(constraint, rangeAddressList);
            if (validation instanceof HSSFDataValidation) {
                validation.setSuppressDropDownArrow(false);
            } else {
                validation.setSuppressDropDownArrow(true);
                validation.setShowErrorBox(true);
            }
            // 阻止输入非下拉框的值
            validation.setErrorStyle(DataValidation.ErrorStyle.STOP);
            validation.createErrorBox("提示", "此值与单元格定义格式不一致！");
            // 添加下拉框约束
            writeSheetHolder.getSheet().addValidationData(validation);
            // 设置列为隐藏
            int hiddenIndex = workbook.getSheetIndex(hiddenName);
            if (!workbook.isSheetHidden(hiddenIndex)) {
                workbook.setSheetHidden(hiddenIndex, true);
            }
        }
    }

    /**
     * 将数字列转化成为字母列
     * @param num
     * @author: CUI
     * @date: 2022-05-27 9:12
     * @return: java.lang.String
     */
    private String getExcelColumn(int num) {
        String column = "";
        int len = alphabet.length - 1;
        int first = num / len;
        int second = num % len;
        if (num <= len) {
            column = alphabet[num] + "";
        } else {
            column = alphabet[first - 1] + "";
            if (second == 0) {
                column = column + alphabet[len] + "";
            } else {
                column = column + alphabet[second - 1] + "";
            }
        }
        return column;
    }
}
