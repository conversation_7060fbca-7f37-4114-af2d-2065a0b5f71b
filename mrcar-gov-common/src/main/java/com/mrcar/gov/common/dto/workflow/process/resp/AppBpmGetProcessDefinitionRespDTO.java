package com.mrcar.gov.common.dto.workflow.process.resp;

import lombok.Data;

/**
 * 获取流程定义的响应 DTO
 * <p>
 * 该类用于返回流程定义和父流程实例的信息。
 * </p>
 *
 * @autor YePengPeng
 * @date 2024/9/5 15:09
 */
@Data
public class AppBpmGetProcessDefinitionRespDTO {

    /**
     * 父流程实例信息 - 如果是子流程创建，则会返回此信息
     */
    private AppBpmProcessInstanceRespDTO parentProcessInstance;

    /**
     * 流程定义信息
     */
    private AppBpmProcessDefinitionRespDTO processDefinition;
}
