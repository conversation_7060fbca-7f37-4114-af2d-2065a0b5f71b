package com.mrcar.gov.common.util;

import com.itextpdf.text.pdf.PdfReader;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.PDFRenderer;

import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2024/9/19 14:41
 */
@Slf4j
public class PdfUtil {

    public static void pdf2Image(File file, String dstImgFolder, int dpi) {
        PDDocument pdDocument;
        try {
            String imgPDFPath = file.getParent();
            int dot = file.getName().lastIndexOf('.');
            String imagePDFName = file.getName().substring(0, dot); // 获取图片文件名
            String imgFolderPath = null;
            if (dstImgFolder.equals("")) {
                imgFolderPath = imgPDFPath + File.separator + imagePDFName;// 获取图片存放的文件夹路径
            } else {
                imgFolderPath = dstImgFolder + File.separator + imagePDFName;
            }
            pdDocument = PDDocument.load(file);
            PDFRenderer renderer = new PDFRenderer(pdDocument);
            /* dpi越大转换后越清晰，相对转换速度越慢 */
            PdfReader reader = new PdfReader(file.getAbsolutePath());
            int pages = reader.getNumberOfPages();
            StringBuffer imgFilePath = null;
            BufferedImage[] bufferedImages = new BufferedImage[pages];
            for (int i = 0; i < pages; i++) {
                String imgFilePathPrefix = imgFolderPath + imagePDFName;
                imgFilePath = new StringBuffer();
                imgFilePath.append(imgFilePathPrefix);
                imgFilePath.append("_");
                imgFilePath.append(i + 1);
                imgFilePath.append(".png");
                BufferedImage image = renderer.renderImageWithDPI(i, dpi);
                bufferedImages[i] = image;
            }
            ImageMergeUtil.mergeImage(bufferedImages, 2, dstImgFolder);
        } catch (IOException e) {
            log.error("pdf2Image exception", e);
        }
    }
}
