package com.mrcar.gov.common.dto.business.response;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;

/**
 * Author:  wangM
 * Date:  2024/12/27 16:56
 * DESC: 模板下拉框响应DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EnumResDTO {


    /**
     * 枚举编码
     */
    private Integer code;

    /**
     * 枚举项描述
     */
    private String desc;

    public static List<EnumResDTO> enumList(Class<?> enumClass) {
        List<EnumResDTO> enumResDTOList = new ArrayList<>();
        for (Object enumObj : enumClass.getEnumConstants()) {
            try {
                Method getCodeMethod = enumClass.getMethod("getCode");
                Method getDescMethod = enumClass.getMethod("getDesc");
                Integer code = (Integer) getCodeMethod.invoke(enumObj);
                String desc = (String) getDescMethod.invoke(enumObj);
                enumResDTOList.add(new EnumResDTO(code, desc));
            } catch (NoSuchMethodException | InvocationTargetException | IllegalAccessException e) {
                throw new RuntimeException(e);
            }
        }
        return enumResDTOList;
    }
}
