package com.mrcar.gov.common.util;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.mrcar.gov.common.dto.device.resp.BaiduRegeoAddressDTO;
import com.mrcar.gov.common.dto.device.resp.GaodeRegeoAddressDTO;
import com.mrcar.gov.common.dto.device.resp.RegeoAddressDTO;
import com.mrcar.gov.common.dto.device.resp.RegeoResDTO;
import com.mrcar.gov.common.enums.device.RegeoApiEnum;

/**
 * 百度&高德逆地理位置解析
 * <AUTHOR> ON 2021/7/23.
 */
public class RegeoUtil {
	
	private static final Logger logger = LoggerFactory.getLogger(RegeoUtil.class);

    private static final String GAODE_MAP_URL = "https://restapi.amap.com";
    private static final String BAIDU_MAP_URL = "http://api.map.baidu.com";

    private static final String MARS_CODE = "gcj02ll";
    private static final String BD_CODE = "bd09ll";
    private static final String WGS_CODE = "wgs84ll";

    private static String  baiduAk = "d3y8woH5gfh63bB8RqVMml2GrsO52id0";
    private static String gaodeKey = "9bfa00b3b342752c1cc42d80505db61a";

    /**
     * 最多支持20个坐标逆地理解析
     * @param locations String lng在前lat在后，中间用,分割
     * @return
     * @throws Exception
     */
    public static List<RegeoResDTO> reverseGeocodingBatch(List<String> locations) throws Exception {
    	//目前都是国内坐标,为了防止循环调研高德判断是否中国坐标，此处注释掉直接返回空数组
    	return new ArrayList<>();
//        return reverseGeocodingBatchWithRetry(locations,3);
    }

    /**
     * 可重试逆地址解析
     * @param locations
     * @param retryTimes 重试次数
     * @return
     */
    public static List<RegeoResDTO> reverseGeocodingBatchWithRetry(List<String> locations,int retryTimes){
        List<RegeoResDTO> list = null;
        if(retryTimes > 0){
            list = reqGaodeBatch(locations);
            if(list == null || list.size() < 1){
                //请求高德失败，请求百度
                list = reqBaiduBatch(locations);
                if(list == null || list.size() < 1){
                    return reverseGeocodingBatchWithRetry(locations,retryTimes-1);
                }
            }
        }
        return list;
    }

    public static RegeoResDTO reverseGeocoding(BigDecimal lng, BigDecimal lat) throws Exception {
        //坐标的类型，目前支持的坐标类型包括：bd09ll（百度经纬度坐标）、bd09mc（百度米制坐标）、gcj02ll（国测局经纬度坐标，仅限中国）、wgs84ll（ GPS经纬度） 坐标系说明
        String location = lng.setScale(6,BigDecimal.ROUND_DOWN).toString()
                .concat(",")
                .concat(lat.setScale(6,BigDecimal.ROUND_DOWN).toString());
        String url =  GAODE_MAP_URL +"/v3/geocode/regeo?key="+gaodeKey+"&location="+location;

        JSONObject res = RestClient.requestThirdApi(BaseHttpClient.HttpMethod.GET,url,null,null,JSONObject.class);

        Integer status = res.getInteger("status");
        if(status!=null&&status==1) {
            JSONObject json = res.getJSONObject("regeocode");
            return gaodeConvert(json);
        }
        return null;
    }

    public static List<RegeoResDTO> reqGaodeBatch(List<String> locations){
        try {
            String location = StringUtils.join(locations,"|");
            //坐标的类型，目前支持的坐标类型包括：bd09ll（百度经纬度坐标）、bd09mc（百度米制坐标）、gcj02ll（国测局经纬度坐标，仅限中国）、wgs84ll（ GPS经纬度） 坐标系说明
            String url =  GAODE_MAP_URL +"/v3/geocode/regeo";
            Map<String,Object> params = new HashMap<String,Object>();
            params.put("key",gaodeKey);
            params.put("location",location);
            params.put("batch",true);
            JSONObject jsonObject = RestClient.requestThirdApi(BaseHttpClient.HttpMethod.GET, url, params, null, JSONObject.class);
            Integer status = jsonObject.getInteger("status");
            if(status!=null&&status==1) {
                JSONArray regeocodes = jsonObject.getJSONArray("regeocodes");
                return regeocodes.toJavaList(String.class).stream().map(s -> {
                    JSONObject res = JSON.parseObject(s);
                    return gaodeConvert(res);
                }).collect(Collectors.toList());
            }else{
                //请求高德失败转换请求百度api
                logger.warn("请求高德逆地理解析失败={}",jsonObject);
            }
        }catch (Exception e){
            logger.warn("请求高德逆地理解析失败={}",e);
        }
        return null;
    }

    private static RegeoResDTO gaodeConvert(JSONObject res){
        String formatted_address = res.getString("formatted_address");
        RegeoResDTO regeoResDTO = new RegeoResDTO();
        GaodeRegeoAddressDTO regeoAddressDTO = JSON.parseObject(res.getString("addressComponent"), GaodeRegeoAddressDTO.class);
        regeoResDTO.setFormatted_address(formatted_address);
        regeoResDTO.setAddressComponent(regeoAddressDTO);
        regeoResDTO.setRegeoApiEnum(RegeoApiEnum.GAODE);
        return regeoResDTO;
    }

    /**
     * 逆地理 坐标系火星=高德
     * @param locations lng在前lat在后，中间用,分割
     * @return
     */
    public static List<RegeoResDTO> reqBaiduBatch(List<String> locations){
        List<RegeoResDTO> list = new ArrayList<>();
        for(int i = 0;i < locations.size();i++){
            String[] l = locations.get(i).split(",");
            if (l.length < 2) break;
            String baiduLoca = l[1].concat(",").concat(l[0]);
            RegeoResDTO regeoResDTO = reqBaidu(baiduLoca,MARS_CODE);
            if(regeoResDTO != null){
                list.add(regeoResDTO);
            }else{
                return null;
            }
        }
        return list;
    }

    /**
     * @param location lat<纬度>,lng<经度>
     * @param coordinate
     * @return
     */
    public static RegeoResDTO reqBaidu(String location, String coordinate) {
        //坐标的类型，目前支持的坐标类型包括：bd09ll（百度经纬度坐标）、bd09mc（百度米制坐标）、gcj02ll（国测局经纬度坐标，仅限中国）、wgs84ll（ GPS经纬度） 坐标系说明
        String url =  BAIDU_MAP_URL +"/reverse_geocoding/v3/?ak="+baiduAk+"&output=json&coordtype="+coordinate+"&location="+location;
        try {
            JSONObject jsonObject = RestClient.requestThirdApi(BaseHttpClient.HttpMethod.GET, url, null, null, JSONObject.class);
            Integer status = jsonObject.getInteger("status");
            if(status!=null&&status==0) {
                JSONObject res = jsonObject.getJSONObject("result");
                String formatted_address = res.getString("formatted_address");
                RegeoResDTO regeoResDTO = new RegeoResDTO();
                BaiduRegeoAddressDTO regeoAddressDTO = JSON.parseObject(res.getString("addressComponent"), BaiduRegeoAddressDTO.class);
                regeoResDTO.setFormatted_address(formatted_address);
                regeoResDTO.setAddressComponent(regeoAddressDTO);
                regeoResDTO.setRegeoApiEnum(RegeoApiEnum.BAIDU);
                return regeoResDTO;
            }else{
                logger.warn("请求百度逆地理解析失败={}",jsonObject);
            }
            return null;
        }catch (Exception e){
            logger.error("请求百度地图服务异常{}",e);
            return null;
        }
    }



    public static void main(String[] args) {
        String url = "http://api.map.baidu.com/reverse_geocoding/v3/?ak=d3y8woH5gfh63bB8RqVMml2GrsO52id0&output=json&coordtype=gcj02ll&location=39.912831,116.281732";
        JSONObject jsonObject = RestClient.requestThirdApi(BaseHttpClient.HttpMethod.GET, url, null, null, JSONObject.class);
        Integer status = jsonObject.getInteger("status");
        if(status!=null&&status==0) {
            JSONObject res = jsonObject.getJSONObject("result");
            String formatted_address = res.getString("formatted_address");
            RegeoResDTO regeoResDTO = new RegeoResDTO();
            RegeoAddressDTO regeoAddressDTO = JSON.parseObject(res.getString("addressComponent"), BaiduRegeoAddressDTO.class);
            regeoResDTO.setFormatted_address(formatted_address);
            regeoResDTO.setAddressComponent(regeoAddressDTO);
            regeoResDTO.setRegeoApiEnum(RegeoApiEnum.BAIDU);
            System.out.println(regeoResDTO.toString());
        }else{
            logger.warn("请求百度逆地理解析失败={}",jsonObject);
        }
    }

}
