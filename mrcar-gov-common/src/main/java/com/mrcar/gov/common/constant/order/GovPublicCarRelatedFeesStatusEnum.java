package com.mrcar.gov.common.constant.order;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum GovPublicCarRelatedFeesStatusEnum {
//关联车杂费状态 0:未关联 1:已关联
    UNASSOCIATED(0, "未关联"),
    ASSOCIATED(1, "已关联");


    private final Integer code;
    private final String name;

    public static String getName(Integer code) {
        return Arrays.stream(values()).filter(e -> e.getCode().equals(code)).findFirst().map(GovPublicCarRelatedFeesStatusEnum::getName).orElse(null);
    }
}