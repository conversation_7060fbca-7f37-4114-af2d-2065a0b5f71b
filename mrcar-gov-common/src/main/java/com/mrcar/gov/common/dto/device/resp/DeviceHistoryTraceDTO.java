package com.mrcar.gov.common.dto.device.resp;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import lombok.Data;

/**
 * <AUTHOR>
 * @description: 视频设备历史轨迹
 * @date 2023/10/13 14:06
 */
@Data
public class DeviceHistoryTraceDTO {

	/**
	 * 起点经度
	 */
    private BigDecimal startLng;
    /**
     * 起点纬度
     */
    private BigDecimal startLat;
    /**
     * 起点地址名称
     */
    private String startAddRess;
    /**
     * 起点时间
     */
    private Date startCreateDate;

    /**
     * 终点经度
     */
    private BigDecimal endtLng;
    /**
     * 终点纬度
     */
    private BigDecimal endLat;

    /**
     * 终点地址名称
     */
    private String endAddRess;
    
    /**
     * 终点时间
     */
    private Date endCreateDate;
    /**
     * 距离
     */
    private BigDecimal distance;
    /**
     * 轨迹列表集合
     */
    private List<PointDetailDTO> pointList;

    /**
     * 总时长
     */
    private String trackTime;

    /**
     * 总里程
     * 比如 100.43Km
     */
    private String distanceDesc;

    /**
     * 车牌号
     */
    private String vehicleLicense;

    /**
     * 设备号
     */
    private String deviceId;
    

}
