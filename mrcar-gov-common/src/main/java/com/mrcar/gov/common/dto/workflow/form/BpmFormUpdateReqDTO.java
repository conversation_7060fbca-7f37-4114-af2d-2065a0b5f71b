package com.mrcar.gov.common.dto.workflow.form;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 管理后台 - 动态表单更新 Request VO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BpmFormUpdateReqDTO extends BpmFormBaseDTO {

    /**
     * 表单编号
     * 必填
     * 示例值: 1024
     */
    @NotNull(message = "表单编号不能为空")
    private Long id;

    /**
     * 表单的配置
     * 必填
     * JSON 字符串
     */
    @NotNull(message = "表单的配置不能为空")
    private String conf;

    /**
     * 表单项的数组
     * 必填
     * JSON 字符串的数组
     */
    @NotNull(message = "表单项的数组不能为空")
    private List<String> fields;

}
