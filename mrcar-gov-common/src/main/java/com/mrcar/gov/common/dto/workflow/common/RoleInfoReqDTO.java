package com.mrcar.gov.common.dto.workflow.common;

import com.mrcar.gov.common.dto.PageParamDTO;
import lombok.Data;
import java.util.List;

/**
 * 角色列表查询请求参数 DTO
 *
 * 用于表示查询角色列表的请求参数，包括企业信息、角色信息等。
 *
 * @program mrcar-user-common
 * @description 角色列表查询请求参数 DTO
 * <AUTHOR>
 * @create 2023-01-28 16:23
 **/
@Data
public class RoleInfoReqDTO extends PageParamDTO {

    /** 客户企业名称，非必填 **/
    private String customerCompanyName;

    /** 客户企业编码，非必填 **/
    private String companyCode;

    /** 角色名称，非必填 **/
    private String roleName;

    /** 角色编码，非必填 **/
    private String roleCode;

    /** 角色类型编码，非必填 **/
    private Byte roleType;

    /** 所属平台编码，非必填 **/
    private Byte systemType;

    /** 员工编码，非必填 **/
    private String staffCode;

    /** 根据成员查询角色列表，可能有多个 **/
    private List<String> roleCodeList;

    /** 状态 **/
    private Boolean valid;
}
