package com.mrcar.gov.common.constant.login;

import lombok.Getter;

public class LoginEnum {

    /**
     * 登录认证方式.
     */
    @Getter
    public enum AuthStrategyEnum {

        VERIFYCODE(1, "验证码登录"),
        PASSWORD(2, "密码登录"),
        MINIPROGRAM(3, "小程序登录")

        ;

        private final int code;
        private final String description;

        AuthStrategyEnum(int code, String description) {
            this.code = code;
            this.description = description;
        }

        public static AuthStrategyEnum of(int code) {
            for (AuthStrategyEnum entry : AuthStrategyEnum.values()) {
                if (entry.code == code) {
                    return entry;
                }
            }
            return null;
        }
    }

    /**
     * 登录平台.
     */
    @Getter
    public enum LoginPlatformEnum {

        PC(1, "PC端"),
        MINIPROGRAM(2, "小程序端"),

        ;

        private final int code;
        private final String description;

        LoginPlatformEnum(int code, String description) {
            this.code = code;
            this.description = description;
        }

        public static LoginPlatformEnum of(int code) {
            for (LoginPlatformEnum entry : LoginPlatformEnum.values()) {
                if (entry.code == code) {
                    return entry;
                }
            }
            return null;
        }

    }


}
