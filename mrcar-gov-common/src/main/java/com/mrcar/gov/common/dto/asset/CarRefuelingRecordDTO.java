package com.mrcar.gov.common.dto.asset;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.mrcar.gov.common.constant.asset.OilConsumptionEnum;
import com.mrcar.gov.common.constant.user.GovCommonJudgeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("加油记录-分页查询返回值")
public class CarRefuelingRecordDTO extends OilConsumptionBaseDTO {
    /**
     * 加油单号
     */
    @ApiModelProperty(value = "加油单号")
    private String refuelingCode;
    /**
     * 加油升数
     */
    @ApiModelProperty(value = "加油升数")
    private BigDecimal addValue;
    /**
     * 加油金额（元）
     */
    @ApiModelProperty(value = "加油金额（元）")
    private BigDecimal addFee;
    /**
     * 仪表盘里程
     */
    @ApiModelProperty(value = "仪表盘里程")
    private BigDecimal dashboardMileage;
    /**
     * 加油日期
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "加油日期")
    private Date refuelingTime;
    /**
     * 加油地址
     */
    @ApiModelProperty(value = "加油地址")
    private String refuelingAddress;
    /**
     * 加油小票url
     */
    @ApiModelProperty(value = "加油小票url")
    private String voucherUrl;
    /**
     * 加油小票拍摄时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "加油小票拍摄时间")
    private Date voucherTime;
    /**
     * 仪表盘url
     */
    @ApiModelProperty(value = "仪表盘url")
    private String dashboardUrl;
    /**
     * 仪表盘拍摄时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "仪表盘拍摄时间")
    private Date dashboardTime;
    /**
     * 支付方式1.油卡2.现金
     */
    @ApiModelProperty(value = "支付方式1.油卡2.现金")
    private Integer payType;
    /**
     * 支付方式1.油卡2.现金
     */
    @ApiModelProperty(value = "支付方式1.油卡2.现金")
    private String payTypeValue;
    /**
     *  异常提示1油耗过高（仪表盘）2油耗过高（GPS）3油耗过低（仪表盘）4油耗过低（GPS）5补录工单 6、首次加油不支持分析 7、超60天加油不支持分析
     */
    @ApiModelProperty(value = "异常提示1油耗过高（仪表盘）2油耗过高（GPS）3油耗过低（仪表盘）4油耗过低（GPS）5补录工单")
    private String warnType;
    /**
     * 异常提示1油耗过高（仪表盘）2油耗过高（GPS）3油耗过低（仪表盘）4油耗过低（GPS）5补录工单 6、首次加油不支持分析 7、超60天加油不支持分析
     */
    @ApiModelProperty(value = "异常提示1油耗过高（仪表盘）2油耗过高（GPS）3油耗过低（仪表盘）4油耗过低（GPS）5补录工单")
    private List<String> warnTypeValue;
    /**
     * 异常提示字符串描述拼接
     */
    private String warnTypeValueStr;

    /**
     * GPS里程
     */
    @ApiModelProperty(value = "GPS里程")
    private BigDecimal gpsMileage;

    /**
     * GPS里程
     */
    @ApiModelProperty(value = "GPS百公里油耗")
    private BigDecimal gpsOilConsumption;

    /**
     * 百公里油耗-仪表盘
     */
    @ApiModelProperty(value = "百公里油耗-仪表盘")
    private BigDecimal dashboardOilConsumption;
    /**
     * 是否是合作机构状态，0：否；1：是
     */
    @ApiModelProperty(value = "是否是合作机构状态，0：否；1：是")
    private Integer cooperationOrgStatus;
    /**
     * 是否是合作机构状态描述，0：否；1：是
     */
    @ApiModelProperty(value = "是否是合作机构状态描述，0：否；1：是")
    private String cooperationOrgStatusDesc;
    /**
     * 合作机构名称
     */
    @ApiModelProperty(value = "合作机构名称")
    private String orgName;

     /**
     * 车辆所有人
     */
    @ApiModelProperty(value = "车辆所有人")
    private String vehicleBelongDeptName;


    /**
     * 车辆使用人-单位
     */
    @ApiModelProperty(value = "车辆使用人-单位")
    private String vehicleUseDeptName;

    /**
     * 车辆使用单位-内部部门
     */
    @ApiModelProperty(value = "车辆使用单位")
    private String vehicleUseStructName;


    /**
     * 车辆管理单位
     */
    @ApiModelProperty(value = "车辆管理单位")
    private String vehicleManageDeptName;

    /**
     * 司机姓名
     */
    @ApiModelProperty(value = "司机姓名")
    private String userName;

    private Integer manageCarType;

    private String manageCarTypeName;

    public void setWarnType(String warnType) {
        this.warnType = warnType;
        if (StringUtils.isNotEmpty(warnType)) {
            this.warnTypeValue = Arrays.stream(warnType.split(",")).map(e -> OilConsumptionEnum.WarnTypeEnum.getValueByCode(Byte.valueOf(e))).collect(Collectors.toList());
            this.warnTypeValueStr = this.warnTypeValue.stream().collect(Collectors.joining(","));

        }
    }

    public void setPayType(Integer payType) {
        this.payType = payType;
        this.payTypeValue = OilConsumptionEnum.PayTypeEnum.getValueByCode(Byte.valueOf(payType.toString()));
    }

    public void setCooperationOrgStatus(Integer cooperationOrgStatus) {
        this.cooperationOrgStatus = cooperationOrgStatus;
        this.cooperationOrgStatusDesc = GovCommonJudgeEnum.YES.getCode().equals(cooperationOrgStatus) ?
                GovCommonJudgeEnum.YES.getDesc(): GovCommonJudgeEnum.NO.getDesc();
    }
}