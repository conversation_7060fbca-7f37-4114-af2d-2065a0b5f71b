package com.mrcar.gov.common.constant.user;

/**
 * <AUTHOR>
 * @description: 角色状态;1-启用;0-停用
 * @date 2024/11/7 15:13
 */
public enum RoleStatusEnum {
    /**
     * 有效
     */
    VALID(1, "启用"),
    /**
     * 无效
     */
    INVALID(0, "停用");

    private final Integer code;
    private final String desc;

    RoleStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDesc(int code) {
        for (RoleStatusEnum item : values()) {
            if (item.getCode() == code) {
                return item.getDesc();
            }
        }
        return "";
    }
}
