package com.mrcar.gov.common.dto.config;

import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class BaseEnumDTO {

    private Integer id;
    @ApiModelProperty(value = "车杂费code")
    private Byte code;
    @ApiModelProperty(value = "父类code")
    private Byte parentCode;
    @ApiModelProperty(value = "枚举名称")
    private String name;
    @ApiModelProperty(value = "英文名称")
    private String enName;
    @ApiModelProperty(value = "枚举值")
    private String value;
    @ApiModelProperty(value = "是否有效1.有效")
    private Boolean status;
    @ApiModelProperty(value = "排序")
    private Byte sort;
    @ApiModelProperty(value = "是否默认勾选,true 1勾选")
    private Boolean checked;
    @ApiModelProperty(value = "创建人id")
    private Integer createId;
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    @ApiModelProperty(value = "更新人id")
    private Integer updateId;
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

}