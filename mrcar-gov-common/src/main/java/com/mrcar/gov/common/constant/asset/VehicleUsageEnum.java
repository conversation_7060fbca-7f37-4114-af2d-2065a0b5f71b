package com.mrcar.gov.common.constant.asset;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.stream.Stream;

@Getter
public enum VehicleUsageEnum {

    COMMON_USE(1, "普通用车"),
    SELF_HELP_USE(2, "自助取还"),
    LING_SAN_USE(3, "零散用车"),
    COMMERCIAL_VEHICLES_USE(4, "商务用车"),
    DAI_BU_USE(5, "代步车"),
    OFFICIAL_BUSINESS_USE(6, "公务用车");

    private final int code;
    private final String name;

    VehicleUsageEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }


    public static VehicleUsageEnum getByValue(int code) {
        return Stream.of(values()).filter((c) -> c.code == code).findFirst().orElse(null);
    }

    public static String getNameByCodes(String codes) {
        if (StringUtils.isBlank(codes)) {
            return "";
        }
        String[] codeArray = codes.split(",");
        StringBuilder name = new StringBuilder();
        for (String s : codeArray) {
            VehicleUsageEnum vehicleUsageEnum = getByValue(Integer.parseInt(s));
            if (vehicleUsageEnum != null) {
                name.append(vehicleUsageEnum.getName()).append(",");
            }
        }
        return name.substring(0, name.length() - 1);
    }

}
