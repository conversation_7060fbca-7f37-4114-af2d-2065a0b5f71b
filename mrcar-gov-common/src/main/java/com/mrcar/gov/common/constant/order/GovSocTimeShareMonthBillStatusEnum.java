package com.mrcar.gov.common.constant.order;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;


/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum GovSocTimeShareMonthBillStatusEnum {

    PENDING_CONFIRMATION(0, "待确认"),
    CONFIRMED(1, "已确认");
    private final Integer code;
    private final String name;

    public static String getName(Integer code) {
        return Arrays.stream(values()).filter(e -> e.getCode().equals(code)).findFirst().map(GovSocTimeShareMonthBillStatusEnum::getName).orElse(null);
    }
}
