package com.mrcar.gov.common.constant.user;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 部门类型枚举类
 * <AUTHOR>
 * @date 2024/11/7 15:01
 */
@AllArgsConstructor
@Getter
public enum GovStructTypeEnum {

    REGION(1, "区域"),

    UNIT(2, "单位"),
    DEPARTMENT(3, "内设部门");

    private final Integer code;

    private final String desc;

    // 根据 code 获取枚举实例
    public static GovStructTypeEnum getByCode(int code) {
        for (GovStructTypeEnum status : values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        return null;
    }

    // 根据 code 获取文本描述
    public static String getDesByCode(Integer code) {
        for (GovStructTypeEnum status : values()) {
            if (Objects.equals(status.getCode(), code)) {
                return status.getDesc();
            }
        }
       return "";
    }
}
