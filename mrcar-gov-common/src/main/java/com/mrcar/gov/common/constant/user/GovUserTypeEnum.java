package com.mrcar.gov.common.constant.user;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/11/10 16:13
 */
@AllArgsConstructor
@Getter
public enum GovUserTypeEnum {

    //     1:普通员工 2:司机 3:维修厂员工 4:保险公司员工 99 首汽技术支持
    NORMAL(1, "普通员工"),
    DRIVER(2, "驾驶员"),
    REPAIR_STATION_EMPLOYEE(3, "维修厂员工"),
    INSURANCE_COMPANY_EMPLOYEE(4, "保险公司员工"),
    OIL_COMPANY_EMPLOYEE(5, "加油机构员工"),
    RENT_COMPANY_EMPLOYEE(6, "租赁机构员工"),
    SOCIAL_DRIVER(7, "供应商驾驶员"),
    TECHNICAL_SUPPORT(99, "首汽技术支持");

    private Integer code;
    private String desc;

    // 通过code获取枚举
    public static GovUserTypeEnum of(Integer code) {
        for (GovUserTypeEnum item : GovUserTypeEnum.values()) {
            if (Objects.equals(item.getCode(), code)) {
                return item;
            }
        }
        return null;
    }

    // 根据code获取描述
    public static String getDescByCode(Integer code) {
        GovUserTypeEnum enu = of(code);
        if (Objects.isNull(enu)) {
            return "";
        }
        return enu.desc;
    }

    //判断是不是公司内部员工
    public static Boolean isCompanyUserType(Integer userType) {
        return Objects.equals(NORMAL.getCode(), userType) || Objects.equals(DRIVER.getCode(), userType);
    }

    //判断是否是租赁服务机构员工
    public static Boolean isRentCompanyUserType(Integer userType) {
        return Objects.equals(RENT_COMPANY_EMPLOYEE.getCode(), userType) || Objects.equals(SOCIAL_DRIVER.getCode(), userType) ;
    }

    public static List<Integer> getOrgUserTypeList() {
        return Lists.newArrayList(REPAIR_STATION_EMPLOYEE.getCode(), INSURANCE_COMPANY_EMPLOYEE.getCode(), OIL_COMPANY_EMPLOYEE.getCode(), RENT_COMPANY_EMPLOYEE.getCode(), SOCIAL_DRIVER.getCode());
    }

    public static Boolean isOrgUserType(Integer userType) {
        return getOrgUserTypeList().contains(userType);
    }

    public static Boolean isDriver(Integer userType) {
        return Objects.equals(DRIVER.getCode(), userType) || Objects.equals(SOCIAL_DRIVER.getCode(), userType);
    }

    /**
     * 机构联系人用户类型
     */
    public static List<Integer> listOrgContactorUserType() {
        return Lists.newArrayList(REPAIR_STATION_EMPLOYEE.getCode(), INSURANCE_COMPANY_EMPLOYEE.getCode(), OIL_COMPANY_EMPLOYEE.getCode(), RENT_COMPANY_EMPLOYEE.getCode());
    }


}
