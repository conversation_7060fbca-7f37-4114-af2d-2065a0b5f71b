package com.mrcar.gov.common.dto.device;

public class GpsDeviceRestUrl {
    public static final String GOV_GPS_DEVICE_GET_PAGE_LIST = "/gov/gps/device/getPageList";
    public static final String GOV_GPS_DEVICE_EXPORT_LIST = "/gov/gps/device/exportList";
    public static final String GOV_GPS_DEVICE_EXPORT_HISTORY_LIST = "/gov/gps/device/exportHistoryList";

    public static final String GOV_GPS_DEVICE_SAVE = "/gov/gps/device/save";
    public static final String GOV_GPS_DEVICE_UPDATE = "/gov/gps/device/update";
    public static final String GOV_GPS_DEVICE_DELETE = "/gov/gps/device/delete";
    public static final String GOV_GPS_DEVICE_DETAIL = "/gov/gps/device/detail";

    public static final String GOV_GPS_DEVICE_GET_DETAIL_BY_SIM = "/gov/gps/device/getDetailBySimNo";

    public static final String GOV_GPS_DEVICE_GET_SAVE_BATCH_TEMPLATE = "/gov/gps/device/getSaveBatchTemplate";
    public static final String GOV_GPS_DEVICE_SAVE_BATCH = "/gov/gps/device/saveBatch";

    /**
     * 供应商终端导入模板
     */
    public static final String SUPPLIER_GPS_DEVICE_GET_SAVE_BATCH_TEMPLATE = "/supplier/gps/device/getSaveBatchTemplate";
    /**
     * 供应商设备导出
     */
    public static final String SUPPLIER_GPS_DEVICE_EXPORT_LIST = "/supplier/gps/device/exportList";
    /**
     * 供应商设备历史记录导出
     */
    public static final String SUPPLIER_GPS_DEVICE_EXPORT_HISTORY_LIST = "/supplier/gps/device/exportHistoryList";
    /**
     * 供应商设备批量导入
     */
    public static final String SUPPLIER_GPS_DEVICE_SAVE_BATCH = "/supplier/gps/device/saveBatch";


}
