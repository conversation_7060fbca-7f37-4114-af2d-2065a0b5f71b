package com.mrcar.gov.common.config;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.mongodb.client.MongoClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.convert.converter.Converter;
import org.springframework.data.mongodb.MongoDatabaseFactory;
import org.springframework.data.mongodb.MongoDbFactory;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.SimpleMongoClientDatabaseFactory;
import org.springframework.data.mongodb.core.convert.DefaultDbRefResolver;
import org.springframework.data.mongodb.core.convert.DefaultMongoTypeMapper;
import org.springframework.data.mongodb.core.convert.MappingMongoConverter;
import org.springframework.data.mongodb.core.convert.MongoConverter;
import org.springframework.data.mongodb.core.convert.MongoCustomConversions;
import org.springframework.data.mongodb.core.mapping.MongoMappingContext;

import java.util.ArrayList;
import java.util.List;

/**
 * MongoDB配置
 */
@Configuration
public class MongoConfig {

    @NacosValue(value = "${mongodb.mrcar.uri}")
    private String uriMrcar;
    @NacosValue(value = "${mongodb.mrcar.database}")
    private String databaseMrcar;

    @NacosValue(value = "${mongodb.bi.uri}")
    private String uriBi;
    @NacosValue(value = "${mongodb.bi.database}")
    private String databaseBi;

    @Bean(name = {"mrcarMongoTemplate", "mongoTemplate"})
    @Primary
    public MongoTemplate mrcarMongoTemplate() {

        MongoDatabaseFactory factory =
                new SimpleMongoClientDatabaseFactory(MongoClients.create(uriMrcar), databaseMrcar);

        // resolver
        DefaultDbRefResolver dbRefResolver = new DefaultDbRefResolver(factory);
        // converter
//        MappingMongoConverter converter = new MappingMongoConverter(resolver, context);
//        converter.setTypeMapper(new DefaultMongoTypeMapper(null));
//
//        List<Object> list = new ArrayList<>();
//        list.add(new BigDecimalToDecimal128Converter());
//        list.add(new Decimal128ToBigDecimalConverter());
//        converter.setCustomConversions(new MongoCustomConversions(list));
//        return new MongoTemplate(factory, converter);


        List<Converter<?, ?>> converters = new ArrayList<>();
        converters.add(new BigDecimalToDecimal128Converter());
        converters.add(new Decimal128ToBigDecimalConverter());
        MongoCustomConversions conversions = new MongoCustomConversions(converters);
        MongoMappingContext mappingContext = new MongoMappingContext();
        mappingContext.setSimpleTypeHolder(conversions.getSimpleTypeHolder());
        mappingContext.afterPropertiesSet();

        MappingMongoConverter converter = new MappingMongoConverter(dbRefResolver, mappingContext);
        converter.setTypeMapper(new DefaultMongoTypeMapper(null));
        converter.setCustomConversions(conversions);
        converter.setCodecRegistryProvider(factory);
        converter.afterPropertiesSet();
        return new MongoTemplate(factory, converter);
    }


    @Bean(name = "biMongoTemplate")
    public MongoTemplate biMongoTemplate() {

        MongoDatabaseFactory factory =
                new SimpleMongoClientDatabaseFactory(MongoClients.create(uriBi), databaseBi);

        // resolver
        DefaultDbRefResolver dbRefResolver = new DefaultDbRefResolver(factory);
        // converter
//        MappingMongoConverter converter = new MappingMongoConverter(resolver, context);
//        converter.setTypeMapper(new DefaultMongoTypeMapper(null));
//
//        List<Object> list = new ArrayList<>();
//        list.add(new BigDecimalToDecimal128Converter());
//        list.add(new Decimal128ToBigDecimalConverter());
//        converter.setCustomConversions(new MongoCustomConversions(list));
//        return new MongoTemplate(factory, converter);


        List<Converter<?, ?>> converters = new ArrayList<>();
        converters.add(new BigDecimalToDecimal128Converter());
        converters.add(new Decimal128ToBigDecimalConverter());
        MongoCustomConversions conversions = new MongoCustomConversions(converters);
        MongoMappingContext mappingContext = new MongoMappingContext();
        mappingContext.setSimpleTypeHolder(conversions.getSimpleTypeHolder());
        mappingContext.afterPropertiesSet();

        MappingMongoConverter converter = new MappingMongoConverter(dbRefResolver, mappingContext);
        converter.setTypeMapper(new DefaultMongoTypeMapper(null));
        converter.setCustomConversions(conversions);
        converter.setCodecRegistryProvider(factory);
        converter.afterPropertiesSet();
        return new MongoTemplate(factory, converter);
    }


}
