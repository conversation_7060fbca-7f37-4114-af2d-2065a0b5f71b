package com.mrcar.gov.common.dto.order.resp;

import com.mrcar.gov.common.dto.device.resp.BdTravelDTO;
import lombok.Data;

import java.util.Date;
import java.util.List;


@Data
public class GovPublicCarOrderInfoDTO {

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 订单类型：1-办公用车，2-无任务用车
     */
    private Integer orderType;

    /**
     * 订单类型：1-办公用车，2-无任务用车
     */
    private String orderTypeStr;

    /**
     * 订单状态(10待审批  20待调度 30待出发 40用车中 50已完成 60已取消 70 审批撤回 80 审批驳回 90已结束)
     */
    private Integer orderStatus;

    /**
     * 订单状态(10待审批  20待调度 30待出发 40用车中 50已完成 60已取消 70 审批撤回 80 审批驳回 90已结束)
     */
    private String orderStatusStr;

    /**
     * 下单人部门
     */
    private String createStructName;

    /**
     * 下单人
     */
    private String createUserName;

    /**
     * 下单时间
     */
    private Date createTime;

    /**
     * 用车人
     */
    private List<String> passengerUserList;

    /**
     * 用车人部门
     */
    private List<String> passengerStructList;

    /**
     * 车牌号
     */
    private String vehicleLicense;

    /**
     * 出发地
     */
    private String estimatedDepartureLocation;

    /**
     * 目的地
     */
    private String estimatedDestinationLocation;


    /**
     * 实际出发短地址
     */
    private String actualDepartureShortLocation;

    /**
     * 实际目的地短地址
     */
    private String actualDestinationShortLocation;


    /**
     * 预计开始时间
     */
    private Date expectedPickupTime;

    /**
     * 预计结束时间
     */
    private Date expectedReturnTime;

    /**
     * 实际开始时间
     */
    private Date orderStartTime;

    /**
     * 实际结束时间
     */
    private Date orderEndTime;

    /**
     * 用车事由
     */
    private String carUseReason;

    /**
     * 用车备注
     */
    private String orderUserMemo;


    /**
     * 调度类型：0-无需调度，1-调度
     */
    private String scheduleTypeStr;

    /**
     * 核实状态 0:未核实 1:已核实 2:无需核实
     */
    private Integer verifyStatus;


    /**
     * 核实状态 0:未核实 1:已核实 2:无需核实
     */
    private String verifyStatusStr;

    /**
     * 驾车路线规划
     */
    private BdTravelDTO bdTravelDTO;



}