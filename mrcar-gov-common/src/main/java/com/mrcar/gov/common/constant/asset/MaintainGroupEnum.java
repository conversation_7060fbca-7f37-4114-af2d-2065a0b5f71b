package com.mrcar.gov.common.constant.asset;


import java.util.Objects;
import java.util.stream.Stream;

/**
 * Author:  wangM
 * Date:  2025/1/11 11:50
 * DESC:  维修组枚举
 */
public enum MaintainGroupEnum {
    ELECTRICAL_EQUIPMENT_GROUP("MG001", "电器组"),
    CHASSIS_GROUP("MG002", "底盘组"),
    CAR_BODY_GROUP("MG003", "车身组"),
    MAINTENANCE_GROUP("MG004", "保养组"),
    ENGINE_GROUP("MG005", "发动机组"),
    AIR_CONDITION_GROUP("MG006", "空调系统");

    private String code;
    private String message;

    private MaintainGroupEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return this.code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMessage() {
        return this.message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public static String getMaintainGroupName(String code) {
        return (String) Stream.of(values()).filter((e) -> {
            return Objects.equals(e.getCode(), code);
        }).map(MaintainGroupEnum::getMessage).findFirst().orElse("");
    }

    public static String getName(String name) {
        return (String)Stream.of(values()).filter((e) -> {
            return Objects.equals(e.getMessage(), name);
        }).map(MaintainGroupEnum::getCode).findFirst().orElse("");
    }
}
