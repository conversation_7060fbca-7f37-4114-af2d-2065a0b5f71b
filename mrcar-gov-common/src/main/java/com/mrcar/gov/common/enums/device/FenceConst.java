package com.mrcar.gov.common.enums.device;

/**
 * Created by hp on 2019/10/23.
 */
public class FenceConst {

    public enum FenceType {
        /**
         * 圆形
         */
        ROUND(1),
        /**
         * 多边形
         */
        POLYGON(2),
        /**
         * 行政区划（省市）
         */
        REGION(3),

        province(4),

        city(5),

        district(6),
        ;

        private final int value;

        public int getValue() {
            return value;
        }

        private FenceType(int value) {
            this.value = value;
        }

        public static FenceType getEnumByCode(int code){
            for (FenceType fenceType : values()){
                if (fenceType.getValue() == code){
                    return fenceType;
                }
            }
            return null;
        }
    }

}
