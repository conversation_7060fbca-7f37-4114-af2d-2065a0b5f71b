package com.mrcar.gov.common.dto.device.resp;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class GaodeRegeoAddressDTO extends RegeoAddressDTO{

	/**
     * 当城市是省直辖县时返回为空，以及城市为北京、上海、天津、重庆四个直辖市时，该字段返回为空
     */
    private List<String> city;
    private String citycode;
    private String township;
    private String towncode;
}
