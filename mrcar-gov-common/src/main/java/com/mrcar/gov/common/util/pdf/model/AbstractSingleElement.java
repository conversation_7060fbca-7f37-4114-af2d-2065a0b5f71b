package com.mrcar.gov.common.util.pdf.model;

import com.mrcar.gov.common.util.pdf.layout.MetricType;
import com.mrcar.gov.common.util.pdf.layout.MetricValue;
import com.mrcar.gov.common.util.pdf.layout.RoundMetric;

public abstract class AbstractSingleElement implements SingleElement {

    protected final BlockElement _parent;

    protected MetricValue _height;
    protected MetricValue _width;

    protected RoundMetric _margin;

    protected AbstractSingleElement(BlockElement parent,
                                    MetricValue height,
                                    MetricValue width,
                                    RoundMetric margin) {
        this._parent = parent;
        this._height = height;
        this._width = width;
        this._margin = margin;
    }

    @Override
    public BlockElement parent() {
        return this._parent;
    }

    @Override
    public MetricValue height() {
        return this._height;
    }

    @Override
    public MetricValue width() {
        return this._width;
    }

    @Override
    public RoundMetric margin() {
        return this._margin;
    }

    @Override
    public boolean refreshHeight() {
        if (this._height.type() == MetricType.PERCENTAGE) {
            if (this._parent.height().type() != MetricType.ABSOLUTE) {
                return false;
            }
            // 百分比
            this._height = MetricValue.create(MetricType.ABSOLUTE,
                    this._parent.height().value() * this._height.value());
        } else if (this._height.type() == MetricType.ADAPTIVE) {
            // 自适应
            this._height = doRefreshAdaptiveHeightMetric();
        }
        return true;
    }

    @Override
    public boolean refreshWidth() {
        if (this._width.type() == MetricType.PERCENTAGE) {
            if (this._parent.width().type() != MetricType.ABSOLUTE) {
                return false;
            }
            // 百分比
            this._width = MetricValue.create(MetricType.ABSOLUTE,
                    this._parent.width().value() * this._width.value());
        } else if (this._width.type() == MetricType.ADAPTIVE) {
            // 自适应
            this._width = doRefreshAdaptiveWidthMetric();
        }
        return true;
    }

    protected abstract MetricValue doRefreshAdaptiveWidthMetric();

    protected abstract MetricValue doRefreshAdaptiveHeightMetric();

    @Override
    public abstract String name();

}
