package com.mrcar.gov.common.dto.thirdparty.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

@Data
public class BLicenseInfoDTO {


    /**
     * code
     */
    @JSONField(name ="code")
    private int code;
    /**
     * data
     */
    @JSO<PERSON>ield(name ="data")
    private DataBean data;
    /**
     * requestId
     */
    @JSONField(name ="requestId")
    private String requestId;


    @Data
    public static class DataBean {
        /**
         * figure
         */
        @JSONField(name ="figure")
        private List<FigureBean> figure;
        /**
         * ftype
         */
        @JSONField(name ="ftype")
        private int ftype;
        /**
         * algoVersion
         */
        @JSONField(name ="algo_version")
        private String algoVersion;
        /**
         * data
         */
        @JSONField(name ="data")
        private InnerDataBean data;
        /**
         * orgWidth
         */
        @JSONField(name ="orgWidth")
        private int orgWidth;
        /**
         * sliceRect
         */
        @JSONField(name ="sliceRect")
        private SliceRectBean sliceRect;
        /**
         * requestId
         */
        @J<PERSON><PERSON>ield(name ="requestId")
        private String requestId;
        /**
         * width
         */
        @JSONField(name ="width")
        private int width;
        /**
         * orgHeight
         */
        @JSONField(name ="orgHeight")
        private int orgHeight;
        /**
         * prismKeyvalueinfo
         */
        @JSONField(name ="prism_keyValueInfo")
        private List<PrismKeyValueInfoBean> prismKeyvalueinfo;
        /**
         * height
         */
        @JSONField(name ="height")
        private int height;


        @Data
        public static class InnerDataBean {
            /**
             * registrationDate
             */
            @JSONField(name ="RegistrationDate")
            private String registrationDate;
            /**
             * companyType
             */
            @JSONField(name ="companyType")
            private String companyType;
            /**
             * companyName
             */
            @JSONField(name ="companyName")
            private String companyName;
            /**
             * businessScope
             */
            @JSONField(name ="businessScope")
            private String businessScope;
            /**
             * title
             */
            @JSONField(name ="title")
            private String title;
            /**
             * validPeriod
             */
            @JSONField(name ="validPeriod")
            private String validPeriod;
            /**
             * companyForm
             */
            @JSONField(name ="companyForm")
            private String companyForm;
            /**
             * creditCode
             */
            @JSONField(name ="creditCode")
            private String creditCode;
            /**
             * registeredCapital
             */
            @JSONField(name ="registeredCapital")
            private String registeredCapital;
            /**
             * legalPerson
             */
            @JSONField(name ="legalPerson")
            private String legalPerson;
            /**
             * validFromDate
             */
            @JSONField(name ="validFromDate")
            private String validFromDate;
            /**
             * businessAddress
             */
            @JSONField(name ="businessAddress")
            private String businessAddress;
            /**
             * issueDate
             */
            @JSONField(name ="issueDate")
            private String issueDate;
            /**
             * validToDate
             */
            @JSONField(name ="validToDate")
            private String validToDate;


        }

        @Data
        public static class SliceRectBean {
            /**
             * y0
             */
            @JSONField(name ="y0")
            private int y0;
            /**
             * x0
             */
            @JSONField(name ="x0")
            private int x0;
            /**
             * y1
             */
            @JSONField(name ="y1")
            private int y1;
            /**
             * x1
             */
            @JSONField(name ="x1")
            private int x1;
            /**
             * y2
             */
            @JSONField(name ="y2")
            private int y2;
            /**
             * x2
             */
            @JSONField(name ="x2")
            private int x2;
            /**
             * y3
             */
            @JSONField(name ="y3")
            private int y3;
            /**
             * x3
             */
            @JSONField(name ="x3")
            private int x3;
        }

        @Data
        public static class FigureBean {
            /**
             * w
             */
            @JSONField(name ="w")
            private int w;
            /**
             * x
             */
            @JSONField(name ="x")
            private int x;
            /**
             * h
             */
            @JSONField(name ="h")
            private int h;
            /**
             * y
             */
            @JSONField(name ="y")
            private int y;
            /**
             * box
             */
            @JSONField(name ="box")
            private BoxBean box;
            /**
             * type
             */
            @JSONField(name ="type")
            private String type;
            /**
             * points
             */
            @JSONField(name ="points")
            private List<PointsBean> points;


            @Data
            public static class BoxBean {
                /**
                 * w
                 */
                @JSONField(name ="w")
                private int w;
                /**
                 * x
                 */
                @JSONField(name ="x")
                private int x;
                /**
                 * h
                 */
                @JSONField(name ="h")
                private int h;
                /**
                 * y
                 */
                @JSONField(name ="y")
                private int y;
                /**
                 * angle
                 */
                @JSONField(name ="angle")
                private int angle;
            }

            @Data
            public static class PointsBean {
                /**
                 * x
                 */
                @JSONField(name ="x")
                private int x;
                /**
                 * y
                 */
                @JSONField(name ="y")
                private int y;
            }
        }

        @Data
        public static class PrismKeyValueInfoBean {
            /**
             * valuePos
             */
            @JSONField(name ="valuePos")
            private List<ValuePosBean> valuePos;
            /**
             * keyProb
             */
            @JSONField(name ="keyProb")
            private int keyProb;
            /**
             * valueProb
             */
            @JSONField(name ="valueProb")
            private int valueProb;
            /**
             * value
             */
            @JSONField(name ="value")
            private String value;
            /**
             * key
             */
            @JSONField(name ="key")
            private String key;


            @Data
            public static class ValuePosBean {
                /**
                 * x
                 */
                @JSONField(name ="x")
                private int x;
                /**
                 * y
                 */
                @JSONField(name ="y")
                private int y;
            }
        }
    }
}
