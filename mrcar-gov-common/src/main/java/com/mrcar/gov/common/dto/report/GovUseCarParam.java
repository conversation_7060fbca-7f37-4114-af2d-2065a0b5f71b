package com.mrcar.gov.common.dto.report;

import com.google.gson.annotations.Expose;
import com.mrcar.gov.common.constant.asset.BiDateSearchTypeEnum;
import com.mrcar.gov.common.dto.BaseDTO;
import lombok.Data;

import java.time.LocalDate;
import java.util.Objects;
import java.util.List;

@Data
public class GovUseCarParam extends BaseDTO {
    /**
     * 开始日期
     */
    private LocalDate startDate = LocalDate.of(2024, 10, 1);

    /**
     * 结束日期
     */
    private LocalDate endDate = LocalDate.of(2024, 12, 30);

    @Expose(serialize = false)
    private LocalDate originalEndDate;

    /**
     * 单位性质 党政机关、多事业单位、事业单位、团体组织
     */
    private Integer structType;

    /**
     * 单位状态 启用 停用
     */
    private Integer enableStatus;

    /**
     * 车辆类型
     */
    private Integer vehicleType;

    /**
     * 车辆使用性质
     */
    private Integer vehicleUseType;

    /**
     * 机构编码
     */
    private String structCode;


    /**
     * 筛选类型
     * 1-年 2-月 3-日 4-自定义
     */
    private Integer searchType = BiDateSearchTypeEnum.DAY.getCode();


    @Expose(serialize = false)
    private List<String> deptCodeList;

    public void setStartDate(LocalDate startDate) {
        if(Objects.nonNull(startDate)){
            this.startDate = startDate;
        }

    }

    public void setEndDate(LocalDate endDate) {
        if(Objects.nonNull(endDate)){
            this.endDate = endDate;
        }
    }

    public void setStructType(Integer structType) {
        if(Objects.nonNull(structType)){
            this.structType = structType;
        }

    }

    public void setEnableStatus(Integer enableStatus) {
        if(Objects.nonNull(enableStatus)){
            this.enableStatus = enableStatus;
        }
    }

    public void setVehicleType(Integer vehicleType) {
        if(Objects.nonNull(vehicleType)){
            this.vehicleType = vehicleType;
        }
    }

    public void setVehicleUseType(Integer vehicleUseType) {
        if(Objects.nonNull(vehicleUseType)){
            this.vehicleUseType = vehicleUseType;
        }
    }

    public void setSearchType(Integer searchType) {
        if(Objects.nonNull(searchType)){
            this.searchType = searchType;
        }
    }
}
