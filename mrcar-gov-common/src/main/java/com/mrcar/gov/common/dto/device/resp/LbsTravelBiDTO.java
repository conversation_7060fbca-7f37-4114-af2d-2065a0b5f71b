package com.mrcar.gov.common.dto.device.resp;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class LbsTravelBiDTO {
    private BigDecimal startLng;
    private BigDecimal startLat;
    private BigDecimal startLngBaidu;
    private BigDecimal startLatBaidu;
    private String startCity;
    private String startPosition;

    private BigDecimal endLng;
    private BigDecimal endLat;
    private BigDecimal endLngBaidu;
    private BigDecimal endLatBaidu;
    private String endCity;
    private String endPosition;

    private BigDecimal  maxSpeed;

    private Date startDate;
    private Date endDate;
    private String vehicleLicense;
    private String simNo;
    private String deviceNo;
    private String deviceType;
    private String vehicleVin;
    private String vehicleSerialNo;

    private BigDecimal distance;

    /**
     * 起始车机里程读数
     */
    private Integer startMile;
    /**
     * 终止车机里程读数
     */
    private Integer endMile;

    /**
     * 行驶时长 11：22：12
     */
    private long duration;
    /**
     * 停车次数
     */
    private String stopCount;
    /**
     * 耗油量L
     */
    private BigDecimal useFuel;
    /**
     * 行程标签
     */
    private String tag;
    /**
     * 行程中停车时长
     */
    private Integer stopDuration;
    /**
     * 行程净行驶时长
     */
    private Integer driveDuration;
}

