package com.mrcar.gov.common.dto.device.req;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class GpsDeviceImportRowDataDTO {
    /**
     * 序号
     */
    private Integer rowDataIndex;
    /**
     * 设备编号
     */
    @ExcelProperty(index = 0)
    private String deviceNo;
    /**
     * SIM卡号
     */
    @ExcelProperty(index = 1)
    private String simNo;
    /**
     * 设备厂商名称
     */
    @ExcelProperty(index = 2)
    private String manufactName;
    /**
     * 设备型号名称
     */
    @ExcelProperty(index = 3)
    private String modelName;
    /**
     * 车架号
     */
    @ExcelProperty(index = 4)
    private String vehicleVin;
    /**
     * 供应商
     */
    @ExcelProperty(index = 5)
    private String supplierName;
}
