package com.mrcar.gov.common.dto.workflow.copy;

import lombok.Data;
import java.util.Date;

/**
 * 抄送列表响应DTO
 *
 * <AUTHOR>
 * @date 2024/9/3 15:03
 */
@Data
public class CopyProcessRespDTO {

    /** 流程编号 */
    private String processInstanceId;

    /** 流程名称 */
    private String processName;

    /** 抄送人姓名 */
    private String copyUserName;

    /** 发起人id */
    private Integer copyUserId;

    /** 流程发起时间 */
    private Date processStartDate;

    /** 抄送时间 */
    private Date copyDate;

    /** 抄送创建人id */
    private Integer copyCreatorId;

    /** 抄送创建人名称 */
    private String copyCreatorName;

    /** 抄送创建人电话 */
    private String copyCreatorMobile;

    /** 公司名称 */
    private String companyName;

    /** 企业id */
    private Integer companyId;

}
