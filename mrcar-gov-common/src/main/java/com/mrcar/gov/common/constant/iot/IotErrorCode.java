package com.mrcar.gov.common.constant.iot;

import com.izu.framework.web.rest.response.ResultMessage;

/**iot业务错误码**/
public class IotErrorCode {

	@ResultMessage("司机不存在")
	public static final int  DRIVER_NOT_FOUND                   = 1001;
	@ResultMessage("乘客被拉黑，禁止乘坐顺风车")
	public static final int  RIDE_FORBIDDEN                     = 1002;

	@ResultMessage("产品代码{0}不合法，原因为：{1}")
	public static final int  WRONG_PRODUCT_CODE                 = 1003;

	@ResultMessage("查询信息不存在")
	public static final int QUERY_RESULT_NOT_EXISTS				=1004;

	@ResultMessage("圆形围栏center项必传")
	public static final int CENTER_IS_NULL						=1005;

	@ResultMessage("圆形围栏radius项必传")
	public static final int RADIUS_IS_NULL						=1006;

	@ResultMessage("多边形围栏points项必传")
	public static final int POINTS_IS_NULL						=1007;

	@ResultMessage("至少需要指派一辆车")
	public static final int VEHICLE_NOT_ASSIGN					=1008;

	@ResultMessage("电子围栏数据不存在")
	public static final int FENCE_NOT_EXIST						=1009;

	@ResultMessage("您关注车辆数已超过限制")
	public static final int FOCUS_CAR_TOO_MUCH = 1050;

	@ResultMessage("该车辆已关注")
	public static final int FOCUS_CAR_MUTI = 1051;

	@ResultMessage("车辆信息不存在")
	public static final int VEHICLE_NOT_EXSITS					=2000;

	@ResultMessage("设备信息已存在，不允许重复添加！")
	public static final int DEVICE_IS_EXIST					=2001;

	@ResultMessage("车辆已绑定设备，不允许重复添加！")
	public static final int VEHICLE_IS_EXSITS					=2002;

	@ResultMessage("新增设备失败")
	public static final int DEVICE_INSERT_ERROR					=2003;

	@ResultMessage("设备信息不存在")
	public static final int DEVICE_NOT_EXIST					=2004;

	@ResultMessage("编辑设备失败")
	public static final int DEVICE_UPDATE_ERROR					=2005;

	@ResultMessage("绑定设备失败")
	public static final int DEVICE_BIND_ERROR					=2006;

	@ResultMessage("解绑设备失败")
	public static final int DEVICE_UN_BIND_ERROR					=2007;

	@ResultMessage("不允许解绑设备")
	public static final int DEVICE_NOT_ALLOW_UN_BIND					=2008;

	@ResultMessage("设备已绑定车辆，不允许删除!")
	public static final int DEVICE_NOT_ALLOW_DELETE					=2009;

	@ResultMessage("删除设备失败")
	public static final int DEVICE_DELETE_ERROR					=2010;

	@ResultMessage("不允许取消废止设备")
	public static final int DEVICE_NOT_ALLOW_UN_DELETE					=2011;

	@ResultMessage("取消废止设备失败")
	public static final int DEVICE_UN_DELETE_ERROR					=2012;

	@ResultMessage("上传文件数据为空")
	public static final int FILE_NULL_ERROR					=2013;

	@ResultMessage("厂商型号信息有误")
	public static final int DEVICE_MANFACT_MODEL_ERROR					=2014;

	@ResultMessage("当前企业不允许操作")
	public static final int DEVICE_NOT_ALLOW_ERROR					=2015;

	@ResultMessage("设备已绑定，请先解除绑定")
	public static final int DEVICE_NOT_ALLOW_BIND					=2016;

	@ResultMessage("非自有安装设备，不允许操作")
	public static final int DEVICE_NOT_ALLOW_OPRETE					=2017;

	@ResultMessage("车架号输入有误，请核实")
	public static final int VEHICLE_VIN_ERROR					=2018;

	@ResultMessage("{0}")
	public static final int VEHICLE_CONTROL_FAIL					=2019;

	@ResultMessage("只能绑定自有车辆，不能绑定首汽车辆")
	public static final int VEHICLETYPE_NOT_BIND				=2030;

	@ResultMessage("车辆已绑定同类型设备，请删除绑定后再操作！")
	public static final int VEHICLE_IS_BIND_SAME_DEVICE_TYPE					=2020;

	@ResultMessage("当前车队设置围栏数量已达到3个，不允许再次添加")
	public static final int DRIVER_FENCE_COUNT_EXCEED = 2021;

	@ResultMessage("该车队已被删除，请选择其他车队或者删除当前围栏")
	public static final int MOTORCADE_NOT_EXIST = 2022;

	@ResultMessage("围栏信息不存在")
	public static final int ATTENDANCE_FENCE_EMTYP = 2023;

	@ResultMessage("行程信息不存在")
	public static final int TRAVEL_INFO_NOT_EXIST = 2024;

	@ResultMessage("不存在车机设备")
	public static final int TBOX_NOT_EXIST = 2025;

	@ResultMessage("获取车辆信息失败！")
	public static final int REST_FOR_VEHICLE_ERROR = 2026;

	@ResultMessage("试用设备不允许操作车辆！")
	public static final int TRIAL_DEVICE_NOT_ALLOWED_OPERATE_VEHICLE  = 2027;

	@ResultMessage("设备未在线，请稍后再试！")
	public static final int VIDEO_DEVICE_OFFLINE  = 2028;

	@ResultMessage("返回数据为空")
	public static final int VIDEO_TRACE_DATE_NOT_EXIST=2029;

	@ResultMessage("连接视频设备超时，请稍后重试！")
	public static final int VIDEO_CONNECT_TIME_OUT=2031;

	@ResultMessage("该车辆和设备的绑定关系不正确")
	public static final int VIDEO_CAR_RELATION_NOT=2032;

	@ResultMessage("该车辆未绑定车机")
	public static final int CAR_NOT_BIND_DEVICE=2033;

	@ResultMessage("此通道未安装设备")
	public static final int CHANNEL_NOT_EXIST=2034;

	@ResultMessage("{0}")
	public static final int VEHICLE_CONTROL_WARN = 2040;

	@ResultMessage("车辆用途错误")
	public static final int VEHICLE_USAGE_ERROR = 2041;

	@ResultMessage("公务车围栏区域不能重叠。重叠围栏名称: {0}")
	public static final int FENCE_INTERSECT_ERROR = 2042;

}
