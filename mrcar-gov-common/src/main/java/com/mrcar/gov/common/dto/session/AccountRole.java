package com.mrcar.gov.common.dto.session;

import lombok.Data;

import java.io.Serializable;

/**
 * 用户角色信息
 */
@Data
public class AccountRole implements Serializable {

    /**
     * 角色编码
     */
    private String roleCode;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 角色说明
     */
    private String roleDesc;

    /**
     * 角色状态;1-有效;0-无效
     */
    private Integer roleStatus;

    /**
     * 角色类型;0-未知;1-系统角色;2-自定义角色
     */
    private Integer roleType;

    /**
     * 角色标志;0-正常角色;1-系统管理员;
     */
    private Integer roleMark;

}
