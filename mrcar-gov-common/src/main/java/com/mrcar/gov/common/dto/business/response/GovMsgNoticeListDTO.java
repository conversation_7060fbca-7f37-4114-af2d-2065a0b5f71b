package com.mrcar.gov.common.dto.business.response;


import java.math.BigDecimal;
import java.util.Date;

import com.mrcar.gov.common.constant.business.GovMsgModuleEnum;
import com.mrcar.gov.common.constant.business.GovMsgNoticeStatusEnum;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.nodes.Document;
import org.jsoup.Jsoup;

/**
 * <AUTHOR>
 * @date 2024/12/27 18:48
 */
@Data
public class GovMsgNoticeListDTO {

    /**
     * id
     */
    private Integer id;

    /**
     * 企业id
     */
    private Integer companyId;

    /**
     * 通知分类 1公文公告 2系统通知
     */
    private Integer msgModule;
    /**
     * 通知分类描述
     */
    private String msgModuleDesc;

    /**
     * 公告通知编号
     */
    private String noticeNo;

    /**
     * 公告通知标题
     */
    private String noticeName;
    /**
     * 公告内容
     */
    private String noticeContent;

    /**
     * 发布人编码
     */
    private String publisherCode;

    /**
     * 发布人名字
     */
    private String publisherName;

    /**
     * 发布单位编码
     */
    private String publishDeptCode;

    /**
     * 发布单位名称
     */
    private String publishDeptName;

    /**
     * 发布时间
     */
    private Date publishTime;

    /**
     * 状态 0:删除;1:草稿；2:已发布;3推送中;4已推送;5推送失败
     */
    private Integer noticeStatus;
    /**
     * 状态描述
     */
    private String noticeStatusDesc;

    /**
     * 推送部门列表
     */
    private Object pushDeptList;

    /**
     * 推送部门模式 1:全部 2指定部门
     */
    private Integer pushDeptModel;


    /**
     * 推送数量
     */
    private Integer pushNum;

    /**
     * 触达数量
     */
    private Integer reachNum;

    /**
     * 已读数量
     */
    private Integer readNum;
    /**
     * 已读率. 例如：30.23%
     */
    private String readRatio;
    /**
     * 未读数量
     */
    private Integer unReadNum;



    public void setMsgModule(Integer msgModule) {
        this.msgModule = msgModule;
        this.msgModuleDesc = GovMsgModuleEnum.getDescByCode(msgModule);
    }

    public void setNoticeStatus(Integer noticeStatus){
        this.noticeStatus = noticeStatus;
        this.noticeStatusDesc = GovMsgNoticeStatusEnum.getDescByCode(noticeStatus);
    }

    public void setNoticeContent(String noticeContent) {
        this.noticeContent = noticeContent;
        if(StringUtils.isNotBlank(noticeContent)){
            Document doc = Jsoup.parse(noticeContent);
            this.noticeContent = doc.text();
        }
    }
}
