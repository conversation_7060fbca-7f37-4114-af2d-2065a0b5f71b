package com.mrcar.gov.common.constant.order;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;


/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum GovSocTimeShareOperationTypeEnum {

    CREATE(1, "创建账单"),
    ADJUST_SETTLE(2, "调整最终结算金额"),
    CONFIRM_BILL(3, "确认账单"),
    ;
    private final Integer code;
    private final String name;

    public static String getName(Integer code) {
        return Arrays.stream(values()).filter(e -> e.getCode().equals(code)).findFirst().map(GovSocTimeShareOperationTypeEnum::getName).orElse(null);
    }
}
