package com.mrcar.gov.common.dto.order.req;

import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @description: 订单创建人员信息入参（司机、乘车人）
 * @date 2024/11/20 10:58
 */
@Data
public class GovOrderUserInfoDTO {
    /**
     * 用户、司机编码
     */
    @NotEmpty(message = "用户编码不能为空")
    private String userCode;
    /**
     * 姓名
     */
    private String userName;
    /**
     * 电话
     */
    private String userMobile;
    /**
     * 所属单位名称
     */
    private String deptName;
    /**
     * 所属单位名称code
     */
    private String deptCode;
    /**
     * 所属部门名称
     */
    private String structName;
    /**
     * 所属部门名称code
     */
    private String structCode;
}
