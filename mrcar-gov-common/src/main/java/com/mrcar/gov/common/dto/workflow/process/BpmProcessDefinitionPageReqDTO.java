package com.mrcar.gov.common.dto.workflow.process;

import com.mrcar.gov.common.dto.workflow.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 管理后台 - 流程定义分页请求数据传输对象
 * 用于分页获取流程定义的列表。
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class BpmProcessDefinitionPageReqDTO extends PageParam {

    /**
     * 标识
     * 示例值: process1641042089407
     * 用于精准匹配流程定义。
     */
    private String key;

    /**
     * 所属企业 ID
     */
    private Integer companyId;

}
