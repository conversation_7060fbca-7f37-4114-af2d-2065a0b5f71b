package com.mrcar.gov.common.dto.device.resp;


import java.math.BigDecimal;
import java.util.Date;

import com.mrcar.gov.common.enums.device.GovGpsDeviceEnum;
import com.mrcar.gov.common.enums.device.LocationFlagEnum;
import com.mrcar.gov.common.enums.device.RapidAccelerationEnum;
import com.mrcar.gov.common.enums.device.RapidBrakeEnum;
import com.mrcar.gov.common.enums.device.RapidTurningEnum;

/**
 * @ClassName LocationsDetailDTO
 * @Description TODO
 * <AUTHOR>
 * @Date 2019/10/22 16:14
 * @Version 1.0
 */
public class LocationsDetailDTO {
    /** 经度高德  */
    private BigDecimal longitude;
    /** 纬度高德  */
    private BigDecimal latitude;
    /** 经度百度  */
    private BigDecimal lngBaidu;
    /** 纬度百度  */
    private BigDecimal latBaidu;

    /** 经度-WGS84  */
    private BigDecimal lngWgs84;
    /** 纬度-WGS84 */
    private BigDecimal latWgs84;

    /** 速度  */
    private BigDecimal speed = BigDecimal.ZERO;
    /** 方向角  */
    private Integer direction = 0;
    /** 方向角描述  */
    private String  directionMsg;
    /** 载噪比  */
    private Integer snr;
    /** 急刹⻋  */
    private Integer rapidBrake;
    private String rapidBrakeMsg;
    /** 急加速 */
    private Integer rapidAcceleration;
    private String rapidAccelerationMsg;
    /** 急转弯 */
    private Integer rapidTurning;
    private String rapidTurningMsg;

    private Date createDate;

    private String carNo;
    private String deviceId;
    private String vehicleVin;
    private String manfactCode;
    private String deviceType;
    private String deviceTypeMsg;
    private String locationName;

    private Boolean  stopPoint=false;
    private String  stopPointDesc;
    private Integer  stopDuration=0;
    private String   stopDurationDetail;


    private Integer locationFlag;
    private String  locationFlagName;


    /** mrcar 订单 start...............................**/
    private  String orderNo;
    private  String sourceId;
    private  String sourceType;


    /** mrcar 订单 end...............................**/



    public BigDecimal getLongitude() {
        return longitude;
    }

    public void setLongitude(BigDecimal longitude) {
        this.longitude = longitude;
    }

    public BigDecimal getLatitude() {
        return latitude;
    }

    public void setLatitude(BigDecimal latitude) {
        this.latitude = latitude;
    }

    public BigDecimal getLngBaidu() {
        return lngBaidu;
    }

    public void setLngBaidu(BigDecimal lngBaidu) {
        this.lngBaidu = lngBaidu;
    }

    public BigDecimal getLatBaidu() {
        return latBaidu;
    }

    public void setLatBaidu(BigDecimal latBaidu) {
        this.latBaidu = latBaidu;
    }

    public BigDecimal getLngWgs84() {
        return lngWgs84;
    }

    public void setLngWgs84(BigDecimal lngWgs84) {
        this.lngWgs84 = lngWgs84;
    }

    public BigDecimal getLatWgs84() {
        return latWgs84;
    }

    public void setLatWgs84(BigDecimal latWgs84) {
        this.latWgs84 = latWgs84;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public String getCarNo() {
        return carNo;
    }

    public void setCarNo(String carNo) {
        this.carNo = carNo;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getVehicleVin() {
        return vehicleVin;
    }

    public void setVehicleVin(String vehicleVin) {
        this.vehicleVin = vehicleVin;
    }

    public String getManfactCode() {
        return manfactCode;
    }

    public void setManfactCode(String manfactCode) {
        this.manfactCode = manfactCode;
    }

    public String getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(String deviceType) {
        this.deviceType = deviceType;
    }

    public BigDecimal getSpeed() {
        return speed;
    }

    public void setSpeed(BigDecimal speed) {
        this.speed = speed;
    }

    public Integer getDirection() {
        return direction;
    }

    public void setDirection(Integer direction) {
        this.direction = direction;
    }

    public String getDeviceTypeMsg() {
        return GovGpsDeviceEnum.DeviceTypeEnum.getMsg(this.deviceType==null?null:Integer.parseInt(this.deviceType));
    }

    public void setDeviceTypeMsg(String deviceTypeMsg) {
        this.deviceTypeMsg = deviceTypeMsg;
    }

    public String getDirectionMsg() {
        if(this.direction==null)
        {
            return "未知";
        }
        if(this.direction<0||this.direction>=360)
        {
            return "未知";
        }
        if(this.direction==0)
        {
            return "正北";
        }
        if(this.direction>0&&this.direction<90)
        {
            return "东北";
        }
        if(this.direction==90)
        {
            return "正东";
        }
        if(this.direction>90&&this.direction<180)
        {
            return "东南";
        }
        if(this.direction==180)
        {
            return "正南";
        }
        if(this.direction>180&&this.direction<270)
        {
            return "西南";
        }
        if(this.direction==270)
        {
            return "正西";
        }
        if(this.direction>270&&this.direction<360)
        {
            return "西北";
        }
        return "未知";
    }

    public void setDirectionMsg(String directionMsg) {
        this.directionMsg = directionMsg;
    }

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    public Integer getStopDuration() {
        return stopDuration;
    }

    public void setStopDuration(Integer stopDuration) {
        this.stopDuration = stopDuration;
    }

    public Boolean getStopPoint() {
        return stopPoint;
    }

    public void setStopPoint(Boolean stopPoint) {
        this.stopPoint = stopPoint;
    }

    public String getStopDurationDetail() {
        return getStopDurationMsgV2(this.stopDuration);
    }

    public void setStopDurationDetail(String stopDurationDetail) {
        this.stopDurationDetail = stopDurationDetail;
    }

    public static String getStopDurationMsgV2(Integer durations)
    {
        StringBuilder sb = new StringBuilder();
        int[] units = {86400,3600,60,1};
        String[] devided= {"天","小时","分","秒"};
        for (int i = 0; i < units.length; i++) {
            if(durations>=units[i])
            {
                sb.append(durations/(units[i]));
                sb.append(devided[i]);
                durations = durations%(units[i]);
            }
        }
        return sb.toString();
    }


    public Integer getRapidBrake() {
        return rapidBrake;
    }

    public void setRapidBrake(Integer rapidBrake) {
        this.rapidBrake = rapidBrake;
    }

    public Integer getRapidAcceleration() {
        return rapidAcceleration;
    }

    public void setRapidAcceleration(Integer rapidAcceleration) {
        this.rapidAcceleration = rapidAcceleration;
    }

    public Integer getRapidTurning() {
        return rapidTurning;
    }

    public void setRapidTurning(Integer rapidTurning) {
        this.rapidTurning = rapidTurning;
    }

    public String getRapidBrakeMsg() {
        return RapidBrakeEnum.getMsg(this.rapidBrake);
    }

    public void setRapidBrakeMsg(String rapidBrakeMsg) {
        this.rapidBrakeMsg = rapidBrakeMsg;
    }

    public String getRapidAccelerationMsg() {
        return RapidAccelerationEnum.getMsg(this.rapidAcceleration);
    }

    public void setRapidAccelerationMsg(String rapidAccelerationMsg) {
        this.rapidAccelerationMsg = rapidAccelerationMsg;
    }

    public String getRapidTurningMsg() {
        return RapidTurningEnum.getMsg(this.rapidTurning);
    }

    public void setRapidTurningMsg(String rapidTurningMsg) {
        this.rapidTurningMsg = rapidTurningMsg;
    }

    public Integer getSnr() {
        return snr;
    }

    public void setSnr(Integer snr) {
        this.snr = snr;
    }

    public Integer getLocationFlag() {
        return locationFlag;
    }

    public void setLocationFlag(Integer locationFlag) {
        this.locationFlag = locationFlag;
    }

    public String getLocationFlagName() {
        return LocationFlagEnum.getMsg(this.locationFlag);
    }

    public void setLocationFlagName(String locationFlagName) {
        this.locationFlagName = locationFlagName;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getSourceId() {
        return sourceId;
    }

    public void setSourceId(String sourceId) {
        this.sourceId = sourceId;
    }

    public String getSourceType() {
        return sourceType;
    }

    public void setSourceType(String sourceType) {
        this.sourceType = sourceType;
    }

	public String getStopPointDesc() {
		return stopPointDesc;
	}

	public void setStopPointDesc(String stopPointDesc) {
		this.stopPointDesc = stopPointDesc;
	}
    
    
}
