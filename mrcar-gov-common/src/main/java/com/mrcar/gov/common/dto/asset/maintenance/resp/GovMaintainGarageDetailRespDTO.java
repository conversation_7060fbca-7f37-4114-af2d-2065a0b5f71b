package com.mrcar.gov.common.dto.asset.maintenance.resp;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class GovMaintainGarageDetailRespDTO implements Serializable {

    /**
     * 维修厂id
     */
    private Integer id;

    /**
     * 维修厂编号
     */
    private String garageNo;

    /**
     * 维修厂名称
     */
    private String garageName;

    /**
     * 综合评分
     */
    private BigDecimal evaluationScore;

    /**
     * 评价总次数
     */
    private Integer evaluationNums;

    /**
     * 修理范围标签（多个英文逗号分隔）  维保,事故,油车,电车
     */
    private String supportRepairLabel;

    /**
     * 服务电话（多个英文逗号分隔）
     */
    private String serviceTelephone;

    /**
     * 详细地址
     */
    private String addressDetail;

    /**
     * 坐标经度
     */
    private BigDecimal longitude;

    /**
     * 坐标纬度
     */
    private BigDecimal latitude;

    /**
     * 高德坐标经度
     */
    private BigDecimal amapLongitude;

    /**
     * 高德坐标纬度
     */
    private BigDecimal amapLatitude;

    /**
     * 导航预计距离和时间展示
     */
    private String navigationHint;

    /**
     * 营业执照照片
     */
    private String businessLicensePhoto;

    /**
     * 门头照片（多张英文逗号分隔）
     */
    private String headPhoto;

    /**
     * 客户休息室照片（多张英文逗号分隔）
     */
    private String customerLoungePhoto;

    /**
     * 工区照片（多张英文逗号分隔）
     */
    private String workAreaPhoto;

    /**
     * 评价列表
     */
    private List<GovMaintainGarageEvaluationRespDTO> evaluationList;
}