package com.mrcar.gov.common.dto.thirdparty.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2019-11-12 22:07
 */
@Data
public class CarLicenseDTO {

    /**
     * 车牌号
     */
    @JSONField(name = "txt")
    private String licenseNum;

    /**
     * 车牌类型
     */
    @JSONField(name = "cls_name")
    private String className;

    /**
     * 车牌类型置信度
     */
    @JSONField(name = "cls_prob")
    private String classProb;

}
