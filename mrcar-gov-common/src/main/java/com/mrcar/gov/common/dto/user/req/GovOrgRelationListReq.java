package com.mrcar.gov.common.dto.user.req;

import com.mrcar.gov.common.dto.PageParamDTO;
import lombok.Data;

import java.util.List;

@Data
public class GovOrgRelationListReq extends PageParamDTO {

    /**
     * 服务机构名称
     */
    private String orgName;


    /**
     * 服务机构类型（1维保 2保险 3加油 4租赁）
     **/
    private Integer orgType;


    /**
     * 合作状态 1合作中 0已过期
     */
    private Integer cooperateStatus;


    /**
     * 单位code
     */
    private String deptCode;


    /**
     * 辅助字段 用于根据父节点转换子节点
     */
    private List<String> deptCodeList;


    /**
     * 企业id
     */
    private Integer companyId;

    /**
     * 负责人手机号
     */
    private String orgContactorPhone;
}
