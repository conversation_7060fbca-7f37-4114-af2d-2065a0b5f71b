package com.mrcar.gov.common.constant.order;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;


@Getter
@AllArgsConstructor
public enum GovPublicCarOrderUserTypeEnum {

    //订单人员类型（1-下单人，2-主乘车人，3-次乘车人，4-司机，5-订单核实人)
    ORDER_PLACER(1, "下单人"),
    PRIMARY_RIDER(2, "主乘车人"),
    SECONDARY_RIDER(3, "次乘车人"),
    DRIVER(4, "司机"),
    ORDER_VERIFIER(5, "订单核实人"),
    ORDER_START_OPERATOR(6, "订单开始操作人"),
    ORDER_END_OPERATOR(7, "订单结束操作人"),
    ORDER_CANCEL_OPERATOR(8, "订单取消操作人"),
    ORDER_OPEN_LOCK_OPERATOR(9, "订单开锁操作人"),
    ORDER_CLOSE_LOCK_OPERATOR(10, "订单关锁操作人"),
    ORDER_FORCE_END_OPERATOR(11, "订单强制结束操作人"),
    ORDER_MODIFY_OPERATOR(12, "订单修改操作人"),
    ORDER_VERIFY_OPERATOR(13, "订单核实操作人"),
    ORDER_APPROVAL_WITHDRAW_OPERATOR(14, "订单审批撤回操作人"),
    SCHEDULER(15, "调度员"),
    ORDER_FORCE_START_OPERATOR(16, "订单强制开始操作人"),
    ;

    private final Integer code;
    private final String name;

    public static String getName(Integer code) {
        return Arrays.stream(values()).filter(e -> e.getCode().equals(code)).findFirst().map(GovPublicCarOrderUserTypeEnum::getName).orElse(null);
    }
}
