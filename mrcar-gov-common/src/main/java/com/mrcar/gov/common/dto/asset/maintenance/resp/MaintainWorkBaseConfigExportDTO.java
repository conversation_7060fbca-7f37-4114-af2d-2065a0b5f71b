package com.mrcar.gov.common.dto.asset.maintenance.resp;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * Author:  wangM
 * Date:  2025/1/13 19:52
 */
@Data
public class MaintainWorkBaseConfigExportDTO {
    @ColumnWidth(20)
    @ExcelProperty(value = "工时基数编号")
    private String templateId;
    @ColumnWidth(20)
    @ExcelProperty(value = "维修名称")
    private String repairItem;
    @ColumnWidth(20)
    @ExcelProperty(value = "维修组")
    private String repairTypeName;
    @ColumnWidth(20)
    @ExcelProperty(value = "工时基数(元)")
    private BigDecimal repairLaborAmount;
    @ColumnWidth(20)
    @ExcelProperty(value = "是否常用")
    private String isFrequentlyName;
    @ColumnWidth(20)
    @ExcelProperty(value = "状态")
    private String stateName;
    @ColumnWidth(20)
    @ExcelProperty(value = "修改人")
    private String updaterName;
    @ColumnWidth(20)
    @ExcelProperty(value = "修改时间")
    private Date updateTime;
}
