package com.mrcar.gov.common.dto.business.request;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 消息模板表
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class GovMsgTemplateEditReqDTO extends GovMsgTemplateSaveReqDTO {


    /**
     * 模版ID
     */
    @NotNull(message = "模版ID不能为空")
    @Min(value = 1, message = "模版ID不能小于1")
    private Integer id;


}