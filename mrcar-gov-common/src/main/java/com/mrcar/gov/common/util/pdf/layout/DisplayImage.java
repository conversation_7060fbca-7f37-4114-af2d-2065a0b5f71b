package com.mrcar.gov.common.util.pdf.layout;

import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.graphics.image.PDImageXObject;

import java.io.File;
import java.io.IOException;

public class DisplayImage {

    private final PDImageXObject _image;

    private DisplayImage(PDImageXObject image) {
        _image = image;
    }

    public static DisplayImage create(File file, PDDocument document) {
        try {
            PDImageXObject object = PDImageXObject.createFromFileByExtension(file, document);
            return new DisplayImage(object);
        } catch (IOException e) {
            throw new IllegalArgumentException("Cannot open image file");
        }
    }

    public PDImageXObject image() {
        return this._image;
    }

}
