package com.mrcar.gov.common.constant;

public interface WebSocketConstants {

    String MESSAGE_KEY_COMMAND = "command";

    String MESSAGE_COMMAND_LOGIN = "login";

    String MESSAGE_COMMAND_REQUEST = "request";

    String MESSAGE_COMMAND_PING = "ping";

    String MESSAGE_COMMAND_PONG = "pong";

    String MESSAGE_COMMAND_NOTIFY = "notify";

    // 重复注册
    int ERROR_CODE_DUPLICATED_REGISTER = 1001;
    String ERROR_MSG_DUPLICATED_REGISTER = "重复登录";

    // 认证失败
    int ERROR_CODE_AUTHENTICATION_FAILURE = 1002;
    String ERROR_MSG_AUTHENTICATION_FAILURE = "认证失败";

}
