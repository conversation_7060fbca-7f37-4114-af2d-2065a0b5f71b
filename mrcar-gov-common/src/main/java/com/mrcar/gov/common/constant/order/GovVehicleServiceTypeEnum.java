package com.mrcar.gov.common.constant.order;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum GovVehicleServiceTypeEnum {

    // 车辆服务类型
    POINT_CAR(1, "定点车"),
    STAGE_CAR(2, "平台车"),
    ;

    private final Integer code;
    private final String name;

    public static String getName(Integer code) {
        return Arrays.stream(values()).filter(e -> e.getCode().equals(code)).findFirst().map(GovVehicleServiceTypeEnum::getName).orElse(null);
    }

    public static List<String> getDescList() {
        return Arrays.stream(values()).map(GovVehicleServiceTypeEnum::getName).collect(Collectors.toList());

    }

    public static Integer getCodeByDesc(String desc) {
        for (GovVehicleServiceTypeEnum govVehicleServiceTypeEnum : GovVehicleServiceTypeEnum.values()) {
            if (Objects.equals(govVehicleServiceTypeEnum.getName(), desc)) {
                return govVehicleServiceTypeEnum.getCode();
            }
        }
        return null;
    }


}