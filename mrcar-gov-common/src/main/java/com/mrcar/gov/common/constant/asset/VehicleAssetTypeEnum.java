package com.mrcar.gov.common.constant.asset;

import lombok.Getter;

@Getter
public enum VehicleAssetTypeEnum {
    ENERGY_TYPE(1, "按能源类型"),
    VEHICLE_TYPE(2, "按车型"),
    USAGE_NATURE(3, "按使用性质"),
    USAGE_YEARS(4, "按使用年限");

    private final int code;
    private final String description;

    VehicleAssetTypeEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据代码获取枚举实例。
     *
     * @param code 分类标准的代码。
     * @return 对应的枚举实例，如果未找到则返回 null。
     */
    public static VehicleAssetTypeEnum fromCode(int code) {
        for (VehicleAssetTypeEnum standard : values()) {
            if (standard.getCode() == code) {
                return standard;
            }
        }
        return null;
    }
}
