package com.mrcar.gov.common.constant.order;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;


@Getter
@AllArgsConstructor
public enum GovPublicCarOrderOperationTypeEnum {

    //订单人员类型（1-下单人，2-主乘车人，3-次乘车人，4-司机，5-订单核实人)
    CREATE(1, "创建行程"),
    CANCEL(2, "取消行程"),
    UPDATE(3, "订单调度"),
    START_TRIP(4, "开始行程"),
    END_TRIP(5, "结束行程"),
    FORCE_END_TRIP(6, "强制结束"),
    ORDER_SCHEDULE(7, "重新调度"),
//    CLICK_LOCK(8, "点击关锁"),
    CHECK_ORDER(9, "核实订单"),
    UPDATE_ORDER_TYPE(10, "变更订单类型"),
    APPROVAL_WITHDRAW(11, "审批撤回"),
    APPROVAL_PASS(12, "审批通过"),
    APPROVAL_REJECTED(13, "审批驳回"),
    OUT_FENCE(14, "驶出围栏"),
    IN_FENCE(15, "驶入围栏"),
    FORCE_START_TRIP(16, "强制开始"),
    AUTO_END_TRIP(17, "自动结束"),
    OPEN_LOCK(18, "开锁"),
    CLOSE_LOCK(19, "关锁"),

    ;
    private final Integer code;
    private final String name;

    public static String getName(Integer code) {
        return Arrays.stream(values()).filter(e -> e.getCode().equals(code)).findFirst().map(GovPublicCarOrderOperationTypeEnum::getName).orElse(null);
    }
}
