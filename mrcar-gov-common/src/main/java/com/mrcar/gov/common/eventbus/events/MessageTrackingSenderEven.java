package com.mrcar.gov.common.eventbus.events;

import com.mrcar.gov.common.eventbus.Event;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * Author:  wangM
 * Date:  2025/1/2 11:08
 * Desc: 消息事件
 */
@Data
public class MessageTrackingSenderEven implements Event {

    private List<MessageTrackingSenderEven.MessageTrackingSender> messageTrackingSenders;

    @Data
    @AllArgsConstructor
    public static class MessageTrackingSender{

        /**
         * 消息记录编码
         */
        private String recordNo;

        /**
         * 消息内容
         */
        private String content;

        /**
         * 人员编码信息
         */
        private Set<String> receiverCodeSet;

        private Set<String> mobileSet;
    }
}
