package com.mrcar.gov.common.dto.business.response;

import com.mrcar.gov.common.dto.FileDTO;
import com.mrcar.gov.common.dto.asset.request.VehicleApplyBtnDTO;
import com.mrcar.gov.common.dto.asset.response.VehicleInfoDTO;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 车辆 配备，更新处置调拨 响应DTO
 *
 * <AUTHOR>
 */
@Data
public class GovTransactionApplyRespDTO extends VehicleApplyBtnDTO {
    private static final long serialVersionUID = 1L;
    /**
     * 主键 申请id
     */
    private Integer id;

    /**
     * 业务申请编码
     */
    private String applyNo;

    /**
     * 申请类型 1 更新 2 配备 3 处理及调拨
     */
    private Integer applyType;

    /**
     * 申请类型值  更新  配备  处理及调拨
     */
    private String applyTypeName;

    /**
     * 申请状态
     */
    private Integer applyStatus;

    /**
     * 申请状态值
     */
    private String applyStatusName;

    /**
     * 申请时间
     */
    private Date applyDate;

    /**
     * 申请说明
     */
    private String applyRemark;

    /**
     * 申请单位编码
     */
    private String applyStructCode;

    /**
     * 申请单位名称
     */
    private String applyStructName;

    /**
     * 车辆所属单位编码
     */
    private String vehicleBelongDeptCode;

    /**
     * 车辆所属单位名称
     */
    private String vehicleBelongDeptName;

    /**
     * 审批id
     */
    private String approveId;

    /**
     * 所属公司id
     */
    private Integer companyId;

    /**
     * 创建人编码
     */
    private String createCode;

    /**
     * 创建人
     */
    private String createName;

    /**
     * 修改人
     */
    private String updateCode;

    /**
     * 修改人
     */
    private String updateName;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 车辆信息列表
     */
    private List<GovApplyVehicleRespDTO> govApplyVehicleDTOList;


    /**
     * 车辆数
     */
    private Integer vehicleNum;


    /**
     * 车辆牌照
     */
    private String vehicleLicense;

    /**
     * 车辆编码
     */
    private String vehicleNo;

    /**
     * 申请文件
     */
    private List<FileDTO> applyFileList;

    /**
     * 批复文件
     */
    private List<FileDTO> replyFileList;

    /**
     * 批复说明
     */
    private String replyRemark;

    @Data
    public static class GovApplyVehicleRespDTO {
        /**
         * 车辆拟购信息
         **/
        private GovVehiclePlanRespDTO govVehiclePlan;
        /**
         * 原车信息
         **/
        private VehicleInfoDTO govVehicleInfo;

        /**
         * 批复信息
         **/
        private GovApproveReplyRespDTO govApproveReply;

    }

}