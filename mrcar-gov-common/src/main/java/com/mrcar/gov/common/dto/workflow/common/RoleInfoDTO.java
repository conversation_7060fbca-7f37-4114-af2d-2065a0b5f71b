package com.mrcar.gov.common.dto.workflow.common;

import lombok.Data;
import java.util.Date;

/**
 * 角色列表 DTO
 *
 * 表示角色的基本信息，包括角色编码、名称、备注、状态等。
 *
 * @program mrcar-user-common
 * @description 角色列表DTO
 * <AUTHOR>
 * @create 2023-01-17 15:13
 **/
@Data
public class RoleInfoDTO {

    /** 角色ID **/
    private Integer roleId;

    /** 角色编码 **/
    private String roleCode;

    /** 角色名称 **/
    private String roleName;

    /** 备注 **/
    private String roleExplain;

    /** 状态 **/
    private Boolean valid;

    /** 角色类型 **/
    private Byte roleType;

    /** 角色类型名称 **/
    private String roleTypeStr;

    /** 所属平台 **/
    private Byte systemType;

    /** 所属平台名称 **/
    private String systemTypeStr;

    /**
     * 特殊角色标识
     * 角色标志;0-正常角色;1-系统管理员;2-首汽驾驶员
     **/
    private Byte roleMark;

    /** 所属公司编码 **/
    private String companyCode;

    /** 所属企业 **/
    private String companyName;

    /** 所属服务商编码 **/
    private String providerCode;

    /** 服务商名称 **/
    private String providerName;

    /** 创建人ID **/
    private Integer createId;

    /** 创建人 **/
    private String createName;

    /** 最后修改人ID **/
    private Integer updateId;

    /** 修改人 **/
    private String updateName;

    /** 创建时间 **/
    private Date createTime;

    /** 修改时间 **/
    private Date updateTime;

    /** 是否展示修改按钮 true：展示 false：隐藏 **/
    private Boolean isShowUpd;

    /** 是否展示启用按钮 true：展示 false：隐藏 **/
    private Boolean isShowOn;

    /** 是否展示停用按钮 true：展示 false：隐藏 **/
    private Boolean isShowOff;

    /** 是否展示选择成员按钮 true：展示 false：隐藏 **/
    private Boolean isShowSelect;

    /** 角色成员数量 **/
    private Integer countOfRoleStaff;
}
