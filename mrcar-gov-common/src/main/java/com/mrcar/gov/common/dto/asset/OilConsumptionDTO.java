package com.mrcar.gov.common.dto.asset;

import com.mrcar.gov.common.constant.asset.OilConsumptionEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Data
@ApiModel("油耗分析")
public class OilConsumptionDTO {
    private Integer id;
    /**
     * 本次加油记录Id
     */
    @ApiModelProperty(value = "本次加油记录Id")
    private Integer carRefuelingId;
    /**
     * 本次加油记录编码
     */
    @ApiModelProperty(value = "本次加油记录编码")
    private String refuelingCode;
    /**
     * 上次加油记录编码
     */
    @ApiModelProperty(value = "上次加油记录编码")
    private String lastRefuelingCode;
    /**
     * 上次加油时间
     */
    @ApiModelProperty(value = "上次加油时间")
    private Date lastRefuelingTime;
    /**
     * 上次加油升数
     */
    @ApiModelProperty(value = "上次加油升数")
    private BigDecimal lastAddValue;
    /**
     * 上次仪表盘里程
     */
    @ApiModelProperty(value = "上次仪表盘里程")
    private BigDecimal lastDashboardMileage;

    /**
     * 上次加油金额（元）
     */
    @ApiModelProperty(value = "上次加油金额（元）")
    private BigDecimal lastAddFee;
    /**
     * 距上次加油间隔天数
     */
    @ApiModelProperty(value = "距上次加油间隔天数")
    private BigDecimal intervalDays;
    /**
     * 累计油耗
     */
    @ApiModelProperty(value = "累计油耗")
    private BigDecimal oilConsumption;
    /**
     * 行驶里程-仪表盘
     */
    @ApiModelProperty(value = "行驶里程-仪表盘")
    private BigDecimal dashboardMileage;
    /**
     * 行驶里程-gps
     */
    @ApiModelProperty(value = "行驶里程-gps")
    private BigDecimal gpsMileage;
    /**
     * 百公里油耗-仪表盘
     */
    @ApiModelProperty(value = "百公里油耗-仪表盘")
    private BigDecimal dashboardOilConsumption;
    /**
     * 百公里油耗
     */
    @ApiModelProperty(value = "百公里油耗-gps")
    private BigDecimal gpsOilConsumption;
    /**
     * 异常提示1油耗过高（仪表盘）2油耗过高（GPS）3油耗过低（仪表盘）4油耗过低（GPS）5补录工单 6、首次加油不支持分析 7、超60天加油不支持分析
     */
    @ApiModelProperty(value = "异常提示1油耗过高（仪表盘）2油耗过高（GPS）3油耗过低（仪表盘）4油耗过低（GPS）5补录工单")
    private String warnType;
    /**
     * 异常提示1油耗过高（仪表盘）2油耗过高（GPS）3油耗过低（仪表盘）4油耗过低（GPS）5补录工单 6、首次加油不支持分析 7、超60天加油不支持分析
     */
    @ApiModelProperty(value = "异常提示1油耗过高（仪表盘）2油耗过高（GPS）3油耗过低（仪表盘）4油耗过低（GPS）5补录工单")
    private List<String> warnTypeValue;
    /**
     * 异常提示 字符串描述
     */
    private String warnTypeValueStr;

    @ApiModelProperty(value = "状态1有效")
    private Byte status;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    public void setWarnType(String warnType) {
        this.warnType = warnType;
        if (StringUtils.isNotEmpty(warnType)) {
            this.warnTypeValue = Arrays.stream(warnType.split(",")).map(e -> OilConsumptionEnum.WarnTypeEnum.getValueByCode(Byte.valueOf(e))).collect(Collectors.toList());
            this.warnTypeValueStr = this.warnTypeValue.stream().collect(Collectors.joining(","));

        }
    }
}