package com.mrcar.gov.common.util.gps;


import java.math.BigDecimal;

/**
 * 1.WGS－84原始坐标系，一般用国际GPS纪录仪记录下来的经纬度，通过GPS定位拿到的原始经纬度，
 * Google和高德地图定位的的经纬度（国外）都是基于WGS－84坐标系的；但是在国内是不允许直 接用WGS84坐标系标注的，必须经过加密后才能使用
 * 
 * 2.GCJ－02坐标系，又名“火星坐标系”，是我国国测局独创的坐标体系，由WGS－84加密而成，在
 * 国内，必须至少使用GCJ－02坐标系，或者使用在GCJ－02加密后再进行加密的坐标系，如百度
 * 坐标系。高德和Google在国内都是使用GCJ－02坐标系，可以说，GCJ－02是国内最广泛使用的 坐标系
 * 
 * 3.百度坐标系:bd-09，百度坐标系是在GCJ－02坐标系的基础上再次加密偏移后形成的坐标系，
 * 只适用于百度地图。(目前百度API提供了从其它坐标系转换为百度坐标系的API，但却没有从百度 坐标系转为其他坐标系的API)
 * 
 * <AUTHOR>
 *
 */
public class GPSUtil {

	private static double x_pi = 3.14159265358979324 * 3000.0 / 180.0;
	
	private static double pi = 3.14159265358979324;

	static double a = 6378245.0;
	
	private static final String GD_KEY = "d6316a1a97308a4d51ab10c62211224b";

	/**
	 * 对double类型数据保留小数点后多少位 高德地图转码返回的就是 小数点后6位，为了统一封装一下
	 * 
	 * @param in
	 *            输入
	 * @return 保留小数位后的数
	 */
	private static double dataDigit(double in) {
		return BigDecimal.valueOf(in).setScale(7, BigDecimal.ROUND_HALF_UP).doubleValue();
	}

	/**
	 * 将火星坐标转变成百度坐标
	 * 
	 * @param lngLatGd
	 *            火星坐标（高德、腾讯地图坐标等）
	 * @return 百度坐标
	 */

	private static double[] bdEncrypt(double[] lngLatGd) {
		double x = lngLatGd[0], y = lngLatGd[1];
		double z = Math.sqrt(x * x + y * y) + 0.00002 * Math.sin(y * x_pi);
		double theta = Math.atan2(y, x) + 0.000003 * Math.cos(x * x_pi);
		return new double[] { dataDigit(z * Math.cos(theta) + 0.0065), dataDigit(z * Math.sin(theta) + 0.006) };

	}

	/**
	 * 将百度坐标转变成火星坐标
	 * 
	 * @param lngLatBd
	 *            百度坐标（百度地图坐标）
	 * @return 火星坐标(高德、腾讯地图等)
	 */
	public static double[] bdDecrypt(double[] lngLatBd) {
		double x = lngLatBd[0] - 0.0065, y = lngLatBd[1] - 0.006;
		double z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * x_pi);
		double theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * x_pi);
		return new double[] { dataDigit(z * Math.cos(theta)), dataDigit(z * Math.sin(theta)) };
	}
	
	public static double[] wgs2bd(double lat, double lon) {
	    double[] wgs2gcj = wgs2gcj(lat, lon);
	    double[] gcj2bd = gcj2bd(wgs2gcj[0], wgs2gcj[1]);
	    return gcj2bd;
	}
	
	private static double[] gcj2bd(double lat, double lon) {
       double x = lon, y = lat;
       double z = Math.sqrt(x * x + y * y) + 0.00002 * Math.sin(y * x_pi);
       double theta = Math.atan2(y, x) + 0.000003 * Math.cos(x * x_pi);
       double bdLon = z * Math.cos(theta) + 0.0065;
       double bdLat = z * Math.sin(theta) + 0.006;
       return new double[] { bdLat, bdLon };
	}
	
	public static double[] wgs2gcj(double lat, double lon) {
       double dLat = transformLat(lon - 105.0, lat - 35.0);
       double dLon = transformLon(lon - 105.0, lat - 35.0);
       double radLat = lat / 180.0 * pi;
       double magic = Math.sin(radLat);
		double ee = 0.00669342162296594323;
		magic = 1 - ee * magic * magic;
       double sqrtMagic = Math.sqrt(magic);
       dLat = (dLat * 180.0) / ((a * (1 - ee)) / (magic * sqrtMagic) * pi);
       dLon = (dLon * 180.0) / (a / sqrtMagic * Math.cos(radLat) * pi);
       double mgLat = lat + dLat;
       double mgLon = lon + dLon;
		return new double[]{ mgLat, mgLon };
	}
	
	private static double transformLat(double lat, double lon) {
       double ret = -100.0 + 2.0 * lat + 3.0 * lon + 0.2 * lon * lon + 0.1 * lat * lon + 0.2 * Math.sqrt(Math.abs(lat));
       ret += (20.0 * Math.sin(6.0 * lat * pi) + 20.0 * Math.sin(2.0 * lat * pi)) * 2.0 / 3.0;
       ret += (20.0 * Math.sin(lon * pi) + 40.0 * Math.sin(lon / 3.0 * pi)) * 2.0 / 3.0;
       ret += (160.0 * Math.sin(lon / 12.0 * pi) + 320 * Math.sin(lon * pi  / 30.0)) * 2.0 / 3.0;
       return ret;
	}
	
	private static double transformLon(double lat, double lon) {
       double ret = 300.0 + lat + 2.0 * lon + 0.1 * lat * lat + 0.1 * lat * lon + 0.1 * Math.sqrt(Math.abs(lat));
       ret += (20.0 * Math.sin(6.0 * lat * pi) + 20.0 * Math.sin(2.0 * lat * pi)) * 2.0 / 3.0;
       ret += (20.0 * Math.sin(lat * pi) + 40.0 * Math.sin(lat / 3.0 * pi)) * 2.0 / 3.0;
       ret += (150.0 * Math.sin(lat / 12.0 * pi) + 300.0 * Math.sin(lat / 30.0 * pi)) * 2.0 / 3.0;
       return ret;
	}
	
	



}