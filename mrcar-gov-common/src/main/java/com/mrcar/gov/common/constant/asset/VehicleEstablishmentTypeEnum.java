package com.mrcar.gov.common.constant.asset;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/6/6 14:07
 */
@AllArgsConstructor
@Getter
public enum VehicleEstablishmentTypeEnum {

    ESTABLISHMENT_SUMMARY("establishmentSummary", "合计编制数"),
    OLD_AGE_VEHICLE("oldAgeVehicleNum", "老干部服务用车编制"),
    PHYSICAL_VEHICLE("physicalVehicleNum", "实物保障用车编制"),
    COMMUNICATION_VEHICLE("communicationVehicleNum", "机要通信用车编制"),
    EMERGENCY_VEHICLE("emergencyVehicleNum", "应急保障用车编制"),
    RECEPTION_VEHICLE("receptionVehicleNum", "接待用车编制"),
    REGULATION_VEHICLE("regulationVehicleNum", "行政执法用车编制"),
    SPECIAL_VEHICLE("specialVehicleNum", "特种专业技术用车编制"),
    RESEARCH_VEHICLE("researchVehicleNum", "调研用车编制"),
    POLICE_VEHICLE("policeVehicleNum", "执法执勤用车编制"),
    BUSINESS_VEHICLE("businessVehicleNum", "业务用车编制");

    private final String code;
    private final String desc;

    public static String getDescByCode(String code) {
        for (VehicleEstablishmentTypeEnum item : VehicleEstablishmentTypeEnum.values()) {
            if (item.getCode().equals(code)) {
                return item.getDesc();
            }
        }
        return "";
    }
}
