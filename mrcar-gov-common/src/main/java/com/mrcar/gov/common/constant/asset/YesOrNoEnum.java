package com.mrcar.gov.common.constant.asset;

import lombok.Getter;

/**
 * 是否标识
 * <AUTHOR>
 * @date 2024/11/25 19:19
 */
@Getter
public enum YesOrNoEnum {
    //1 是 0 否 2 未定义
    YES(1,"是"),
    NO(0,"否"),
    UNDEFINED(2,"");
    private final Integer code;
    private final String desc;

    YesOrNoEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    public static String getDesc(Integer code){
        for (YesOrNoEnum value : YesOrNoEnum.values()) {
            if(value.getCode().equals(code)){
                return value.getDesc();
            }
        }
        return "";
    }
}
