package com.mrcar.gov.common.dto.workflow;

import com.mrcar.gov.common.dto.DicKeyValueDTO;
import lombok.Data;
import java.util.List;

/**
 * 业务流程分类信息数据传输对象
 *
 * 该类用于表示业务流程的分类信息，包括分类标识、分类名称，以及分类下的业务类型枚举列表。
 *
 * <AUTHOR>
 */
@Data
public class BpmCategoryInfoDTO {

    /**
     * 分类标识
     * 例如：1 表示业务分类，2 表示 OA 分类
     */
    private String category;

    /**
     * 分类名称
     * 例如：1 表示业务分类，2 表示 OA 分类
     */
    private String categoryName;

    /**
     * 分类下的业务类型枚举列表
     */
    private List<DicKeyValueDTO> dicList;
}
