package com.mrcar.gov.common.constant.asset;

import com.google.common.collect.Lists;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/11/11 17:48
 */
public enum VehicleStatusEnum {
    //状态码:1:可使用;2:整备中;3:维修中;4:待处置;5已处置;6:已退出(购买服务已结束); 7待调拨
    USABLE(1,"可使用"),
    DISPOSAL(4,"待处置"),
    EXIST_DISPOSAL(5,"已处置"),
    EXIT(6,"已退出(购买服务已结束)"),
    TRANSFER(7,"待调拨"),
    ;

    private final int code;
    private final String desc;

    VehicleStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
    public static String getDesc(int code) {
        for (VehicleStatusEnum vehicleStatusEnum : VehicleStatusEnum.values()) {
            if (vehicleStatusEnum.getCode() == code) {
                return vehicleStatusEnum.getDesc();
            }
        }
        return "";
    }

    // 使用list 返回所有描述信息
    public static List<String> getDescList() {
        return Lists.newArrayList(USABLE.desc, EXIT.desc);
    }

    // 根据描述获取code
    public static Integer getCodeByDesc(String desc) {
        for (VehicleStatusEnum vehicleStatusEnum : VehicleStatusEnum.values()) {
            if (Objects.equals(vehicleStatusEnum.getDesc(), desc)) {
                return vehicleStatusEnum.getCode();
            }
        }
        return null;
    }




}
