package com.mrcar.gov.common.constant.asset;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 保险状态枚举
 * @date 2024/11/20
 */
public enum InsuranceStatusEnum {

    UNINSURED(0,"未投保"),
    UNASSURED(1,"未起保"),
    ENSUREING(2,"保障中"),
    EXPIRE(3,"已过期");

    private final int code;
    private final String desc;

    InsuranceStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
    public static String getDesc(int code) {
        for (InsuranceStatusEnum vehicleStatusEnum : InsuranceStatusEnum.values()) {
            if (vehicleStatusEnum.getCode() == code) {
                return vehicleStatusEnum.getDesc();
            }
        }
        return "";
    }

    // 使用list 返回所有描述信息
    public static List<String> getDescList() {
        return Arrays.stream(InsuranceStatusEnum.values()).map(InsuranceStatusEnum::getDesc).collect(Collectors.toList());
    }

    // 根据描述获取code
    public static Integer getCodeByDesc(String desc) {
        for (InsuranceStatusEnum vehicleStatusEnum : InsuranceStatusEnum.values()) {
            if (Objects.equals(vehicleStatusEnum.getDesc(), desc)) {
                return vehicleStatusEnum.getCode();
            }
        }
        return null;
    }




}
