package com.mrcar.gov.common.dto.workflow.form;

import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 表单信息请求 DTO
 */
@Data
public class AppFormInfoReqDTO {

    /**
     * 表单编号
     */
    @NotBlank(message = "表单编号不能为空")
    private String businessNo;

    /**
     * 表单业务类型
     */
    @NotNull(message = "表单业务类型不能为空")
    private Byte businessType;
}
