package com.mrcar.gov.common.dto.business.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 处置拟购车信息
 *
 * <AUTHOR>
 * @date 2024/11/22 9:03
 */
@Data
public class VehicleDisposalPlanReqDTO {
    /**
     * 车辆编码
     */
    @NotBlank(message = "车辆编码不能为空")
    private String vehicleNo;

    /**
     * 拟处置方式 1报废 2拍卖 3调拨 4损失核销 5厂家回收 6 对外捐赠
     */
    @NotNull(message = "拟处置方式不能为空")
    private Integer disposalMethod;

    /**
     * 拟调入单位编码
     */
    private String transferInStructCode;

    /**
     * 拟调入单位名称
     */
    private String transferInStructName;

}
