package com.mrcar.gov.common.dto.thirdparty.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020-03-11 10:53
 */
@Data
public class BankCardDTO {

    /**
     * 银行卡号
     */
    @JSONField(name = "card_num")
    private String cardNum;

    /**
     * 信用卡有效期
     */
    @JSONField(name = "valid_date")
    private String validDate;

    /**
     * 开户行名称
     */
    @JSONField(name = "bank_name")
    private String bankName;

    /**
     * 响应是否成功
     */
    private boolean success;
}
