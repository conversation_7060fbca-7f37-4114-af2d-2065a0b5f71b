package com.mrcar.gov.common.constant.user;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/11/7 16:15
 */
@AllArgsConstructor
@Getter
public enum GovDataPermTypeEnum {
    // 本企业
    COMPANY(1, "全部"),
    // 员工数据权限指定部门
    ASSIGN_STRUCT(2, "指定部门"),
    // 本人
    SELF(3, "本人"),
    ;
    private Integer code;

    private String desc;

    //校验数据类型是否有效
    public static boolean checkCode(Integer code) {
        for (GovDataPermTypeEnum item : GovDataPermTypeEnum.values()) {
            if (Objects.equals(code, item.getCode())) {
                return true;
            }
        }
        return false;
    }


    // 根据code获取枚举
    public static String getDescByCode(Integer code) {
        for (GovDataPermTypeEnum item : GovDataPermTypeEnum.values()) {
            if (Objects.equals(code, item.getCode())) {
                return item.getDesc();
            }
        }
        return "";
    }

    // 根据编码获取枚举
    public static GovDataPermTypeEnum getByCode(Integer code) {
        for (GovDataPermTypeEnum item : GovDataPermTypeEnum.values()) {
            if (Objects.equals(code, item.getCode())) {
                return item;
            }
        }
        return null;
    }

    // 根据描述获取对应枚举
    public static GovDataPermTypeEnum getByDesc(String desc) {
        for (GovDataPermTypeEnum item : GovDataPermTypeEnum.values()) {
            if (Objects.equals(desc, item.getDesc())) {
                return item;
            }
        }
        return null;
    }

    public static List<String> getDescList() {
        List<String> descList = new ArrayList<>(GovDataPermTypeEnum.values().length);
        for (GovDataPermTypeEnum enu : GovDataPermTypeEnum.values()) {
            descList.add(enu.getDesc());
        }
        return descList;
    }


}

