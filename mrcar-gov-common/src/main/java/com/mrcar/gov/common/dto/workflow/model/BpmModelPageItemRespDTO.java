package com.mrcar.gov.common.dto.workflow.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;

/**
 * 管理后台 - 流程模型的分页的每一项 Response VO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BpmModelPageItemRespDTO extends BpmModelBaseDTO {

    /**
     * 编号
     * 示例值: 1024
     */
    private String id;

    /**
     * 表单名字
     * 示例值: 请假表单
     */
    private String formName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 关联子流程名称
     * 示例值: 中铁建审批
     */
    private String subProcessStr;

    /**
     * 关联父流程名称
     * 示例值: 中铁建审批
     */
    private String parentProcessStr;

    /**
     * 关联父流程 ID
     * 示例值: 31232131231
     */
    private String parentProcessId;

    /**
     * 是否关联父流程
     * 示例值: true
     */
    private int connectSwitch;

    /**
     * 所属企业 ID
     * 示例值: 用车
     */
    private Integer companyId;

    /**
     * 最新部署的流程定义
     */
    private ProcessDefinition processDefinition;



    /**
     * 部门单位名称
     */
    private String structName;

    /**
     * 配置类型
     */
    private String configTypeName;

    /**
     * 配置类型 1.本单位配置， 2.继承配置
     */
    private Integer configType;


    /**
     * 流程定义
     */
    @Data
    public static class ProcessDefinition {

        /**
         * 编号
         * 示例值: 1024
         */
        private String id;

        /**
         * 版本
         * 示例值: 1
         */
        private Integer version;

        /**
         * 部署时间
         */
        private Date deploymentTime;

        /**
         * 中断状态
         * 示例值: 1
         * 参见 SuspensionState 枚举
         */
        private Integer suspensionState;

        /**
         * 批量审批
         */
        private boolean batchApproval;

    }

    public void setConfigType(Integer configType) {
        this.configType = configType;
        if (configType != null) {
            this.configTypeName = configType == 1 ? "本单位配置" : "继承配置";
        }
    }


}
