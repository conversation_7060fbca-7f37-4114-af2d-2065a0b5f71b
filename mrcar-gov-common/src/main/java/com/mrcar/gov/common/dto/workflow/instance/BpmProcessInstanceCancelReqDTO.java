package com.mrcar.gov.common.dto.workflow.instance;

import com.mrcar.gov.common.dto.workflow.BaseDTO;
import lombok.Data;
import javax.validation.constraints.NotEmpty;

/**
 * 流程实例的取消请求 VO
 */
@Data
public class BpmProcessInstanceCancelReqDTO extends BaseDTO {

    /**
     * 流程实例的编号
     * 必须提供，不能为空
     */
    @NotEmpty(message = "流程实例的编号不能为空")
    private String id;

    /**
     * 取消原因
     * 必须提供，不能为空
     */
    @NotEmpty(message = "取消原因不能为空")
    private String reason;

    /**
     * 是否业务口取消
     */
    private Boolean isBusiness;

}
