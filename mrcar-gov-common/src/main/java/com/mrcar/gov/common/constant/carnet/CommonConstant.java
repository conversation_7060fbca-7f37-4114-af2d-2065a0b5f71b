package com.mrcar.gov.common.constant.carnet;

public class CommonConstant {

    public static final int manfact_stop = 2;

    public static final int model_stopt = 2;

    public static final int position_only = 1;

    public static final int position_and_car = 2;

    public static final int success_code = 0;

    public static final int install_check = 1;

    public static final int uninstall_check = 2;

    // 标识位
    public static final int pkg_delimiter = 0x7e;

    // 设置终端参数
    public static final int cmd_terminal_param_settings = 0X8103;

    /**
     * 终端心跳
     */
    public static final int HEART_BEAT_808 = 0X0002;

    public static final String PARAM_TAG = "param_tag_";

    public static final String PARAM_PREX = "param_";

    /**
     * 有线设备
     */
    public static final int WIRED = 1;

    /**
     * 无线设备
     */
    public static final int WIFI = 2;

    /**
     * obd设备
     */
    public static final int OBD = 3;

    /**
     * 车机设备
     */
    public static final int TBOX = 4;


    /**
     * MrCar App端上报数据
     */
    public static final int MRCAR = 5;


    /**
     * 未安装
     */
    public static final int DEVICE_NOTINSTALL = 1;

    /**
     * 已安装
     */
    public static final int DEVICE_INSTALL = 2;

    /**
     * 报废
     */
    public static final int DEVICE_DELETE = 4;

    /**
     * 正常状态
     */
    public static final int NORMAL_STATUS = 1;

    /**
     * 删除状态
     */
    public static final int DELETE_STATUS = 4;

    /**
     * 厂商停用
     */
    public static final int STOP_STATUS = 2;

    /**
     * 中间表未绑定
     */
    public static final int NOT_BIND = 2;

    /**
     * 已切换地址
     */
    public static final String MOVED = "MOVED";


    /**
     * 设备操下发作类型
     */
    public static final int GATE_WAY = 1;



    //停车点计算时间阈值  //3分钟，180秒的时间阈值
    public static final int LBS_STOP_DURTAION = 180;

    //停车点计算速度阈值 速度阈值 km/h
    public static final int LBS_STOP_SPEED = 5;

}
