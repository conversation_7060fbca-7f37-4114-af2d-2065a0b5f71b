package com.mrcar.gov.common.constant.business;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/12/30 21:22
 */
@Getter
@AllArgsConstructor
public enum GovMsgModuleEnum {

    GOVERNMENT_NOTICE(1, "公文公告", GovMsgTypeEnum.NOTICE, "https://sqzl-img.oss-cn-beijing.aliyuncs.com/online/mrcar/gov/6774c8f994c05beef2444aeb.png"),

    SYSTEM_MESSAGE(2, "系统消息", GovMsgTypeEnum.NOTICE, "https://sqzl-img.oss-cn-beijing.aliyuncs.com/online/mrcar/gov/6774ca6094c05beef2444aec.png"),

    VEHICLE_NOTICE(3, "用车通知", GovMsgTypeEnum.TEMPLATE_MSG, "https://sqzl-img.oss-cn-beijing.aliyuncs.com/online/mrcar/gov/6774ca7694c05beef2444aed.png"),

    APPROVAL_NOTICE(4, "审批通知", GovMsgTypeEnum.TEMPLATE_MSG, "https://sqzl-img.oss-cn-beijing.aliyuncs.com/online/mrcar/gov/6774ca8c94c05beef2444aee.png"),

    MONITOR_ALARM(5, "监控报警", GovMsgTypeEnum.TEMPLATE_MSG, "https://sqzl-img.oss-cn-beijing.aliyuncs.com/online/mrcar/gov/6774ca9f94c05beef2444aef.png"),

    VEHICLE_SERVICE(6, "车务消息", GovMsgTypeEnum.TEMPLATE_MSG, "https://sqzl-img.oss-cn-beijing.aliyuncs.com/online/mrcar/gov/6774cab794c05beef2444af0.png"),

    MAINTENANCE_NOTICE(7, "维保通知", GovMsgTypeEnum.TEMPLATE_MSG, "https://sqzl-img.oss-cn-beijing.aliyuncs.com/online/mrcar/gov/6774cac694c05beef2444af1.png");


    private final Integer code;

    private final String desc;

    //所属的消息类型
    private final GovMsgTypeEnum msgTypeEnum;

    //模块消息的图标
    private final String icon;


    //获取消息类型下面的枚举列表
    public static List<GovMsgModuleEnum> getMsgModuleEnumByMsgType(Integer msgTypeEnumType) {


        return Arrays.stream(GovMsgModuleEnum.values())
                .filter(value -> Objects.equals(value.msgTypeEnum.getCode(), msgTypeEnumType))
                .collect(Collectors.toList());

    }

    //通过code获取desc
    public static String getDescByCode(Integer code) {
        for (GovMsgModuleEnum value : GovMsgModuleEnum.values()) {
            if (Objects.equals(value.getCode(), code)) {
                return value.getDesc();
            }
        }
        return StringUtils.EMPTY;
    }

    //通过code获取图标
    public static String getIconByCode(Integer code) {
        for (GovMsgModuleEnum value : GovMsgModuleEnum.values()) {
            if (Objects.equals(value.getCode(), code)) {
                return value.getIcon();
            }
        }
        return StringUtils.EMPTY;
    }

    //根据code获取所属消息类型
    public static GovMsgTypeEnum getMsgTypeEnumByCode(Integer code) {
        for (GovMsgModuleEnum value : GovMsgModuleEnum.values()) {
            if (Objects.equals(value.getCode(), code)) {
                return value.getMsgTypeEnum();
            }
        }
        return null;
    }

    public static GovMsgModuleEnum getByCode(Integer msgModule) {
        for (GovMsgModuleEnum value : GovMsgModuleEnum.values()) {
            if (Objects.equals(value.getCode(), msgModule)) {
                return value;
            }
        }
        return null;

    }
}
