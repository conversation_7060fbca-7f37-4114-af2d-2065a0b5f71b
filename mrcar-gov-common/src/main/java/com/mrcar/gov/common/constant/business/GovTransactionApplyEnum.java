package com.mrcar.gov.common.constant.business;

import com.mrcar.gov.common.constant.asset.DisposalTypeEnum;

/**
 * <AUTHOR>
 * @description: 车辆 配备，更新处置调拨
 */
public class GovTransactionApplyEnum {
    /**
     * 申请状态枚举
     */
    public enum ApplyStatusEnum {
        PENDING_SUBMISSION(1, "待提交"),
        APPROVAL_IN_PROGRESS(2, "审批中"),
        AWAITING_APPROVAL_FEEDBACK(3, "待上传批复"),
        APPROVAL_REJECTED(4, "审批退回"),
        COMPLETED(5, "已完成"),
        VOIDED(6, "作废");
        private final int code;
        private final String name;

        ApplyStatusEnum(int code, String name) {
            this.code = code;
            this.name = name;
        }

        public int getCode() {
            return code;
        }

        public String getName() {
            return name;
        }


        public static String getNameByCode(int code) {
            for (ApplyStatusEnum item : ApplyStatusEnum.values()) {
                if (item.getCode() == code) {
                    return item.getName();
                }
            }
            return "";
        }

    }


    /**
     * 申请类型枚举
     */
    public enum ApplyTypeEnum {
        UPDATE(1, "更新"),
        CONFIGURE(2, "配备"),
        DISPOSE(3, "处置"),
        ALLOCATE(4, "调拨"),
        //DISPOSE_AND_ALLOCATE(99, "处置及调拨");

        ;

        private final int code;
        private final String name;

        ApplyTypeEnum(int code, String name) {
            this.code = code;
            this.name = name;
        }

        public int getCode() {
            return code;
        }

        public String getName() {
            return name;
        }


        public static String getNameByCode(int code) {
            for (ApplyTypeEnum item : ApplyTypeEnum.values()) {
                if (item.getCode() == code) {
                    return item.getName();
                }
            }
            return "";
        }

    }

    /**
     * 处置方式枚举     // 1.报废、2.拍卖、3.调拨、4.损失核销、5.厂家回收、6.对外捐赠
     */
    public enum DisposalMethodEnum {
        SCRAP(1, "报废", DisposalTypeEnum.DISPOSAL.getCode()),
        AUCTION(2, "拍卖",DisposalTypeEnum.DISPOSAL.getCode()),
        ALLOCATION(3, "调拨",DisposalTypeEnum.ALLOCATION.getCode()),
        LOSS_WRITE_OFF(4, "损失核销",DisposalTypeEnum.DISPOSAL.getCode()),
        MANUFACTURER_RECALL(5, "厂家回收",DisposalTypeEnum.DISPOSAL.getCode()),
        DONATION(6, "对外捐赠",DisposalTypeEnum.DISPOSAL.getCode());

        private final int code;
        private final String name;
        /**
         * 类型 属于处置 还是调拨
         */
        private final int type;


        DisposalMethodEnum(int code, String name, int type) {
            this.code = code;
            this.name = name;
            this.type = type;
        }

        public int getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public int getType() {
            return type;
        }


        public static String getNameByCode(int code) {
            for (DisposalMethodEnum item : DisposalMethodEnum.values()) {
                if (item.getCode() == code) {
                    return item.getName();
                }
            }
            return "";
        }

        public static int getTypeByCode(int code) {
            for (DisposalMethodEnum item : DisposalMethodEnum.values()) {
                if (item.getCode() == code) {
                    return item.getType();
                }
            }
            return 0;
        }


    }


}
