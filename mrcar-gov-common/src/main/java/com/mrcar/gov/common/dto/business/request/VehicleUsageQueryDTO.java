package com.mrcar.gov.common.dto.business.request;

import com.mrcar.gov.common.dto.PageParamDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class VehicleUsageQueryDTO extends PageParamDTO {

    /**
     * 车牌号
     */
    private String vehicleLicense;

    /**
     * 车架号
     */
    private String vehicleVin;

    /**
     * 统计开始日期
     */
    private String statisticsDateStart;

    /**
     * 统计结束日期
     */
    private String statisticsDateEnd;

    /**
     * 车辆所有人
     */
    private String vehicleBelongDeptCode;

    /**
     * 车辆使用人-单位
     */
    private String vehicleUseDeptCode;

    /**
     * 车辆使用单位-内部部门
     */
    private String vehicleUseStructCode;

    /**
     * 车辆管理部门
     */
    private String vehicleManageDeptCode;

    /**
     * 父级车辆所属编码
     */
    private String parentVehicleBelongDeptCode;

    /**
     * 管车类型
     */
    private Integer manageCarType;

}
