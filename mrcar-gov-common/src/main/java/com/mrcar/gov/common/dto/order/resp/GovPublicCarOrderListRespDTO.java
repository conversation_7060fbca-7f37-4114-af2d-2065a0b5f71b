package com.mrcar.gov.common.dto.order.resp;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


@Data
public class GovPublicCarOrderListRespDTO extends GovPublicOrderButtonRespDTO{

    /**
     * 申请单号
     */
    private String applyNo;

    /**
     * 行程单号
     */
    private String orderNo;

    /**
     * 行程类型：1-办公用车，2-紧急用车
     */
    private Integer orderType;

    /**
     * 申请单类型 1:公务用车 2:社会租赁
     */
    private Integer applyType;

    /**
     * 行程类型：1-办公用车，2-无任务用车
     */
    private String orderTypeStr;

    /**
     * 审批状态 1:待审核 2:审核通过 3:无需审核 4:审核驳回 5:审核撤回
     */
    private Integer approvalStatus;

    /**
     * 调度状态 1:待调度 2:无需调度 3:已调度 4:调度取消
     */
    private Integer scheduleStatus;

    /**
     * 核实状态 0:未核实 1:已核实 2:无需核实
     */
    private Integer verifyStatus;

    /**
     * 核实状态 0:未核实 1:已核实 2:无需核实
     */
    private String verifyStatusStr;

    /**
     * 行程状态(10待审批  20待调度 30待出发 40用车中 50已完成 60已取消)
     */
    private Integer orderStatus;

    /**
     * 行程状态(10待审批  20待调度 30待出发 40用车中 50已完成 60已取消)
     */
    private String orderStatusStr;

    /**
     * 驾驶员类型 1-自驾，2-驾驶员
     */
    private Integer drivingType;

    /**
     * 驾驶员类型 1-自驾，2-驾驶员
     */
    private String drivingTypeStr;

    /**
     * 用车类型: 1:公务用车 2:社会租赁
     */
    private Integer useType;

    /**
     * 驾驶员信息(拼好了)
     */
    private String driverInfo;

    /**
     * 创建人
     */
    private String createUserName;

    /**
     * 创建人单位
     */
    private String createDeptName;

    /**
     * 主用车人
     */
    private String passengerUserName;

    /**
     * 主用车人单位
     */
    private String passengerDeptName;

    /**
     * 主用车人部门
     */
    private String passengerStructName;

    /**
     * 车辆类型
     */
    private Integer vehicleType;

    /**
     * 车辆类型
     */
    private String vehicleTypeStr;

    /**
     * 车牌号
     */
    private String vehicleLicense;


    /**
     * 车辆所属单位code
     */
    private String vehicleBelongDeptCode;

    /**
     * 车辆所有单位
     */
    private String vehicleBelongDeptName;

    /**
     * 预计出发地
     */
    private String estimatedDepartureShortLocation;

    /**
     * 预计目的地
     */
    private String estimatedDestinationShortLocation;

    /**
     * 预计出发地城市name
     */
    private String estimatedDepartureCityName;

    /**
     * 预计目的地城市name
     */
    private String estimatedDestinationCityName;

    /**
     * 实际出发短地址
     */
    private String actualDepartureShortLocation;

    /**
     * 实际目的地短地址
     */
    private String actualDestinationShortLocation;

    /**
     * 出发地城市
     */
    private String startCityName;

    /**
     * 目的地城市
     */
    private String endCityName;
    /**
     * 预计开始时间
     */
    private Date expectedPickupTime;

    /**
     * 预计结束时间
     */
    private Date expectedReturnTime;


    /**
     * 实际开始时间
     */
    private Date orderStartTime;

    /**
     * 实际结束时间
     */
    private Date orderEndTime;

    /**
     * 用车时长(小时)
     */
    private String userTime;

    /**
     * 里程数(km)
     */
    private BigDecimal totalMileage;

    /**
     * 用车备注
     */
    private String orderUserMemo;

    /**
     * 用车事由
     */
    private String carUseReason;


    /**
     * 无任务用车备注
     */
    private String noTaskOrderUserMemo;

    /**
     * 无任务用车事由
     */
    private String noTaskCarUseReason;

    /**
     * 是否跨单位 1:是 0:否
     */
    private Integer crossType;

    /**
     * 是否跨单位 1:是 0:否
     */
    private String crossTypeStr;

    /**
     * 租赁供应商code
     */
    private String supplierCode;

    /**
     * 租赁供应商名称
     */
    private String supplierName;

    /**
     * 租赁方式 1:日租 2:分时
     */
    private Integer rentType;

    /**
     * 租赁方式 1:日租 2:分时
     */
    private String rentTypeStr;

    /**
     * 费用合计
     */
    private String totalFee;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 单位性质 党政机关、多事业单位、事业单位、团体组织
     * 多选，英文逗号分隔
     */
    private String attributeTypeStr;

    /**
     *
     * 主管单位
     * 1-机关事务管理局；2-财政部门
     * 多选，英文逗号分隔
     *
     */
    private String managerCarTypeStr;

    /**
     * 车辆服务类型 （1 定点车 2 平台车）
     */
    private Integer vehicleServiceType;

    /**
     * 车辆服务类型 （1 定点车 2 平台车）
     */
    private String vehicleServiceTypeStr;

    /**
     * 车辆管理单位名称
     */
    private String vehicleManagementDeptName;

    /**
     * 调度类型
     */
    private Integer scheduleType;


}
