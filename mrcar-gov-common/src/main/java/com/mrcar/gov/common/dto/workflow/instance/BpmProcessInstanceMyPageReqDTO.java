package com.mrcar.gov.common.dto.workflow.instance;

import com.mrcar.gov.common.dto.workflow.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

/**
 * 管理后台 - 流程实例的分页 Item Response VO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BpmProcessInstanceMyPageReqDTO extends PageParam {

    /**
     * 流程名称
     * 示例值: 用车
     */
    private String name;

    /**
     * 流程定义的编号
     * 示例值: 2048
     */
    private String processDefinitionId;

    /**
     * 流程实例的状态
     * 参见 bpm_process_instance_status
     * 示例值: 1
     */
    private Integer status;

    /**
     * 流程实例的结果（办理状态）
     * 参见 bpm_process_instance_result
     * 示例值: 2
     */
    private Integer result;

    /**
     * 流程分类
     * 参见 bpm_model_category 数据字典
     * 示例值: 1
     */
    private String category;

    /**
     * 开始的创建时间
     */
    private String beginCreateTime;

    /**
     * 结束的创建时间
     */
    private String endCreateTime;

    /**
     * 流程实例ID
     */
    private String processInstanceId;

    /**
     * 业务类型
     */
    private Byte businessType;

    /**
     * 父流程编码
     * 示例值: 158123f9-6b5d-11ef-88b8-129e55812520
     */
    private String parentProcessInstanceId;

    /**
     * 子流程状态
     * 示例值: 1
     */
    private Byte associationProcessStatus;

    /**
     * 所属企业Id
     * 示例值: 1
     */
    private Integer companyId;
    /**
     * 业务编号
     */
    private String businessNo;

    /**
     * 部门id 数据权限用
     */
    private List<Integer> deptIds;

}
