package com.mrcar.gov.common.dto.asset.maintenance.resp;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description:
 * @date 2025/1/11 8:28
 */
@Data
public class MaintenancePCListDTO {

    /**
     * 维保单号
     */
    private String maintenanceNo;
    /**
     * 车牌号
     */
    private String vehicleLicense;
    /**
     * 工单状态
     */
    private Integer maintenanceStatus;
    /**
     * 工单状态（名称）
     */
    private String maintenanceStatusStr;
    /**
     * 工单类型
     */
    private Integer maintenanceType;
    /**
     * 工单类型（名称）
     */
    private String maintenanceTypeStr;
    /**
     * 维修厂类型
     */
    private Integer constructionGarageType;
    /**
     * 维修厂类型（名称）
     */
    private String constructionGarageTypeStr;
    /**
     * 维修厂名称
     */
    private String constructionGarageName;
    /**
     * 维修城市名称
     */
    private String constructionGarageCityName;
    /**
     * 实收合计
     */
    private BigDecimal actualCost;
    /**
     * 不含税价
     */
    private BigDecimal totalPrice;
    /**
     * 车辆送修的时间
     */
    private Date submissionTime;
    /**
     * 出厂时间
     */
    private Date pickupTime;
    /**
     * 送修人姓名
     */
    private String submitterName;
    /**
     * 送修人电话
     */
    private String submitterPhone;
    /**
     * 送修人（列表展示用）
     */
    private String submitter;
    /**
     * 驾驶员姓名
     */
    private String driverName;
    /**
     * 驾驶员电话
     */
    private String driverMobile;
    /**
     * 驾驶员（列表展示用）
     */
    private String driver;
    /**
     * 车辆所有人
     */
    private String vehicleBelongDeptName;
    /**
     * 车辆使用人
     */
    private String vehicleUseDeptName;
    /**
     * 车辆使用部门
     */
    private String vehicleUseStructName;
    /**
     * 车辆管理部门code
     */
    private Integer manageCarType;
    /**
     * 车辆管理部门（列表展示用）
     */
    private String manageCarTypeStr;
    /**
     * 是否已评价 1：是 0：否
     */
    private Integer appraiseSubmited;
    /**
     * 是否已评价 （列表展示用）
     */
    private String appraiseSubmitedStr;
    /**
     * 创建人
     */
    private String createdName;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改人
     */
    private String updatedName;
    /**
     * 修改时间
     */
    private Date updateTime;
    /**
     * 车架号
     */
    private String vehicleVin;

    /**
     * 到厂时间
     */
    private Date arrivalContactTime;
}
