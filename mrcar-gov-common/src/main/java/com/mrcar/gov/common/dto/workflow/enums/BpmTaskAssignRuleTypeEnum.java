package com.mrcar.gov.common.dto.workflow.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * BPM 任务分配规则的类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum BpmTaskAssignRuleTypeEnum {

    INITIATOR_VEHICLE_UNIT_ROLE(10, "发起车辆单位的角色"),
    INITIATOR_DEPT_LEADER(20, "发起人单位的负责人"),
    USER(30, "用户"),
    USER_GROUP(40, "用户组"),
    ASSIGN_ROLE(50, "指定角色"),

    // 暂不支持的类型，保留注释以供参考
    // ROLE(60, "角色"),
    // DEPT_MEMBER(61, "指定部门的成员"), // 包括负责人
    // DEPT_LEADER(62, "指定部门的负责人"),
    // POST(63, "岗位"),
    // SCRIPT(70, "自定义脚本"), // 例如说，发起人所在部门的领导、发起人所在部门的领导的领导
    ;

    /**
     * 类型
     */
    private final Integer type;
    /**
     * 描述
     */
    private final String desc;

    public static BpmTaskAssignRuleTypeEnum fromType(Integer type) {
        for (BpmTaskAssignRuleTypeEnum ruleType : values()) {
            if (ruleType.getType().equals(type)) {
                return ruleType;
            }
        }
        throw new IllegalArgumentException("未知的规则类型: " + type);
    }
}
