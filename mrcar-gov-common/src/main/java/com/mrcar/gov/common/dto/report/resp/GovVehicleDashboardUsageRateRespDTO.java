package com.mrcar.gov.common.dto.report.resp;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/12/18 10:29
 */
@Data
public class GovVehicleDashboardUsageRateRespDTO {

    /**
     * 出车率
     */
    private BigDecimal vehicleDispatchRate;
    /**
     * 使用时常率
     */
    private BigDecimal usageTimeRate;
    /**
     * 出车率（环比）正上升 父下降
     * 例：23%、-5%
     */
    private String ringVehicleDispatchRate;
    /**
     * 出车率增长。 1正增长；0 无增长；-1 负增长
     */
    private Integer ringVehicleDispatchRateGrowth;
    /**
     * 使用时常率（环比）正上升 父下降
     */
    private String ringUsageTimeRate;
    /**
     * 使用时常率增长。1正增长；0 无增长；-1 负增长
     */
    private Integer ringUsageTimeRateGrowth;
}
