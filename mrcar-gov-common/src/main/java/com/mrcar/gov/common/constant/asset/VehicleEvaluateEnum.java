package com.mrcar.gov.common.constant.asset;

import lombok.Getter;

@Getter
public enum VehicleEvaluateEnum {
    STANDARD_COMPLIANCE_SCORE(1, "编制标准合规分"),
    ONLINE_USAGE_SCORE(2, "用车线上化评分"),
    SAFETY_SCORE(3, "安全评分"),
    VEHICLE_OPERATION_SCORE(4, "车务效率分"),
    COST_CONTROL_SCORE(5, "成本控制分");

    private final int code;
    private final String description;

    VehicleEvaluateEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据代码获取枚举实例。
     *
     * @param code 评估类型的代码。
     * @return 对应的枚举实例，如果未找到则返回 null。
     */
    public static VehicleEvaluateEnum fromCode(int code) {
        for (VehicleEvaluateEnum type : values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        return STANDARD_COMPLIANCE_SCORE;
    }
}
