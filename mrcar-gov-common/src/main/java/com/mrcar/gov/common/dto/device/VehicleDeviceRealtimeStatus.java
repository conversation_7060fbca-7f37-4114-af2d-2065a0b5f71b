package com.mrcar.gov.common.dto.device;

import com.mrcar.gov.common.dto.iot.resp.CarGpsFenceDTO;
import lombok.Data;

import java.util.List;

/**
 * 车辆设备的实时状态信息
 */
@Data
public class VehicleDeviceRealtimeStatus {

    /**
     * 是否在围栏内
     */
    private Boolean inFence;

    /**
     * 车辆最新点位信息
     */
    private DeviceRealtimeData deviceStatus;

    /**
     * 车辆最新状态信息
     */
    private DeviceStatusInfo statusInfo;

    /**
     *
     */
    private String address;

    /**
     * 最近的围栏信息
     */
    private CarGpsFenceDTO nearestFence;

    /**
     * 车辆对应的所有围栏信息
     */
    private List<CarGpsFenceDTO> candidateFences;

}
