package com.mrcar.gov.common.dto.workflow.enums;

import com.mrcar.gov.common.util.ObjectUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 流程实例的结果
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum BpmProcessInstanceResultEnum {

    PROCESS((byte)1, "处理中"),
    APPROVE((byte)2, "通过"),
    REJECT((byte)3, "不通过"),
    CANCEL((byte)4, "已取消"),
    BACK((byte)5, "退回");

    /**
     * 结果
     */
    private final Byte result;
    /**
     * 描述
     */
    private final String desc;

    public static BpmProcessInstanceResultEnum getEnum(byte result){
        for(BpmProcessInstanceResultEnum b : BpmProcessInstanceResultEnum.values()){
            if(b.getResult().equals(result)){
                return b;
            }
        }
        return BpmProcessInstanceResultEnum.PROCESS;
    }

    /**
     * 判断该结果是否已经处于 End 最终结果
     *
     * 主要用于一些结果更新的逻辑，如果已经是最终结果，就不再进行更新
     *
     * @param result 结果
     * @return 是否
     */
    public static boolean isEndResult(Byte result) {
        return ObjectUtils.equalsAny(result, APPROVE.getResult(), REJECT.getResult(), CANCEL.getResult(),BACK.getResult());
    }

}
