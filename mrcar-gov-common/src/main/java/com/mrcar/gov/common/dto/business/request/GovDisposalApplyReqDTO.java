package com.mrcar.gov.common.dto.business.request;

import com.mrcar.gov.common.dto.BaseDTO;
import com.mrcar.gov.common.dto.FileDTO;
import com.mrcar.gov.common.dto.business.response.GovTransactionApplyRespDTO;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/21 9:34
 */
@Data
public class GovDisposalApplyReqDTO extends BaseDTO {

    /**
     * 业务申请编码
     */
    private String applyNo;

    /**
     * 申请类型 1 更新 2 配备 3 处理及调拨
     */
    private Integer applyType;

    /**
     * 申请时间
     */
    private Date applyDate;

    /**
     * 申请说明
     */
    private String applyRemark;

    /**
     * 申请单位编码
     */
    @NotBlank(message = "申请单位编码不能为空")
    private String applyStructCode;

    /**
     * 申请单位名称
     */
    private String applyStructName;

    /**
     * 车辆所属单位编码
     */
    @NotBlank(message = "车辆所属单位编码不能为空")
    private String vehicleBelongDeptCode;

    /**
     * 车辆所属单位名称
     */
    private String vehicleBelongDeptName;
    /**
     * 车辆信息列表
     */
    @NotEmpty(message = "车辆信息列表不能为空")
    private List<GovTransactionApplyRespDTO.GovApplyVehicleRespDTO> govApplyVehicleDTOList;
    /**
     * 申请文件集合
     */
    private List<FileDTO> applyFileList;

    /**
     * 审批id
     */
    private String approveId;
}
