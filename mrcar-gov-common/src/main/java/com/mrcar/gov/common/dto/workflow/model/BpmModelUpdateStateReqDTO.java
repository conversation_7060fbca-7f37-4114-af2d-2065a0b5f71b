package com.mrcar.gov.common.dto.workflow.model;

import com.mrcar.gov.common.dto.workflow.BaseDTO;
import lombok.Data;
import javax.validation.constraints.NotNull;

/**
 * 管理后台 - 流程模型更新状态 Request VO
 */
@Data
public class BpmModelUpdateStateReqDTO extends BaseDTO {

    /**
     * 编号
     * 示例值: 1024
     */
    @NotNull(message = "编号不能为空")
    private String id;

    /**
     * 状态
     * 示例值: 1
     * 见 SuspensionState 枚举
     */
    @NotNull(message = "状态不能为空")
    private Integer state;
}
