package com.mrcar.gov.common.constant.asset;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 车辆颜色枚举
 * <AUTHOR>
 * @date 2024/11/29 17:25
 */
@Getter
public enum VehicleColorEnum {

    BLACK(1, "黑色"),
    SILVERY(2, "银色"),
    GREY(3, "灰色"),
    WHITE(4, "白色"),
    RED(5, "红色"),
    GOLDEN(6, "金色"),
    BLUE(7, "蓝色"),
    BROWN(8, "棕色"),
    PURPLE(9, "紫色"),
    GREEN(10, "绿色"),
    PINK(11, "粉色"),
    YELLOW(12, "黄色");

    private Integer code;

    private String desc;

    VehicleColorEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static List<String> getDescList() {
        return Arrays.stream(VehicleColorEnum.values()).map(VehicleColorEnum::getDesc).collect(Collectors.toList());
    }
}
