package com.mrcar.gov.common.dto.workflow.process.resp;

import com.mrcar.gov.common.dto.workflow.task.AppBpmTaskRespDTO;
import lombok.Data;
import java.util.List;

/**
 * AppBpmProcessInstanceDetail 响应 DTO
 * 返回流程实例的详细信息，包括任务信息
 *
 * <AUTHOR>
 * @date 2024/9/7 10:26
 */
@Data
public class AppBpmProcessInstanceDetailRespDTO {

    /**
     * 流程实例信息
     */
    private AppBpmProcessInstanceRespDTO processInstance;

    /**
     * 流程任务信息
     */
    private List<AppBpmTaskRespDTO> bpmTasks;
}
