package com.mrcar.gov.common.dto.business.response;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 车辆里程数据统计返回dto对象.
 *
 * <AUTHOR>
 * @date 2025-02-14
 */
@Data
public class GovVehicleMileageStatLiteRespDTO implements Serializable {

    /**
     * 车辆编码(按单位统计时, 无此值)
     */
    private String vehicleNo;

    /**
     * 车辆所属单位
     */
    private String vehicleBelongDeptCode;

    /**
     * 行驶里程汇总
     */
    private BigDecimal travelMileage;

    /**
     * 行驶时长(单位:秒)
     */
    private Integer travelDuration;

}
