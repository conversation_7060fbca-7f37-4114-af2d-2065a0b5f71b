package com.mrcar.gov.common.dto.report.resp;

import lombok.Data;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/12/18 14:33
 */
@Data
public class VehicleFeeDTO {
    /**
     * 统计费用类型 1:加油费 2:保险费 3：维修保养费 4:过路过桥费 5：洗车费 6：其他费用
     */
    private Integer feeType;
    /**
     * 费用类型描述
     */
    private String feeTypeStr;
    /**
     * 费用金额
     */
    private BigDecimal totalFee;

    /**
     * 费用金额(万元)
     */
    private BigDecimal totalFeeTenThousand;
    /**
     * 费用类型占比
     */
    private String percentage;


    public void setTotalFee(BigDecimal totalFee) {
        this.totalFee = totalFee;
        if(Objects.nonNull(totalFee)){
            this.totalFeeTenThousand = totalFee.divide(new BigDecimal(10000), 2, RoundingMode.HALF_UP);
        }
    }

}
