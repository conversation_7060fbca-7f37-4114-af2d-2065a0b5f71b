package com.mrcar.gov.common.constant.user;

import lombok.AllArgsConstructor;
import lombok.Getter;


@Getter
@AllArgsConstructor
public enum OrgTypeEnum {

    WB(1, "维保服务机构", GovUserTypeEnum.REPAIR_STATION_EMPLOYEE),
    BX(2, "保险服务机构", GovUserTypeEnum.INSURANCE_COMPANY_EMPLOYEE),
    JY(3, "加油服务机构", GovUserTypeEnum.OIL_COMPANY_EMPLOYEE),
    ZL(4, "租赁服务机构", GovUserTypeEnum.RENT_COMPANY_EMPLOYEE);



    private final Integer code;
    private final String name;


    private final GovUserTypeEnum userTypeEnu;

    public static OrgTypeEnum getEnum(Integer code) {
        for (OrgTypeEnum e : OrgTypeEnum.values()) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        return OrgTypeEnum.WB;
    }

    public static OrgTypeEnum getEnum(String name) {
        for (OrgTypeEnum e : OrgTypeEnum.values()) {
            if (e.getName().equals(name)) {
                return e;
            }
        }
        return OrgTypeEnum.WB;
    }

    public static String getDescByCode(Integer supplierServiceType) {
        for (OrgTypeEnum e : OrgTypeEnum.values()) {
            if (e.getCode().equals(supplierServiceType)) {
                return e.getName();
            }
        }
        return "";
    }
}
