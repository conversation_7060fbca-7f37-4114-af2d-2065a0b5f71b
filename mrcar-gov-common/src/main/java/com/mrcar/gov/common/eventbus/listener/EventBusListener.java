package com.mrcar.gov.common.eventbus.listener;

import com.mrcar.gov.common.eventbus.EventBusDispatcher;
import com.mrcar.gov.common.eventbus.EventBusHandler;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

import java.util.Collection;
import java.util.Map;

public class EventBusListener implements ApplicationContextAware, InitializingBean, DisposableBean {

    private ApplicationContext applicationContext;

    private EventBusDispatcher eventBusDispatcher;

    private Map<String, EventBusHandler> eventBusHandlers;

    /**
     * 销毁.
     * @throws Exception
     */
    @Override
    public void destroy() throws Exception {
        if (eventBusDispatcher == null) {
            return;
        }
        if (eventBusHandlers != null && !eventBusHandlers.isEmpty()) {
            Collection<EventBusHandler> ebhs = eventBusHandlers.values();
            for (EventBusHandler ebh : ebhs) {
                eventBusDispatcher.unregister(ebh);
            }
        }
    }

    /**
     * 初始化.
     * @throws Exception
     */
    @Override
    public void afterPropertiesSet() throws Exception {
        if (eventBusDispatcher == null) {
            return;
        }
        eventBusHandlers = applicationContext.getBeansOfType(EventBusHandler.class);
        if (eventBusHandlers != null && !eventBusHandlers.isEmpty()) {
            Collection<EventBusHandler> ebhs = eventBusHandlers.values();
            for (EventBusHandler ebh : ebhs) {
                eventBusDispatcher.register(ebh);
            }
        }
    }

    /**
     * 设置上下文.
     * @param ac
     * @throws BeansException
     */
    @Override
    public void setApplicationContext(ApplicationContext ac) throws BeansException {
        this.applicationContext = ac;
        eventBusDispatcher = ac.getBean(EventBusDispatcher.class);
    }
}
