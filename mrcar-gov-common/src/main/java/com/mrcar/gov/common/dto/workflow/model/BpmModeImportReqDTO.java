package com.mrcar.gov.common.dto.workflow.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import javax.validation.constraints.NotNull;

/**
 * 管理后台 - 流程模型的导入 Request VO
 * <p>
 * 相比流程模型的新建来说，只是多了一个 bpmnFile 文件。
 * </p>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BpmModeImportReqDTO extends BpmModelCreateReqDTO {

    /**
     * BPMN 文件
     */
    @NotNull(message = "BPMN 文件不能为空")
    private String bpmnFile;

}
