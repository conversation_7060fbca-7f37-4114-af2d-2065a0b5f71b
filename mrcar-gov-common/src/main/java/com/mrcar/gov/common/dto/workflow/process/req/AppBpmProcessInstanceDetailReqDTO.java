package com.mrcar.gov.common.dto.workflow.process.req;

import com.mrcar.gov.common.dto.workflow.BaseDTO;
import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * AppBpmProcessInstanceDetail 请求 DTO
 * 请求用于获取流程实例详细信息
 *
 * <AUTHOR>
 * @date 2024/9/5 16:46
 */
@Data
public class AppBpmProcessInstanceDetailReqDTO extends BaseDTO {

    /**
     * 流程实例id
     */
    @NotBlank(message = "流程实例id不能为空")
    private String processInstanceId;

    /**
     * 请求来源
     * 1待办理 2已办理 3我发起的 4抄送我的
     */
    @NotNull(message = "请求来源不能为空")
    private Integer bpmRequestSource;

}
