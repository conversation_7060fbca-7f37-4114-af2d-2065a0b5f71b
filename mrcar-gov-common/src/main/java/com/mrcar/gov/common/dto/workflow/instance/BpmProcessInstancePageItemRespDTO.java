package com.mrcar.gov.common.dto.workflow.instance;

import com.mrcar.gov.common.dto.workflow.enums.BpmModelCategoryEnum;
import com.mrcar.gov.common.dto.workflow.enums.BpmProcessInstanceResultEnum;
import com.mrcar.gov.common.dto.workflow.enums.BpmProcessInstanceStatusEnum;
import com.mrcar.gov.common.dto.workflow.enums.ModelEnum;
import lombok.Data;
import java.util.Date;
import java.util.List;

/**
 * 管理后台 - 流程实例的分页 Item Response VO
 */
@Data
public class BpmProcessInstancePageItemRespDTO {

    /**
     * 流程实例的编号
     * 示例值: 1024
     */
    private String id;

    /**
     * 流程名称
     * 示例值: 用车
     */
    private String name;

    /**
     * 流程定义的编号
     * 示例值: 2048
     */
    private String processDefinitionId;

    /**
     * 流程分类
     * 参见 bpm_model_category 数据字典
     * 示例值: 1
     */
    private String category;

    /**
     * 流程分类名称
     * 示例值: OA
     */
    private String categoryStr;

    /**
     * 流程实例的状态
     * 参见 bpm_process_instance_status
     * 示例值: 1
     */
    private Integer status;

    private String statusStr;

    /**
     * 流程实例的结果
     * 参见 bpm_process_instance_result
     * 示例值: 2
     */
    private Integer result;

    /**
     * 流程实例的结果名称
     * 示例值: 处理中
     */
    private String resultStr;

    /**
     * 提交时间
     */
    private Date createTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 单据编号
     * 示例值: M202211010001
     */
    private String businessNo;

    /**
     * 业务类型
     * 示例值: 1
     */
    private Byte businessType;

    /**
     * 业务类型名称
     * 示例值: 内部用车申请
     */
    private String businessTypeName;

    /**
     * 发起人的用户昵称
     * 示例值: hxc
     */
    private String startUserNickname;

    /**
     * 发起人的部门名称
     * 示例值: 技术部
     */
    private String startDeptName;

    /**
     * 撤回（取消）按钮是否展示（true展示 false不展示）
     * 示例值: false
     */
    private boolean buttonCancel;

    /**
     * 父流程编码
     * 示例值: qweqwrqwerqwewq
     */
    private String parentProcessInstanceId;

    /**
     * 子流程的状态字符串
     * 示例值: 审批中
     */
    private String associationProcessStatusStr;

    /**
     * 子流程状态
     * 示例值: 1
     */
    private Byte associationProcessStatus;

    /**
     * 是否展示发起下级流程
     * 示例值: false
     */
    private boolean buttonApprove;

    /**
     * 模型 ID
     * 示例值: eqweq
     */
    private String modelId;

    /**
     * 所属企业 Id
     * 示例值: eqweq
     */
    private String companyId;

    /**
     * 所属企业名称
     * 示例值: eqweq
     */
    private String companyName;

    /**
     * 当前任务
     */
    private List<Task> tasks;

    /**
     * 流程任务
     */
    @Data
    public static class Task {

        /**
         * 流程任务的编号
         * 示例值: 1024
         */
        private String id;

        /**
         * 任务名称
         * 示例值: 用车
         */
        private String name;

    }

    public void setCategory(String category) {
        this.category = category;
        this.categoryStr = BpmModelCategoryEnum.getEnum(category).getDesc();
    }

    public void setStatus(Integer status) {
        this.status = status;
        this.statusStr = BpmProcessInstanceStatusEnum.getEnum(Byte.valueOf(status + "")).getDesc();
    }

    public void setResult(Integer result) {
        this.result = result;
        this.resultStr = BpmProcessInstanceResultEnum.getEnum(Byte.valueOf(result + "")).getDesc();
    }

    public void setBusinessType(Byte businessType) {
        this.businessType = businessType;
        this.businessTypeName = ModelEnum.BusinessTypeEnum.getNameByCode(businessType);
    }

    public String getAssociationProcessStatusStr() {
        if (associationProcessStatus == null || associationProcessStatus == 0) {
            return "";
        }
        return BpmProcessInstanceResultEnum.getEnum(associationProcessStatus).getDesc();
    }
}
