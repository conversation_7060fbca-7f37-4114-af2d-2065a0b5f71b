package com.mrcar.gov.common.constant.business;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/12/28 09:05
 */
@Getter
@AllArgsConstructor
public enum GovMsgNoticePublishDeptModelEnum {

    ALL(1, "全部"),

    ASSIGN_DEPT(2, "指定部门"),
    ;


    private final Integer code;

    private final String desc;

    public static String getDescByCode(Integer code) {
        for (GovMsgNoticePublishDeptModelEnum item : GovMsgNoticePublishDeptModelEnum.values()) {
            if (Objects.equals(item.getCode(), code)) {
                return item.getDesc();
            }
        }
        return "";
    }



}
