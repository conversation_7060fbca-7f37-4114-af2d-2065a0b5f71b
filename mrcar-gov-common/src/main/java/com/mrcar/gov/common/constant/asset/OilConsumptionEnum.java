package com.mrcar.gov.common.constant.asset;

import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR> dongxiya 2023/3/31 16:40
 */
public class OilConsumptionEnum {

    public enum WarnTypeEnum {
        HIGH_DASHBOARD((byte) 1, "油耗过高（仪表盘）"),
        HIGH_GPS((byte) 2, "油耗过高（GPS）"),
        LOW_DASHBOARD((byte) 3, "油耗过低（仪表盘）"),
        LOW_GPS((byte) 4, "油耗过低（GPS）"),
        ADD_RECORD((byte) 5, "补录工单"),
        FIRST_ADD_REFUEL((byte) 6, "首次加油不支持分析"),
        MORE_THAN_31_DAYS((byte) 7, "超31天加油不支持分析"),
        ;
        private final Byte code;
        private final String value;

        WarnTypeEnum(Byte code, String value) {
            this.code = code;
            this.value = value;
        }


        public static String getValueByCode(Byte code) {
            Optional<WarnTypeEnum> any = Arrays.stream(values()).filter(e -> ObjectUtils.equals(e.getCode(), code)).findAny();
            return any.isPresent() ? any.get().getValue() : "";
        }

        /**
         *
         * @param warnTypeStr  逗号分割多个值 例如： 1,2,3,4
         * @return
         */
        public static Boolean needOilConsumptionAnalysis(String warnTypeStr) {
            if(StringUtils.isBlank(warnTypeStr)){
                return false;
            }
            List<Byte> warnTypeList = Arrays.stream(warnTypeStr.split(",")).map(Byte::parseByte).collect(Collectors.toList());
            return warnTypeList.contains(HIGH_DASHBOARD.getCode())
                    || warnTypeList.contains(HIGH_GPS.getCode())
                    || warnTypeList.contains(LOW_DASHBOARD.getCode())
                    || warnTypeList.contains(LOW_GPS.getCode());
        }

        public Byte getCode() {
            return code;
        }

        public String getValue() {
            return value;
        }

    }
    public enum ApprovalStatusEnum {
        APPROVAL_PENDING((byte) 1, "待审批"),
        APPROVED((byte) 2, "审批通过"),
        APPROVAL_REJECTION((byte) 3, "审批驳回"),
        APPROVAL_WITHDRAWAL((byte) 4, "已撤回"),
        ;
        private final Byte code;
        private final String value;

        ApprovalStatusEnum(Byte code, String value) {
            this.code = code;
            this.value = value;
        }


        public static String getValueByCode(Byte code) {
            Optional<ApprovalStatusEnum> any = Arrays.stream(values()).filter(e -> ObjectUtils.equals(e.getCode(), code)).findAny();
            return any.isPresent() ? any.get().getValue() : "";
        }

        public Byte getCode() {
            return code;
        }

        public String getValue() {
            return value;
        }

    }


    public enum RecordEnum {
        GPS((byte) 1, "GPS"),
        DASHBOARD((byte) 2, "DASHBOARD"),
        ;
        private final Byte code;
        private final String value;

        RecordEnum(Byte code, String value) {
            this.code = code;
            this.value = value;
        }


        public static String getValueByCode(Byte code) {
            Optional<RecordEnum> any = Arrays.stream(values()).filter(e -> ObjectUtils.equals(e.getCode(), code)).findAny();
            return any.isPresent() ? any.get().getValue() : "";
        }

        public Byte getCode() {
            return code;
        }

        public String getValue() {
            return value;
        }
    }

    public enum OcrPatternEnum {
        PAY((byte) 1, "加油金额"),
        TIME((byte) 2, "加油时间"),
        VOLUME((byte) 3, "加油升数"),
        ;
        private final Byte code;
        private final String value;

        OcrPatternEnum(Byte code, String value) {
            this.code = code;
            this.value = value;
        }


        public static String getValueByCode(Byte code) {
            Optional<OcrPatternEnum> any = Arrays.stream(values()).filter(e -> ObjectUtils.equals(e.getCode(), code)).findAny();
            return any.isPresent() ? any.get().getValue() : "";
        }

        public Byte getCode() {
            return code;
        }

        public String getValue() {
            return value;
        }
    }

    public enum DriveFeeCreateTypeEnum {
        DRIVE_FEE((byte) 1, "车杂费"),
        DRIVE_RECORD((byte) 2, "出车日志"),
        INTERNAL_USE_CAR((byte) 3, "内部用车"),
        ;
        private final Byte code;
        private final String value;

        DriveFeeCreateTypeEnum(Byte code, String value) {
            this.code = code;
            this.value = value;
        }


        public static String getValueByCode(Byte code) {
            Optional<DriveFeeCreateTypeEnum> any = Arrays.stream(values()).filter(e -> ObjectUtils.equals(e.getCode(), code)).findAny();
            return any.isPresent() ? any.get().getValue() : "";
        }

        public Byte getCode() {
            return code;
        }

        public String getValue() {
            return value;
        }
    }

    public enum PayTypeEnum {
        OIL_CARD((byte) 1, "油卡"),
        CASH((byte) 2, "现金"),
        ;
        private final Byte code;
        private final String value;

        PayTypeEnum(Byte code, String value) {
            this.code = code;
            this.value = value;
        }


        public static String getValueByCode(Byte code) {
            Optional<PayTypeEnum> any = Arrays.stream(values()).filter(e -> ObjectUtils.equals(e.getCode(), code)).findAny();
            return any.isPresent() ? any.get().getValue() : "";
        }

        public Byte getCode() {
            return code;
        }

        public String getValue() {
            return value;
        }
    }
    public enum OilConsumptionThreshold {
        LOW((byte) 1, "油耗过低"),
        HIGH((byte) 2, "油耗过高"),
        ;
        private final Byte code;
        private final String value;

        OilConsumptionThreshold(Byte code, String value) {
            this.code = code;
            this.value = value;
        }


        public static String getValueByCode(Byte code) {
            Optional<OilConsumptionThreshold> any = Arrays.stream(values()).filter(e -> ObjectUtils.equals(e.getCode(), code)).findAny();
            return any.isPresent() ? any.get().getValue() : "";
        }

        public Byte getCode() {
            return code;
        }

        public String getValue() {
            return value;
        }
    }

}
