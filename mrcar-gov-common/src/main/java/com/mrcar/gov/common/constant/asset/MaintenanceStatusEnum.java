package com.mrcar.gov.common.constant.asset;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum MaintenanceStatusEnum {

    // 维保单状态；1-维修审批中，2-维修审批驳回，3-待到厂，4-已到厂，5-报价审批中，6-报价审批驳回，7-维修中，8-增项审批中，9-增项审批驳回，10-已竣工，11-接车完成，12已废弃
    REPAIR_APPROVAL_PENDING(1, "维修审批中"),
    REPAIR_APPROVAL_REJECTED(2, "维修审批驳回"),
    WAITING_FOR_FACTORY(3, "待到厂"),
    ARRIVED_AT_FACTORY(4, "已到厂"),
    QUOTE_APPROVAL_PENDING(5, "报价审批中"),
    QUOTE_APPROVAL_REJECTED(6, "报价审批驳回"),
    UNDER_REPAIR(7, "维修中"),
    ADDITIONAL_ITEM_APPROVAL_PENDING(8, "增项审批中"),
    ADDITIONAL_ITEM_APPROVAL_REJECTED(9, "增项审批驳回"),
    COMPLETED(10, "已竣工"),
    VEHICLE_RECEIVED(11, "接车完成"),
    DISCARDED(12, "已废弃"),
    APPROVAL_WITHDRAWAL(13, "审批撤回"),
    ;

    private final Integer code;
    private final String name;

    public static String getName(Integer code) {
        return Arrays.stream(values()).filter(e -> e.getCode().equals(code)).findFirst().map(MaintenanceStatusEnum::getName).orElse(null);
    }

    public static MaintenanceStatusEnum getEnum(Integer maintenanceStatus) {
        return Arrays.stream(values()).filter(e -> e.getCode().equals(maintenanceStatus)).findFirst().orElse(null);
    }
}