package com.mrcar.gov.common.dto.business.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 处置-批复信息
 *
 * <AUTHOR>
 * @date 2024/11/21 10:47
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class DisposalReplyReqDTO {

    /**
     * 车辆编码
     */
    @NotBlank(message = "车辆编码不能为空")
    private String vehicleNo;

    /**
     * 处置方式 1报废 2拍卖 3调拨 4损失核销 5厂家回收 6 对外捐赠
     */
    @NotNull(message = "处置方式不能为空")
    private Integer disposalMethod;

    /**
     * 填调入单位
     */
    private String transferInStructCode;

    /**
     * 填调入单位
     */
    private String transferInStructName;
}
