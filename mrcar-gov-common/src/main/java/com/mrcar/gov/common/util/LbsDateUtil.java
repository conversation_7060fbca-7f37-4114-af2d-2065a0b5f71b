package com.mrcar.gov.common.util;

import java.time.Duration;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;

public class LbsDateUtil {
	
	/**
     * 返回两日期的间隔秒
     * @param start
     * @param end
     * @return
     */
    public static long getDuration(Date start, Date end){
        LocalDateTime localDate1 = convertDate2LocalDateTime(start);
        LocalDateTime localDate2 = convertDate2LocalDateTime(end);
        Duration duration = Duration.between(localDate1,localDate2);
        return duration.getSeconds();
    }

    public static LocalDate convertDate2LocalDate(Date date) {
        Instant instant = date.toInstant();
        ZoneId zoneId = ZoneId.of("GMT+8");
        return instant.atZone(zoneId).toLocalDate();
    }

    public static LocalDateTime convertDate2LocalDateTime(Date date) {
        Instant instant = date.toInstant();
        ZoneId zoneId = ZoneId.of("GMT+8");
        return instant.atZone(zoneId).toLocalDateTime();
    }

}
