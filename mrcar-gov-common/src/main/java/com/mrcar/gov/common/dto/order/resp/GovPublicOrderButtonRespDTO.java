package com.mrcar.gov.common.dto.order.resp;

import com.mrcar.gov.common.constant.order.IsShowEnum;
import lombok.Data;


@Data
public class GovPublicOrderButtonRespDTO {

    /**
     * 是否展示派车单按钮 0:不展示 1:展示
     */
    private Integer showDispatchButton = IsShowEnum.NO_SHOW.getCode();

    /**
     * 是否展示调价按钮 0:不展示 1:展示
     */
    private Integer showAdjustPriceButton = IsShowEnum.NO_SHOW.getCode();
    /**
     * 是否展示取消按钮 0:不展示 1:展示
     */
    private Integer showCancelButton = IsShowEnum.NO_SHOW.getCode();
    /**
     * 是否展示强制结束按钮 0:不展示 1:展示
     */
    private Integer showForceFinishButton = IsShowEnum.NO_SHOW.getCode();

    /**
     * 是否展示开始按钮 0:不展示 1:展示
     */
    private Integer showStartButton = IsShowEnum.NO_SHOW.getCode();
}
