package com.mrcar.gov.common.constant.asset;

import com.google.common.collect.Lists;
import com.mrcar.gov.common.dto.DictionaryEnumDTO;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/11/11 16:19
 */
@Getter
@AllArgsConstructor
public enum VehicleReportUseAttributeEnum {

    //机要通信用车、应急保障用车、实物保障用车、调研用车、行政执法用车、接待用车、老干部服务用车、业务用车、执法执勤用车、特种专业技术用车
    COMMUNICATION_VEHICLE(1,"机要通信用车",  Lists.newArrayList(UseAttributeEnum.COMMUNICATION_VEHICLE)),
    EMERGENCY_VEHICLE(2,"应急保障用车", Lists.newArrayList(UseAttributeEnum.EMERGENCY_VEHICLE)),
    BUSINESS_VEHICLE(8,"业务用车", Lists.newArrayList(UseAttributeEnum.BUSINESS_VEHICLE)),
    POLICE_VEHICLE(9,"执法执勤用车", Lists.newArrayList(UseAttributeEnum.POLICE_VEHICLE)),
    OTHER_VEHICLE(99, "其它车型", Lists.newArrayList(UseAttributeEnum.PHYSICAL_VEHICLE,
            UseAttributeEnum.RESEARCH_VEHICLE, UseAttributeEnum.REGULATION_VEHICLE, UseAttributeEnum.RECEPTION_VEHICLE, UseAttributeEnum.OLD_AGE_VEHICLE, UseAttributeEnum.SPECIAL_VEHICLE));
    private final int code;
    private final String desc;
    //GovAttributeTypeEnum

    private final List<UseAttributeEnum> mappingList;


    //        WHEN use_attribute IN (1, 2, 8, 9) THEN
//                CASE
//        WHEN use_attribute = 1 THEN '机要通信用车'
//        WHEN use_attribute = 2 THEN '应急保障用车'
//        WHEN use_attribute = 8 THEN '业务用车'
//        WHEN use_attribute = 9 THEN '执法执勤用车'
//        END
//        ELSE '其他用车'
    public static List<DictionaryEnumDTO> convertToDicEnum(){
        List<DictionaryEnumDTO> list = new ArrayList<>(VehicleReportUseAttributeEnum.values().length);
        for(VehicleReportUseAttributeEnum enu : VehicleReportUseAttributeEnum.values()){
            DictionaryEnumDTO dto = new DictionaryEnumDTO();
            dto.setCode(enu.getCode() + "");
            dto.setDesc(enu.getDesc());
            list.add(dto);
        }
        return list;
    }

    public static List<Integer> listMappingCode(Integer code){
        for(VehicleReportUseAttributeEnum enu : VehicleReportUseAttributeEnum.values()){
            if(Objects.equals(enu.getCode(), code)){
                return enu.getMappingList().stream().map(UseAttributeEnum::getCode).collect(Collectors.toList());
            }
        }
        return Lists.newArrayList();
    }



}
