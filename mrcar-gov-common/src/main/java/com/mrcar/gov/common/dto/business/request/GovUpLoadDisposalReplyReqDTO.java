package com.mrcar.gov.common.dto.business.request;

import com.mrcar.gov.common.dto.BaseDTO;
import com.mrcar.gov.common.dto.FileDTO;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/21 9:51
 */
@Data
public class GovUpLoadDisposalReplyReqDTO extends BaseDTO {

    /**
     * 业务申请编码
     */
    @NotBlank(message = "业务申请编码不能为空")
    private String applyNo;

    /**
     * 批复信息
     */
    @NotEmpty(message = "批复信息不能为空")
    @Valid
    private List<DisposalReplyReqDTO> disposalReplyList;

    /**
     * 批复文件
     */
    private List<FileDTO> replyFileList;

    /**
     * 批复说明
     */
    private String replyRemark;

}
