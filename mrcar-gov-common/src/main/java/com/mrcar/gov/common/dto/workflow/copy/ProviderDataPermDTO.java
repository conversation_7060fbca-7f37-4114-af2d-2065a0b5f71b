package com.mrcar.gov.common.dto.workflow.copy;

import lombok.Data;
import java.util.List;
import java.util.Set;

/**
 * ProviderDataPermDTO - 数据权限传输对象
 *
 * <AUTHOR>
 * @date 2023/11/22 13:50
 */
@Data
public class ProviderDataPermDTO {
    /**
     * 系统类型
     */
    private Byte systemType;

    /**
     * 登录人企业id
     */
    private Integer companyId;

    /**
     * 登录人企业code
     */
    private String companyCode;

    /**
     * 登录人企业name
     */
    private String companyName;

    /**
     * 登录人id
     */
    private Integer staffId;

    /**
     * 登录人code
     */
    private String staffCode;

    /**
     * 登录人name
     */
    private String staffName;

    /**
     * 数据权限类型
     */
    private Byte dataPermType;

    /**
     * 具体需要查询的权限编码集合
     */
    private Set<String> dataCodeSet;

    /**
     * 登录人的权限不是空值
     */
    private Boolean dataPermIsNotNull;

    /**
     * 用户登录部门编码（运营）
     */
    private List<String> loginStructCodes;
}
