package com.mrcar.gov.common.dto.device.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @program: mrcar-iot-common
 * @description: 视频报警附件实体
 * @author: ljw
 * @create: 2023-11-03 11:06
 **/
@AllArgsConstructor
@NoArgsConstructor
@Data
public class VideoWarnFileDTO {

    /** 文件类型 1：图片 2：视频 **/
    @ApiModelProperty(value = "文件类型 1：图片 2：视频 ", example = "1")
    private Integer fileType;

    @ApiModelProperty(value = "视频封面图url", example = "")
    private String videoImageUrl;

    @ApiModelProperty(value = "文件地址", example = "")
    private String fileUrl;

}
