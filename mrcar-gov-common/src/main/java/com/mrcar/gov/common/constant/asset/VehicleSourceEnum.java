package com.mrcar.gov.common.constant.asset;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/11/11 17:51
 */
@Getter
public enum VehicleSourceEnum {
    //车辆来源码：1:购置;2:接受捐赠;3:划拨;4:调货
    PURCHASE(1,"财政经费购置"),
    ACCEPT_DONATION(2,"慈善组织捐赠"),
    ALLOTMENT(3,"上级部门划拨"),
    SHIPPING(4,"机关内部调拨");

    private final int code;
    private final String desc;

    VehicleSourceEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
    public static String getDesc(int code) {
        for (VehicleSourceEnum vehicleSourceEnum : VehicleSourceEnum.values()) {
            if (vehicleSourceEnum.getCode() == code) {
                return vehicleSourceEnum.getDesc();
            }
        }
        return "";
    }
    // 使用list 返回所有的desc
    public static List<String> getDescList() {
        return Arrays.stream(VehicleSourceEnum.values()).map(VehicleSourceEnum::getDesc).collect(Collectors.toList());
    }

    // 根据描述获取code
    public static Integer getCodeByDesc(String desc) {
        for (VehicleSourceEnum vehicleSourceEnum : VehicleSourceEnum.values()) {
            if (Objects.equals(vehicleSourceEnum.getDesc(), desc)) {
                return vehicleSourceEnum.getCode();
            }
        }
        return null;
    }
}
