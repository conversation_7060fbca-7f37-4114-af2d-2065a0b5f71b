package com.mrcar.gov.common.constant.asset;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/11/13 20:53
 */
@Getter
public enum VehicleTypeEnum {
    //车辆分类 轿车、越野车、商务车、大型客车、中型客车、其他小型客车、救护车、消防车、垃圾清运车、货车
    CAR(1, "轿车"),
    SUV(2, "越野车"),
    BUS(3, "商务车"),
    LARGE_CAR(4, "大型客车"),
    MIDDLE_CAR(5, "中型客车"),
    OTHER_SMALL_CAR(6, "其他小型客车"),
    AMBULANCE(7, "救护车"),
    FIRE_CAR(8, "消防车"),
    GARBAGE_CLEANING_CAR(9, "垃圾清运车"),
    TRUCK(10, "货车"),
    ;


    private final Integer code;
    private final String desc;
    VehicleTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    public static String getVehicleTypeDesc(Integer code) {
        for (VehicleTypeEnum vehicleTypeEnum : VehicleTypeEnum.values()) {
            if (Objects.equals(vehicleTypeEnum.getCode(), code)) {
                return vehicleTypeEnum.getDesc();
            }
        }
        return "";
    }
    // 使用list 返回所有描述信息
    public static List<String> getDescList() {
        return Arrays.stream(VehicleTypeEnum.values()).map(VehicleTypeEnum::getDesc).collect(Collectors.toList());
    }
    // 根据描述获取 code
    public static Integer getCodeByDesc(String desc) {
        for (VehicleTypeEnum vehicleTypeEnum : VehicleTypeEnum.values()) {
            if (Objects.equals(vehicleTypeEnum.getDesc(), desc)) {
                return vehicleTypeEnum.getCode();
            }
        }
        return null;
    }
}
