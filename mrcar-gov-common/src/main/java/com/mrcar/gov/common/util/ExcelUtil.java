package com.mrcar.gov.common.util;

import org.apache.commons.lang.RandomStringUtils;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.poifs.filesystem.POIFSFileSystem;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.lang.reflect.Field;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.atomic.AtomicInteger;

public class ExcelUtil {
    private static final Logger logger = LoggerFactory.getLogger(com.mrcar.gov.common.util.ExcelUtil.class);


    /**
     * @param list     数据源
     * @param fieldMap 类的英文属性和Excel中的中文列名的对应关系
     * @param response 使用response可以导出到浏览器
     * @throws Exception
     * @MethodName : listToExcel
     * @Description : 导出Excel（导出到浏览器，兼容 2003  2007）
     */
    public static <T> void listToExcelAll(
            List<T> list,
            LinkedHashMap<String, String> fieldMap,
            String sheetName,
            HttpServletResponse response
    ) throws Exception {

        listToExcelAll(list, fieldMap, sheetName, 65535, response);
    }

    /**
     * @param list     数据源
     * @param fieldMap 类的英文属性和Excel中的中文列名的对应关系
     * @param response 使用response可以导出到浏览器
     * @throws Exception
     * @MethodName : listToExcel
     * @Description : 导出Excel（导出到浏览器，兼容 2003  2007）
     */
    public static <T> void listToExcelWithTwoLevelHeader(
            List<T> list,
            LinkedHashMap<String, LinkedHashMap<String, String>> fieldMap,
            String sheetName,
            HttpServletResponse response
    ) throws Exception {

        listToExcelWithTwoLevelHeader(list, fieldMap, sheetName, 65535, response);
    }


    /**
     * @param list      数据源
     * @param fieldMap  类的英文属性和Excel中的中文列名的对应关系
     * @param sheetSize 每个工作表中记录的最大个数
     * @param response  使用response可以导出到浏览器
     * @throws Exception
     * @MethodName : listToExcel
     * @Description : 导出Excel（导出到浏览器，可以自定义工作表的大小）
     */
    public static <T> void listToExcelWithTwoLevelHeader(
            List<T> list,
            LinkedHashMap<String, LinkedHashMap<String, String>> fieldMap,
            String sheetName,
            int sheetSize,
            HttpServletResponse response
    ) throws Exception {

        //设置默认文件名为当前时间：年月日时分秒
        String fileName = new SimpleDateFormat("yyyyMMddhhmmss").format(new Date()).toString();
//        String filePath = "D:\\tmp";
//        String fileName=filePath+ File.separator+"收入报表首页"+System.currentTimeMillis()+".xlsx";
        //设置response头信息
        response.reset();
        response.setContentType("application/vnd.ms-excel;charset=utf-8"); //改成输出excel文件
        response.setHeader("Content-disposition", "attachment; filename=" + fileName + ".xlsx");
        response.addHeader("Access-Control-Allow-Origin", "*");

        //创建工作簿并发送到浏览器
        try {

            OutputStream out = response.getOutputStream();
//            FileOutputStream out = new FileOutputStream(fileName);
            listToExcelWithTwoLevelHeader(list, fieldMap, sheetName, sheetSize, out);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * @param list      数据源
     * @param fieldMap  类的英文属性和Excel中的中文列名的对应关系
     *                  如果需要的是引用对象的属性，则英文属性使用类似于EL表达式的格式
     *                  如：list中存放的都是student，student中又有college属性，而我们需要学院名称，则可以这样写
     *                  fieldMap.put("college.collegeName","学院名称")
     * @param sheetName 工作表的名称
     * @param sheetSize 每个工作表中记录的最大个数
     * @param out       导出流
     * @throws Exception
     * @MethodName : listToExcel
     * @Description : 导出Excel（可以导出到本地文件系统，也可以导出到浏览器，可自定义工作表大小）
     */
    public static <T> void listToExcelWithTwoLevelHeader(
            List<T> list,
            LinkedHashMap<String, LinkedHashMap<String, String>> fieldMap,
            String sheetName,
            int sheetSize,
            OutputStream out
    ) throws Exception {


        if (list.size() == 0 || list == null) {
            throw new Exception("数据源中没有任何数据");
        }

        if (sheetSize > 65535 || sheetSize < 1) {
            sheetSize = 65535;
        }

        //创建工作簿并发送到OutputStream指定的地方
        SXSSFWorkbook wb;
        try {
//            wb = new XSSFWorkbook();
            wb = new SXSSFWorkbook(-1);
            //压缩临时文件，很重要，否则磁盘很快就会被写满
            wb.setCompressTempFiles(true);
            //因为2003的Excel一个工作表最多可以有65536条记录，除去列头剩下65535条
            //所以如果记录太多，需要放到多个工作表中，其实就是个分页的过程
            //1.计算一共有多少个工作表
            double sheetNum = Math.ceil(list.size() / new Integer(sheetSize).doubleValue());

            //2.创建相应的工作表，并向其中填充数据
            for (int i = 0; i < sheetNum; i++) {
                //如果只有一个工作表的情况
                if (1 == sheetNum) {
                    // 创建工作表
                    Sheet sheet = wb.createSheet(sheetName);

                    fillSheetWithTwoLevelHeader(sheet, list, fieldMap, 0, list.size() - 1);
                    AtomicInteger cell = new AtomicInteger(0);
                    fieldMap.forEach((key, values) -> {
                        if (values.size() > 1) {
                            mergeRowCell(0, cell.get(), values.size(), sheet);
                        } else {
                            mergeCell(0, cell.get(), 1, sheet);
                        }
                        cell.set(cell.get() + values.size());
                    });
                    //有多个工作表的情况
                } else {
                    // 创建工作表
                    Sheet sheet = wb.createSheet(sheetName + (i + 1));
                    //获取开始索引和结束索引
                    int firstIndex = i * sheetSize;
                    int lastIndex = (i + 1) * sheetSize - 1 > list.size() - 1 ? list.size() - 1 : (i + 1) * sheetSize - 1;
                    //填充工作表
                    fillSheetWithTwoLevelHeader(sheet, list, fieldMap, firstIndex, lastIndex);
                    AtomicInteger cell = new AtomicInteger(0);
                    fieldMap.forEach((key, values) -> {
                        if (values.size() > 1) {
                            mergeRowCell(0, cell.get(), values.size(), sheet);
                        } else {
                            mergeCell(0, cell.get(), 1, sheet);
                        }
                        cell.set(cell.get() + values.size());
                    });
                }
            }

            wb.write(out);

            out.flush();
            out.close();
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    /**
     * @return void
     * @Description 合并单元格
     * @Param [row, incomeStatementItemVos, newsheet]
     **/
    private static void mergeCell(int row, int cell, int size, Sheet newsheet) {
        CellRangeAddress region1 = new CellRangeAddress(row, row + size, cell, cell);
        newsheet.addMergedRegion(region1);

    }

    /**
     * @return void
     * @Description 合并单元格
     * @Param [row, incomeStatementItemVos, newsheet]
     **/
    private static void mergeRowCell(int row, int cell, int size, Sheet newsheet) {
        CellRangeAddress region1 = new CellRangeAddress(row, row, cell, cell + size - 1);
        newsheet.addMergedRegion(region1);
    }


    /**
     * @param sheet      工作表
     * @param list       数据源
     * @param fieldMap   中英文字段对应关系的Map，现改为支持二级映射 Map<一级标题, Map<二级标题, 英文字段名>>
     * @param firstIndex 开始索引
     * @param lastIndex  结束索引
     * @MethodName : fillSheetWithTwoLevelHeader
     * @Description : 向工作表中填充数据并支持两层表头
     */
    public static <T> void fillSheetWithTwoLevelHeader(
            Sheet sheet,
            List<T> list,
            LinkedHashMap<String, LinkedHashMap<String, String>> fieldMap,
            int firstIndex,
            int lastIndex
    ) throws Exception {
        // 创建工作簿的工作区样式对象
        Workbook workbook = sheet.getWorkbook();
        CellStyle headerStyle = workbook.createCellStyle();
        headerStyle.setWrapText(true);
        // 设置水平居中
        headerStyle.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        // 设置垂直居中
        headerStyle.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
        // 生成一个样式
        CellStyle leftStyle = workbook.createCellStyle();
        leftStyle.setWrapText(true);
        // 设置这些样式 靠左对齐
        leftStyle.setAlignment(HSSFCellStyle.ALIGN_LEFT);
        //垂直居中
        leftStyle.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
        AtomicInteger collSize = new AtomicInteger();
        fieldMap.forEach((key, values) -> {
            if (values.size() > 1) {
                collSize.addAndGet(values.size());
            } else {
                collSize.addAndGet(1);
            }
        });
        for (int j = 0; j < collSize.get() + 1; j++) {
            //设置列宽
            sheet.setColumnWidth(j, 256 * 20);
        }

        int rowIndex = 0;

        Row firstTitleRow = sheet.createRow(rowIndex++);
        Row secondTitleRow = sheet.createRow(rowIndex++);
        int collNum = 0;
        // 遍历一级标题
        for (Entry<String, LinkedHashMap<String, String>> firstLevelEntry : fieldMap.entrySet()) {
            // 创建一级表头行

            // 填充二级表头
            LinkedHashMap<String, String> secondLevelFieldMap = firstLevelEntry.getValue();
            String[] enFields = new String[secondLevelFieldMap.size()];
//            String[] cnFields = new String[secondLevelFieldMap.size()];
            int count = 0;
            for (Entry<String, String> secondLevelEntry : secondLevelFieldMap.entrySet()) {
                enFields[count] = secondLevelEntry.getValue();
//                cnFields[count] = secondLevelEntry.getKey();
                count++;
            }
            for (int i = 0; i < enFields.length; i++) {
                Cell firstTitleCell = firstTitleRow.createCell(collNum);
                firstTitleCell.setCellValue(firstLevelEntry.getKey());
                firstTitleCell.setCellStyle(headerStyle);
                Cell cell = secondTitleRow.createCell(collNum++);
                cell.setCellValue(enFields[i]);
                cell.setCellStyle(headerStyle);
            }
        }

        // 填充内容
        int contentRowIndex = rowIndex;
        for (int index = firstIndex; index <= lastIndex; index++) {
            T item = list.get(index);
            Row valueRow = sheet.createRow(contentRowIndex++);
            int colIndex = 0;
            for (Entry<String, LinkedHashMap<String, String>> firstLevelEntry : fieldMap.entrySet()) {
                LinkedHashMap<String, String> secondLevelFieldMap = firstLevelEntry.getValue();
                Set<String> enFields = secondLevelFieldMap.keySet();
                for (String enField : enFields) {
                    Object objValue = getFieldValueByNameSequence(enField, item);
                    String fieldValue = objValue == null ? "" : objValue.toString();
                    Cell cell = valueRow.createCell(colIndex++);
                    cell.setCellValue(fieldValue);
                    cell.setCellStyle(leftStyle);
                }
            }
        }
        logger.info("listToExcelWithTwoLevelHeader: " + contentRowIndex);

    }

    /**
     * @param
     * @MethodName : setColumnAutoSize
     * @Description : 设置工作表自动列宽和首行加粗
     */
    @SuppressWarnings("unused")
    private static void setColumnAutoSizeBySheet(Sheet sheet, int extraWith) {
        //获取本列的最宽单元格的宽度
        for (int i = 0; i < sheet.getPhysicalNumberOfRows(); i++) {
            Row row = sheet.getRow(0);
            int colWith = 0;
            for (int j = 0; j < row.getPhysicalNumberOfCells(); j++) {
                String content = row.getCell(j).getStringCellValue();
                int cellWith = content.length();
                if (colWith < cellWith) {
                    colWith = cellWith;
                }
            }
            //设置单元格的宽度为最宽宽度+额外宽度
            sheet.setColumnWidth(i, colWith + extraWith);
        }

    }

    private static void setColumnWidthsWithMergedHeaders(Sheet sheet) {
        // 获取最后一行的索引，这里假设表头都在这之前
        int lastHeaderRowIndex = sheet.getLastRowNum();

        // 初始化一个数组来保存每列的最大宽度
        int[] maxColumnWidths = new int[sheet.getRow(0).getLastCellNum()];

        // 遍历所有可能包含表头的行
        for (int rowIndex = 0; rowIndex <= lastHeaderRowIndex; rowIndex++) {
            Row row = sheet.getRow(rowIndex);
            if (row != null) {
                // 遍历该行的所有单元格
                for (Cell cell : row) {
                    if (cell != null) {
                        // 如果是合并单元格的一部分，则获取其真实的跨列范围
                        CellRangeAddress mergedRegion = getMergedRegionForCell(sheet, cell);
                        if (mergedRegion != null) {
                            // 跨列时，取起始列到结束列的最大宽度
                            for (int colIndex = mergedRegion.getFirstColumn(); colIndex <= mergedRegion.getLastColumn(); colIndex++) {
                                String text = cell.getStringCellValue();
                                int cellWidth = calculateCellWidth(text);
                                maxColumnWidths[colIndex] = Math.max(maxColumnWidths[colIndex], cellWidth);
                            }
                        } else {
                            // 非合并单元格，直接计算宽度
                            String text = cell.getStringCellValue();
                            int cellWidth = calculateCellWidth(text);
                            maxColumnWidths[cell.getColumnIndex()] = Math.max(maxColumnWidths[cell.getColumnIndex()], cellWidth);
                        }
                    }
                }
            }
        }

        // 根据计算出的最大宽度设置列宽
        for (int i = 0; i < maxColumnWidths.length; i++) {
            sheet.setColumnWidth(i, maxColumnWidths[i]);
        }
    }

    private static CellRangeAddress getMergedRegionForCell(Sheet sheet, Cell cell) {
        for (int i = 0; i < sheet.getNumMergedRegions(); i++) {
            CellRangeAddress range = sheet.getMergedRegion(i);
            if (range.isInRange(cell.getRowIndex(), cell.getColumnIndex())) {
                return range;
            }
        }
        return null;
    }

    private static int calculateCellWidth(String text) {
        // 这里简化处理，实际应用中可能需要更复杂的逻辑来考虑字体、字号等因素
        return (int) (text.length() * 256); // 256是一个大致的转换系数
    }


    /**
     * @param list      数据源
     * @param fieldMap  类的英文属性和Excel中的中文列名的对应关系
     * @param sheetSize 每个工作表中记录的最大个数
     * @param response  使用response可以导出到浏览器
     * @throws Exception
     * @MethodName : listToExcel
     * @Description : 导出Excel（导出到浏览器，可以自定义工作表的大小）
     */
    public static <T> void listToExcelAll(
            List<T> list,
            LinkedHashMap<String, String> fieldMap,
            String sheetName,
            int sheetSize,
            HttpServletResponse response
    ) throws Exception {

        //设置默认文件名为当前时间：年月日时分秒
        String fileName = new SimpleDateFormat("yyyyMMddhhmmss").format(new Date()).toString();

        //设置response头信息
        response.reset();
        response.setContentType("application/vnd.ms-excel;charset=utf-8"); //改成输出excel文件
        response.setHeader("Content-disposition", "attachment; filename=" + fileName + ".xlsx");
        response.addHeader("Access-Control-Allow-Origin", "*");

        //创建工作簿并发送到浏览器
        try {

            OutputStream out = response.getOutputStream();
            listToExcelAll(list, fieldMap, sheetName, sheetSize, out);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * @param list      数据源
     * @param fieldMap  类的英文属性和Excel中的中文列名的对应关系
     *                  如果需要的是引用对象的属性，则英文属性使用类似于EL表达式的格式
     *                  如：list中存放的都是student，student中又有college属性，而我们需要学院名称，则可以这样写
     *                  fieldMap.put("college.collegeName","学院名称")
     * @param sheetName 工作表的名称
     * @param sheetSize 每个工作表中记录的最大个数
     * @param out       导出流
     * @throws Exception
     * @MethodName : listToExcel
     * @Description : 导出Excel（可以导出到本地文件系统，也可以导出到浏览器，可自定义工作表大小）
     */
    public static <T> void listToExcelAll(
            List<T> list,
            LinkedHashMap<String, String> fieldMap,
            String sheetName,
            int sheetSize,
            OutputStream out
    ) throws Exception {


        if (list.size() == 0 || list == null) {
            throw new Exception("数据源中没有任何数据");
        }

        if (sheetSize > 65535 || sheetSize < 1) {
            sheetSize = 65535;
        }

        //创建工作簿并发送到OutputStream指定的地方
        XSSFWorkbook wb;
        try {
            wb = new XSSFWorkbook();

            //因为2003的Excel一个工作表最多可以有65536条记录，除去列头剩下65535条
            //所以如果记录太多，需要放到多个工作表中，其实就是个分页的过程
            //1.计算一共有多少个工作表
            double sheetNum = Math.ceil(list.size() / new Integer(sheetSize).doubleValue());

            //2.创建相应的工作表，并向其中填充数据
            for (int i = 0; i < sheetNum; i++) {
                //如果只有一个工作表的情况
                if (1 == sheetNum) {
                    // 创建工作表
                    Sheet sheet = wb.createSheet(sheetName);

                    fillSheet(sheet, list, fieldMap, 0, list.size() - 1);

                    //有多个工作表的情况
                } else {
                    // 创建工作表
                    Sheet sheet = wb.createSheet(sheetName + (i + 1));
                    //获取开始索引和结束索引
                    int firstIndex = i * sheetSize;
                    int lastIndex = (i + 1) * sheetSize - 1 > list.size() - 1 ? list.size() - 1 : (i + 1) * sheetSize - 1;
                    //填充工作表
                    fillSheet(sheet, list, fieldMap, firstIndex, lastIndex);
                }
            }

            wb.write(out);

            out.flush();
            out.close();
        } catch (Exception e) {
            e.printStackTrace();
        }

    }


    /**
     * @param list      数据源
     * @param fieldMap  类的英文属性和Excel中的中文列名的对应关系
     *                  如果需要的是引用对象的属性，则英文属性使用类似于EL表达式的格式
     *                  如：list中存放的都是student，student中又有college属性，而我们需要学院名称，则可以这样写
     *                  fieldMap.put("college.collegeName","学院名称")
     * @param sheetName 工作表的名称
     * @param sheetSize 每个工作表中记录的最大个数
     * @param out       导出流
     * @throws Exception
     * @MethodName : listToExcel
     * @Description : 导出Excel（可以导出到本地文件系统，也可以导出到浏览器，可自定义工作表大小）
     */
    public static <T> void listToExcel(
            List<T> list,
            LinkedHashMap<String, String> fieldMap,
            String sheetName,
            int sheetSize,
            OutputStream out
    ) throws Exception {


        if (list.size() == 0 || list == null) {
            throw new Exception("数据源中没有任何数据");
        }

        if (sheetSize > 65535 || sheetSize < 1) {
            sheetSize = 65535;
        }

        //创建工作簿并发送到OutputStream指定的地方
        HSSFWorkbook wb;
        try {
            wb = new HSSFWorkbook();

            //因为2003的Excel一个工作表最多可以有65536条记录，除去列头剩下65535条
            //所以如果记录太多，需要放到多个工作表中，其实就是个分页的过程
            //1.计算一共有多少个工作表
            double sheetNum = Math.ceil(list.size() / new Integer(sheetSize).doubleValue());

            //2.创建相应的工作表，并向其中填充数据
            for (int i = 0; i < sheetNum; i++) {
                //如果只有一个工作表的情况
                if (1 == sheetNum) {
                    HSSFSheet sheet = wb.createSheet(sheetName);
                    fillSheet(sheet, list, fieldMap, 0, list.size() - 1);

                    //有多个工作表的情况
                } else {
                    HSSFSheet sheet = wb.createSheet(sheetName + (i + 1));

                    //获取开始索引和结束索引
                    int firstIndex = i * sheetSize;
                    int lastIndex = (i + 1) * sheetSize - 1 > list.size() - 1 ? list.size() - 1 : (i + 1) * sheetSize - 1;
                    //填充工作表
                    fillSheet(sheet, list, fieldMap, firstIndex, lastIndex);
                }
            }

            wb.write(out);

            out.flush();
            out.close();
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    /**
     * @param list     数据源
     * @param fieldMap 类的英文属性和Excel中的中文列名的对应关系
     * @param out      导出流
     * @throws Exception
     * @MethodName : listToExcel
     * @Description : 导出Excel（可以导出到本地文件系统，也可以导出到浏览器，工作表大小为2003支持的最大值）
     */
    public static <T> void listToExcel(
            List<T> list,
            LinkedHashMap<String, String> fieldMap,
            String sheetName,
            OutputStream out
    ) throws Exception {

        listToExcel(list, fieldMap, sheetName, 65535, out);

    }


    /**
     * @param list      数据源
     * @param fieldMap  类的英文属性和Excel中的中文列名的对应关系
     * @param sheetSize 每个工作表中记录的最大个数
     * @param response  使用response可以导出到浏览器
     * @throws Exception
     * @MethodName : listToExcel
     * @Description : 导出Excel（导出到浏览器，可以自定义工作表的大小）
     */
    public static <T> void listToExcel(
            List<T> list,
            LinkedHashMap<String, String> fieldMap,
            String sheetName,
            int sheetSize,
            HttpServletResponse response
    ) throws Exception {

        //设置默认文件名为当前时间：年月日时分秒
        String fileName = new SimpleDateFormat("yyyyMMddhhmmss").format(new Date()).toString();

        //设置response头信息
        response.reset();
        response.setContentType("application/vnd.ms-excel;charset=utf-8"); //改成输出excel文件
        response.setHeader("Content-disposition", "attachment; filename=" + fileName + ".xls");
        response.addHeader("Access-Control-Allow-Origin", "*");

        //创建工作簿并发送到浏览器
        try {

            OutputStream out = response.getOutputStream();
            listToExcel(list, fieldMap, sheetName, sheetSize, out);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * @param list     数据源
     * @param fieldMap 类的英文属性和Excel中的中文列名的对应关系
     * @param response 使用response可以导出到浏览器
     * @throws Exception
     * @MethodName : listToExcel
     * @Description : 导出Excel（导出到浏览器，工作表的大小是2003支持的最大值）
     */
    public static <T> void listToExcel(
            List<T> list,
            LinkedHashMap<String, String> fieldMap,
            String sheetName,
            HttpServletResponse response
    ) throws Exception {

        listToExcel(list, fieldMap, sheetName, 65535, response);
    }

    /**
     * @param file                 ：excel文件
     * @param ：要导入的工作表序号sheetIndex
     * @param entityClass          ：List中对象的类型（Excel中的每一行都要转化为该类型的对象）
     * @param fieldMap             ：Excel中的中文列头和类的英文属性的对应关系Map
     * @param uniqueFields         ：指定业务主键组合（即复合主键），这些列的组合不能重复
     * @return ：List
     * @throws Exception
     * @MethodName : excelToList
     * @Description : 将Excel转化为List,兼容03和07
     */
    public static <T> List<T> excelToList(
            File file,
            String sheetName,
            Class<T> entityClass,
            LinkedHashMap<String, String> fieldMap,
            String[] uniqueFields
    ) throws Exception {

        //定义要返回的list
        List<T> resultList = new ArrayList<T>();

        //根据Excel数据源创建WorkBook,兼容03和07版
        Workbook wb = null;
        try {
            wb = new XSSFWorkbook(new FileInputStream(file));
        } catch (Exception e) {
            try {
                wb = new HSSFWorkbook(new POIFSFileSystem(new BufferedInputStream(new FileInputStream(file))));
            } catch (IOException e1) {
                // TODO Auto-generated catch block
                e1.printStackTrace();
            }
        }

        try {
            //获取工作表
            Sheet sheet = wb.getSheet(sheetName);

            //获取工作表的有效行数
            int realRows = sheet.getPhysicalNumberOfRows();

            //如果Excel中没有数据则提示错误
            if (realRows <= 1) {
                throw new Exception("Excel文件中没有任何数据");
            }


            Row firstRow = sheet.getRow(0);

            String[] excelFieldNames = new String[firstRow.getPhysicalNumberOfCells()];

            //获取Excel中的列名
            for (int i = 0; i < excelFieldNames.length; i++) {
                Cell cell = firstRow.getCell(i);
                if (cell != null) {
                    excelFieldNames[i] = cell.getStringCellValue();
                } else {
                    excelFieldNames[i] = "";
                }
            }

            //判断需要的字段在Excel中是否都存在
            boolean isExist = true;
            List<String> excelFieldList = Arrays.asList(excelFieldNames);
            for (String cnName : fieldMap.keySet()) {
                if (!excelFieldList.contains(cnName)) {
                    isExist = false;
                    break;
                }
            }

            //如果有列名不存在，则抛出异常，提示错误
            if (!isExist) {
                throw new Exception("Excel中缺少必要的字段，或字段名称有误");
            }

            //将列名和列号放入Map中,这样通过列名就可以拿到列号
            LinkedHashMap<String, Integer> colMap = new LinkedHashMap<String, Integer>();
            for (int i = 0; i < excelFieldNames.length; i++) {
                colMap.put(excelFieldNames[i], i);
            }

            //判断是否有重复行
            //1.获取uniqueFields指定的列
//            int[] uniqueCells = new int[uniqueFields.length];
//            for(int i = 0; i < uniqueFields.length; i++){
//                int col = colMap.get(uniqueFields[i]);
//                uniqueCells[i] = col;
//            }
//
//            //2.从指定列中寻找重复行
//            for(int i = 1; i < realRows; i++){
//                int nullCols=0;
//                for(int j = 0; j < uniqueFields.length; j++){
//                    String currentContent = sheet.getRow(i).getCell(j).getStringCellValue();
//
//
//
////                    if(sameCell!=null){
////                        nullCols++;
////                    }
//                }
//
//                if(nullCols==uniqueFields.length){
//                    throw new ExcelException("Excel中有重复行，请检查");
//                }
//            }

            Iterator<Row> rowIt = sheet.rowIterator();
            while (rowIt.hasNext()) {
                Row row = rowIt.next();
                if (row.getRowNum() == 0) {
                    continue;
                }
                //新建要转换的对象
                T entity = entityClass.newInstance();
                Cell cell = null;
                //给对象中的字段赋值
                for (Entry<String, String> entry : fieldMap.entrySet()) {
                    //获取中文字段名
                    String cnNormalName = entry.getKey();
                    //获取英文字段名
                    String enNormalName = entry.getValue();
                    //根据中文字段名获取列号
                    int col = colMap.get(cnNormalName);

                    //获取当前单元格中的内容
                    String content = "";
                    cell = row.getCell(col);
                    if (cell == null) {
                        break;
                    }
                    if (cell.getCellType() == Cell.CELL_TYPE_NUMERIC) {
                        content = String.valueOf(cell.getNumericCellValue());
                    } else {
                        content = cell.getStringCellValue();
                    }

                    //给对象赋值
                    setFieldValueByName(enNormalName, content, entity);
                }
                if (cell != null) {
                    resultList.add(entity);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return resultList;
    }

    /**
     * @param ：excel文件
     * @param ：要导入的工作表序号
     * @param entityClass  ：List中对象的类型（Excel中的每一行都要转化为该类型的对象）
     * @param fieldMap     ：Excel中的中文列头和类的英文属性的对应关系Map
     * @param uniqueFields ：指定业务主键组合（即复合主键），这些列的组合不能重复
     * @return ：List
     * @throws Exception
     * @MethodName : excelToList
     * @Description : 将Excel转化为List,兼容03和07
     */
    public static <T> List<T> excelExportToList(
            String filePath,
            String sheetName,
            Class<T> entityClass,
            LinkedHashMap<String, String> fieldMap,
            String[] uniqueFields
    ) throws Exception {

        //定义要返回的list
        List<T> resultList = new ArrayList<T>();

        //根据Excel数据源创建WorkBook,兼容03和07版
        Workbook wb = null;
        URL url = null;
        HttpURLConnection connection = null;
        try {

            url = new URL(filePath);
            connection = (HttpURLConnection) url.openConnection();
            wb = new XSSFWorkbook(connection.getInputStream());
            // ---begin--
            int i = 0;
            while (wb == null) {
                if (i > 5) {
                    break;
                }
                if (wb == null) {
                    i++;
                    wb = new XSSFWorkbook(connection.getInputStream());
                }
            }
            // ---end--
        } catch (Exception e) {
            try {
                url = new URL(filePath);
                connection = (HttpURLConnection) url.openConnection();
                wb = new HSSFWorkbook(new POIFSFileSystem(new BufferedInputStream(connection.getInputStream())));
            } catch (IOException e1) {
                // TODO Auto-generated catch block
                e1.printStackTrace();
            }
        }

        try {
            //获取工作表
            Sheet sheet = wb.getSheet(sheetName);

            //获取工作表的有效行数
            int realRows = sheet.getPhysicalNumberOfRows();

            //如果Excel中没有数据则提示错误
            if (realRows <= 1) {
                throw new Exception("Excel文件中没有任何数据");
            }


            Row firstRow = sheet.getRow(0);

            String[] excelFieldNames = new String[firstRow.getPhysicalNumberOfCells()];

            //获取Excel中的列名
            for (int i = 0; i < excelFieldNames.length; i++) {
                Cell cell = firstRow.getCell(i);
                if (cell != null) {
                    excelFieldNames[i] = cell.getStringCellValue();
                } else {
                    excelFieldNames[i] = "";
                }
            }

            //判断需要的字段在Excel中是否都存在
            boolean isExist = true;
            List<String> excelFieldList = Arrays.asList(excelFieldNames);
            for (String cnName : fieldMap.keySet()) {
                if (!excelFieldList.contains(cnName)) {
                    isExist = false;
                    break;
                }
            }

            //如果有列名不存在，则抛出异常，提示错误
            if (!isExist) {
                throw new Exception("Excel中缺少必要的字段，或字段名称有误");
            }

            //将列名和列号放入Map中,这样通过列名就可以拿到列号
            LinkedHashMap<String, Integer> colMap = new LinkedHashMap<String, Integer>();
            for (int i = 0; i < excelFieldNames.length; i++) {
                colMap.put(excelFieldNames[i], i);
            }

            //判断是否有重复行
            //1.获取uniqueFields指定的列
//            int[] uniqueCells = new int[uniqueFields.length];
//            for(int i = 0; i < uniqueFields.length; i++){
//                int col = colMap.get(uniqueFields[i]);
//                uniqueCells[i] = col;
//            }
//
//            //2.从指定列中寻找重复行
//            for(int i = 1; i < realRows; i++){
//                int nullCols=0;
//                for(int j = 0; j < uniqueFields.length; j++){
//                    String currentContent = sheet.getRow(i).getCell(j).getStringCellValue();
//
//
//
////                    if(sameCell!=null){
////                        nullCols++;
////                    }
//                }
//
//                if(nullCols==uniqueFields.length){
//                    throw new ExcelException("Excel中有重复行，请检查");
//                }
//            }

            Iterator<Row> rowIt = sheet.rowIterator();
            while (rowIt.hasNext()) {
                Row row = rowIt.next();
                if (row.getRowNum() == 0) {
                    continue;
                }
                //新建要转换的对象
                T entity = entityClass.newInstance();
                Cell cell = null;
                //给对象中的字段赋值
                for (Entry<String, String> entry : fieldMap.entrySet()) {
                    //获取中文字段名
                    String cnNormalName = entry.getKey();
                    //获取英文字段名
                    String enNormalName = entry.getValue();
                    //根据中文字段名获取列号
                    int col = colMap.get(cnNormalName);

                    //获取当前单元格中的内容
                    String content = "";
                    cell = row.getCell(col);
                    if (cell == null) {
                        break;
                    }
                    if (cell.getCellType() == Cell.CELL_TYPE_NUMERIC) {
                        content = String.valueOf(cell.getNumericCellValue());
                    } else {
                        content = cell.getStringCellValue();
                    }

                    //给对象赋值
                    setFieldValueByName(enNormalName, content, entity);
                }
                if (cell != null) {
                    resultList.add(entity);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return resultList;
    }


    /**
     * @param file         ：excel文件
     * @param ：要导入的工作表序号
     * @param entityClass  ：List中对象的类型（Excel中的每一行都要转化为该类型的对象）
     * @param fieldMap     ：Excel中的中文列头和类的英文属性的对应关系Map
     * @param uniqueFields ：指定业务主键组合（即复合主键），这些列的组合不能重复
     * @return ：List
     * @throws Exception
     * @MethodName : excelToList
     * @Description : 将Excel转化为List,兼容03和07
     */
    public static <T> List<T> excelToIdCardAuthInfoList(
            File file,
            String sheetName,
            Class<T> entityClass,
            LinkedHashMap<String, String> fieldMap,
            String[] uniqueFields
    ) throws Exception {

        //定义要返回的list
        List<T> resultList = new ArrayList<T>();

        //根据Excel数据源创建WorkBook,兼容03和07版
        Workbook wb = null;
        try {
            wb = new XSSFWorkbook(new FileInputStream(file));
        } catch (Exception e) {
            try {
                wb = new HSSFWorkbook(new POIFSFileSystem(new BufferedInputStream(new FileInputStream(file))));
            } catch (IOException e1) {
                // TODO Auto-generated catch block
                e1.printStackTrace();
            }
        }

        try {
            //获取工作表
            Sheet sheet = wb.getSheet(sheetName);

            //获取工作表的有效行数
            int realRows = sheet.getPhysicalNumberOfRows();

            //如果Excel中没有数据则提示错误
            if (realRows <= 1) {
                throw new Exception("Excel文件中没有任何数据");
            }


            Row firstRow = sheet.getRow(0);

            String[] excelFieldNames = new String[firstRow.getPhysicalNumberOfCells()];

            //获取Excel中的列名
            for (int i = 0; i < excelFieldNames.length; i++) {
                Cell cell = firstRow.getCell(i);
                if (cell != null) {
                    excelFieldNames[i] = cell.getStringCellValue();
                } else {
                    excelFieldNames[i] = "";
                }
            }

            //判断需要的字段在Excel中是否都存在
            boolean isExist = true;
            List<String> excelFieldList = Arrays.asList(excelFieldNames);
            for (String cnName : fieldMap.keySet()) {
                if (!excelFieldList.contains(cnName)) {
                    isExist = false;
                    break;
                }
            }

            //如果有列名不存在，则抛出异常，提示错误
            if (!isExist) {
                throw new Exception("Excel中缺少必要的字段，或字段名称有误");
            }

            //将列名和列号放入Map中,这样通过列名就可以拿到列号
            LinkedHashMap<String, Integer> colMap = new LinkedHashMap<String, Integer>();
            for (int i = 0; i < excelFieldNames.length; i++) {
                colMap.put(excelFieldNames[i], i);
            }

            //判断是否有重复行
            //1.获取uniqueFields指定的列
//            int[] uniqueCells = new int[uniqueFields.length];
//            for(int i = 0; i < uniqueFields.length; i++){
//                int col = colMap.get(uniqueFields[i]);
//                uniqueCells[i] = col;
//            }
//
//            //2.从指定列中寻找重复行
//            for(int i = 1; i < realRows; i++){
//                int nullCols=0;
//                for(int j = 0; j < uniqueFields.length; j++){
//                    String currentContent = sheet.getRow(i).getCell(j).getStringCellValue();
//
//
//
////                    if(sameCell!=null){
////                        nullCols++;
////                    }
//                }
//
//                if(nullCols==uniqueFields.length){
//                    throw new ExcelException("Excel中有重复行，请检查");
//                }
//            }

            Iterator<Row> rowIt = sheet.rowIterator();
            while (rowIt.hasNext()) {
                Row row = rowIt.next();
                if (row.getRowNum() == 0) {
                    continue;
                }
                //新建要转换的对象
                T entity = entityClass.newInstance();
                Cell cell = null;
                //给对象中的字段赋值
                for (Entry<String, String> entry : fieldMap.entrySet()) {
                    //获取中文字段名
                    String cnNormalName = entry.getKey();
                    //获取英文字段名
                    String enNormalName = entry.getValue();
                    //根据中文字段名获取列号
                    int col = colMap.get(cnNormalName);

                    //获取当前单元格中的内容
                    String content = "";
                    cell = row.getCell(col);
                    if (cell == null) {
                        break;
                    }
                    cell.setCellType(HSSFCell.CELL_TYPE_STRING);
                    content = cell.getStringCellValue();

                    //给对象赋值
                    setFieldValueByName(enNormalName, content, entity);
                }
                if (cell != null) {
                    resultList.add(entity);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return resultList;
    }


    /*<-------------------------辅助的私有方法----------------------------------------------->*/

    /**
     * @param fieldName 字段名
     * @param o         对象
     * @return 字段值
     * @MethodName : getFieldValueByName
     * @Description : 根据字段名获取字段值
     */
    private static Object getFieldValueByName(String fieldName, Object o) throws Exception {

        Object value = null;
        // 兼容map
        if (o instanceof Map) {
            @SuppressWarnings("unchecked")
            Map<String, Object> map = (Map<String, Object>) o;
            return map.get(fieldName);
        }

        Field field = getFieldByName(fieldName, o.getClass());

        if (field != null) {
            field.setAccessible(true);
            value = field.get(o);
        } else {
            throw new Exception(o.getClass().getSimpleName() + "类不存在字段名 " + fieldName);
        }

        return value;
    }

    /**
     * @param fieldName 字段名
     * @param clazz     包含该字段的类
     * @return 字段
     * @MethodName : getFieldByName
     * @Description : 根据字段名获取字段
     */
    private static Field getFieldByName(String fieldName, Class<?> clazz) {
        //拿到本类的所有字段
        Field[] selfFields = clazz.getDeclaredFields();

        //如果本类中存在该字段，则返回
        for (Field field : selfFields) {
            if (field.getName().equals(fieldName)) {
                return field;
            }
        }

        //否则，查看父类中是否存在此字段，如果有则返回
        Class<?> superClazz = clazz.getSuperclass();
        if (superClazz != null && superClazz != Object.class) {
            return getFieldByName(fieldName, superClazz);
        }

        //如果本类和父类都没有，则返回空
        return null;
    }


    /**
     * @param fieldNameSequence 带路径的属性名或简单属性名
     * @param o                 对象
     * @return 属性值
     * @throws Exception
     * @MethodName : getFieldValueByNameSequence
     * @Description :
     * 根据带路径或不带路径的属性名获取属性值
     * 即接受简单属性名，如userName等，又接受带路径的属性名，如student.department.name等
     */
    private static Object getFieldValueByNameSequence(String fieldNameSequence, Object o) throws Exception {

        Object value = null;
        // 兼容map
        if (o instanceof Map) {
            @SuppressWarnings("unchecked")
            Map<String, Object> map = (Map<String, Object>) o;
            return map.get(fieldNameSequence);
        }

        //将fieldNameSequence进行拆分
        String[] attributes = fieldNameSequence.split("\\.");
        if (attributes.length == 1) {
            value = getFieldValueByName(fieldNameSequence, o);
        } else {
            //根据属性名获取属性对象
            Object fieldObj = getFieldValueByName(attributes[0], o);
            if (fieldObj != null) {
                String subFieldNameSequence = fieldNameSequence.substring(fieldNameSequence.indexOf(".") + 1);
                value = getFieldValueByNameSequence(subFieldNameSequence, fieldObj);
            } else {
                value = "";
            }
        }
        return value;

    }


    /**
     * @param fieldName  字段名
     * @param fieldValue 字段值
     * @param o          对象
     * @MethodName : setFieldValueByName
     * @Description : 根据字段名给对象的字段赋值
     */
    private static void setFieldValueByName(String fieldName, Object fieldValue, Object o) throws Exception {

        Field field = getFieldByName(fieldName, o.getClass());
        if (field != null) {
            field.setAccessible(true);
            //获取字段类型
            Class<?> fieldType = field.getType();

            //根据字段类型给字段赋值
            if (String.class == fieldType) {
                field.set(o, String.valueOf(fieldValue));
            } else if ((Integer.TYPE == fieldType)
                    || (Integer.class == fieldType)) {
                field.set(o, Integer.parseInt(fieldValue.toString().split("[.]")[0]));
            } else if ((Long.TYPE == fieldType)
                    || (Long.class == fieldType)) {
                field.set(o, Long.valueOf(fieldValue.toString()));
            } else if ((Float.TYPE == fieldType)
                    || (Float.class == fieldType)) {
                field.set(o, Float.valueOf(fieldValue.toString()));
            } else if ((Short.TYPE == fieldType)
                    || (Short.class == fieldType)) {
                field.set(o, Short.valueOf(fieldValue.toString()));
            } else if ((Double.TYPE == fieldType)
                    || (Double.class == fieldType)) {
                field.set(o, Double.valueOf(fieldValue.toString()));
            } else if (Character.TYPE == fieldType) {
                if ((fieldValue != null) && (fieldValue.toString().length() > 0)) {
                    field.set(o, Character
                            .valueOf(fieldValue.toString().charAt(0)));
                }
            } else if (Date.class == fieldType) {
                field.set(o, new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(fieldValue.toString()));
            } else {
                field.set(o, fieldValue);
            }
        } else {
            throw new Exception(o.getClass().getSimpleName() + "类不存在字段名 " + fieldName);
        }
    }


    /**
     * @param
     * @MethodName : setColumnAutoSize
     * @Description : 设置工作表自动列宽和首行加粗
     */
    @SuppressWarnings("unused")
    private static void setColumnAutoSize(HSSFSheet sheet, int extraWith) {
        //获取本列的最宽单元格的宽度
        for (int i = 0; i < sheet.getPhysicalNumberOfRows(); i++) {
            HSSFRow row = sheet.getRow(0);
            int colWith = 0;
            for (int j = 0; j < row.getPhysicalNumberOfCells(); j++) {
                String content = row.getCell(j).getStringCellValue();
                int cellWith = content.length();
                if (colWith < cellWith) {
                    colWith = cellWith;
                }
            }
            //设置单元格的宽度为最宽宽度+额外宽度
            sheet.setColumnWidth(i, colWith + extraWith);
        }

    }

    /**
     * @param sheet      工作表
     * @param list       数据源
     * @param fieldMap   中英文字段对应关系的Map
     * @param firstIndex 开始索引
     * @param lastIndex  结束索引
     * @MethodName : fillSheet
     * @Description : 向工作表中填充数据
     */
    public static <T> void fillSheet(
            Sheet sheet,
            List<T> list,
            LinkedHashMap<String, String> fieldMap,
            int firstIndex,
            int lastIndex
    ) throws Exception {

        //定义存放英文字段名和中文字段名的数组
        String[] enFields = new String[fieldMap.size()];
        String[] cnFields = new String[fieldMap.size()];

        //填充数组
        int count = 0;
        for (Entry<String, String> entry : fieldMap.entrySet()) {
            enFields[count] = entry.getKey();
            cnFields[count] = entry.getValue();
            count++;
        }
        //填充表头
        Row titleRow = sheet.createRow(0);
        for (int i = 0; i < cnFields.length; i++) {
            Cell cell = titleRow.createCell(i);
            cell.setCellValue(cnFields[i]);
        }

        //填充内容
        int rowNo = 1;
        for (int index = firstIndex; index <= lastIndex; index++) {
            //获取单个对象
            T item = list.get(index);
            Row valueRow = sheet.createRow(rowNo);
            for (int i = 0; i < enFields.length; i++) {
                Object objValue = getFieldValueByNameSequence(enFields[i], item);
                String fieldValue = objValue == null ? "" : objValue.toString();
                Cell cell = valueRow.createCell(i);
                cell.setCellValue(fieldValue);
            }

            rowNo++;
        }

        //设置自动列宽
//        setColumnAutoSize(sheet, 0);
    }


    @SuppressWarnings("serial")
    public class Vo implements Serializable {

        String key;

        String value;

        public Vo() {
            super();
        }

        public Vo(String key, String value) {
            super();
            this.key = key;
            this.value = value;
        }

        public String getKey() {
            return key;
        }

        public void setKey(String key) {
            this.key = key;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }
    }


    public static String GetFileSize(File file) {
        String size = "";
        if (file.exists() && file.isFile()) {
            long fileS = file.length();
            DecimalFormat df = new DecimalFormat("#.00");
            if (fileS < 1024) {
                size = df.format((double) fileS) + "BT";
            } else if (fileS < 1048576) {
                size = df.format((double) fileS / 1024) + "KB";
            } else if (fileS < 1073741824) {
                size = df.format((double) fileS / 1048576) + "MB";
            } else {
                size = df.format((double) fileS / 1073741824) + "GB";
            }
        } else if (file.exists() && file.isDirectory()) {
            size = "";
        } else {
            size = "0BT";
        }
        return size;
    }

    public static void downloadLocalFile(File file, HttpServletResponse response) {
        InputStream fis = null;
        FileInputStream fin = null;
        OutputStream toClient = null;
        ServletOutputStream sout = null;
        try {
            // 以流的形式下载文件。
            fin = new FileInputStream(file.getPath());
            fis = new BufferedInputStream(fin);
            sout = response.getOutputStream();

            response.reset();
            toClient = new BufferedOutputStream(sout);
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment;filename="
                    + file.getName());

            int len = -1;
            byte[] buffer = new byte[1024 * 4];
            while ((len = fis.read(buffer)) != -1) {
                toClient.write(buffer, 0, len);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            try {
                if (toClient != null) {
                    toClient.flush();
                    toClient.close();
                }
                sout.close();
                fis.close();
            } catch (Exception e) {
            }
        }
    }

    /**
     * 下载xss文件
     *
     * @param workbook
     * @param response
     */
    public static void downloadBySxssWork(String fileName, SXSSFWorkbook workbook, HttpServletResponse response) {
        logger.info("文件下载xss：fileName{}", JsonUtil.toJson(fileName));
        OutputStream out = null;
        try {
            out = response.getOutputStream();


//            response.setContentType("application/vnd.ms-excel;charset=utf-8"); //改成输出excel文件
//            response.setHeader("Content-disposition","attachment; filename="+fileName+".xlsx" );
//            response.addHeader("Access-Control-Allow-Origin","*");


            response.reset();
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-disposition", "attachment; filename = " + URLEncoder.encode(fileName, "UTF-8"));
//            response.setHeader("Content-disposition", "attachment; filename=" + fileName + ".xlsx");
//            response.setContentType("application/octet-streem");
            response.addHeader("Access-Control-Allow-Origin", "*");
            response.setContentType("application/vnd.ms-excel;charset=utf-8");


            workbook.write(out);

        } catch (Exception ex) {
            logger.error("文件下载出现异常xss：fileName{}", JsonUtil.toJson(fileName), ex);
        } finally {
            if (null != out) {
                try {
                    out.close();
                } catch (Exception ex) {
                    logger.error("文件下载关闭out异常xss：fileName{}", JsonUtil.toJson(fileName), ex);
                }
            }
            if (null != workbook) {
                try {
                    workbook.dispose();
                } catch (Exception ex) {
                    logger.error("文件下载关闭xss异常xss：fileName{}", JsonUtil.toJson(fileName), ex);
                }
            }

        }
    }

    /**
     * first step :第一步 创建excel表头
     * 创建sheet 表头标题
     *
     * @param sheet
     * @param fieldMap
     */
    public static void createSheetTitle(Sheet sheet, LinkedHashMap<String, String> fieldMap) {
        //填充表头
        Row titleRow = sheet.createRow(0);
        int i = 0;
        for (Entry<String, String> m : fieldMap.entrySet()) {
            Cell cell = titleRow.createCell(i);
            cell.setCellValue(m.getValue() + "");
            i++;
        }
    }

    /**
     * 创建下拉框
     *
     * @param workbook
     * @param hiddenSheet
     * @param mainSheet
     * @param items
     * @param rowIndex
     * @param columnIndex
     */
    public static void createDropdownSelectionItems(Workbook workbook,
                                                    Sheet hiddenSheet,
                                                    Sheet mainSheet,
                                                    List<String> items,
                                                    Integer rowIndex,
                                                    Integer columnIndex) {
        if (items.isEmpty()) {
            return;
        }
        // 将选项写入隐藏工作表
        for (int i = 0; i < items.size(); i++) {
            Row row = hiddenSheet.getRow(i);
            if (row == null) {
                row = hiddenSheet.createRow(i);
            }
            Cell cell = row.getCell(columnIndex);
            if (cell == null) {
                cell = row.createCell(columnIndex);
            }
            cell.setCellValue(items.get(i));
        }
        // 创建名称管理器引用
        Name name = workbook.createName();
        name.setNameName(RandomStringUtils.randomAlphabetic(32));
        String reference = hiddenSheet.getSheetName() + "!$" + ((char) ('A' + columnIndex)) + "$1:$" + ((char) ('A' + columnIndex)) + "$" + items.size();
        name.setRefersToFormula(reference);

        // 数据验证辅助对象
        DataValidationHelper helper = mainSheet.getDataValidationHelper();
        DataValidationConstraint constraint = helper.createFormulaListConstraint(name.getNameName());
        DataValidation validation = helper.createValidation(constraint, new CellRangeAddressList(rowIndex, 65535, columnIndex, columnIndex));

        validation.setSuppressDropDownArrow(true);
        // 设置验证错误样式为停止
        validation.setErrorStyle(DataValidation.ErrorStyle.STOP);
        // 显示错误提示框
        validation.setShowErrorBox(true);
        // 设置错误提示标题和内容
        validation.createErrorBox("禁止编辑", "此单元格只能从下拉列表中选择值");
        mainSheet.addValidationData(validation);
    }


    /**
     * 计算formula
     *
     * @param offset   偏移量，如果给0，表示从A列开始，1，就是从B列
     * @param rowId    第几行
     * @param colCount 一共多少列
     * @return 如果给入参 1,1,10. 表示从B1-K1。最终返回 $B$1:$K$1
     */
    public static String getRange(int offset, int rowId, int colCount) {
        char start = (char) ('A' + offset);
        if (colCount <= 25) {
            char end = (char) (start + colCount - 1);
            return "$" + start + "$" + rowId + ":$" + end + "$" + rowId;
        } else {
            char endPrefix = 'A';
            char endSuffix = 'A';
            if ((colCount - 25) / 26 == 0 || colCount == 51) {// 26-51之间，包括边界（仅两次字母表计算）
                if ((colCount - 25) % 26 == 0) {// 边界值
                    endSuffix = (char) ('A' + 25);
                } else {
                    endSuffix = (char) ('A' + (colCount - 25) % 26 - 1);
                }
            } else {// 51以上
                if ((colCount - 25) % 26 == 0) {
                    endSuffix = (char) ('A' + 25);
                    endPrefix = (char) (endPrefix + (colCount - 25) / 26 - 1);
                } else {
                    endSuffix = (char) ('A' + (colCount - 25) % 26 - 1);
                    endPrefix = (char) (endPrefix + (colCount - 25) / 26);
                }
            }
            return "$" + start + "$" + rowId + ":$" + endPrefix + endSuffix + "$" + rowId;
        }
    }
}
