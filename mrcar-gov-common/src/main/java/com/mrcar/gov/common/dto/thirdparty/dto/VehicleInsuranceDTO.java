package com.mrcar.gov.common.dto.thirdparty.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/9/30 09:01
 */
@Data
public class VehicleInsuranceDTO {

    /**
     * 响应code
     */
    @JSONField(name = "code")
    private int code;
    /**
     * 请求流水号
     */
    @JSONField(name = "reqId")
    private String reqId;
    /**
     * 响应状态 200 代表成功响应
     */
    @JSONField(name = "status")
    private int status;
    /**
     * 提示信息
     */
    @JSONField(name = "message")
    private String message;
    /**
     * 日志ID
     */
    @JSONField(name = "serialNo")
    private String serialNo;
    /**
     * 保单信息
     */
    @JSONField(name = "data")
    private InsuranceInfo insuranceInfo;
}
