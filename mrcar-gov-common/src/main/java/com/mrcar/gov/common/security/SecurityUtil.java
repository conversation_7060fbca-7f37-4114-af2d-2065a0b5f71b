package com.mrcar.gov.common.security;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.jwt.JWT;
import cn.hutool.jwt.JWTUtil;
import cn.hutool.jwt.signers.JWTSignerUtil;
import com.izu.cache.core.redis.RedisCache;
import com.izu.framework.exception.ApiException;
import com.izu.framework.resp.InfoCode;
import com.mrcar.gov.common.constant.login.LoginEnum;
import com.mrcar.gov.common.properties.ConfigProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.concurrent.TimeUnit;

public class SecurityUtil {

    private static final String TOKEN_SECRET = "4CKCGVPA2T4LP4TN";

    private static final String TOKEN_KEY = "M_G_T_";
    private static final Logger log = LoggerFactory.getLogger(SecurityUtil.class);

    private static RedisCache redisCache;

    private static final ThreadLocal<LoginUser> THREAD_LOCAL = new InheritableThreadLocal<>();

    /**
     * 登录操作
     *
     * @param loginUser
     * @return
     */
    public static String login(LoginUser loginUser) {
        if (loginUser == null) {
            throw new NullPointerException(" loginUser is null");
        }
        Map<String, Object> claims = BeanUtil.beanToMap(loginUser);
        ConfigProperties cp = SpringUtil.getBean(ConfigProperties.class);
        if (cp == null) {
            throw new ApiException(InfoCode.HTTP_SYSTEM_ERROR);
        }
        String secret = TOKEN_SECRET;
        if (StrUtil.isNotBlank(cp.getTokenSecret())) {
            secret = cp.getTokenSecret();
        }
        String token = JWTUtil.createToken(claims, JWTSignerUtil.hs256(secret.getBytes()));
        THREAD_LOCAL.set(loginUser);
        String key = buildTokenKey(loginUser);
        RedisCache redisCache = getRedisCache();
        if (redisCache == null) {
            return token;
        }

        redisCache.setCacheObject(key, token, cp.getTokenExpire(), TimeUnit.HOURS);
        return token;
    }

    private static String buildTokenKey(LoginUser loginUser) {
       return buildTokenKey(loginUser.getId(), loginUser.getPlat());
    }

    private static String buildTokenKey(Long loginUserId, Integer plat){
        return TOKEN_KEY + plat + StrUtil.DASHED + loginUserId;
    }

    /**
     * 验证token
     *
     * @param token
     * @return
     */
    public static boolean verify(String token) {
        if (StrUtil.isBlank(token)) {
            return false;
        }
        try {
            boolean result = JWTUtil.verify(token, TOKEN_SECRET.getBytes());
            if (result) {
                JWT jwt = JWTUtil.parseToken(token);
                LoginUser loginUser = jwt.getPayloads().toBean(LoginUser.class);
                THREAD_LOCAL.set(loginUser);
                String key = buildTokenKey(loginUser);
                RedisCache redisCache = getRedisCache();
                if (redisCache == null) {
                    log.error("redis 连接失败");
                    return false;
                }
                if (!redisCache.hasKey(key)) {
                    return false;
                }
                ConfigProperties cp = SpringUtil.getBean(ConfigProperties.class);
                if (cp == null) {
                    return true;
                }
                redisCache.setCacheObject(key, token, cp.getTokenExpire(), TimeUnit.HOURS);
            }
            return result;
        } catch (Exception e) {
            throw new ApiException(InfoCode.HTTP_UNAUTHORIZED);
        }
    }

    public static void clean() {
        THREAD_LOCAL.remove();
    }

    /**
     * 获取当前登录用户
     *
     * @return
     */
    public static LoginUser currLoginUser() {
        return THREAD_LOCAL.get();
    }

    /**
     * 登出当前登陆用户
     */
    public static boolean logout() {
        LoginUser loginUser = currLoginUser();
        if (loginUser == null) {
            return false;
        }
        RedisCache redisCache = getRedisCache();
        if (redisCache == null) {
            return false;
        }
        String key = buildTokenKey(loginUser);
        if (!redisCache.hasKey(key)) {
            return false;
        }
        return redisCache.deleteObject(key);
    }


    /**
     * 登出指定的用户 PC 和 小程序都登出
     *
     * @param userId
     * @return
     */
    public static boolean logout(Long userId) {

        String pcToken = buildTokenKey(userId, LoginEnum.LoginPlatformEnum.PC.getCode());

        String miniProgramToken = buildTokenKey(userId, LoginEnum.LoginPlatformEnum.MINIPROGRAM.getCode());


        RedisCache redisCache = getRedisCache();
        if (redisCache == null) {
            return false;
        }

        redisCache.deleteObject(pcToken);

        redisCache.deleteObject(miniProgramToken);
        return true;
    }


    private static RedisCache getRedisCache() {
        if (redisCache != null) {
            return redisCache;
        }
        redisCache = SpringUtil.getBean("redisCache", RedisCache.class);
        return redisCache;
    }
}
