package com.mrcar.gov.common.dto.device.resp;

import java.math.BigDecimal;
import java.util.Date;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;

import lombok.Data;

@Data
public class BackpointDataExportRespDTO {
	
	/**
	 * 返点时间
	 */
	@ExcelProperty(value = {"返点时间"}, index = 0)
    @ColumnWidth(25)
    private Date createDate;
	 /** 经度高德  */
	@ExcelProperty(value = {"经度高德"}, index = 1)
    @ColumnWidth(25)
    private BigDecimal longitude;
    /** 纬度高德  */
	@ExcelProperty(value = {"纬度高德"}, index = 2)
    @ColumnWidth(25)
    private BigDecimal latitude;
	/**
     * 起点地址名称
     */
	@ExcelProperty(value = {"地址"}, index = 3)
    @ColumnWidth(50)
    private String locationName;
	
	/**
	 * 停车点
	 */
	@ExcelProperty(value = {"停车点"}, index = 4)
    @ColumnWidth(25)
	private String  stopPointDesc;
	
    /**
     * 停车时长
     */
	@ExcelProperty(value = {"停车时长"}, index = 5)
    @ColumnWidth(25)
	private String   stopDurationDetail;
	
    /** 方向角描述  */
	@ExcelProperty(value = {"方向角描述"}, index = 6)
    @ColumnWidth(25)
    private String  directionMsg;

}
