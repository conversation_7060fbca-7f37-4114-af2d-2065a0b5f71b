package com.mrcar.gov.common.dto.report;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Objects;

@Data
public class GovUseCarDetail {
    /**
     * 第二级部门名称
     */
    private String secondLevelDeptName;
    /**
     * 第二级部门编码
     */
    private String secondLevelDeptCode;

    /**
     * 第三级部门名称，可能为空
     */
    private String thirdLevelDeptName;

    /**
     * 第三级部门编码
     */
    private String thirdLevelDeptCode;

    /**
     * 单位编码
     */
    private String deptCode;
    /**
     * 单位名称
     */
    private String deptName;

    /**
     * 订单数
     */
    private Integer orderNum;

    /**
     * 无任务订单数
     */
    private Integer noTaskOrderNum;

    /**
     * 无任务订单比例
     */
    private BigDecimal noTaskOrderRatio;

    /**
     * 无任务订单比例，百分比展示
     */
    private String noTaskOrderRatioStr;

    /**
     * 行驶里程（KM）
     */
    private BigDecimal driveDistance;

    /**
     * 行驶时长（小时）
     */
    private BigDecimal driveTime;

    /**
     * 车辆使用率
     */
    private BigDecimal vehicleUseRate;

    /**
     * 车辆使用率，百分比展示
     */
    private String vehicleUseRateStr;

    /**
     * 使用时长率
     */
    private BigDecimal driveTimeRate;

    /**
     * 使用时长率,因为最大值为300%,将其缩放到100%以内
     */
    private BigDecimal driveTimeRateDisplay;

    /**
     * 使用时长率，百分比展示
     */
    private String driveTimeRateStr;

    /**
     * 运行总费用
     */
    private BigDecimal totalFee;

    /**
     * 百公里费用
     */
    private BigDecimal hundredKMFee;

    /**
     * 加油费
     */
    private BigDecimal oilFee;

    /**
     * 加油量
     */
    private BigDecimal oilQuantity;

    /**
     * 过路过桥费
     */
    private BigDecimal tollFee;

    /**
     * 保险费
     */
    private BigDecimal insuranceFee;

    /**
     * 维修保养费
     */
    private BigDecimal maintainFee;

    /**
     * 年检费
     */
    private BigDecimal annualInspectionFee;

    /**
     * 其他费用
     */
    private BigDecimal otherFee;

    /**
     * 车辆数
     */
    private Integer vehicleNum;

    /**
     * 自主品牌车数
     */
    private Integer selfBrandNum;

    /**
     * 自主品牌比例
     */
    private BigDecimal selfBrandRatio;

    /**
     * 自主品牌比例，百分比展示
     */
    private String selfBrandRatioStr;

    /**
     * 轿车
     */
    private Integer sedanNum;

    /**
     * 越野车
     */
    private Integer offRoadNum;

    /**
     * 其他小型客车
     */
    private Integer otherSmallCarNum;

    /**
     * 中型客车
     */
    private Integer mediumCarNum;

    /**
     * 大型客车
     */
    private Integer largeCarNum;

    /**
     * 其他车型
     */
    private Integer otherCarNum;

    // 出车数量
    private Integer outCarNum;

    /**
     * 洗车费
     */
    private BigDecimal carWashFee;


    public BigDecimal getTotalFee() {
        BigDecimal sum = BigDecimal.ZERO;
        if(Objects.nonNull(oilFee)){
            sum = sum.add(oilFee);
        }
        if(Objects.nonNull(tollFee)){
            sum = sum.add(tollFee);
        }
        if(Objects.nonNull(insuranceFee)){
            sum = sum.add(insuranceFee);
        }
        if(Objects.nonNull(maintainFee)){
            sum = sum.add(maintainFee);
        }
        if(Objects.nonNull(annualInspectionFee)){
            sum = sum.add(annualInspectionFee);
        }
        if(Objects.nonNull(otherFee)){
            sum = sum.add(otherFee);
        }
        if(Objects.nonNull(carWashFee)){
            sum = sum.add(carWashFee);
        }
        return sum;
    }

    public void initValue(){
        orderNum = 0;
        noTaskOrderNum = 0;
        driveDistance = BigDecimal.ZERO;
        driveTime = BigDecimal.ZERO;
        vehicleUseRate = BigDecimal.ZERO;
        driveTimeRate = BigDecimal.ZERO;
        totalFee = BigDecimal.ZERO;
        hundredKMFee = BigDecimal.ZERO;
        oilFee = BigDecimal.ZERO;
        oilQuantity = BigDecimal.ZERO;
        tollFee = BigDecimal.ZERO;
        insuranceFee = BigDecimal.ZERO;
        maintainFee = BigDecimal.ZERO;
        annualInspectionFee = BigDecimal.ZERO;
        otherFee = BigDecimal.ZERO;
        carWashFee = BigDecimal.ZERO;
        vehicleNum = 0;
        selfBrandNum = 0;
        sedanNum = 0;
        offRoadNum = 0;
        otherSmallCarNum = 0;
        mediumCarNum = 0;
        largeCarNum = 0;
        otherCarNum = 0;
        outCarNum = 0;
    }


}
