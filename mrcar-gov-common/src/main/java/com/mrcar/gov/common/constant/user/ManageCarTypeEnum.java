package com.mrcar.gov.common.constant.user;

import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR> on  2024/11/16 下午3:13
 */
@Getter
public enum ManageCarTypeEnum {

    GOVERNMENT_AFFAIRS_BUREAU(1, "机关事务部门"),
    FINANCE_DEPARTMENT(2, "财政部门");

    private Integer code;
    private String description;

    ManageCarTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }


    public static ManageCarTypeEnum fromCode(Integer code) {
        for (ManageCarTypeEnum type : ManageCarTypeEnum.values()) {
            if (Objects.equals(type.getCode(), code)) {
                return type;
            }
        }
        return null;
    }

    //获取描述
    public static String getDesByCode(Integer code) {
        for (ManageCarTypeEnum type : ManageCarTypeEnum.values()) {
            if (Objects.equals(type.getCode(), code)) {
                return type.getDescription();
            }
        }
        return "";
    }

}
