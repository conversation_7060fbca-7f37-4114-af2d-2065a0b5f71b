package com.mrcar.gov.common.constant.workflow;

import com.izu.framework.resp.InfoCode;

import java.text.MessageFormat;

/**
 * 工作流 错误码枚举类
 * <p>
 * 工作流系统，使用 1-009-000-000 段
 */
public interface WorkflowErrorCode {
    static InfoCode render(InfoCode code, Object... args) {
        return InfoCode.build(code.getStatus(), MessageFormat.format(code.getMsg(), args));
    }

    // ==========  通用流程处理 模块 1-009-000-000 ==========
    InfoCode HIGHLIGHT_IMG_ERROR = InfoCode.build(1009000002, "获取高亮流程图异常");

    // ========== OA 流程模块 1-009-001-000 ==========
    InfoCode OA_LEAVE_NOT_EXISTS = InfoCode.build(1009001001, "请假申请不存在");
    InfoCode OA_PM_POST_NOT_EXISTS = InfoCode.build(1009001002, "项目经理岗位未设置");
    InfoCode OA_DEPART_PM_POST_NOT_EXISTS = InfoCode.build(1009001009, "部门的项目经理不存在");
    InfoCode OA_BM_POST_NOT_EXISTS = InfoCode.build(1009001004, "部门经理岗位未设置");
    InfoCode OA_DEPART_BM_POST_NOT_EXISTS = InfoCode.build(1009001005, "部门的部门经理不存在");
    InfoCode OA_HR_POST_NOT_EXISTS = InfoCode.build(1009001006, "HR岗位未设置");
    InfoCode OA_DAY_LEAVE_ERROR = InfoCode.build(1009001007, "请假天数必须>=1");

    // ========== 流程模型 1-009-002-000 ==========
    InfoCode MODEL_KEY_EXISTS = InfoCode.build(1009002000, "已经存在流程标识为【{0}】的流程");
    InfoCode MODEL_NOT_EXISTS = InfoCode.build(1009002001, "流程模型不存在");

    InfoCode EXTEND_MODEL_NOT_EXISTS = InfoCode.build(1009002007, "继承配置模型不存在");


    InfoCode MODEL_KEY_VALID = InfoCode.build(1009002002, "流程标识格式不正确，需要以字母或下划线开头，后接任意字母、数字、中划线、下划线、句点！");
    InfoCode MODEL_DEPLOY_FAIL_FORM_NOT_CONFIG = InfoCode.build(1009002003, "部署流程失败，原因：流程表单未配置，请点击【修改流程】按钮进行配置");
    InfoCode MODEL_DEPLOY_FAIL_TASK_ASSIGN_RULE_NOT_CONFIG = InfoCode.build(1009002004, "部署流程失败，" +
            "原因：用户任务({0})未配置分配规则，请点击【分配规则】按钮进行配置");
    InfoCode MODEL_CATEGORY_NOT_ALLOW = InfoCode.build(1009002005, "流程分类不支持");
    InfoCode MODEL_BUSINESS_TYPE_EXISTS = InfoCode.build(1009002006, "已经存在业务类型为【{0}】的流程");
    InfoCode MODEL_DEPLOY_FAIL_TASK_INFO_EQUALS = InfoCode.build(1009003005, "流程定义部署失败，原因：信息未发生变化");
    // ========== 流程定义 1-009-003-000 ==========
    InfoCode PROCESS_DEFINITION_KEY_NOT_MATCH = InfoCode.build(1009003000, "流程定义的标识期望是({0})，当前是({1})，请修改 BPMN 流程图");
    InfoCode PROCESS_DEFINITION_NAME_NOT_MATCH = InfoCode.build(1009003001, "流程定义的名字期望是({0})，当前是({1})，请修改 BPMN 流程图");
    InfoCode PROCESS_DEFINITION_NOT_EXISTS = InfoCode.build(1009003002, "流程定义不存在");
    InfoCode PROCESS_DEFINITION_IS_SUSPENDED = InfoCode.build(1009003003, "流程已修改，请重新提交");
    InfoCode PROCESS_DEFINITION_BPMN_MODEL_NOT_EXISTS = InfoCode.build(1009003004, "流程定义的模型不存在");
    InfoCode PROCESS_DEFINITION_BPMN__ILLEGAL = InfoCode.build(1009003006, "设计流程不合法，请修改 BPMN 流程图");
    InfoCode PROCESS_DEFINITION_BPMN__EMPTY = InfoCode.build(1009003007, "BPMN 流程图不能为空");
    InfoCode PROCESS_DEFINITION_BPMN_ILLEGAL_ERROR = InfoCode.build(1009003008, "设计流程不合法，请重新绘制流程图");
    InfoCode PROCESS_DEFINITION_BPMN_ILLEGAL_ERROR_REASON = InfoCode.build(1009003009, "设计流程不合法({0})，请重新绘制流程图");

    // ========== 流程实例 1-009-004-000 ==========
    InfoCode PROCESS_INSTANCE_NOT_EXISTS = InfoCode.build(1009004000, "流程实例不存在");
    InfoCode PROCESS_INSTANCE_CANCEL_FAIL_NOT_EXISTS = InfoCode.build(1009004001, "流程取消失败，流程不处于运行中");
    InfoCode PROCESS_INSTANCE_CANCEL_FAIL_NOT_SELF = InfoCode.build(1009004002, "流程取消失败，该流程不是你发起的");
    InfoCode PROCESS_INSTANCE_START_FAIL_IN_PROCESS = InfoCode.build(1009004003, "流程发起失败，该流程已处于运行中");

    // ========== 流程任务 1-009-005-000 ==========
    InfoCode TASK_COMPLETE_FAIL_NOT_EXISTS = InfoCode.build(1009005000, "审批任务失败，原因：该任务不处于未审批");
    InfoCode TASK_COMPLETE_FAIL_ASSIGN_NOT_SELF = InfoCode.build(1009005001, "审批任务失败，原因：该任务的审批人不是你");
    InfoCode TASK_COMPLETE_FAIL_HIS_KEY_NOT_EXISTS = InfoCode.build(1009005002, "审批任务失败，原因：回退节点不存在");

    // ========== 流程任务分配规则 1-009-006-000 ==========
    InfoCode TASK_ASSIGN_RULE_EXISTS = InfoCode.build(1009006000, "流程({0}) 的任务({1}) 已经存在分配规则");
    InfoCode TASK_ASSIGN_RULE_NOT_EXISTS = InfoCode.build(1009006001, "流程任务分配规则不存在");
    InfoCode TASK_UPDATE_FAIL_NOT_MODEL = InfoCode.build(1009006002, "只有流程模型的任务分配规则，才允许被修改");
    InfoCode TASK_CREATE_FAIL_NO_CANDIDATE_USER = InfoCode.build(1009006003, "操作失败，原因：找不到任务的审批人！");
    InfoCode TASK_ASSIGN_SCRIPT_NOT_EXISTS = InfoCode.build(1009006004, "操作失败，原因：任务分配脚本({0}) 不存在");

    // ========== 动态表单模块 1-009-010-000 ==========
    InfoCode FORM_NOT_EXISTS = InfoCode.build(1009010000, "动态表单不存在");
    InfoCode FORM_FIELD_REPEAT = InfoCode.build(1009010001, "表单项({0}) 和 ({1}) 使用了相同的字段名({2})");

    // ========== 用户组模块 1-009-011-000 ==========
    InfoCode USER_GROUP_NOT_EXISTS = InfoCode.build(1009011000, "用户组不存在");
    InfoCode USER_GROUP_IS_DISABLE = InfoCode.build(1009011001, "名字为【{0}】的用户组已被禁用");
    InfoCode TASK_ID_NOT_EXIST = InfoCode.build(1009011002, "任务编号不能为空");
    InfoCode MODEL_ID_NOT_EXIST = InfoCode.build(1009011003, "参数id不能为空");

    InfoCode BATCH_TASK_COMPLETE_FAIL_NOT_EXISTS = InfoCode.build(1009011004, "审批任务失败，原因：审批任务中存在不是未审批状态的任务");
    InfoCode TASK_COMPLETE_NOT_EXISTS = InfoCode.build(1009011005, "批量审批任务失败，原因：审批任务不存在");

    InfoCode TASK_NOT_EXISTS = InfoCode.build(1009011006, "审批任务不存在");

    InfoCode MODEL_CONNECT_SUB_UPDATE = InfoCode.build(1009011007, "该流程存在其他已激活状态的子流程关联，不允许将激活状态修改为关闭");
    InfoCode MODEL_CONNECT_SUB_EXISTS = InfoCode.build(1009011008, "该流程存在其他子流程关联，不允许删除");

    InfoCode COPY_LIST_CAN_NOT_INCLUDE_START_USER = InfoCode.build(1009011009, "抄送人列表不能包含流程发起人");
    InfoCode MODEL_ACTIVE_SUB_ERROR = InfoCode.build(1009011010, "该流程关联父流程目前处于未激活，请先激活父流程再进行该流程激活");

    InfoCode COPY_LIST_EXCEED_LIMIT = InfoCode.build(1009011011, "抄送人列表不能超过{0}人");

    InfoCode MAIL_LIST_EXCEED_LIMIT = InfoCode.build(1009011012, "邮件列表不能超过{0}个");

    InfoCode MODEL_CONNECT_ERROR = InfoCode.build(1009011013, "该流程已被其他流程所关联，不可以再关联其他流程");
    InfoCode DO_NOT_INPUT_CONTAINS_EMOJI_REASON = InfoCode.build(1009011014, "请不要输入表情符号");

    // ========== BPM扩展模块 ==========
    InfoCode BPM_FORM_FIELD_REPETITION = InfoCode.build(9001, "表单项({0}) 和 ({1}) 使用了相同的字段名({2})");
    InfoCode BPM_FORM_NOT_EXISTS = InfoCode.build(9002, "流程表单不存在");
    InfoCode BPM_FORM_UPDATE_FAIL = InfoCode.build(9003, "流程表单更新失败");
    InfoCode BPM_USER_GROUP_NOT_EXISTS = InfoCode.build(9021, "用户组不存在");
    InfoCode BPM_MODEL_NOT_EXISTS = InfoCode.build(9031, "流程模型不存在");
}
