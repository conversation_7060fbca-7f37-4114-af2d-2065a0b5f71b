package com.mrcar.gov.common.eventbus.events;

import com.mrcar.gov.common.eventbus.Event;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> on  2024/11/18 下午5:14
 */
@Data
public class ManageCarStructEven implements Event {


    private List<ManagerStruct> managerStructs;


    @Data
    @AllArgsConstructor
    public static class ManagerStruct {

        /**
         * 被管理部门的编码
         */
        private String structCode; //所有人


        /**
         * 管车部门的编码
         */
        private String manageStructCode;

    }

}
