package com.mrcar.gov.common.util;

import cn.hutool.core.util.HexUtil;
import com.itextpdf.text.Document;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.Image;
import com.itextpdf.text.Rectangle;
import com.itextpdf.text.pdf.*;
import com.izu.framework.exception.ApiException;
import com.izu.framework.resp.InfoCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.pdfbox.io.MemoryUsageSetting;
import org.apache.pdfbox.multipdf.PDFMergerUtility;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.ImageType;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.URL;
import java.util.List;
import java.util.*;
import java.util.stream.Collectors;

/**
 * pdf工具类
 *
 * <AUTHOR>
 * @date 2020年4月13日
 */
@Slf4j
public class PdfUtils {

    private static final String PDF_FILE_HEADER = "255044462D312E";
    private static final int PDF_FILE_HEADER_LENTH = PDF_FILE_HEADER.length();

    /**
     * 判断该字节流是否为pdf文件
     *
     * @param buffer
     * @return
     */
    public static boolean isPdfFile(byte[] buffer) {
        String hex = HexUtil.encodeHexStr(buffer, false);
        // 截止
        String fileType = hex.substring(0, PDF_FILE_HEADER_LENTH);
        return PDF_FILE_HEADER.equals(fileType);
    }

    /**
     * pdf转图片
     *
     * @param pdfBuffer
     * @return
     */
    public static byte[] pdf2Image(byte[] pdfBuffer, String imgType) {

        if (!isPdfFile(pdfBuffer)) {
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "pdf转换成图片失败，不是pdf文件");

        }

        try (
                PDDocument doc = PDDocument.load(pdfBuffer); //
                ByteArrayOutputStream baos = new ByteArrayOutputStream()) {

            PDFRenderer renderer = new PDFRenderer(doc);
            // pdf页数
            int pageCount = doc.getNumberOfPages();

            List<BufferedImage> bufferedImages = new ArrayList<>();
            for (int i = 0; i < pageCount; i++) {
                BufferedImage image = renderer.renderImageWithDPI(i, 144, ImageType.RGB); // Windows native DPI
                bufferedImages.add(image);
            }

            // 如果生成多张图片，按照高度叠加合并一起
            BufferedImage bufferedImage = mergeImages(bufferedImages);

            // 写入字节流中
            byte[] imageBuffer = new byte[baos.size()];
            ImageIO.write(bufferedImage, imgType, baos);

            imageBuffer = baos.toByteArray();
            baos.flush();

            return imageBuffer;
        } catch (Exception e) {
            log.error("pdf2Image is fail: {}", e.getMessage(), e);
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "pdf2Image is fail: " + e.getMessage());

        }
    }

    /**
     * 根据pdf模板以及值填充生成pdf
     *
     * @param dataMap
     * @param templateName
     * @param loadFont
     * @return
     */
    public static byte[] createByTemplate(Map<String, Object> dataMap, String templateName, boolean loadFont) {
        templateName = "template/" + templateName + ".pdf";
        Resource resource = new ClassPathResource(templateName);
        if (!resource.exists()) {
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "找不到模板");

        }

        try (InputStream io = resource.getInputStream(); //

             ByteArrayOutputStream bos = new ByteArrayOutputStream()) {
            PdfReader reader = new PdfReader(io);
            PdfStamper stamper = new PdfStamper(reader, bos);
            // 获取模板中所有编辑域
            AcroFields acFields = stamper.getAcroFields();
            // 生成字体
            BaseFont font = createFont(loadFont);

            // 创建map存放image编辑域
            Map<String, Object> imageMap = new HashMap<>();
            for (String key : dataMap.keySet()) {
                // 判断编辑域是否是以“image”开头
                if (key.contains("image") && key.indexOf("image") > -1) {
                    String imageUrl = dataMap.get(key).toString();
                    log.debug("图片地址：" + imageUrl);
                    if (StringUtils.isNotBlank(imageUrl)) {
                        // 说明此编辑域需要插入图片
                        imageMap.put(key, imageUrl);
                    }
                } else {
                    // 设置非图片的编辑域的值
                    acFields.setFieldProperty(key, "textfont", font, null);
                    acFields.setField(key, dataMap.get(key) == null ? "" : dataMap.get(key).toString());
                }
            }

            // 往pdf中批量插入图片
            for (String fieldName : imageMap.keySet()) {
                String imageUrl = imageMap.get(fieldName).toString();
                insertImage(stamper, acFields, fieldName, imageUrl);
            }

            // 如果为false那么生成的PDF文件还能编辑，一定要设为true
            stamper.setFormFlattening(true);

            stamper.close();
            reader.close();
            bos.flush();
            return bos.toByteArray();
        } catch (Exception e) {
            log.error("生成pdf出错: " + e.getMessage(), e);
        }
        return null;
    }

    /**
     * 多张图片合并成一张
     *
     * @param bufferedImages
     * @return
     */
    private static BufferedImage mergeImages(List<BufferedImage> bufferedImages) {
        if (CollectionUtils.isEmpty(bufferedImages)) {
            log.warn("image is null!");
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "mergeImage is fail, image is null!");

        }

        BufferedImage bufferedImage0 = bufferedImages.get(0);
        if (bufferedImage0 == null) {
            log.warn("image0 is null!");
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "mergeImage is fail, image0 is null!");
        }

        if (bufferedImages.size() == 1) {
            return bufferedImage0;
        }

        int width = bufferedImage0.getWidth();
        // 所有图片的高度汇总，即为新图片的高度
        int height = (int) bufferedImages.stream().mapToInt(BufferedImage::getHeight).summaryStatistics().getSum();

        BufferedImage bufferedImage = new BufferedImage(width, height, BufferedImage.TYPE_INT_BGR);
        Graphics g = bufferedImage.getGraphics();

        // 当前写到什么高度
        int tempHeight = 0;

        for (int i = 0; i < bufferedImages.size(); i++) {
            BufferedImage bufferedImagei = bufferedImages.get(i);
            g.drawImage(bufferedImagei, 0, tempHeight, null);
            tempHeight += bufferedImagei.getHeight();
        }

        return bufferedImage;
    }

    /**
     * pdf模板动态插入图片
     *
     * @param pdfStamper
     * @param acFields
     * @param fieldName
     * @param imageUrl
     * @return
     */
    public static void insertImage(PdfStamper pdfStamper, AcroFields acFields, String fieldName, String imageUrl) {
        if (StringUtils.isBlank(imageUrl)) {
            return;
        }
        try {
            // 获取图片
            List<AcroFields.FieldPosition> list = acFields.getFieldPositions(fieldName);
            Image image = loadImage(imageUrl);

            for (int i = 0; i < list.size(); i++) {
                // 获取所在页数
                PdfContentByte under = pdfStamper.getOverContent(list.get(i).page);
                // 获取位置
                Rectangle signRect = list.get(i).position;
                image.setAbsolutePosition(signRect.getLeft(), signRect.getBottom());
                image.scaleToFit(signRect.getWidth(), signRect.getHeight());
                under.addImage(image);
            }
        } catch (Exception e) {
            log.error("插入图片失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 创建pdf字体
     *
     * @param loadFont
     * @return
     * @throws DocumentException
     * @throws IOException
     */
    private static BaseFont createFont(boolean loadFont) throws DocumentException, IOException {
        BaseFont font = null;
        if (loadFont) {
            // simsun.ttc位置 新宋体
            String fontPath = "/template/simsun.ttc";
            font = BaseFont.createFont(fontPath + ",1", BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
        } else {
            // pdf默认字体
            font = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
        }
        return font;
    }

    /**
     * 加载图片
     *
     * @param imageUrl
     * @return
     */
    private static Image loadImage(String imageUrl) {
        Image image = null;
        try {
            if (imageUrl.contains("http")) {
                // 加载网络图片
                log.debug("加载网络图片, imageUrl: {}", imageUrl);
                image = Image.getInstance(new URL(imageUrl));
            } else {
                log.debug("加载本地图片, imageUrl: {}", imageUrl);
                image = loadLocalImage(imageUrl);
            }
        } catch (Exception e) {
            log.error("加载图片出错: {}", e.getMessage());
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "加载图片出错: " + e.getMessage());
        }
        return image;
    }

    /**
     * 加载本地图片
     *
     * @param imageUrl
     * @return
     */
    private static Image loadLocalImage(String imageUrl) {
        try (ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream(); //
             InputStream inputStream = new FileInputStream(imageUrl)) {
            IOUtils.copy(inputStream, byteArrayOutputStream);
            byteArrayOutputStream.flush();
            return Image.getInstance(byteArrayOutputStream.toByteArray());
        } catch (Exception e) {
            log.error("加载本地图片出错: {}", e.getMessage());
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "加载图片出错: " + e.getMessage());
        }
    }

    public static byte[] createMorePageByTemplate(List<Map<String, Object>> dataMapList, String templateName, boolean loadFont) {
        templateName = "template/" + templateName + ".pdf";
        Resource resource = new ClassPathResource(templateName);
        if (!resource.exists()) {
            throw new ApiException(InfoCode.HTTP_PARAM_INVALID, "找不到模板");

        }

        try {
            int size = dataMapList.size();
            // 加载pdf模板
            ByteArrayOutputStream template = new ByteArrayOutputStream();
            IOUtils.copy(resource.getInputStream(), template);

            ByteArrayOutputStream bos[] = new ByteArrayOutputStream[size];
            for (int i = 0; i < size; i++) {
                Map<String, Object> dataMap = dataMapList.get(i);
                bos[i] = new ByteArrayOutputStream();
                PdfReader reader = new PdfReader(template.toByteArray());
                // 设置页面大小
                PdfStamper stamper = new PdfStamper(reader, bos[i]);
                // 获取模板中所有编辑域
                AcroFields acFields = stamper.getAcroFields();
                // 生成字体
                BaseFont font = createFont(loadFont);

                // 创建map存放image编辑域
                Map<String, Object> imageMap = new HashMap<>();
                for (String key : dataMap.keySet()) {
                    // 判断编辑域是否是以“image”开头
                    if (key.contains("image")) {
                        String imageUrl = dataMap.get(key).toString();
                        log.debug("图片地址：" + imageUrl);
                        if (StringUtils.isNotBlank(imageUrl)) {
                            // 说明此编辑域需要插入图片
                            imageMap.put(key, imageUrl);
                        }
                    } else {
                        // 设置非图片的编辑域的值
                        acFields.setFieldProperty(key, "textfont", font, null);
                        acFields.setFieldProperty(key, "textsize", Float.parseFloat(8 + ""), null);
                        acFields.setField(key, dataMap.get(key) == null ? "" : dataMap.get(key).toString());
                    }
                }

                // 往pdf中批量插入图片
                for (String fieldName : imageMap.keySet()) {
                    String imageUrl = imageMap.get(fieldName).toString();
                    insertImage(stamper, acFields, fieldName, imageUrl);
                }

                // 如果为false那么生成的PDF文件还能编辑，一定要设为true
                stamper.setFormFlattening(true);
                stamper.close();
            }

            // 合并多个pdf
            PDFMergerUtility merger = new PDFMergerUtility();
            // 添加页面资源
            merger.addSources(Arrays.stream(bos)
                    .map(s -> new ByteArrayInputStream(s.toByteArray()))
                    .collect(Collectors.toList()));

            ByteArrayOutputStream output = new ByteArrayOutputStream();
            merger.setDestinationStream(output);
            merger.mergeDocuments(MemoryUsageSetting.setupMainMemoryOnly());

            return output.toByteArray();

        } catch (Exception e) {
            log.error("生成pdf出错: " + e.getMessage(), e);
        }

        return null;
    }

    /**
     * 将多个pdf合并成一个pdf文件
     *
     * @param pdfFilesPath  全路径
     * @param mergeFilePath 全路径
     * @auther: YSL
     * @date: 2022/8/2 16:46
     */
    public static Boolean mergePdf(List<String> pdfFilesPath, String mergeFilePath) {
        Document document = null;
        ByteArrayOutputStream os = null;
        try {
            // 获取纸张大小并实例化一个新的空文档, 例如 A5 纸
            document = new Document(new PdfReader(pdfFilesPath.get(0)).getPageSize(1));
            os = new ByteArrayOutputStream();
            // 实例化复制工具
            final PdfCopy copy = new PdfCopy(document, os);
            // 打开文档准备写入内容
            document.open();
            // 循环所有pdf文件
            for (String s : pdfFilesPath) {
                // 读取pdf
                final PdfReader reader = new PdfReader(new FileInputStream(s));
                copy.addDocument(reader);
                copy.freeReader(reader);
                reader.close();
            }
            //输出到指定目录文件中
            FileOutputStream fos = new FileOutputStream(mergeFilePath);
            fos.write(os.toByteArray());
            fos.close();
            copy.close();

            return true;
        } catch (IOException | DocumentException e) {
            return false;
        } finally {
            if (document != null) {
                document.close();
            }
            if (os != null) {
                try {
                    os.close();
                } catch (IOException e) {
                }
            }
        }
    }
}

