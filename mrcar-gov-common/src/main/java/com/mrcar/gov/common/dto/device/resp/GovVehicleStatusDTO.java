package com.mrcar.gov.common.dto.device.resp;

import java.math.BigDecimal;
import java.util.Date;

import org.springframework.data.annotation.Id;

import com.mrcar.gov.common.dto.tbox.TboxLogReqDTO;

import lombok.Data;

@Data
public class GovVehicleStatusDTO {

	
	@Id
    private String id;

    /**
     * 设备上报时间
     */
    private Date timestamp;

    /**
     * 设备元信息
     */
    private GpsDeviceMetaDTO deviceMeta;

    /*动力状态*/
    private Integer powerStatus;
    /*动力状态详情*/
    private String powerStatusMsg;
    /*钥匙状态*/
    private Integer keyStatus;
    /*钥匙状态详情*/
    private String keyStatusMsg;
    /*钥匙插入状态*/
    private Integer keyInsertionStatus;
    /*钥匙插入状态详情*/
    private String keyInsertionStatusMsg;
    /*左前门状态*/
    private Integer doorLeftFor;
    /*左后门状态*/
    private Integer doorLeftBeh;
    /*右前门状态*/
    private Integer doorRightFor;
    /*右后门状态*/
    private Integer doorRightBeh;
    /*车门状态*/
    private Integer doorStatus;
    /*车门状态详情*/
    private String doorStatusMsg;
    /*后尾箱状态*/
    private Integer rearTailboxStatus;
    /*后尾箱状态详情*/
    private String rearTailboxStatusMsg;
    /*左前车门锁状态*/
    private Integer doorLockLeftFor;
    /*左后车门锁状态*/
    private Integer doorLockLeftBeh;
    /*右前车门锁状态*/
    private Integer doorLockRightFor;
    /*右后车门锁状态*/
    private Integer doorLockRightBeh;
    /*车门锁状态*/
    private Integer doorLockStatus;
    /*车门锁状态详情*/
    private String doorLockStatusMsg;
    /*左前车窗状态*/
    private Integer windowLeftFor;
    /*右前车窗状态*/
    private Integer windowLeftBeh;
    /*右前车窗状态*/
    private Integer windowRightFor;
    /*右后车窗状态*/
    private Integer windowRightBeh;
    /*车窗状态*/
    private Integer windowStatus;
    /*车窗状态详情*/
    private String windowStatusMsg;
    /*近光灯*/
    private Integer ligthLow;
    /*远光灯*/
    private Integer lightHead;
    /*示宽灯*/
    private Integer lightWide;
    /*紧急灯*/
    private Integer lightEmer;
    /*前雾灯*/
    private Integer fogFor;
    /*后雾灯*/
    private Integer fogBeh;
    /*车灯状态*/
    private Integer lampStatus;
    /*车灯状态详情*/
    private String lampStatusMsg;
    /*天窗状态*/
    private Integer sunroofStatus;
    /*天窗状态详情*/
    private String sunroofStatusMsg;
    /*档位*/
    private Integer gearsStatus;
    /*档位详情*/
    private String gearsStatusMsg;
    /*驻车制动*/
    private Integer parkingStatus;
    /*驻车制动详情*/
    private String parkingStatusMsg;
    /*电瓶电压*/
    private BigDecimal batteryStatus;
    /*转速*/
    private Integer rotationRate;
    /*车速*/
    private Integer speed;
    /*总里程*/
    private Integer totalMileage;
    /*剩余油量/电量, 百分比*/
    private Integer remainingFuel;
    /*续航里程*/
    private Integer enduranceMileage;
    /*引擎状态*/
    private Integer engineStatus;
    /*引擎状态详情*/
    private String engineStatusMsg;
    /*上报时间*/
    private Date createDate;
    
    
}
