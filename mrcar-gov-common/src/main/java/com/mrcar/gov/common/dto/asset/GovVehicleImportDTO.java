package com.mrcar.gov.common.dto.asset;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.mrcar.gov.common.dto.asset.request.VehicleConfigurationRequestDTO;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/11/14 15:42
 */
@Data
public class GovVehicleImportDTO {
    /**
     * 车辆编码,编辑时传值，新增不用传值
     */
    @ExcelIgnore
    private String vehicleNo;

    /**
     * 车牌号
     */
    @ExcelProperty({"车牌号"})
    @ColumnWidth(20)
    private String vehicleLicense;

    /**
     * 车架号
     */
    @ExcelProperty({"车架号"})
    @ColumnWidth(20)
    private String vehicleVin;

    /**
     * 使用性质
     */
    @ExcelProperty({"使用性质"})
    @ColumnWidth(20)
    private String useAttributeName;

    @ExcelIgnore
    private Integer useAttribute;

    /**
     * 车辆类型
     */
    @ExcelIgnore
    private Integer vehicleType;

    @ExcelProperty({"车辆类型"})
    @ColumnWidth(20)
    private String vehicleTypeStr;

    /**
     * 车辆品牌CODE码
     */
    @ExcelIgnore
    private Integer vehicleBrandId;

    /**
     * 车辆品牌
     */
    @ExcelProperty({"车辆品牌"})
    @ColumnWidth(20)
    private String vehicleBrandName;

    /**
     * 车系code码
     */
    @ExcelIgnore
    private String vehicleModelCode;

    /**
     * 车系名
     */
    @ExcelProperty({"车系"})
    @ColumnWidth(20)
    private String vehicleSeriesName;

    @ExcelIgnore
    private Integer vehicleSeriesId;

    /**
     * 车辆所有人
     */
    @ExcelIgnore
    private String vehicleBelongDeptCode;
    @ExcelIgnore
    private Integer vehicleBelongDeptId;

    /**
     * 车辆所有人
     */
    @ExcelProperty({"车辆所有人"})
    @ColumnWidth(20)
    private String vehicleBelongDeptLevelName;
    @ExcelIgnore
    private String vehicleBelongDeptName;

    /**
     * 车辆使用人-单位
     */
    @ExcelIgnore
    private String vehicleUseDeptCode;
    @ExcelIgnore
    private Integer vehicleUseDeptId;


    /**
     * 车辆使用人-单位
     */
    @ExcelIgnore
    private String vehicleUseDeptName;
    @ExcelProperty({"车辆使用人"})
    @ColumnWidth(20)
    private String vehicleUseDeptLevelName;


    /**
     * 行驶证注册日期
     */
    @ExcelIgnore
    private Date registerDate;
    @ExcelProperty({"注册日期（格式：yyyy-mm-dd）"})
    @ColumnWidth(20)
    private String registerDateStr;

    /**
     * 状态码；;1:可使用;2:整备中;3:维修中;4:待处置;5:已处置;6:已退出(购买服务已结束);
     */
    @ExcelIgnore
    private Integer vehicleStatus;
    @ExcelProperty({"车辆状态"})
    @ColumnWidth(20)
    private String vehicleStatusStr;

    /**
     * 车辆来源码：1:购置;2:接受捐赠;3:划拨;4:调货
     */
    @ExcelIgnore
    private Integer vehicleSource;
    @ExcelProperty({"车辆来源"})
    @ColumnWidth(20)
    private String vehicleSourceStr;
    /**
     * 编制类型码 1实有车辆 2编制内购买服务
     */
    @ExcelIgnore
    private Integer preparationType;
    @ExcelProperty({"编制类型"})
    @ColumnWidth(20)
    private String preparationTypeStr;
    /**
     * 购买服务供应商编码
     */
    @ExcelIgnore
    private String supplierServiceCode;

    /**
     * 购买服务供应商名称
     */
    @ExcelProperty({"购买服务供应商"})
    @ColumnWidth(20)
    private String supplierServiceName;

    /**
     * 车辆性质码 1.分散管理、2.集中管理
     */
    @ExcelIgnore
    private Integer vehicleAttribute;
    @ExcelProperty({"车辆性质"})
    @ColumnWidth(20)
    private String vehicleAttributeStr;


    /**
     * 车辆使用部门-内部部门
     */
    @ExcelIgnore
    private String vehicleUseStructCode;
    @ExcelIgnore
    private Integer vehicleUseStructId;

    /**
     * 车辆使用部门-内部部门
     */
    @ExcelProperty({"车辆使用部门"})
    @ColumnWidth(20)
    private String vehicleUseStructLevelName;
    @ExcelIgnore
    private String vehicleUseStructName;
    /**
     * 车辆管理单位
     */
    @ExcelIgnore
    private String vehicleManageDeptCode;
    /**
     * 车辆管理单位
     */
    @ExcelIgnore
    private String vehicleManageDeptName;

    /**
     * 发动机号
     */
    @ExcelProperty({"发动号"})
    @ColumnWidth(20)
    private String engineNum;

    /**
     * 是否喷涂标识 1 是 0 否
     */
    @ExcelIgnore
    private Integer sprayLogoFlag;

    @ExcelProperty({"是否喷涂标识"})
    @ColumnWidth(20)
    private String sprayLogoFlagStr;

    /**
     * 配置日期
     */
    @ExcelIgnore
    private Date configurationDate;

    @ExcelProperty({"配置日期"})
    @ColumnWidth(20)
    private String configurationDateStr;

    /**
     * 配置款
     */
    @ExcelIgnore
    private String configurationVersionCode;

    /**
     *配置款
     */
    @ExcelProperty({"配置款"})
    @ColumnWidth(20)
    private String configurationVersionName;

    /**
     * 发动机排量
     */
    @ExcelProperty({"发动机排量（L）"})
    @ColumnWidth(20)
    private String outputVolume;

    /**
     * 燃料类型：1汽油 2柴油 3纯电动 4油混 5汽液双燃料 6电混 7甲醇 8天然气 9混合动力 10轻混动力 11插电式混动 12增程式混动
     */
    @ExcelIgnore
    private Integer fuelType;
    @ExcelProperty({"动力类型"})
    @ColumnWidth(20)
    private String fuelTypeStr;

    /**
     * 燃料号
     */
    @ExcelProperty({"燃油标号"})
    @ColumnWidth(20)
    private String fuelNo;


    /**
     * 变速箱类型
     */
    @ExcelIgnore
    private Integer gearboxType;
    @ExcelProperty({"变速箱类型"})
    @ColumnWidth(20)
    private String gearboxTypeStr;

    /**
     * 油箱容量
     */
    @ExcelProperty({"油箱容积（L）"})
    @ColumnWidth(20)
    private String oilTankVolume;

    /**
     * 核定载人数量
     */
    @ExcelProperty({"核定载人数量"})
    @ColumnWidth(20)
    private String approvedPassengerSum;

    /**
     * 车身颜色
     */
    @ExcelProperty({"车辆颜色"})
    @ColumnWidth(20)
    private String vehicleBodyColor;


    /**
     * 裸车价格
     */
    @ExcelProperty({"裸车价格（元）"})
    @ColumnWidth(20)
    private String vehicleBarePrice;

    /**
     * 备注
     */
    @ExcelProperty({"备注"})
    @ColumnWidth(20)
    private String remark;


    /**
     * 终端设备号
     */
    @ExcelIgnore
    private String deviceNo;

    /**
     * 上一次年检日期；格式：yyyy-MM-dd
     */
    @ExcelIgnore
    private Date lastInspectDate;

    /**
     * 错误信息
     */
    @ExcelProperty({"错误信息"})
    @ColumnWidth(20)
    private String errorMsg;


}
