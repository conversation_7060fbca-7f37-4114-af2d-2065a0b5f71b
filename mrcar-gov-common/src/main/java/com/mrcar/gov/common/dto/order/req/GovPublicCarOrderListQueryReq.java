package com.mrcar.gov.common.dto.order.req;

import com.mrcar.gov.common.dto.PageParamDTO;
import lombok.Data;

import java.util.List;

@Data
public class GovPublicCarOrderListQueryReq extends PageParamDTO {
    /**
     * 申请单号
     */
    private String applyNo;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 司机code
     */
    private String driverUserCode;
    /**
     * 社会类型入口type 1.公务用车-台账  2:社会租赁-台账 3:社会租赁供应商-台账 4:公务用车-紧急用车台账
     */
    private Integer socialEntryType;

    /**
     * 下拉树code
     */
    private String parentCode;

    /**
     * 行程类型：1-办公用车，2-紧急用车
     */
    private Integer orderType;


    /**
     * 用车类型: 1:公务用车 2:社会租赁
     */
    private Integer useType;

    /**
     * 行程状态 (10待审批  20待调度 30待出发 40用车中 50已完成 60已取消)
     */
    private List<Integer> orderStatusList;

    /**
     * 行程状态 (10待审批  20待调度 30待出发 40用车中 50已完成 60已取消)
     */
    private Integer orderStatus;

    /**
     * 核实状态 0:未核实 1:已核实 2:无需核实
     */
    private Integer verifyStatus;

    /**
     * 是否跨单位 1:是 0:否
     */
    private Integer crossType;

    /**
     * 审核状态 1:待审核 2:审核通过 3:无需审核 4:审核驳回 5:审核撤回
     */
    private Integer approvalStatus;

    /**
     * 调度状态 1:待调度 2:无需调度 3:已调度 4:调度取消
     */
    private Integer scheduleStatus;

    /**
     * 车辆所有人
     */
    private String vehicleBelongDeptCode;

    /**
     * 车辆所有人
     */
    private String vehicleBelongDeptName;

    /**
     * 创建人单位
     */
    private String createDeptName;

    /**
     * 创建人
     */
    private String createUserName;

    /**
     * 用车人
     */
    private String passengerUserName;

    /**
     * 用车人单位
     */
    private String passengerDeptName;

    /**
     * 驾驶员
     */
    private String driverName;

    /**
     * 预计出发时间开始
     */
    private String expectedPickupTimeStart;

    /**
     * 预计出发时间结束
     */
    private String expectedPickupTimeEnd;

    /**
     * 实际用车区间开始
     */
    private String actualTimeStart;

    /**
     * 实际用车区间结束
     */
    private String actualTimeEnd;

    /**
     * 预计结束时间开始
     */
    private String expectedReturnTimeStart;

    /**
     * 预计结束时间结束
     */
    private String expectedReturnTimeEnd;

    /**
     * 订单创建日期开始
     */
    private String orderCreateTimeStart;

    /**
     * 订单创建日期结束
     */
    private String orderCreateTimeEnd;

    /**
     * 车牌号
     */
    private String vehicleLicense;

    /**
     * 车辆类型
     */
    private Integer vehicleType;

    /**
     * 车辆使用性质
     */
    private Integer useAttribute;

    /**
     * 订单号集合 辅助字段 前端勿传
     */
    private List<String> orderNoList;


    /**
     * 申请单号集合 辅助字段 前端勿传
     */
    private List<String> applyNoList;


    /**
     * 订单号集合 辅助字段 前端勿传
     */
    private List<String> permOrderNoList;

    /**
     * 租赁方式 1:日租 2:分时
     */
    private Integer rentType;

    /**
     * 权限使用 查询登录用户下的供应商
     */
    private String permSupplierOrgNo;

    /**
     * 辅助字段，前端不用传
     */
    private String permSupplierCode;

    /**
     * 单位性质 党政机关、多事业单位、事业单位、团体组织
     * 多选，英文逗号分隔
     */
    private String attributeType;

    /**
     *
     * 主管单位
     * 1-机关事务管理局；2-财政部门
     * 多选，英文逗号分隔
     *
     */
    private Integer managerCarType;

    /**
     * 辅助字段,前端不用传
     */
    private List<String> deptCodeList;

    /**
     *
     * 城市code
     */
    private String cityCode;

    /**
     * 搜索类型 1:四川省 2：省本级 3:市本级
     * 默认四川省
     */
    private Integer searchAreaType;

    /**
     * 车辆服务类型 （1 定点车 2 平台车）
     */
    private Integer vehicleServiceType;


    /**
     * 车辆管理单位code
     */
    private String vehicleManagementDeptCode;


    private List<String> belongDeptCodeList;
}
