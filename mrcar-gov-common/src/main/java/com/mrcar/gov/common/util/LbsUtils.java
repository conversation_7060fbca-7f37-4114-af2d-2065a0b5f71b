package com.mrcar.gov.common.util;


import java.math.BigDecimal;

import com.mrcar.gov.common.dto.catnet.GPS;

/**
 * @ClassName LbsUtils
 * @Description 坐标系转换工具类
 * <AUTHOR>
 * @Date 2019/5/7 11:17
 * @Version 1.0
 */
public class LbsUtils {
    private static final double x_PI = 3.14159265358979324 * 3000.0 / 180.0;
    public static final String BAIDU_LBS_TYPE = "bd09ll";
    public static double pi = 3.1415926535897932384626;
    public static double a = 6378245.0;
    public static double ee = 0.00669342162296594323;

    public static GPS gps84ToGcj02(BigDecimal lat, BigDecimal lon) {
        if(lat==null||lon==null) return null;
        if(BigDecimal.ZERO.equals(lat)&&BigDecimal.ZERO.equals(lon)) return null;
        return gps84ToGcj02(lat.doubleValue(),lon.doubleValue());
    }

    /**
     * 84 to 火星坐标系 (GCJ-02) World Geodetic System ==> Mars Geodetic System
     * @param lat
     * @param lon
     */
    private static GPS gps84ToGcj02(double lat, double lon) {
        try{
            double dLat = transformLat(lon - 105.0, lat - 35.0);
            double dLon = transformLon(lon - 105.0, lat - 35.0);
            double radLat = lat / 180.0 * pi;
            double magic = Math.sin(radLat);
            magic = 1 - ee * magic * magic;
            double sqrtMagic = Math.sqrt(magic);
            dLat = (dLat * 180.0) / ((a * (1 - ee)) / (magic * sqrtMagic) * pi);
            dLon = (dLon * 180.0) / (a / sqrtMagic * Math.cos(radLat) * pi);
            double mgLat = lat + dLat;
            double mgLon = lon + dLon;
            return new GPS(mgLat, mgLon);
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }
    }

    public static GPS gcjToGps84(BigDecimal lat, BigDecimal lon) {
        if(lat==null||lon==null) return null;
        if(BigDecimal.ZERO.equals(lat)&&BigDecimal.ZERO.equals(lon)) return null;
        return gcjToGps84(lat.doubleValue(),lon.doubleValue());
    }


    /**
     * * 火星坐标系 (GCJ-02) to 84 * * @param lon * @param lat * @return
     */
    private static GPS gcjToGps84(double lat, double lon) {
        try {
            GPS gps = transform(lat, lon);
            double lontitude = lon * 2 - gps.getLon();
            double latitude = lat * 2 - gps.getLat();
            return new GPS(latitude, lontitude);
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }
    }

    @Deprecated
    public static GPS gcj02ToBd09(BigDecimal gg_lat, BigDecimal gg_lon) {
        return gcj02ToBd09(gg_lat.doubleValue(),gg_lon.doubleValue());
    }

    /**
     * 火星坐标系 (GCJ-02) 与百度坐标系 (BD-09) 的转换算法 将 GCJ-02 坐标转换成 BD-09 坐标
     *
     * @param gg_lat
     * @param gg_lon
     */
    @Deprecated
    public static GPS gcj02ToBd09(double gg_lat, double gg_lon) {
        try {
            double x = gg_lon, y = gg_lat;
            double z = Math.sqrt(x * x + y * y) + 0.00002 * Math.sin(y * pi);
            double theta = Math.atan2(y, x) + 0.000003 * Math.cos(x * pi);
            double bd_lon = z * Math.cos(theta) + 0.0065;
            double bd_lat = z * Math.sin(theta) + 0.006;
            return new GPS(bd_lat, bd_lon);
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }
    }


    /**
     * GCJ02 转百度坐标
     *
     * @param lng GCJ02 经度
     * @param lat GCJ02 纬度
     * @return 百度坐标：[经度，纬度]
     */
    public static double[] transformGCJ02ToBD09(double lng, double lat) {
        double z = Math.sqrt(lng * lng + lat * lat) + 0.00002 * Math.sin(lat * x_PI);
        double theta = Math.atan2(lat, lng) + 0.000003 * Math.cos(lng * x_PI);
        double bd_lng = z * Math.cos(theta) + 0.0065;
        double bd_lat = z * Math.sin(theta) + 0.006;
        return new double[]{bd_lng, bd_lat};
    }



    public static GPS transformGCJ02ToBD09ToGpsEntity(double lng, double lat) {
        double[] bd = transformGCJ02ToBD09(lng, lat);
        return new GPS(bd[1],bd[0]);
        //return  gcj02ToBd09(lat,lng);
    }


    public static GPS bd09ToGcj02(BigDecimal bd_lat, BigDecimal bd_lon) {
        if(bd_lat==null||bd_lon==null) return null;
        if(BigDecimal.ZERO.equals(bd_lat)&&BigDecimal.ZERO.equals(bd_lon)) return null;
        return bd09ToGcj02(bd_lat.doubleValue(),bd_lon.doubleValue());
    }

    /**
     * * 火星坐标系 (GCJ-02) 与百度坐标系 (BD-09) 的转换算法 * * 将 BD-09 坐标转换成GCJ-02 坐标 * * @param
     * bd_lat * @param bd_lon * @return
     */
    private static GPS bd09ToGcj02(double bd_lat, double bd_lon) {
        try {
            double x = bd_lon - 0.0065, y = bd_lat - 0.006;
            double z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * x_PI);
            double theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * x_PI);
            double gg_lon = z * Math.cos(theta);
            double gg_lat = z * Math.sin(theta);
            return new GPS(gg_lat, gg_lon);
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }
    }

    /**
     * (BD-09)-->84
     * @param bd_lat
     * @param bd_lon
     * @return
     */
    public static GPS bd09ToGps84(double bd_lat, double bd_lon) {
        try {
            GPS gcj02 = bd09ToGcj02(bd_lat, bd_lon);
            GPS map84 = gcjToGps84(gcj02.getLat(),
                    gcj02.getLon());
            return map84;
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }
    }

    /**
     * is or not outOfChina
     * @param lat
     * @param lon
     * @return
     */
    public static boolean outOfChina(double lat, double lon) {
        if (lon < 72.004 || lon > 137.8347)
            return true;
        if (lat < 0.8293 || lat > 55.8271)
            return true;
        return false;
    }

    public static GPS transform(double lat, double lon) {
        try {
            double dLat = transformLat(lon - 105.0, lat - 35.0);
            double dLon = transformLon(lon - 105.0, lat - 35.0);
            double radLat = lat / 180.0 * pi;
            double magic = Math.sin(radLat);
            magic = 1 - ee * magic * magic;
            double sqrtMagic = Math.sqrt(magic);
            dLat = (dLat * 180.0) / ((a * (1 - ee)) / (magic * sqrtMagic) * pi);
            dLon = (dLon * 180.0) / (a / sqrtMagic * Math.cos(radLat) * pi);
            double mgLat = lat + dLat;
            double mgLon = lon + dLon;
            return new GPS(mgLat, mgLon);
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }
    }

    public static double transformLat(double x, double y) {
        double ret = -100.0 + 2.0 * x + 3.0 * y + 0.2 * y * y + 0.1 * x * y
                + 0.2 * Math.sqrt(Math.abs(x));
        ret += (20.0 * Math.sin(6.0 * x * pi) + 20.0 * Math.sin(2.0 * x * pi)) * 2.0 / 3.0;
        ret += (20.0 * Math.sin(y * pi) + 40.0 * Math.sin(y / 3.0 * pi)) * 2.0 / 3.0;
        ret += (160.0 * Math.sin(y / 12.0 * pi) + 320 * Math.sin(y * pi / 30.0)) * 2.0 / 3.0;
        return ret;
    }


    public static double transformLon(double x, double y) {
        double ret = 300.0 + x + 2.0 * y + 0.1 * x * x + 0.1 * x * y + 0.1
                * Math.sqrt(Math.abs(x));
        ret += (20.0 * Math.sin(6.0 * x * pi) + 20.0 * Math.sin(2.0 * x * pi)) * 2.0 / 3.0;
        ret += (20.0 * Math.sin(x * pi) + 40.0 * Math.sin(x / 3.0 * pi)) * 2.0 / 3.0;
        ret += (150.0 * Math.sin(x / 12.0 * pi) + 300.0 * Math.sin(x / 30.0
                * pi)) * 2.0 / 3.0;
        return ret;
    }

    public static void main(String[] args) {
        GPS gps = gps84ToGcj02(new BigDecimal(39.945736),new BigDecimal(116.343513));
        System.err.println("gps="+gps.toString());
        System.err.println("gps="+gps.toString());
    }
}
