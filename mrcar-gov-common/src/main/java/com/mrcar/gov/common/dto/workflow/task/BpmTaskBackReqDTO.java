package com.mrcar.gov.common.dto.workflow.task;

import com.mrcar.gov.common.dto.workflow.BaseDTO;
import lombok.Data;
import javax.validation.constraints.NotBlank;

/**
 * 退回任务 Request VO
 */
@Data
public class BpmTaskBackReqDTO extends BaseDTO {

    /**
     * 任务编号
     */
    @NotBlank(message = "任务编号不能为空")
    private String id;

    /**
     * 退回节点
     */
    @NotBlank(message = "退回节点不能为空")
    private String backNode;

    /**
     * 退回原因
     */
    @NotBlank(message = "退回原因不能为空")
    private String reason;
}
