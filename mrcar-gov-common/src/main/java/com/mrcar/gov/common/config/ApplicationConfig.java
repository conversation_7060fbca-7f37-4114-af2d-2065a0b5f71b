package com.mrcar.gov.common.config;

import com.mrcar.gov.common.filter.ContentRequestWrapperFilter;
import com.mrcar.gov.common.interceptor.RequestLoggingInterceptor;
import com.mrcar.gov.common.security.filter.AuthHandlerInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.annotation.Resource;

@Configuration
public class ApplicationConfig implements WebMvcConfigurer {

    @Resource
    private AuthHandlerInterceptor authHandlerInterceptor;
    @Autowired
    private RequestLoggingInterceptor requestLoggingInterceptor;

    /**
     * 配置拦截器.
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        //指定拦截器，指定拦截路径
        registry.addInterceptor(authHandlerInterceptor)
                .addPathPatterns("/**");
        registry.addInterceptor(requestLoggingInterceptor)
                .addPathPatterns("/**");
    }

    /**
     * 配置过滤器
     */
    @Bean
    public FilterRegistrationBean<ContentRequestWrapperFilter> requestFilter() {
        FilterRegistrationBean<ContentRequestWrapperFilter> bean = new FilterRegistrationBean<>();
        bean.setFilter(new ContentRequestWrapperFilter());
        bean.addUrlPatterns("/*");
        bean.setOrder(Ordered.HIGHEST_PRECEDENCE);
        return bean;
    }

}
