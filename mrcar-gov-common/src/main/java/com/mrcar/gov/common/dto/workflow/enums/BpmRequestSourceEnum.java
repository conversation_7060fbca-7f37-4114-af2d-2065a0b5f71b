package com.mrcar.gov.common.dto.workflow.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/9/6 16:58
 */
@AllArgsConstructor
@Getter
public enum BpmRequestSourceEnum {

    // 待办理
    WAIT_TODO(1, "待办理"),
    // 已办理
    ALREADY_TODO(2, "已办理"),
    // 我发起的
    MY_START(3, "我发起的"),
    // 抄送给我得
    COPY_ME(4, "抄送给我得");

    private Integer source;

    private String desc;

}
