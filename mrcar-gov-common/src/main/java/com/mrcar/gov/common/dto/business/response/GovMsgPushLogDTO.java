package com.mrcar.gov.common.dto.business.response;


import lombok.Data;

import java.util.Date;

import com.mrcar.gov.common.constant.business.GovMsgTypeEnum;
import com.mrcar.gov.common.constant.business.GovMsgPushLogStatusEnum;
import com.mrcar.gov.common.constant.business.GovMsgPushTypeEnum;
import com.mrcar.gov.common.constant.business.GovMsgPushChannelEnum;
import com.mrcar.gov.common.constant.business.GovMsgPushStatusEnum;
import com.mrcar.gov.common.constant.business.GovMsgModuleEnum;
import com.mrcar.gov.common.constant.business.GovMsgReachStatusEnum;
import com.mrcar.gov.common.constant.business.GovMsgReadStatusEnum;







/**
 * <AUTHOR>
 * @date 2024/12/27 18:48
 */
@Data
public class GovMsgPushLogDTO {

    /**
     * id
     */
    private String id;

    /**
     * 企业id
     */
    private Integer companyId;

    /**
     * 日志编号
     */
    private String logNo;

    /**
     * 记录编号
     */
    private String recordNo;

    /**
     * 推送批次号
     */
    private String batchNo;

    /**
     * 消息类型 1模板消息 2公告消息
     */
    private Integer msgType;
    //  消息类型描述
    private String msgTypeDes;

    /**
     * 模板或公告编码
     */
    private String msgNo;

    /**
     * 模板或公告名称
     */
    private String msgName;

    /**
     * 接收人编码
     */
    private String receiverCode;
    /**
     * 接收人名字
     */
    private String receiverName;

    /**
     * 接收人手机号
     */
    private String receiverMobile;

    /**
     * 推送开始时间
     */
    private Date pushStartTime;

    /**
     * 推送结束时间
     */
    private Date pushEndTime;

    /**
     * 状态 0:删除 1:正常
     */
    private Integer logStatus;
    // 日志状态描述
    private String logStatusDes;

    /**
     * 推送方式 1站内信 2短信
     */
    private Integer pushType;
    // 推送方式描述
    private String pushTypeDesc;

    /**
     * 推送渠道 1pc端 2h5 3APP通知
     */
    private Integer pushChannel;
    // 推送渠道描述
    private String pushChannelDesc;

    /**
     * 推送状态 0失败 1成功 2推送中
     */
    private Integer pushStatus;
    // 推送状态描述
    private String pushStatusDesc;


    /**
     * 消息模块 1公文公告;2系统消息
     */
    private Integer msgModule;
    // 消息模块描述
    private String msgModuleDes;

    /**
     * 到达状态 0未到达 1到达 2处理中 3默认
     */
    private Integer reachStatus;
    // 到达状态描述
    private String reachStatusDes;

    /**
     * 到达时间
     */
    private Date reachTime;

    /**
     * 读取状态 0未读 1已读 2默认
     */
    private Integer readStatus;
    // 读取状态描述
    private String readStatusDes;

    /**
     * 读取时间
     */
    private Date readTime;

    /**
     * 扩展数据.根据业务场景存需要的扩展信息
     */
    private String extData;

    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 创建人编码
     */
    private String createCode;

    /**
     * 创建人名称
     */
    private String createName;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新人编码
     */
    private String updateCode;

    /**
     * 更新人名称
     */
    private String updateName;


    public void setMsgType(Integer msgType) {
        this.msgType = msgType;
        this.msgTypeDes = GovMsgTypeEnum.getDescByCode(msgType);
    }

    public void setLogStatus(Integer logStatus) {
        this.logStatus = logStatus;
        this.logStatusDes = GovMsgPushLogStatusEnum.getDescByCode(logStatus);
    }

    public void setPushType(Integer pushType) {
        this.pushType = pushType;
        this.pushTypeDesc = GovMsgPushTypeEnum.getDescByCode(pushType);
    }

    public void setPushChannel(Integer pushChannel) {
        this.pushChannel = pushChannel;
        this.pushChannelDesc = GovMsgPushChannelEnum.getDescByCode(pushChannel);
    }

    public void setPushStatus(Integer pushStatus) {
        this.pushStatus = pushStatus;
        this.pushStatusDesc = GovMsgPushStatusEnum.getDescByCode(pushStatus);
    }



    public void setMsgModule(Integer msgModule) {
        this.msgModule = msgModule;
        this.msgModuleDes = GovMsgModuleEnum.getDescByCode(msgModule);
    }

    public void setReachStatus(Integer reachStatus) {
        this.reachStatus = reachStatus;
        this.reachStatusDes = GovMsgReachStatusEnum.getDescByCode(reachStatus);
    }

    public void setReadStatus(Integer readStatus) {
        this.readStatus = readStatus;
        this.readStatusDes = GovMsgReadStatusEnum.getDescByCode(readStatus);
    }
}
