package com.mrcar.gov.common.dto.order.resp;

import com.mrcar.gov.common.constant.asset.VehicleTypeEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 计费配置
 */
@Data
public class GovBillingConfigurationDTO {

    /**
     * 自增主键
     */
    private Integer billingConfigurationId;

    /**
     * (社会租赁)供应商code
     */
    private String supplierCode;

    /**
     * (社会租赁)供应商name
     */
    private String supplierName;

    /**
     * 车辆类型
     */
    private Integer vehicleType;

    private String vehicleTypeStr;

    /**
     * 日租金带驾驶员
     */
    private BigDecimal dailyRentWithDriver;
    private String dailyRentWithDriverStr;

    /**
     * 日租金自驾
     */
    private BigDecimal dailyRentWithSelf;
    private String dailyRentWithSelfStr;

    /**
     * 时长租金带驾驶员
     */
    private BigDecimal lengthOfRentWithDriver;
    private String lengthOfRentWithDriverStr;

    /**
     * 时长租金自驾
     */
    private BigDecimal lengthOfRentWithSelf;
    private String lengthOfRentWithSelfStr;

    /**
     * 里程租金带驾驶员
     */
    private BigDecimal mileageRentWithDriver;
    private String mileageRentWithDriverStr;

    /**
     * 里程租金自驾
     */
    private BigDecimal mileageRentWithSelf;
    private String mileageRentWithSelfStr;

    /**
     * 当前版本id
     */
    private Integer billingConfigurationVersionId;

    /**
     * 企业id
     */
    private Integer companyId;

    /**
     * 企业名
     */
    private String companyName;

    /**
     * 创建人编码
     */
    private String createCode;

    /**
     * 创建人
     */
    private String createName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String updateCode;

    /**
     * 修改人
     */
    private String updateName;

    /**
     * 更新时间
     */
    private Date updateTime;

    public void setVehicleType(Integer vehicleType) {
        this.vehicleType = vehicleType;
        this.vehicleTypeStr = VehicleTypeEnum.getVehicleTypeDesc(vehicleType);;
    }

    public void setDailyRentWithDriver(BigDecimal dailyRentWithDriver) {
        this.dailyRentWithDriver = dailyRentWithDriver;
        this.dailyRentWithDriverStr = dailyRentWithDriver + "元/天";
    }

    public void setDailyRentWithSelf(BigDecimal dailyRentWithSelf) {
        this.dailyRentWithSelf = dailyRentWithSelf;
        this.dailyRentWithSelfStr = dailyRentWithSelf + "元/天";
    }

    public void setLengthOfRentWithDriver(BigDecimal lengthOfRentWithDriver) {
        this.lengthOfRentWithDriver = lengthOfRentWithDriver;
        this.lengthOfRentWithDriverStr = lengthOfRentWithDriver + "元/小时";
    }

    public void setMileageRentWithDriver(BigDecimal mileageRentWithDriver) {
        this.mileageRentWithDriver = mileageRentWithDriver;
        this.mileageRentWithDriverStr = mileageRentWithDriver + "元/公里";
    }

    public void setLengthOfRentWithSelf(BigDecimal lengthOfRentWithSelf) {
        this.lengthOfRentWithSelf = lengthOfRentWithSelf;
        this.lengthOfRentWithSelfStr = lengthOfRentWithSelf + "元/小时";
    }

    public void setMileageRentWithSelf(BigDecimal mileageRentWithSelf) {
        this.mileageRentWithSelf = mileageRentWithSelf;
        this.mileageRentWithSelfStr = mileageRentWithSelf + "元/公里";
    }
}
