package com.mrcar.gov.common.constant.order;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum GovPublicCarScheduleTypeEnum {

    // 订单类型：0-无需调度 1-调度
    NO_SCHEDULE(0, "无需调度"),
    SCHEDULE(1, "由调度员派车"),
    ;

    private final Integer code;
    private final String name;

    public static String getName(Integer code) {
        return Arrays.stream(values()).filter(e -> e.getCode().equals(code)).findFirst().map(GovPublicCarScheduleTypeEnum::getName).orElse(null);
    }
}