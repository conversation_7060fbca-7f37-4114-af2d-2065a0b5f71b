package com.mrcar.gov.common.constant.business;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/12/30 13:58
 */
@AllArgsConstructor
@Getter
public enum GovMsgReadStatusEnum {

    // 0未读 1已读
    UNREAD(0, "未读"),

    READ(1, "已读"),
    ;

    private final Integer code;

    private final String desc;

    public static String getDescByCode(Integer readStatus) {
        for (GovMsgReadStatusEnum value : GovMsgReadStatusEnum.values()) {
            if (Objects.equals(value.getCode(), readStatus)) {
                return value.getDesc();
            }
        }
        return "";
    }
}
