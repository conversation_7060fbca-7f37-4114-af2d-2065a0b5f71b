package com.mrcar.gov.common.util;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2024/11/20 15:10
 */
public class StrUtils {


     public static String getOrDefaultEmpty(String str) {
        return str == null ? "" : str;
    }


    /**
     * 获取字符串中英文括号内外的名称和编码 例如：公安厅租赁合作机构(ZL2412310001464)
     */
    public static String[] extractNameAndCode(String input) {
        String[] result = new String[2];
        // 定义正则表达式模式
        String regex = "([^\\(]+)\\(([^\\)]+)\\)";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(input);

        if (matcher.find()) {
            String name = matcher.group(1).trim();
            String code = matcher.group(2).trim();
            result[0] = code;
            result[1] = name;
        }
        return result;
    }

    public static void main(String[] args) {
        String input = "公安厅租赁合作机构(ZL2412310001464)";
        extractNameAndCode(input);
    }
}
