package com.mrcar.gov.common.constant.user;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;
import java.util.stream.Stream;

/**
* @Description: 发票开具税率枚举
* @author: hxc
* @Date: 2023/3/29
**/
@Getter
@AllArgsConstructor
public enum ConstructionInvoiceRate {

	InvoiceCode01("01","0%","0.00000"),
	InvoiceCode02("02","3%","0.03000"),
	InvoiceCode03("03","5%","0.05000"),
	InvoiceCode04("04","6%","0.06000"),
	InvoiceCode05("05","10%","0.10000"),
	InvoiceCode06("06","16%","0.16000"),
	InvoiceCode07("07","7%","0.07000"),
	InvoiceCode08("08","2%","0.02000"),
	InvoiceCode09("09","100%","1.00000"),
	InvoiceCode10("10","25%","0.25000"),
	InvoiceCode11("11","20%","0.20000"),
	InvoiceCode12("12","1%","0.01000"),
	InvoiceCode13("13","0.07%","0.00070"),
	InvoiceCode14("14","0.09%","0.00090"),
	InvoiceCode15("15","0.1%","0.00100"),
	InvoiceCode16("16","0.03%","0.00030"),
	InvoiceCode17("17","0.05%","0.00050"),
	InvoiceCode18("18","0.005%","0.00005"),
	InvoiceCode19("19","0.5%","0.00500"),
	InvoiceCode20("20","0.06%","0.00060"),
	InvoiceCode21("21","9%","0.09000"),
	InvoiceCode22("22","13%","0.13000");

	private final String code;
	private final String name;
	private final String value;

	public static ConstructionInvoiceRate getEnum(String code){
		for(ConstructionInvoiceRate e : ConstructionInvoiceRate.values()){
			if(e.getCode().equals(code)){
				return e;
			}
		}
		return ConstructionInvoiceRate.InvoiceCode01;
	}

	public static ConstructionInvoiceRate getValue(String name){
		return Stream.of(values()).filter(e-> Objects.equals(e.getName(),name)).findFirst().orElse(InvoiceCode01);
	}
}
