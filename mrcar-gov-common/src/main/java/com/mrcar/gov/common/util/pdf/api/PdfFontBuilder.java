package com.mrcar.gov.common.util.pdf.api;

import org.apache.commons.io.FileUtils;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;

public class PdfFontBuilder {

    byte[] source;

    private PdfFontBuilder() { }

    private PdfFontBuilder(byte[] source) {
        this.source = source;
    }

    public static PdfFontBuilder from(File file) throws IOException {
        byte[] source = FileUtils.readFileToByteArray(file);
        return new PdfFontBuilder(source);
    }

    public static PdfFontBuilder from(InputStream inStream) throws IOException {
        ByteArrayOutputStream bytes = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        int len;
        while ((len = inStream.read(buffer, 0, buffer.length)) != -1) {
            bytes.write(buffer, 0, len);
        }
        return new PdfFontBuilder(bytes.toByteArray());
    }

}
