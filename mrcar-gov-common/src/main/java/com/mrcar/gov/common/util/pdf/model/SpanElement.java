package com.mrcar.gov.common.util.pdf.model;

import com.mrcar.gov.common.util.pdf.layout.MetricValue;
import com.mrcar.gov.common.util.pdf.layout.RoundMetric;

public class SpanElement extends AbstractRowElement {

    private static final String NODE_NAME = "Span";

    protected SpanElement(PageElement parent,
                          MetricValue height,
                          RoundMetric margin) {
        super(parent, height, margin);
    }

    public static SpanElement create(PageElement page,
                                     MetricValue height,
                                     RoundMetric margin) {
        return new SpanElement(page, height, margin);
    }

    public static SpanElement create(PageElement page,
                                     MetricValue height) {
        return new SpanElement(page, height, RoundMetric.empty());
    }

    @Override
    public String name() {
        return NODE_NAME;
    }

}
