package com.mrcar.gov.common.dto.workflow.task;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 流程任务返回参数
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BpmTaskRespDTO extends BpmTaskDonePageItemRespDTO {

    /**
     * 任务定义的标识
     */
    private String definitionKey;

    /**
     * 审核的用户信息
     */
    private User assigneeUser;

    /**
     * 用户信息
     */
    @Data
    public static class User {

        /**
         * 用户编号
         */
        private Long id;

        /**
         * 用户昵称
         */
        private String nickname;

        /**
         * 用户手机号
         */
        private String mobile;

        /**
         * 部门编号
         */
        private Long deptId;

        /**
         * 部门名称
         */
        private String deptName;
    }
}
