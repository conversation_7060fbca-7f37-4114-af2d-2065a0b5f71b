package com.mrcar.gov.common.util;


import com.izu.framework.web.util.SequenceUtil;

import com.mrcar.gov.common.dto.iot.FuelInfo;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * <AUTHOR> on 2019/9/23.
 */
public class LbsUtil {
    private static final SimpleDateFormat sf = new SimpleDateFormat("yyMMdd");
    private static final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    /**
     * 区间超过该阈值进行数据分离
     */
    private static final int BASIC_ERR_RANGE = 5;
    /**
     * 超过该阈值认为加油
     */
    private static final int BASIC_OIL_RANGE = 10;
    /**
     * 每分钟油量上下浮动的百分比2%
     */
    private static final int BASIC_PER_MIN_RANGE = 2;
    /**
     * 最大平均油耗30L/100KM
     */
    private static final int MAX_FUEL = 30;
    /**
     * 以最高时速120KM/H计算的每分钟行驶距离 2KM
     */
    private static final int MAX_DIS = 2;
    private List<List<FuelInfo>> list ;
    /**
     * 生成资金池唯一交易号
     *
     * @return
     */
    public static String getSerialNo() {
        return sf.format(new Date()) + SequenceUtil.generate();
    }

    public static int defaultInt(Integer source, int defaultTarget) {
        if (source == null) {
            return defaultTarget;
        } else {
            return source;
        }
    }

    public static BigDecimal getRate(int iFenzi, int iFenmu) {
        BigDecimal bRate = BigDecimal.ZERO;
        try {
            BigDecimal fenzi = new BigDecimal(iFenzi).multiply(new BigDecimal(100));
            BigDecimal fenmu = new BigDecimal(iFenmu);
            bRate = fenzi.divide(fenmu, 2, BigDecimal.ROUND_HALF_UP);
        } catch (Exception e) {
        }
        return bRate;
    }

    /**
     * 秒 转换为 天时分秒
     * @param durations
     * @return
     */
    public static String getTimeDiff(long durations) {
        StringBuilder sb = new StringBuilder();
        int[] units = {86400, 3600, 60, 1};
        String[] devided = {"天", "小时", "分", "秒"};
        for (int i = 0; i < units.length; i++) {
            if (durations >= units[i]) {
                sb.append(durations / (units[i]));
                sb.append(devided[i]);
                durations = durations % (units[i]);
            }
        }
        return sb.toString();
    }

    public static String getMinTime(String time1,String time2){
        String minTime = "";
        LocalDateTime localDateTime1 = null;
        LocalDateTime localDateTime2 = null;
        if(StringUtils.isBlank(time1) && StringUtils.isBlank(time2)){
            return minTime;
        }
        if(StringUtils.isBlank(time1)){
            localDateTime1 = LocalDateTime.MAX;
        }else{
            localDateTime1 = LocalDateTime.parse(time1,dateTimeFormatter);
        }
        if(StringUtils.isBlank(time2)){
            localDateTime2 = LocalDateTime.MAX;
        }else {
            localDateTime2 = LocalDateTime.parse(time2,dateTimeFormatter);
        }
        Duration duration = Duration.between(localDateTime1,localDateTime2);
        if(duration.isNegative()){
            minTime = time2;
        }else{
            minTime = time1;
        }

        return minTime;
    }

    /**
     * 计算平均速度KM/H
     * @param duration 单位秒
     * @param distance 单位米
     * @return
     */
    public static BigDecimal calculateAvgSpeed(long duration,double distance){
        try {
            double hour = (double) duration / 3600D;
            double dis =  distance / 1000D;
            if(hour > 0){
                double avgS = dis / hour;
                return new BigDecimal(avgS).setScale(2,BigDecimal.ROUND_HALF_UP);
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        return BigDecimal.ZERO;
    }

    /**
     * 对油量下降波动超过10%基准的错误数据进行滤波处理
     * @param fuelList
     */
    public static void filter(List<FuelInfo> fuelList){
        Iterator<FuelInfo> iterator = fuelList.iterator();
        FuelInfo before = null;
        //定义基准每分钟允许浮动2个百分点，以44L容量的车以120KM/h的时速计算，油耗为44L/100KM
        int jizhun = 2;
        while (iterator.hasNext()){
            if(before == null){
                before = iterator.next();
                continue;
            }
            FuelInfo now = iterator.next();
            int used = before.getFuel() - now.getFuel();
            //油量上升的不进行过滤
            if(used > 0){
                Date dateStart = before.getCreateDate();
                Date dateEnd = now.getCreateDate();
                long durations = DateUtils.getDuration(dateStart,dateEnd);
                double min = (double) durations/60d;
                if(min < 1) min = 1;
                BigDecimal normalFuel = new BigDecimal(min).multiply(new BigDecimal(jizhun)).setScale(2,BigDecimal.ROUND_HALF_UP);
                //油量上升的不进行过滤，快速下降的进行过滤
                if(new BigDecimal(used).compareTo(normalFuel) >= 0){
                    iterator.remove();
                    filter(fuelList);
                    break;
                }
            }else{
                before = now;
            }
        }
    }

    public Integer calculateFuel(List<FuelInfo> fuel){
        list = new ArrayList<>();
        //拆分加油前后的数据
        int usedFuel = 0;
        List<FuelInfo> ll = splitFuel(fuel);
        if (ll != null) list.add(ll);
        if(list.size() > 0){
            //删除不需要很明显有问题的集合
            list = list.stream().filter(l->l.size()>2).collect(Collectors.toList());
            //融合集合
            List<List<FuelInfo>> newList = filterList(list);
            //清空集合帮助回收
            list = null;
            for(int i = 0;i < newList.size();i++){
                int max = newList.get(i).stream().max(Comparator.comparingInt(FuelInfo::getFuel)).get().getFuel();
                int min = newList.get(i).stream().min(Comparator.comparingInt(FuelInfo::getFuel)).get().getFuel();
                usedFuel += max-min;
            }
            newList = null;
        }
        return usedFuel;
    }

    public List<FuelInfo> fuelListDeNoise(List<FuelInfo> fuel){
        list = new ArrayList<>();
        List<FuelInfo> res = new ArrayList<>();
        //拆分加油前后的数据
        int usedFuel = 0;
        List<FuelInfo> ll = splitFuel(fuel);
        if (ll != null) list.add(ll);
        if(list.size() > 0){
            //删除不需要很明显有问题的集合
            list = list.stream().filter(l->l.size()>2).collect(Collectors.toList());
            //融合集合
            List<List<FuelInfo>> newList = filterList(list);
            //清空集合帮助回收
            list = null;
            for(int i = 0;i < newList.size();i++){
                res.addAll(newList.get(i));
            }
            newList = null;
        }
        return res;
    }


    /**
     * 针对拆分的后的油量集合进行过滤
     * 对于平均油耗超过30L/100KM的错误数据进行滤波处理
     * @return
     */
    private List<List<FuelInfo>> filterList(List<List<FuelInfo>> list){
        List<List<FuelInfo>> newList = new ArrayList<>();
        Iterator<List<FuelInfo>> iterator = list.iterator();
        List<FuelInfo> before = null;
        while (iterator.hasNext()){
            if(before == null){
                before = iterator.next();
                continue;
            }else{
                List<FuelInfo> next = iterator.next();
                FuelInfo minBefore = before.stream().min(Comparator.comparingInt(FuelInfo::getFuel)).get();
                FuelInfo maxBefore = before.stream().max(Comparator.comparingInt(FuelInfo::getFuel)).get();
                FuelInfo maxNow = next.stream().max(Comparator.comparingInt(FuelInfo::getFuel)).get();
                FuelInfo minNow = next.stream().min(Comparator.comparingInt(FuelInfo::getFuel)).get();
                //集合size < 2的不进行融合
                if(before.size() < 2 || next.size() < 2){
                    newList.add(new ArrayList<>(before));
                    before = next;
                    continue;
                }
                if(isMix(maxNow,maxBefore,minNow,minBefore)){
//                    before.addAll(new ArrayList<>(next));
                    ArrayList<FuelInfo> tmp = new ArrayList<>(before);
                    tmp.addAll(next);
                    before = tmp;
                    continue;
                }else{
                    newList.add(new ArrayList<>(before));
                    before = next;
                    continue;
                }
            }
        }
        if(before != null && before.size() > 0){
            newList.add(before);
        }
        return newList;
    }



    /**
     * 1、以油量突然上涨超过10%的基准判断为加油点，对数组进行切分为加油前和加油后
     * 2、对油量波动超过10%基准的错误数据进行滤波处理
     * @param fuel
     * @return
     */
    public  List<FuelInfo> splitFuel(List<FuelInfo> fuel){
        if(fuel == null || fuel.size() < 1){
            return null;
        }
        for(int i = 0;i < fuel.size()-1;i++){
            if(fuel.get(i+1).getFuel() >= fuel.get(i).getFuel() + BASIC_ERR_RANGE
                    || fuel.get(i).getFuel() >= fuel.get(i+1).getFuel() + BASIC_ERR_RANGE){
                list.add(fuel.subList(0,i+1));
                List<FuelInfo> newList = fuel.subList(i+1,fuel.size());
                if(newList.size() > 1){
                    List<FuelInfo> splitList = splitFuel(newList);
                    if(splitList == null){
                        return null;
                    }
                    if(splitList == newList){
                        list.add(splitList);
                        return null;
                    }else{
                        splitFuel(splitList);
                    }
                }else{
                    return null;
                }
            }
        }
        return fuel;
    }

    private boolean isMix(FuelInfo maxNow,FuelInfo maxBefore,FuelInfo minNow,FuelInfo minBefore){
        if((maxNow.getFuel().intValue() - minBefore.getFuel().intValue()) > BASIC_OIL_RANGE){
            return false;
        }
        if(maxNow.getFuel().intValue() <= maxBefore.getFuel().intValue() &&
            maxNow.getFuel() + BASIC_ERR_RANGE >= minBefore.getFuel().intValue()){
            return true;
        }
        return false;
    }


    public static void main(String[] args) throws Exception {

    }

    public static void retryExecute(Supplier<Boolean> supplier,int times) throws ExecutionException, InterruptedException {
        if(times > 0 && !CompletableFuture.supplyAsync(supplier).get()){
            retryExecute(supplier,times-1);
        }
    }
}
