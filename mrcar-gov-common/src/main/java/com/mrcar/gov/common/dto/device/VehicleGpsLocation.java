package com.mrcar.gov.common.dto.device;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 车辆点位消息DTO对象.(用于围栏报警等业务使用)
 *
 * <AUTHOR>
 * @date 2024-11-20
 */
@Data
public class VehicleGpsLocation implements Serializable {

    //tag
    private String carNo;
    private String deviceId;
    private String simNo;
    private String vehicleVin;

    // 车辆编码
    private String vehicleNo;
    // 车辆类型
    private Integer vehicleType;

    private String manfactCode;
    private String deviceType;

    //值 高德坐标
    private BigDecimal longitude;
    private BigDecimal latitude;

    //值 百度坐标
    private BigDecimal lngBaidu;
    private BigDecimal latBaidu;

    private String speed;//gps 速度
    private String direction;//方向角

    // 创建时间
    private Date createDate;

}
