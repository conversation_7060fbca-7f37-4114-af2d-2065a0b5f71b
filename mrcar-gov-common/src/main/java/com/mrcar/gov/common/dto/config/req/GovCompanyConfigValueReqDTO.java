package com.mrcar.gov.common.dto.config.req;

import com.mrcar.gov.common.dto.BaseDTO;
import lombok.Data;

import javax.validation.constraints.NotBlank;


/**
 * <AUTHOR>
 */
@Data
public class GovCompanyConfigValueReqDTO extends BaseDTO {

    /**
     * 所属企业ID
     */
    private Integer companyId;

    @NotBlank(message = "单位code不能为空")
    private String structCode;

    private Integer structId;

    /**
     * 业务配置编码
     *   use_car_apply_driver_type,用车申请驾驶员类型
     *   when_use_car_dispatch_way,用车时的调度方式
     *   rental_way_item,租赁方式
     *   day_driver_type,驾驶员类型
     *   day_dispatch_way,调度方式
     *   day_use_car_max_days,最大用车天数
     *   hour_driver_type,驾驶员类型
     *   hour_dispatch_way,调度方式
     *   hour_use_car_max_days,最大用车天数
     *   return_fence_check,还车围栏校验
     *   use_car_duration_filed,用车计费时长统计字段
     */
    private String businessConfigCode;

    /**
     * 业务配置项编码
     */
    private String businessConfigItemCode;

    /**
     * 选择的值：开关/单选：{"value":"0","label":"否"}，多选:[{"value":"","label":""},{"value":"","label":""}]，文本：""
     */
    private String value;

    private String[] businessConfigCodes;
}
