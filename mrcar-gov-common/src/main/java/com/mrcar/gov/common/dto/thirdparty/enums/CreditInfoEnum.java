package com.mrcar.gov.common.dto.thirdparty.enums;

public enum CreditInfoEnum {

    ID_CARD(1, "idCard"),
    DRIVER_LICENSE(2, "driverLicense"),
    CRIME_INFO(3,"crimeInfo"),
    CAR_LICENSE(4,"carLicense"),
    VEHICLE_LICENSE(5,"vehicleLicense"),
    OCR_TEMPLATE(6,"template"),
    BANK_CARD(7,"bankCard"),
    CAR_VIN(8,"carVin"),
    GENERAL(9,"general"),
    RECOGNIZE_INVOICE(10, "recognizeInvoice"),
    BLICENSE(11, "blicense"),
    BANK_ACCOUNT_PERMIT(12, "bankAccountPermit"),
    VEHICLE_INSURANCE(13, "vehicleInsurance")
    ;

    private int code;
    private String name;

    CreditInfoEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }


    public static CreditInfoEnum getEnumByCode(int code) {
        for (CreditInfoEnum creditInfoEnum : values()) {
            if (creditInfoEnum.code == code) {
                return creditInfoEnum;
            }
        }
        throw new IllegalArgumentException("code is invalid");
    }

    public static CreditInfoEnum getEnumByName(String name) {
        for (CreditInfoEnum creditInfoEnum : values()) {
            if (creditInfoEnum.name.equals(name)) {
                return creditInfoEnum;
            }
        }
        throw new IllegalArgumentException("name is invalid");
    }

    public String getName() {
        return name;
    }
    public int getCode() {
        return code;
    }
}
