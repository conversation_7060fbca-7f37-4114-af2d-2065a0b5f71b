package com.mrcar.gov.common;

import java.util.Objects;
import java.util.stream.Stream;


public enum InvoiceTypeEnum {
    NONE(0,"无发票"),
    SPECIAL_USE(1,"增值税专用发票"),
    ORDINARY_USE(2,"增值税普通发票"),
    ;
    private Integer type;

    private String message;

    InvoiceTypeEnum(Integer type, String message) {
        this.type = type;
        this.message = message;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public static InvoiceTypeEnum getContructionInvoiceType(String name){
        return Stream.of(values()).filter(e-> Objects.equals(e.getMessage(),name)).findFirst().orElse(NONE);
    }

    public static InvoiceTypeEnum getContructionInvoiceName(Integer type){
        return Stream.of(values()).filter(e-> Objects.equals(e.getType(),type)).findFirst().orElse(NONE);
    }
}
