package com.mrcar.gov.common.dto.workflow.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
* @Description: BPM 模型的流程分类的枚举
* @author: hxc
* @Date: 2022/8/3
**/
@Getter
@AllArgsConstructor
public enum BpmModelCategoryEnum {

    DEFAULT("1", "业务流程"), // 对应 BpmForm
    OA("2", "自定义流程") // 业务自己定义的表单，自己进行数据的存储
    ;

    private final String type;
    private final String desc;

    public static BpmModelCategoryEnum getEnum(String type){
        for(BpmModelCategoryEnum b : BpmModelCategoryEnum.values()){
            if(b.getType().equals(type)){
                return b;
            }
        }
        return BpmModelCategoryEnum.DEFAULT;
    }
}
