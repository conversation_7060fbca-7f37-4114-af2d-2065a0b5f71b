package com.mrcar.gov.common.enums.config;

import lombok.Getter;

/**
 * <AUTHOR>
 */
public class PublicConfigValueEnum {
    /**
     * 用车计费时长统计字段 枚举
     */
    @Getter
    public enum UseCarDurationFiledEnum {
        // 用车计费时长统计字段
        START_OR_END_TIME(0, "实际开始/结束时间"),
        ENTRANCE_AND_EXIT_FENCE(1, "出入围栏时间"),
        ;

        /**
         * 值
         */
        private int value;
        /**
         * 名称
         */
        private String label;


        UseCarDurationFiledEnum(int value, String label) {
            this.value = value;
            this.label = label;
        }

        public static UseCarDurationFiledEnum getByValue(int value) {
            for (UseCarDurationFiledEnum item : values()) {
                if (item.getValue()==value) {
                    return item;
                }
            }
            return null;
        }

    }

    // 开启
    @Getter
    public enum RentalWayEnum {
        // 租赁方式
        DAILY(1, "日租"),
        HOUR(2, "分时租赁")
        ;


        /**
         * 值
         */
        private int value;
        /**
         * 名称
         */
        private String label;


        RentalWayEnum(int value, String label) {
            this.value = value;
            this.label = label;
        }

        public static RentalWayEnum getByValue(int value) {
            for (RentalWayEnum item : values()) {
                if (item.getValue()==value) {
                    return item;
                }
            }
            return null;
        }
    }



    //还车围栏校验
    @Getter
    public enum ReturnFenceCheckEnum {

        INSIDE_THE_FENCE_RETURN_CAR(1, "必须在围栏内还车"),
        NO_LIMIT_RETURN_CAR_PLACE(2, "不限制还车地点"),

        ;

        /**
         * 值
         */
        private int value;
        /**
         * 名称
         */
        private String label;


        ReturnFenceCheckEnum(int value, String label) {
            this.value = value;
            this.label = label;
        }

        public static ReturnFenceCheckEnum getByValue(int value) {
            for (ReturnFenceCheckEnum item : values()) {
                if (item.getValue()==value) {
                    return item;
                }
            }
            return null;
        }

    }

}
