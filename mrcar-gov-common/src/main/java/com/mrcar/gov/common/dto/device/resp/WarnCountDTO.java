package com.mrcar.gov.common.dto.device.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @program: mrcar-iot-common
 * @description: 报警数量dto
 * @author: ljw
 * @create: 2022-11-18 14:47
 **/
@Data
public class WarnCountDTO {

    @ApiModelProperty(value = "围栏报警数量")
    private Integer fenceCount;

    @ApiModelProperty(value = "离线报警数量")
    private Integer offCount;

    @ApiModelProperty(value = "拆机报警数量")
    private Integer removeCount;

    @ApiModelProperty(value = "报警总数量")
    private Integer totalCount;

}
