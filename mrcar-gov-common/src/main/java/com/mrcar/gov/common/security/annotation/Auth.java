package com.mrcar.gov.common.security.annotation;


import java.lang.annotation.Target;
import java.lang.annotation.Retention;
import java.lang.annotation.ElementType;
import java.lang.annotation.Documented;
import java.lang.annotation.RetentionPolicy;

/**
 * <AUTHOR>
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.TYPE, ElementType.METHOD})
public @interface Auth {

    /**
     * 是否需要登录，默认需要.
     *
     * @return 是否需要登录
     */
    boolean login() default true;
}
