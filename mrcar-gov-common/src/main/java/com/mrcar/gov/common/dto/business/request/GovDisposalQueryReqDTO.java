package com.mrcar.gov.common.dto.business.request;

import com.mrcar.gov.common.dto.PageParamDTO;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 处置调拨明细列表查询参数
 *
 * <AUTHOR>
 * @date 2024/11/21 9:39
 */
@Data
public class GovDisposalQueryReqDTO extends PageParamDTO {


    /**
     * 车牌号
     */
    private String vehicleLicense;

    /**
     * 车架号
     */
    private String vehicleVin;

    /**
     * 申请单号
     */
    private String applyNo;

    /**
     * 处置类型：1.处置 ;2:调拨
     */
    private Integer disposalType;

    /**
     * 处置方式 1报废 2拍卖 3调拨 4损失核销 5厂家回收 6 对外捐赠
     */
    private Integer disposalMethod;

    /**
     * 处置状态
     */
    private Integer disposalStatus;

    /**
     * 填处置调拨开始时间
     */
    private Date disposalStartTime;
    /**
     * 填处置调拨结束时间
     */
    private Date disposalEndTime;

    /**
     * 车辆所有人 筛选左侧部门使用
     */
    private String vehicleBelongDeptCode;

    /**
     * 车辆所有人list 筛选左侧部门使用
     */
    private Set<String> vehicleBelongDeptCodeList;


}
