package com.mrcar.gov.common.dto.order.resp;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;


@ApiModel("车杂费相关")
@Data
public class GovPublicDetailVehicleFeeRespDTO {

    /**
     * 车杂费单号
     */
    private String vehicleFeeCode;

    /**
     * 费用合计
     */
    private String totalFee = "0";

    /**
     * 租金(社会租赁有值)
     */
    private String rentFee;


    /**
     * 车杂费
     */
    private List<GovPublicVehicleFeeDTO> vehicleFeeList;

}
