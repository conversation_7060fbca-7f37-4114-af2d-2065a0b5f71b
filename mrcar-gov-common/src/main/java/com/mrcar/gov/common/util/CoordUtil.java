package com.mrcar.gov.common.util;

import java.math.BigDecimal;

import com.google.common.geometry.S2LatLng;
import com.mrcar.gov.common.dto.catnet.Point;
import com.mrcar.gov.common.enums.carnet.CoordTypeEnum;

public final class CoordUtil
{
    private static final double A = 6378245.0D;
    private static final double EE = 0.0066934216229659433D;
    private static final double PI = 3.1415926535897931D;
    private static final double X_PI = 52.359877559829883D;
    private static final int DEF_DECIMALS = 6;

    public static boolean outOfChina(double lat, double lon)
    {
        if ((lon < 72.004000000000005D) || (lon > 137.8347D)) {
            return true;
        }

        return ((lat < 0.82930000000000004D) || (lat > 55.827100000000002D));
    }


    public static void transform(double wgLat, double wgLon, double[] latlng) {
        if (outOfChina(wgLat, wgLon)) {
            latlng[0] = wgLat;
            latlng[1] = wgLon;
            return;
        }
        double dLat = transformLat(wgLon - 105.0, wgLat - 35.0);
        double dLon = transformLon(wgLon - 105.0, wgLat - 35.0);
        double radLat = wgLat / 180.0 * PI;
        double magic = Math.sin(radLat);
        magic = 1 - EE * magic * magic;
        double sqrtMagic = Math.sqrt(magic);
        dLat = (dLat * 180.0) / ((A * (1 - EE)) / (magic * sqrtMagic) * PI);
        dLon = (dLon * 180.0) / (A / sqrtMagic * Math.cos(radLat) * PI);
        latlng[0] = wgLat + dLat;
        latlng[1] = wgLon + dLon;
    }


    public static double transformLat(double x, double y)
    {
        double ret = -100.0D + 2.0D * x + 3.0D * y + 0.20000000000000001D * y * y + 0.10000000000000001D * x * y + 0.20000000000000001D * Math.sqrt(Math.abs(x));
        ret += (20.0D * Math.sin(6.0D * x * 3.1415926535897931D) + 20.0D * Math.sin(2.0D * x * 3.1415926535897931D)) * 2.0D / 3.0D;
        ret += (20.0D * Math.sin(y * 3.1415926535897931D) + 40.0D * Math.sin(y / 3.0D * 3.1415926535897931D)) * 2.0D / 3.0D;
        ret += (160.0D * Math.sin(y / 12.0D * 3.1415926535897931D) + 320.0D * Math.sin(y * 3.1415926535897931D / 30.0D)) * 2.0D / 3.0D;
        return ret;
    }

    public static double transformLon(double x, double y) {
        double ret = 300.0D + x + 2.0D * y + 0.10000000000000001D * x * x + 0.10000000000000001D * x * y + 0.10000000000000001D * Math.sqrt(Math.abs(x));
        ret += (20.0D * Math.sin(6.0D * x * 3.1415926535897931D) + 20.0D * Math.sin(2.0D * x * 3.1415926535897931D)) * 2.0D / 3.0D;
        ret += (20.0D * Math.sin(x * 3.1415926535897931D) + 40.0D * Math.sin(x / 3.0D * 3.1415926535897931D)) * 2.0D / 3.0D;
        ret += (150.0D * Math.sin(x / 12.0D * 3.1415926535897931D) + 300.0D * Math.sin(x / 30.0D * 3.1415926535897931D)) * 2.0D / 3.0D;
        return ret;
    }

    public static Point worldToMars(Point p)
    {
        double wgLon = p.getLng();
        double wgLat = p.getLat();

        double dLat = transformLat(wgLon - 105.0D, wgLat - 35.0D);
        double dLon = transformLon(wgLon - 105.0D, wgLat - 35.0D);
        double radLat = wgLat / 180.0D * 3.1415926535897931D;
        double magic = Math.sin(radLat);
        magic = 1D - 0.0066934216229659433D * magic * magic;
        double sqrtMagic = Math.sqrt(magic);
        dLat = dLat * 180.0D / 6335552.7170004258D / magic * sqrtMagic * 3.1415926535897931D;
        dLon = dLon * 180.0D / 6378245.0D / sqrtMagic * Math.cos(radLat) * 3.1415926535897931D;
        return
                new Point(getDecimalScale(Double.valueOf(wgLon + dLon), 6),
                        getDecimalScale(Double.valueOf(wgLat + dLat),
                                6), CoordTypeEnum.MARS);
    }

    public static Point baiduToMars(Point p)
    {
        double bd_lat = p.getLat();
        double bd_lon = p.getLng();

        double x = bd_lon - 0.0064999999999999997D; double y = bd_lat - 0.0060000000000000001D;
        double z = Math.sqrt(x * x + y * y) - 0.000020000000000000002D * Math.sin(y * 52.359877559829883D);
        double theta = Math.atan2(y, x) - 0.0000030000000000000001D * Math.cos(x * 52.359877559829883D);
        double ggLon = z * Math.cos(theta);
        double ggLat = z * Math.sin(theta);
        return
                new Point(getDecimalScale(Double.valueOf(ggLon), 6),
                        getDecimalScale(Double.valueOf(ggLat),
                                6), CoordTypeEnum.MARS);
    }

    public static Point marsToWorld(Point p) throws RuntimeException {
        if (outOfChina(p.getLat(), p.getLng()))
            throw new RuntimeException("Coordinate conversion failure: coordinates are beyond the range of GCJ-02 coordinates");

        double lat = p.getLat();
        double lon = p.getLng();

        Point r = worldToMars(p);
        return
                new Point(getDecimalScale(Double.valueOf(lon + lon - r.getLng()), 6),
                        getDecimalScale(Double.valueOf(lat + lat - r.getLat
                                ()), 6), CoordTypeEnum.WORLD);
    }

    public static Point marsToBaidu(Point p) throws RuntimeException
    {
        if (outOfChina(p.getLat(), p.getLng())) {
            throw new RuntimeException("Coordinate conversion failure: coordinates are beyond the range of GCJ-02 coordinates");
        }

        double ggLat = p.getLat();
        double ggLon = p.getLng();

        double x = ggLon; double y = ggLat;
        double z = Math.sqrt(x * x + y * y) + 0.000020000000000000002D * Math.sin(y * 52.359877559829883D);
        double theta = Math.atan2(y, x) + 0.0000030000000000000001D * Math.cos(x * 52.359877559829883D);
        double bd_lon = z * Math.cos(theta) + 0.0064999999999999997D;
        double bd_lat = z * Math.sin(theta) + 0.0060000000000000001D;

        return
                new Point(getDecimalScale(Double.valueOf(bd_lon), 6), getDecimalScale(Double.valueOf(bd_lat),
                                6), CoordTypeEnum.BAIDU);
    }

    public static Point worldToBaidu(Point p) throws RuntimeException {
        Point mars = worldToMars(p);
        return marsToBaidu(mars);
    }

    public static Point baiduToWorld(Point p) throws RuntimeException {
        Point mars = baiduToMars(p);
        return marsToWorld(mars);
    }

    public static double getDecimalScale(Double value, int decimals)
    {
        BigDecimal b = new BigDecimal(value.doubleValue());
        return b.setScale(decimals, 1).doubleValue();
    }
    public static double simulateDistance(double startLng, double startLat, double endLng, double endLat)
    {
        S2LatLng startPoint = S2LatLng.fromDegrees(startLat, startLng);
        S2LatLng endPoint = S2LatLng.fromDegrees(endLat, endLng);
        return startPoint.getDistance(endPoint, 6367000.0D);
    }

    public static void main(String[] args) {
        double distance = simulateDistance(116.952002, 40.395906, 116.847503, 40.351853);
        System.err.println("distance===="+distance);
    }
}