package com.mrcar.gov.common.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.google.common.collect.Lists;
import com.google.gson.annotations.Expose;
import com.mrcar.gov.common.constant.order.IsShowEnum;
import lombok.Data;

import java.util.List;

/**
 * 登录账号的菜单信息
 * <AUTHOR>
 * @date : 2023/1/14
 */
@Data
public class AccountMenu {
    /**
     * 系统编码
     */
    private String permSysCode;
    /**
     * 菜单权限编码
     */
    private String permCode;
    /**
     * 菜单权限名称
     */
    private String permName;
    /**
     * 跳转该菜单的路由
     */
    private String menuUrl;
    /**
     * 菜单图标(icon)
     */
    private String menuIcon;

    /**
     * 是否展示数量角标 0:不展示 1:展示
     */
    private Integer showCornerMark = IsShowEnum.NO_SHOW.getCode();

    /**
     * 角标数量
     */
    private Integer cornerMarkCount = 0;


    /** 排序字段 **/
    private Integer sortSeq;

    /** APP端功能跳转类型 app跳转原生 web 跳转H5 thirdWeb 跳转第三方链接 **/
    private String jumpType;

    /** 起始的app版本-适用于menu_open_mode=2的情况。 **/
    private Integer startAppVersion;

    /**
     * 原路由（辅助前端字段）
     */
    private String originalRoute;
    /**
     * 子菜单列表
     */
    private List<AccountMenu> submenuList;

    @JsonIgnore
    @Expose(serialize = false)
    private String tmpMenuUrl;
    @JsonIgnore
    @Expose(serialize = false)
    private int level = 0;

    public void setMenuUrl(String menuUrl){
        this.menuUrl = menuUrl;
    }
}
