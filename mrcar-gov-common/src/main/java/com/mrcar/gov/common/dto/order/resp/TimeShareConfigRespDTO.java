package com.mrcar.gov.common.dto.order.resp;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonManagedReference;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: mengshuang
 * @Date: 2025/4/10 18:08
 * @Param:
 * @Return:
 * @Description:
 **/
@Data
public class TimeShareConfigRespDTO {
    /**
     * 自增主键
     */
    private Integer id;

    /**
     * (社会租赁)供应商code
     */
    private String supplierCode;

    /**
     * (社会租赁)供应商name
     */
    private String supplierName;

    /**
     * 定价类型 0按车辆定价  1 按车系定价
     */
    private Integer priceType;

    /**
     * 车辆类型
     */
    private Integer vehicleType;

    /**
     * 车辆类型名称
     */
    private String vehicleTypeName;

    /**
     * 车辆品牌id
     */
    private Integer vehicleBrandId;

    /**
     * 车辆品牌
     */
    private String vehicleBrandName;

    /**
     * 车系code码
     */
    private Integer vehicleSeriesId;

    /**
     * 车系
     */
    private String vehicleSeriesName;

    /**
     * 是否配置多时段 0:未配置  1:已配置
     */
    private Integer isTimePeriods;

    /**
     * 是否配置限额规则 0:未配置  1:已配置
     */
    private Integer isLimitRule;

    /**
     * 快照code
     */
    private String snapshotCode;

    /**
     * 当月最高限额
     */
    private BigDecimal monthLimit;

    /**
     * 当天最高限额
     */
    private BigDecimal dailyLimit;

    /**
     * 企业id
     */
    private Integer companyId;

    /**
     * 企业名
     */
    private String companyName;

    /**
     * 创建人编码
     */
    private String createCode;

    /**
     * 创建人
     */
    private String createName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改code
     */
    private String updateCode;

    /**
     * 修改人
     */
    private String updateName;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 明细信息
     */
    List<TimeShareConfigDetailRespDTO> timeShareConfigDetailRespDTOList;


    /**
     * 自驾里程费明细信息 driveType=1 分组
     */
    List<TimeShareConfigDetailRespDTO> selfDrivingtimeShareConfigDetailList;

    /**
     * 带驾里程费明细信息 driveType=2 分组
     */
    List<TimeShareConfigDetailRespDTO> otherDrivingtimeShareConfigDetailList;


    public void setTimeShareConfigDetailRespDTOList(List<TimeShareConfigDetailRespDTO> timeShareConfigDetailRespDTOList) {
        this.timeShareConfigDetailRespDTOList = timeShareConfigDetailRespDTOList;
        if (CollectionUtils.isEmpty(timeShareConfigDetailRespDTOList)) {
            this.selfDrivingtimeShareConfigDetailList = null;
            this.otherDrivingtimeShareConfigDetailList = null;
        } else {
            this.selfDrivingtimeShareConfigDetailList = timeShareConfigDetailRespDTOList.stream()
                    .filter(item -> item.getDrivingType() == 1).map(item -> {
                        TimeShareConfigDetailRespDTO copy = new TimeShareConfigDetailRespDTO();
                        BeanUtils.copyProperties(item, copy);
                        return copy;
                    })
                    .sorted(Comparator.comparing(TimeShareConfigDetailRespDTO::getId)).collect(Collectors.toList());
            this.otherDrivingtimeShareConfigDetailList = timeShareConfigDetailRespDTOList.stream()
                    .filter(item -> item.getDrivingType() == 2).map(item -> {
                        TimeShareConfigDetailRespDTO copy = new TimeShareConfigDetailRespDTO();
                        BeanUtils.copyProperties(item, copy);
                        return copy;
                    }).sorted(Comparator.comparing(TimeShareConfigDetailRespDTO::getId)).collect(Collectors.toList());
        }
    }


}
