package com.mrcar.gov.common.constant.asset;

/**
 * <AUTHOR>
 * @date 2024/11/11 18:21
 */
public enum WorkStatusEnum {
    //任务状态 1:无任务;2:任务中
    NO_TASK(1,"无任务"),
    TASK_ING(2,"任务中"),
    ;

    private final int code;
    private final String desc;

    WorkStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDesc(int code) {
        for (WorkStatusEnum workStatusEnum :WorkStatusEnum.values()) {
            if(workStatusEnum.getCode() == code){
                return workStatusEnum.getDesc();
            }
        }
        return "";
    }
}
