package com.mrcar.gov.common.dto.thirdparty.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

@Data
public class OcrGeneralDTO {

    /**
     * algoVersion
     */
    @JSONField(name = "algo_version")
    private String algoVersion;
    /**
     * content
     */
    @JSONField(name = "content")
    private String content;
    /**
     * height
     */
    @JSONField(name = "height")
    private int height;
    /**
     * orgHeight
     */
    @JSONField(name = "orgHeight")
    private int orgHeight;
    /**
     * orgWidth
     */
    @JSONField(name = "orgWidth")
    private int orgWidth;
    /**
     * prismVersion
     */
    @JSONField(name = "prism_version")
    private String prismVersion;
    /**
     * prismWnum
     */
    @JSONField(name = "prism_wnum")
    private int rowNums;
    /**
     * prismWordsinfo
     */
    @JSONField(name = "prism_wordsInfo")
    private List<RowInfo> rowInfos;
    /**
     * width
     */
    @JSONField(name = "width")
    private int width;

    @Data
    public static class RowInfo {
        /**
         * angle
         */
        @JSO<PERSON>ield(name = "angle")
        private int angle;
        /**
         * direction
         */
        @JSONField(name = "direction")
        private int direction;
        /**
         * height
         */
        @JSONField(name = "height")
        private int height;
        /**
         * pos
         */
        @JSONField(name = "pos")
        private List<Position> pos;
        /**
         * prob
         */
        @JSONField(name = "prob")
        private int prob;
        /**
         * width
         */
        @JSONField(name = "width")
        private int width;
        /**
         * word
         */
        @JSONField(name = "word")
        private String word;
        /**
         * x
         */
        @JSONField(name = "x")
        private int x;
        /**
         * y
         */
        @JSONField(name = "y")
        private int y;


        @Data
        public static class Position {
            /**
             * x
             */
            @JSONField(name = "x")
            private int x;
            /**
             * y
             */
            @JSONField(name = "y")
            private int y;
        }
    }
}
