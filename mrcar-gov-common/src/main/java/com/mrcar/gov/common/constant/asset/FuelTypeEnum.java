package com.mrcar.gov.common.constant.asset;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 动力类型
 * <AUTHOR>
 * @date 2024/11/14 17:26
 */
@Getter
public enum FuelTypeEnum {
    //0 汽油 1柴油 2电力 3油电 4油气 5天然气 6 氢能源 7 甲醇
    GASOLINE(0,"汽油"),
    DIESEL(1,"柴油"),
    ELECTRICITY(2,"电力"),
    GASOLINE_ELECTRICITY(3,"油电"),
    GASOLINE_DIESEL(4,"油气"),
    GAS(5,"天然气"),
    HYDROGEN(6,"氢能源"),
    ACETOLE(7,"甲烷")
    ;

    private Integer code;
    private String name;

    FuelTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }
    public static String getNameByCode(Integer code){
        for (FuelTypeEnum fuelTypeEnum : FuelTypeEnum.values()) {
            if(fuelTypeEnum.getCode().equals(code)){
                return fuelTypeEnum.getName();
            }
        }
        return "";
    }

    public static List<String> getDescList() {
        return Arrays.stream(FuelTypeEnum.values()).map(FuelTypeEnum::getName).collect(Collectors.toList());
    }

    public static Integer getCodeByDesc(String desc) {
        for (FuelTypeEnum value : FuelTypeEnum.values()) {
            if (Objects.equals(value.getName(), desc)) {
                return value.getCode();
            }
        }
        return null;
    }
}
