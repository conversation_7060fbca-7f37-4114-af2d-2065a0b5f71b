package com.mrcar.gov.common.constant.iot;

import lombok.Getter;

/**
 * 节假日类型枚举
 * <AUTHOR>
 * @date 2024/12/18 14:36
 */
@Getter
public enum HolidayTypeEnum {
    // 元旦 春节 清明节 劳动节 端午节 中秋节 国庆节 周六 周日

    YUAN_DAY(1, "元旦"),
    CHUN_JIE(2, "春节"),
    QING_MING(3, "清明节"),
    LAO_DONG(4, "劳动节"),
    DONG_WU(5, "端午节"),
    ZHONG_QIU(6, "中秋节"),
    GONG_QING(7, "国庆节"),
    SATURDAY(8, "周六"),
    SUNDAY(9, "周日");
    ;

    private Integer code;
    private String desc;
    HolidayTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    public static String getDescByCode(Integer code){
        for (HolidayTypeEnum holidayTypeEnum : HolidayTypeEnum.values()) {
            if(holidayTypeEnum.getCode().equals(code)){
                return holidayTypeEnum.getDesc();
            }
        }
        return "";
    }
    public static Integer getCodeByDesc(String desc){
        for (HolidayTypeEnum holidayTypeEnum : HolidayTypeEnum.values()) {
            if(holidayTypeEnum.getDesc().equals(desc)){
                return holidayTypeEnum.getCode();
            }
        }
        return 0;
    }
}
