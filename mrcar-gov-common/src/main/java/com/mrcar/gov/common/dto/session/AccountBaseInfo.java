package com.mrcar.gov.common.dto.session;

import lombok.Data;

import java.io.Serializable;

/**
 * 用户基本信息.
 *
 * <AUTHOR>
 * @date 2024-11-07
 */
@Data
public class AccountBaseInfo implements Serializable {
    /**
     * 用户ID
     */
    private Integer userId;
    /**
     * 用户编码
     */
    private String userCode;
    /**
     * 用户姓名
     */
    private String userName;
    /**
     * 员工类型 1:普通员工 2:司机 3:维修厂员工 4:保险公司员工 99 首汽技术支持
     */
    private Integer userType;
    /**
     * 人员状态；1：正常；0：停用
     */
    private Integer userStatus;
    /**
     * 拼音名
     */
    private String pinyinName;
    /**
     * 用户手机号
     */
    private String mobile;
    /**
     * 员工岗位名称
     */
    private String position;
    /**
     * 用户性别；1：男；2：女
     */
    private Integer gender;
    /**
     * 头像
     */
    private String headIcon;
    /**
     * 用户邮箱
     */
    private String email;

    /**
     * 员工所属部门ID
     */
    private Integer belongStructId;

    /**
     * 员工所属部门编码
     */
    private String belongStructCode;

    /**
     * 员工所属部门名称
     */
    private String belongStructName;

    /**
     * 员工所属单位ID
     */
    private Integer belongDeptId;

    /**
     * 员工所属单位编码
     */
    private String belongDeptCode;

    /**
     * 员工所属单位名称
     */
    private String belongDeptName;

    /**
     * 创建人编码
     */
    private String createCode;

    /**
     * 创建人姓名
     */
    private String createName;

    /**
     * 修改人编码
     */
    private String updateCode;

    /**
     * 修改人姓名
     */
    private String updateName;
    /**
     * 个人数据权限：1公司；2部门；3个人
     */
    private Integer dataPermType;

}
