package com.mrcar.gov.common.dto.business.request;

import com.mrcar.gov.common.dto.BaseDTO;
import com.mrcar.gov.common.dto.FileDTO;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 处置上传办理结果请求参数
 *
 * <AUTHOR>
 * @date 2024/11/21 9:41
 */
@Data
public class GovDisposalUpLoadResultReqDTO extends BaseDTO {

    /**
     * 处置编码
     */
    @NotBlank(message = "调拨明细处置编码不能为空")
    private String disposalNo;

    /**
     * 使用人部门
     */
    private String vehicleUseDeptCode;

    /**
     * 使用人部门名称
     */
    private String vehicleUseDeptName;

    /**
     * 使用单位部门
     */
    private String vehicleUseStructCode;

    /**
     * 使用单位名称
     */
    private String vehicleUseStructName;

    /**
     * 说明
     */
    private String disposalRemark;

    /**
     * 填处置调拨时间
     */
    @NotNull(message = "填处置调拨时间不能为空")
    private Date disposalTime;

    /**
     * 附件
     */
    @NotEmpty(message = "附件不能为空")
    private List<FileDTO> fileList;
}
