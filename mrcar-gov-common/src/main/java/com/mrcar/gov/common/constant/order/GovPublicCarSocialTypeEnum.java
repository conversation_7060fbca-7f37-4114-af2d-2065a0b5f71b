package com.mrcar.gov.common.constant.order;

import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
public enum GovPublicCarSocialTypeEnum {


    // 公务用车
    GOVERNMENT_VEHICLE(1, "公务用车"),

    // 社会租赁
    SOCIAL_RENTAL(2, "社会租赁"),

    // 社会租赁供应商
    SOCIAL_RENTAL_PROVIDER(3, "社会租赁供应商"),

    //公务用车-紧急台账
    GOVERNMENT_EMERGENCY_VEHICLE(4, "公务用车-紧急");

    private final int code;
    private final String description;

    // 构造函数，用于初始化枚举值
    GovPublicCarSocialTypeEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    // 获取枚举值的代码
    public int getCode() {
        return code;
    }

    // 获取枚举值的描述
    public String getDescription() {
        return description;
    }
}