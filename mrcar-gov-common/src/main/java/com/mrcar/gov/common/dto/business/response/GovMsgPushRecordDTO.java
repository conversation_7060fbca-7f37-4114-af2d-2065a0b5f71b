package com.mrcar.gov.common.dto.business.response;


import com.mrcar.gov.common.constant.business.*;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/12/27 18:48
 */
@Data
public class GovMsgPushRecordDTO {

    /**
     * id
     */
    private Integer id;

    /**
     * 企业id
     */
    private Integer companyId;

    /**
     * 记录编号
     */
    private String recordNo;

    /**
     * 推送批次号
     */
    private String batchNo;

    /**
     * 消息类型 1模板消息 2公告消息
     */
    private Integer msgType;
    // 消息类型描述
    private String msgTypeDes;

    /**
     * 模板或公告编码
     */
    private String msgNo;

    /**
     * 模板或公告名称
     */
    private String msgName;

    /**
     * 推送开始时间
     */
    private Date pushStartTime;

    /**
     * 推送结束时间
     */
    private Date pushEndTime;

    /**
     * 推送数量
     */
    private Integer pushNum;

    /**
     * 触达数量
     */
    private Integer reachNum;

    /**
     * 已读数量
     */
    private Integer readNum;
    /**
     * 未读数量
     */
    private Integer unreadNum;

    /**
     * 记录状态 0:删除 1:正常
     */
    private Integer recordStatus;
    // 记录状态描述
    private String recordStatusDesc;

    /**
     * 推送方式 1站内信 2短信
     */
    private Integer pushType;
    // 推送方式描述
    private String pushTypeDesc;

    /**
     * 推送渠道 1pc端；2h5；3APP通知
     */
    private Integer pushChannel;
    // 推送渠道描述
    private String pushChannelDesc;

    /**
     * 推送状态 0失败 1成功 2推送中
     */
    private Integer pushStatus;
    // 推送状态描述
    private String pushStatusDesc;

    /**
     * 消息场景 1埋点
     */
    private Integer msgScene;
    // 消息场景描述
    private String msgSceneDes;

    /**
     * 消息模块
     */
    private Integer msgModule;
    // 消息模块描述
    private String msgModuleDes;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 消息触达率
     */
    private String reachRate;


    public void setMsgType(Integer msgType) {
        this.msgType = msgType;
        this.msgTypeDes = GovMsgTypeEnum.getDescByCode(msgType);
    }

    public void setRecordStatus(Integer recordStatus) {
        this.recordStatus = recordStatus;
        this.recordStatusDesc = GovMsgPushRecordStatusEnum.getDescByCode(recordStatus);
    }

    public String getRecordStatusDesc() {
        if(this.pushStatus != GovMsgPushStatusEnum.SUCCESS.getCode()){
            return "未完成";
        }
        return recordStatusDesc;
    }

    public void setPushStatus(Integer pushStatus) {
        this.pushStatus = pushStatus;
        this.pushStatusDesc = GovMsgPushStatusEnum.getDescByCode(pushStatus);
    }

    public void setMsgScene(Integer msgScene) {
        this.msgScene = msgScene;
    }

    public void setMsgModule(Integer msgModule) {
        this.msgModule = msgModule;
    }

    public void setPushType(Integer pushType) {
        this.pushType = pushType;
        this.pushTypeDesc = GovMsgPushTypeEnum.getDescByCode(pushType);
    }

    public void setPushChannel(Integer pushChannel) {
        this.pushChannel = pushChannel;
        this.pushChannelDesc = GovMsgPushChannelEnum.getDescByCode(pushChannel);
    }

    public void setPushEndTime(Date pushEndTime) {
        this.pushEndTime = pushEndTime;
    }

}
