package com.mrcar.gov.common.dto.workflow.process.resp;

import com.alibaba.fastjson.JSONObject;
import com.mrcar.gov.common.dto.workflow.enums.BpmModelCategoryEnum;
import com.mrcar.gov.common.dto.workflow.enums.BpmModelFormTypeEnum;
import lombok.Data;
import java.util.List;

/**
 * 流程定义响应 DTO
 * <p>
 * 包含了流程定义的基本信息和表单配置等内容。
 * </p>
 *
 * <AUTHOR>
 * @date 2024/9/6 16:17
 */
@Data
public class AppBpmProcessDefinitionRespDTO {

    /**
     * 编号
     * 示例值: 1024
     */
    private String id;

    /**
     * 版本
     * 示例值: 1
     */
    private Integer version;

    /**
     * 流程名称
     * 示例值: 用车
     */
    private String name;

    /**
     * 流程描述
     * 示例值: 我是描述
     */
    private String description;

    /**
     * 流程分类，参见 bpm_model_category 数据字典
     * 示例值: 1
     */
    private String category;

    /**
     * 流程分类名称
     */
    private String categoryStr;

    /**
     * 表单类型，参见 bpm_model_form_type 数据字典
     * 示例值: 1
     */
    private Integer formType;

    /**
     * 表单编号
     * 示例值: 1024
     * 在表单类型为 {@link BpmModelFormTypeEnum#CUSTOM} 时，必须非空
     */
    private Long formId;

    /**
     * 表单项的数组，JSON 字符串的数组。在表单类型为 {@link BpmModelFormTypeEnum#CUSTOM} 时，必须非空
     */
    private List<JSONObject> formFieldsList;

    /**
     * 中断状态，参见 SuspensionState 枚举
     * 示例值: 1
     */
    private Integer suspensionState;

    /**
     * 关联子流程名称
     * 示例值: 中铁建审批
     */
    private String subProcessStr;

    /**
     * 关联父流程名称
     * 示例值: 中铁建审批
     */
    private String parentProcessStr;

    /**
     * 设置流程分类及对应的分类名称
     *
     * @param category 流程分类
     */
    public void setCategory(String category) {
        this.category = category;
        this.categoryStr = BpmModelCategoryEnum.getEnum(category).getDesc();
    }
}
