package com.mrcar.gov.common.errorcode;

import com.izu.framework.web.rest.response.ResultMessage;

/**
 * @ClassName IotErrorCode
 * @Description 车机服务错误码
 * <AUTHOR>
 * @Date 2019/5/15 14:54
 * @Version 1.0
 */
public class CarnetErrorCode {

    @ResultMessage("至少需要参数{0}中的任意一个")
    public static final int PARAMS_ATLEAST_ONE = 10001;

    @ResultMessage("未查找到相关绑定车机记录")
    public static final int NO_RECORDS = 10002;

    @ResultMessage("请指定查询的时间区间")
    public static final int NO_CREATE_END_DATE = 10003;

    @ResultMessage("查询的时间区间最长为{0}个月")
    public static final int CREATE_END_DATE_LOW = 10004;

    @ResultMessage("未查找到相关绑定设备记录")
    public static final int NO_DEVICE_RECORDS = 10005;

    @ResultMessage("查询的最长时间跨度不能超过{0}天")
    public static final int TIME_REGION_OUT = 10006;

    @ResultMessage("非法的坐标系参数")
    public static final int COORDINATE_ERROR = 10007;



    @ResultMessage("设备未绑定车辆")
    public static final int DEVICE_NO_BIND = 9001;
    @ResultMessage("设备绑定车辆与车牌号不匹配")
    public static final int DEVICE_NOT_MATCH_CARNO = 9002;

    @ResultMessage("设备{0}已绑定车辆,设备号为{1}")
    public static final int DEVICE_HAVING_BINDED = 9003;
    @ResultMessage("车辆{0}已绑定同类型设备,设备号为{1}")
    public static final int CAR_BIND_TYPE_REPEAT = 9004;




    /*----------- 设备绑定-------------*/
    @ResultMessage("设备已报废")
    public static final int DEVICE_OFF = 1003;

    @ResultMessage("设备不存在")
    public static final int DEVICE_IS_NOT_EXIST = 1000;

    @ResultMessage("设备已经绑定了车辆")
    public static final int DEVICE_IS_INSTALL = 1001;

    @ResultMessage("设备已存在")
    public static final int DEVICE_IS_EXIST = 1002;


    @ResultMessage("车辆不存在")
    public static final int VEHICLE_IS_NOT_EXIST = 2000;

    /*------------参数判断--------------*/
    @ResultMessage("设备号不能为空")
    public static final int DEVICE_NO_NULL = 2001;

    @ResultMessage("sim号不能为空")
    public static final int SIM_NO_NULL = 2002;

    @ResultMessage("车架号不能为空")
    public static final int VIN_NULL = 2003;

    @ResultMessage("厂商id不能为空")
    public static final int MANFACT_ID_NULL = 2004;

    @ResultMessage("型号id不能为空")
    public static final int MODEL_ID_NULL = 2005;

    @ResultMessage("设备类型不能为空")
    public static final int DEVICE_TYPE_NUll = 2006;

    @ResultMessage("设备id不能为空")
    public static final int DEVICE_ID_NUll = 2007;

    @ResultMessage("厂商编码不能为空")
    public static final int MANFACT_CODE_NUll = 2008;

    @ResultMessage("厂商名不能为空")
    public static final int MANFACT_NAME_NUll = 2009;

    @ResultMessage("厂商顺序号不能为空")
    public static final int MANFACT_ORDER_NUM = 2010;


    @ResultMessage("厂商id不能为空")
    public static final int MANFACT_ID_NUll = 2011;

    @ResultMessage("型号名不能为空")
    public static final int MODEL_NAME_NULL = 2012;

    @ResultMessage("型号名不能为空")
    public static final int MODEL_ORDER_NUM = 2013;

    @ResultMessage("车牌号不能为空")
    public static final int LICENSE_NULL = 2014;


    @ResultMessage("旧sim号不能为空")
    public static final int OLD_SIM_NO_NULL = 2015;


    /*------------设备添加，修改。。。-----------------*/
    @ResultMessage("厂商不存在")
    public static final int MANFACT_ID_ERROR = 3000;

    @ResultMessage("型号不存在")
    public static final int MODEL_ID_ERROR = 3001;

    @ResultMessage("设备sim卡号重复")
    public static final int SIM_NO_ERROR = 3002;

    @ResultMessage("设备号重复")
    public static final int DEVICE_NO_ERROR = 3003;

    @ResultMessage("参数错误")
    public static final int PARAM_ERROR = 3004;

    @ResultMessage("设备未解绑,不能报废")
    public static final int DEVICE_NOT_REMOVE_BIND = 3005;

    @ResultMessage("设备已报废,不能再次报废")
    public static final int DEVICE_SCRAPED = 3006;

    /*--------------厂商----------------*/
    @ResultMessage("厂商已存在")
    public static final int MANFACT_EXIST = 3007;

    @ResultMessage("厂商不存在")
    public static final int MANFACT_NOT_EXIST = 3008;

    @ResultMessage("厂商已停用")
    public static final int MANFACT_STOPED = 3009;

    @ResultMessage("顺序号重复")
    public static final int ORDER_NUM_EXIST = 3010;

    @ResultMessage("厂商存在未报废设备")
    public static final int MANFACT_HAVE_DEVICE = 3011;

    @ResultMessage("型号存在未报废设备")
    public static final int MODEL_HAVE_DEVICE = 3012;

    @ResultMessage("型号已停用")
    public static final int MODEL_STOPD = 3013;




    /*------------4000 开始到 4050 是鉴权相关错误码 --------------*/
    @ResultMessage("鉴权失败")
    public static final int CHECK_ERROR = 4001;
    @ResultMessage("appName已存在")
    public static final int APP_NAME_EXIST = 4002;




    /*------------4500 开始到 4599 是定位相关错误码 --------------*/
    @ResultMessage("请求参数不符合规范或超出数量限制")
    public static final int LOC_DISTANCE_ESTIMATE_ERROR= 4500;



    /*----- 指令下发9100-9199 -------------*/

    @ResultMessage("未查找到该命令")
    public static final int ORDER_NO_FOUND = 9100;

    @ResultMessage("车架号与车牌号不匹配")
    public static final int ORDER_CAR_INFO_UNMATCH = 9101;

    @ResultMessage("缺少命令详情或命令详情有误")
    public static final int ORDER_NO_DETAIL_ERROR= 9102;

    @ResultMessage("车辆未绑定车机设备")
    public static final int CAR_NO_BIND_DEVICE = 9103;

    @ResultMessage("操作过于频繁,请稍后再试")
    public static final int ORDER_TOO_MUCH_ERROR = 9104;

    @ResultMessage("终端响应超时")
    public static final int TIME_OUT = 9105;

    @ResultMessage("无线设备每24小时只可调整一次频率")
    public static final int PARAM_NOT_USE = 9106;

    @ResultMessage("下发数据设备未迁移至新网关")
    public static final int DEVICE_NOT_MOVED = 9107;

    /*----- 轨迹整合接口 9200-9299 -------------*/

    @ResultMessage("开始时间为空")
    public static final int START_TIME_NULL = 9200;

    @ResultMessage("结束时间为空")
    public static final int END_TIME_NULL = 9201;

    @ResultMessage("开始时间大于结束时间")
    public static final int START_GRE_END = 9202;

    @ResultMessage("开始时间大于当前时间")
    public static final int START_GRE_CURRENT = 9203;

    @ResultMessage("参数为空返回结果")
    public static final int PARAM_NULL = 9204;

    @ResultMessage("车辆设备集合为空")
    public static final int VHEICLE_LIST_NULL = 9205;


    @ResultMessage("当前通道被占用,请检查或稍后重试")
    public static final int DEVICE_CHANNEL_PLAYING = 9300;
    @ResultMessage("当前设备不在线,请检查或稍后重试")
    public static final int DEVICE_OFFLINE = 9301;

    @ResultMessage("参数过多！")
    public static final int PARAM_OVER = 9302;

}
