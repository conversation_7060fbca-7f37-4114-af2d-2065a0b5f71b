package com.mrcar.gov.common.constant.asset;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/11/23 22:16
 */
@Getter
public enum VehicleBindGpsFlagEnum {
    /**
     * 已绑定
     */
    BIND(1, "已绑"),
    /**
     * 未绑定
     */
    UNBIND(0, "未绑");

    private final Integer code;
    private final String desc;

    VehicleBindGpsFlagEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    public static String getDescByCode(Integer code) {
        for (VehicleBindGpsFlagEnum value : VehicleBindGpsFlagEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getDesc();
            }
        }
        return "";
    }
}
