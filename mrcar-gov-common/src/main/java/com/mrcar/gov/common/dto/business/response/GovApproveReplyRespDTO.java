package com.mrcar.gov.common.dto.business.response;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 车辆批复表
 *
 * <AUTHOR>
 * @TableName gov_approve_reply
 */
@Data
public class GovApproveReplyRespDTO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    private Integer id;

    /**
     * 关联申请编码
     */
    private String applyNo;

    /**
     * 拟购车主键id
     */
    private Integer vehiclePlanId;
    ;

    /**
     * 使用性质
     */
    private Integer useAttributeId;

    /**
     * 使用性质
     */
    private String useAttributeName;

    /**
     * 车辆类型
     */
    private Integer vehicleType;


    /**
     * 车辆类型
     */
    private String vehicleTypeName;

    /**
     * 动力类型
     */
    private Integer fuelType;


    /**
     * 动力类型
     */
    private String fuelTypeName;

    /**
     * 价格
     */
    private BigDecimal vehicleBarePrice;

    /**
     * 排量
     */
    private String outputVolume;

    /**
     * 配备时填数量
     */
    private Integer vehicleNum;

    /**
     * 处置方式 1报废 2拍卖 3调拨 4损失核销 5厂家回收 6 对外捐赠
     */
    private Integer disposalMethod;

    /**
     * 处置方式 1报废 2拍卖 3调拨 4损失核销 5厂家回收 6 对外捐赠
     */
    private String disposalMethodName;

    /**
     * 填调入单位
     */
    private String transferInStructCode;

    /**
     * 填调入单位
     */
    private String transferInStructName;

    /**
     * 所属公司id
     */
    private Integer companyId;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

}