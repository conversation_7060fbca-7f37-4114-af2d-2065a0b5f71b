package com.mrcar.gov.common.dto.device.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 轨迹
 * @date 2023/10/20 14:13
 */
@Data
public class PointDetailDTO {


	/**
	 * 百度经度
	 */
    private BigDecimal lngBaidu;

    /**
     * 百度纬度
     */
    private BigDecimal latBaidu;
    /**
     * 时间
     */
    private Date createDate;
    /**
     * 速度
     */
    private BigDecimal speed;
    /**
     * 方向
     */
    private Integer direction;
    /**
     * 是否是停车点
     */
    private Boolean  stopPoint = false;
    /**
     * 停车时长
     */
    private Integer  stopDuration=0;
    /**
     * 停车时长详情
     */
    private String   stopDurationDetail;
    /**
     * 起点是否是停车点
     */
    private Boolean  startStopPoint =  false;
    /**
     * 终点是否是停车点
     */
    private Boolean  endStopPoint =  false;


    /**
     * 报警点类型,0正常点 1 报警点
     */
    private Integer warnPointType=0;

    /**
     * 报警icon类型 1:车内报警,2:驾驶行为报警和ADAS报警,3:多种报警混合类型
     */
    private Integer warnIconType;

    /**
     * 点位报警信息
     */
    private List<PointWarnDTO> pointWarnList;


}
