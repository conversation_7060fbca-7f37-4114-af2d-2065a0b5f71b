package com.mrcar.gov.common.dto.workflow.process.resp;

import com.alibaba.fastjson.JSONObject;
import com.mrcar.gov.common.dto.workflow.copy.CopyProcessSaveReqDTO;
import com.mrcar.gov.common.dto.workflow.enums.BpmModelFormTypeEnum;
import lombok.Data;
import java.util.Date;
import java.util.List;

/**
 * AppBpmProcessInstance 响应 DTO
 * 返回流程实例的基本信息
 *
 * <AUTHOR>
 * @date 2024/9/6 16:17
 */
@Data
public class AppBpmProcessInstanceRespDTO {

    /**
     * 流程实例的编号
     */
    private String id;

    /**
     * 流程名称
     */
    private String name;

    /**
     * 流程分类
     * 参见 bpm_model_category 数据字典
     */
    private String category;

    /**
     * 流程实例的状态
     * 参见 bpm_process_instance_status
     */
    private Integer status;

    /**
     * 流程实例的结果
     * 参见 bpm_process_instance_result
     */
    private Integer result;

    /**
     * 提交时间
     */
    private Date createTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 提交的表单值
     */
    private JSONObject formVariables;

    /**
     * 业务的唯一标识
     * 例如说，请假申请的编号
     */
    private String businessKey;

    /**
     * 发起流程的用户
     */
    private User startUser;

    /**
     * 流程定义
     */
    private ProcessDefinition processDefinition;

    /**
     * 抄送人集合
     */
    private List<CopyProcessSaveReqDTO.CopyUser> copyUserList;

    /**
     * 用户信息
     */
    @Data
    public static class User {

        /**
         * 用户编号
         */
        private Long id;

        /**
         * 用户昵称
         */
        private String nickname;

        /**
         * 部门编号
         */
        private Long deptId;

        /**
         * 部门名称
         */
        private String deptName;
    }

    /**
     * 流程定义信息
     */
    @Data
    public static class ProcessDefinition {

        /**
         * 编号
         */
        private String id;

        /**
         * 表单类型
         * 参见 bpm_model_form_type 数据字典
         */
        private Integer formType;

        /**
         * 表单编号
         * 在表单类型为 {@link BpmModelFormTypeEnum#CUSTOM} 时，必须非空
         */
        private Long formId;

        /**
         * 表单项的数组
         * JSON 字符串的数组。在表单类型为 {@link BpmModelFormTypeEnum#CUSTOM} 时，必须非空
         */
        private List<JSONObject> formFieldsList;
    }
}
