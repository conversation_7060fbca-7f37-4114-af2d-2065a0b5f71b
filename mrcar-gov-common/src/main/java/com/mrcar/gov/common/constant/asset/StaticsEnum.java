package com.mrcar.gov.common.constant.asset;

import lombok.Getter;

@Getter
public enum StaticsEnum {
    TOTAL(1, "汇总"),
    SINGLE_VEHICLE(2, "单车累计");

    private final int code;
    private final String description;

    StaticsEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据代码获取枚举实例。
     *
     * @param code 费用类型的代码。
     * @return 对应的枚举实例，如果未找到则返回 null。
     */
    public static StaticsEnum fromCode(int code) {
        for (StaticsEnum type : StaticsEnum.values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        return TOTAL;
    }
}
