package com.mrcar.gov.common.util.pdf.model;

import com.mrcar.gov.common.util.pdf.layout.MetricType;
import com.mrcar.gov.common.util.pdf.layout.MetricValue;
import com.mrcar.gov.common.util.pdf.layout.Point;
import com.mrcar.gov.common.util.pdf.layout.RoundMetric;
import org.apache.pdfbox.pdmodel.PDPageContentStream;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

public abstract class AbstractRowElement implements RowElement {

    protected final PageElement _parent;
    protected final List<BlockElement> _children = new ArrayList<>();

    protected MetricValue _height;
    protected RoundMetric _margin;

    protected AbstractRowElement(PageElement parent,
                                 MetricValue height,
                                 RoundMetric margin) {
        this._parent = parent;
        this._height = height;
        this._margin = margin;
    }

    @Override
    public PageElement parent() {
        return this._parent;
    }

    @Override
    public Collection<BlockElement> children() {
        return new ArrayList<>(_children);
    }

    @Override
    public void append(BlockElement child) {
        this._children.add(child);
    }

    @Override
    public MetricValue height() {
        return this._height;
    }

    @Override
    public RoundMetric margin() {
        return this._margin;
    }

    @Override
    public boolean refreshWidth() {
        for (BlockElement child : this._children) {
            boolean result = child.refreshWidth();
            if (!result) {
                return false;
            }
        }
        return true;
    }

    @Override
    public boolean refreshHeight() {
        // 子元素计算高度
        for (BlockElement child : children()) {
            boolean result = child.refreshHeight();
            if (!result) {
                return false;
            }
        }
        if (this._height.type() == MetricType.PERCENTAGE) {
            // 页面高度
            float pageHeight = _parent.pageSize().getHeight() - _parent.padding().top().value() - _parent.padding().bottom().value();
            // 百分比
            this._height = MetricValue.create(MetricType.ABSOLUTE,
                    pageHeight * this._height.value());
        } else if (this._height.type() == MetricType.ADAPTIVE) {
            double height = _children
                    .stream()
                    .mapToDouble(s -> s.height().value() + s.margin().top().value() + s.margin().bottom().value())
                    .max()
                    .orElse(0);
            this._height = MetricValue.create(MetricType.ABSOLUTE, (float) height);
        }
        return true;
    }

    @Override
    public void render(Point point,
                       PDPageContentStream stream) throws IOException {
        float x = point.x();
        float y = point.y();
        // 绘制每一个块
        for (BlockElement child : _children) {
            x += child.margin().left().value();
            child.render(Point.of(x, y), stream);
            x += child.width().value();
            x += child.margin().right().value();
        }
    }

    @Override
    public abstract String name();

}
