package com.mrcar.gov.common.util.pdf.model;

import com.mrcar.gov.common.util.pdf.layout.MetricValue;
import com.mrcar.gov.common.util.pdf.layout.RoundMetric;
import org.vandeseer.easytable.structure.Row;

public class TableRowElement extends AbstractBlockElement {

    private static final String NODE_NAME = "Table-Row";

    public TableRowElement(RowElement parent) {
        super(parent, MetricValue.adaptive(), MetricValue.adaptive(), RoundMetric.empty());
    }

    public static TableRowElement create(RowElement parent) {
        return new TableRowElement(parent);
    }

    @Override
    public void append(SingleElement child) {
        if (!(child instanceof TableCellElement)) {
            throw new IllegalArgumentException("Table row element only contains cell element");
        }
        TableCellElement cell = (TableCellElement) child;
        super.append(cell);
    }

    public Row buildRow() {
        Row.RowBuilder builder = Row.builder();
        for (SingleElement child : this._children) {
            TableCellElement cell = (TableCellElement) child;
            builder.add(cell.buildCell());
        }
        return builder.build();
    }

    @Override
    public String name() {
        return NODE_NAME;
    }

}
