package com.mrcar.gov.common.dto.order.resp;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import izu.org.apache.poi.ss.usermodel.BorderStyle;
import izu.org.apache.poi.ss.usermodel.HorizontalAlignment;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@HeadStyle(
        fillForegroundColor = 22,
        fillBackgroundColor = 22
)
@ColumnWidth(30)
@ContentStyle(
        horizontalAlignment = HorizontalAlignment.CENTER,
        borderBottom = BorderStyle.THIN,
        borderLeft = BorderStyle.THIN,
        borderRight = BorderStyle.THIN,
        borderTop = BorderStyle.THIN,
        wrapped = true
)
@Data
public class PublicGovCarOrderForSocialExportDTO {

    @ExcelProperty(value = "行程单号")
    private String orderNo;

    @ExcelProperty(value = "申请单号")
    private String applyNo;

    @ExcelProperty(value = "行程类型")
    private String orderTypeStr;

    @ExcelProperty(value = "行程状态")
    private String orderStatusStr;

    @ExcelProperty(value = "租赁方式")
    private String rentTypeStr;

    @ExcelProperty(value = "供应商")
    private String supplierName;

    @ExcelProperty(value = "驾驶员类型")
    private String drivingTypeStr;

    @ExcelProperty(value = "车辆服务类型")
    private String vehicleServiceTypeStr;

    @ExcelProperty(value = "车辆管理单位")
    private String vehicleManagementDeptName;

    @ExcelProperty(value = "驾驶员")
    private String driverInfo;

    @ExcelProperty(value = "创建人")
    private String createUserName;

    @ExcelProperty(value = "创建人单位")
    private String createDeptName;

    @ExcelProperty(value = "主用车人")
    private String passengerUserName;

    @ExcelProperty(value = "主用车人部门")
    private String passengerStructName;

    @ExcelProperty(value = "主用车人单位")
    private String passengerDeptName;

    @ExcelProperty(value = "车辆类型")
    private String vehicleTypeStr;

    @ExcelProperty(value = "车牌号")
    private String vehicleLicense;

    @ExcelProperty(value = "预计出发城市")
    private String estimatedDepartureCityName;

    @ExcelProperty(value = "预计出发地")
    private String estimatedDepartureShortLocation;

    @ExcelProperty(value = "预计目的城市")
    private String estimatedDestinationCityName;

    @ExcelProperty(value = "预计目的地")
    private String estimatedDestinationShortLocation;

    @ExcelProperty(value = "实际出发城市")
    private String startCityName;

    @ExcelProperty(value = "实际出发地")
    private String actualDepartureShortLocation;

    @ExcelProperty(value = "实际目的城市")
    private String endCityName;

    @ExcelProperty(value = "实际目的地")
    private String actualDestinationShortLocation;

    @ExcelProperty(value = "预计开始时间")
    private Date expectedPickupTime;

    @ExcelProperty(value = "预计结束时间")
    private Date expectedReturnTime;

    @ExcelProperty(value = "实际开始时间")
    private Date orderStartTime;

    @ExcelProperty(value = "实际结束时间")
    private Date orderEndTime;

    @ExcelProperty(value = "用车时长(小时)")
    private String userTime;

    @ExcelProperty(value = "里程数(km)")
    private BigDecimal totalMileage;

    @ExcelProperty(value = "费用合计(元)")
    private String totalFee;

    @ExcelProperty(value = "用车事由")
    private String carUseReason;

    @ExcelProperty(value = "用车备注")
    private String orderUserMemo;
}