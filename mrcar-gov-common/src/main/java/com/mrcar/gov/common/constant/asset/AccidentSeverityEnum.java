package com.mrcar.gov.common.constant.asset;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;
import java.util.stream.Stream;

/**
 * Author:  wangM
 * Date:  2025/1/13 15:11
 * Desc: 事故程度枚举
 */
@Getter
@AllArgsConstructor
public enum AccidentSeverityEnum {
    MAJOR_DISASTER(1, "特大事故"),
    MAJOR_ACCIDENT(2, "重大事故"),
    GENERAL_ACCIDENT(3, "一般事故"),
    MINOR_ACCIDENT(4, "轻微事故");

    private final Integer code;
    private final String desc;

    public static String getSeverityDesc(Integer code) {
        return Stream.of(values())
                .filter(e -> Objects.equals(e.getCode(), code))
                .map(AccidentSeverityEnum::getDesc).findFirst().orElse("");
    }
}
