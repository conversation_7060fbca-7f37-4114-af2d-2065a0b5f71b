package com.mrcar.gov.common.constant.user;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/11/7 15:01
 */
@AllArgsConstructor
@Getter
public enum GovUserStatusEnum {

    // 正常
    NORMAL(1, "已启用"),

    // 停用
    STOP(0, "已停用");

    private final Integer code;

    private final String desc;

    public static GovUserStatusEnum of(Integer code) {
        for (GovUserStatusEnum entry : GovUserStatusEnum.values()) {
            if (Objects.equals(code, entry.getCode())) {
                return entry;
            }
        }
        return null;
    }

    // 根据code获取描述
    public static String getDescByCode(Integer code) {
        GovUserStatusEnum enu = of(code);
        if(Objects.isNull(enu)){
            return "";
        }
        return enu.desc;
    }

}
