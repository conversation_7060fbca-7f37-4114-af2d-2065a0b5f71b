package com.mrcar.gov.common.dto.workflow;

import lombok.Data;

import java.io.Serializable;

/**
* Description: 工作流获取用户信息
* @Author:Hxc
* @Date:2023/8/3 4:37 PM
*/
@Data
public final class WorkflowGetUserResp implements Serializable {

    /**用户ID**/
    private Integer id;
    /**用户名**/
    private String loginName;
    /**手机号码**/
    private String mobile;
    /**状态**/
    private Integer status;
    /** 部门 ID  **/
    private Integer structId;
    /** 部门名称 */
    private String structName;

    private String headIcon;
}
