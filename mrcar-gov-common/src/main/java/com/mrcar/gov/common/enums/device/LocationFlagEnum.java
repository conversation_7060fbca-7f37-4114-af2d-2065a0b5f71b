package com.mrcar.gov.common.enums.device;

public enum LocationFlagEnum {
    NO_LOCATION(0,"未定位"),
    LOCATION(1,"定位"),
    BASE_STATION(2,"基站");
    public Integer code;
    public String msg;


    LocationFlagEnum(Integer code, String msg) {
        this.code =code;
        this.msg = msg;
    }

    public static String getMsg(Integer code){
        if(code==null) return null;
        for(LocationFlagEnum e: LocationFlagEnum.values()){
            if(code == e.getCode()){
                return e.getMsg();
            }
        }
        return null;
    }


    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}
