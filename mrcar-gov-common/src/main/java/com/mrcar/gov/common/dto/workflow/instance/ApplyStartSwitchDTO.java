package com.mrcar.gov.common.dto.workflow.instance;

import com.mrcar.gov.common.dto.workflow.BaseDTO;
import lombok.Data;
import javax.validation.constraints.NotNull;

@Data
public class ApplyStartSwitchDTO extends BaseDTO {

    /**
     * 业务类型
     * 参照 {@link com.mrcar.gov.common.dto.workflow.enums.ModelEnum.BusinessTypeEnum}
     * 必须提供，不能为空
     */
    @NotNull(message = "业务类型不能为空")
    private Byte businessType;

}
