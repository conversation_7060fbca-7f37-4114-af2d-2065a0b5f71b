package com.mrcar.gov.common.dto.workflow.copy;

import com.mrcar.gov.common.dto.workflow.PageParam;
import lombok.Data;
import java.util.Date;


/**
 * 抄送列表参数
 *
 * <AUTHOR>
 * @description:
 * @date 2024/9/3 15:03
 */
@Data
public class CopyProcessReqDTO extends PageParam {

    /** 流程编号 */
    private String processInstanceId;

    /** 流程名称 */
    private String processName;

    /** 抄送接收Id */
    private Integer copyUserId;

    /** 流程发起时间-查询开始时间 */
    private Date processStartBeginDate;

    /** 流程发起时间-查询结束时间 */
    private Date processStartEndDate;

    /** 抄送时间-查询开始时间 */
    private Date copyBeginDate;

    /** 抄送时间-查询结束时间 */
    private Date copyEndDate;

    /** 发起抄送人id-创建抄送的人 */
    private Integer copyCreatorId;

    /** 发起抄送人名称-创建抄送的人 */
    private String copyCreatorName;

}
