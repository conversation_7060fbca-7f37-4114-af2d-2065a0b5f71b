package com.mrcar.gov.common.util.pdf.model;

import com.mrcar.gov.common.util.pdf.layout.MetricType;
import com.mrcar.gov.common.util.pdf.layout.MetricValue;
import com.mrcar.gov.common.util.pdf.layout.Point;
import com.mrcar.gov.common.util.pdf.layout.RoundMetric;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.common.PDRectangle;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

public class DefaultPageElement implements PageElement {

    private static final String NODE_NAME = "Page";

    private final PDRectangle _rectangle;
    private final RoundMetric _padding;
    private final List<RowElement> _children = new ArrayList<>();

    protected DefaultPageElement(PDRectangle rectangle) {
        this(rectangle, RoundMetric.empty());
    }

    protected DefaultPageElement(PDRectangle rectangle,
                                 RoundMetric padding) {
        _rectangle = rectangle;
        // 将百分比转为绝对值
        MetricValue top = padding.top();
        if (top.type() == MetricType.PERCENTAGE) {
            top = MetricValue.create(MetricType.ABSOLUTE, rectangle.getHeight() * top.value());
        }
        MetricValue bottom = padding.bottom();
        if (bottom.type() == MetricType.PERCENTAGE) {
            bottom = MetricValue.create(MetricType.ABSOLUTE, rectangle.getHeight() * bottom.value());
        }
        MetricValue left = padding.left();
        if (left.type() == MetricType.PERCENTAGE) {
            left = MetricValue.create(MetricType.ABSOLUTE, rectangle.getWidth() * left.value());
        }
        MetricValue right = padding.right();
        if (right.type() == MetricType.PERCENTAGE) {
            right = MetricValue.create(MetricType.ABSOLUTE, rectangle.getWidth() * right.value());
        }
        this._padding = RoundMetric.create(top, bottom, left, right);
    }

    @Override
    public PDRectangle pageSize() {
        return this._rectangle;
    }

    @Override
    public RoundMetric padding() {
        return this._padding;
    }

    @Override
    public Collection<RowElement> rows() {
        return new ArrayList<>(_children);
    }

    @Override
    public void append(RowElement child) {
        this._children.add(child);
    }

    @Override
    public boolean refreshWidth() {
        for (RowElement child : _children) {
            boolean result = child.refreshWidth();
            if (!result) {
                return false;
            }
        }
        return true;
    }

    @Override
    public boolean refreshHeight() {
        for (RowElement child : _children) {
            boolean result = child.refreshHeight();
            if (!result) {
                return false;
            }
        }
        return true;
    }

    @Override
    public String name() {
        return NODE_NAME;
    }

    @Override
    public void render(Point point,
                       PDPageContentStream stream) throws IOException {
        // 页面大小
        PDRectangle rectangle = this.pageSize();
        // 逐行渲染
        float x = _padding.left().value();
        float y = rectangle.getHeight() - _padding.top().value();
        for (RowElement row : _children) {
            if (!row.refreshWidth() || !row.refreshHeight()) {
                throw new IllegalArgumentException("Cannot parse pdf layout");
            }
            y -= row.margin().top().value();
            row.render(Point.of(x, y), stream);
            y -= row.height().value();
            y -= row.margin().bottom().value();
        }
    }

    @Override
    public RoundMetric margin() {
        return RoundMetric.empty();
    }
}
