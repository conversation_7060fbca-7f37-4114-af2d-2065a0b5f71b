package com.mrcar.gov.common.dto.workflow.model;

import com.mrcar.gov.common.dto.workflow.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 管理后台 - 流程模型分页 Request VO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BpmModelPageReqDTO extends PageParam {

    /**
     * 标识
     * 示例值: process1641042089407
     * 精准匹配
     */
    private String key;

    /**
     * 名字
     * 示例值: 用车
     * 模糊匹配
     */
    private String name;

    /**
     * 流程分类（参见 工作流-流程分类 数据字典）
     * 示例值: 1
     */
    private String category;

    /**
     * 业务类型（参见 工作流-业务类型 数据字典）
     * 示例值: 1
     */
    private Byte businessType;

    /**
     * 所属企业 ID
     * 示例值: 1
     */
    private Integer companyId;


    /**
     * 模型部门 单位/区域
     */
    private Integer structId;

}
