package com.mrcar.gov.common.constant.config;

/**
 * 废弃该类
 * <AUTHOR> dongxiya
 * @create 2022/12/2 9:26
 */
@Deprecated
public class EnterpriseConfigEnum {

    public enum EnterpriseConfigGroupEnum{
        USE_CAR_TYPE((byte)1,"用车类型"),
        INTERNAL_CAR((byte)2,"内部用车"),
        BASIC_FUNCTION((byte)3,"基础功能"),
        PRIVATECAR_FUNCTION((byte)4,"私车公用"),
        LS((byte)5,"零散用车"),
        SELF_CAR((byte)6,"自助取还"),
        GENERAL_CONFIGURATION((byte)7,"通用配置"),
        ;
        private final Byte code;
        private final String name;

        EnterpriseConfigGroupEnum(Byte code, String name) {
            this.code = code;
            this.name = name;
        }

        public Byte getCode() {
            return code;
        }

        public String getName() {
            return name;
        }
    }
}
