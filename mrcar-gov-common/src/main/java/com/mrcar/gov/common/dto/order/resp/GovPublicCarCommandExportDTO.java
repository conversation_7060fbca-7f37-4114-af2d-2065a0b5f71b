package com.mrcar.gov.common.dto.order.resp;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import izu.org.apache.poi.ss.usermodel.BorderStyle;
import izu.org.apache.poi.ss.usermodel.HorizontalAlignment;
import lombok.Data;
import java.util.Date;

/**
 * <AUTHOR>
 */
@HeadStyle(
        fillForegroundColor = 22,
        fillBackgroundColor = 22
)
@ColumnWidth(30)
@ContentStyle(
        horizontalAlignment = HorizontalAlignment.CENTER,
        borderBottom = BorderStyle.THIN,
        borderLeft = BorderStyle.THIN,
        borderRight = BorderStyle.THIN,
        borderTop = BorderStyle.THIN,
        wrapped = true
)
@Data
public class GovPublicCarCommandExportDTO {


    @ExcelProperty(value = "日志编号")
    private String commandNo;

    @ExcelProperty(value = "车牌号")
    private String vehicleLicense;

    @ExcelProperty(value = "车型")
    private String vehicleSeriesName;

    @ExcelProperty(value = "设备编号")
    private String deviceNo;

    @ExcelProperty(value = "设备厂商名称")
    private String manufactName;

    @ExcelProperty(value = "设备型号")
    private String modelName;

    @ExcelProperty(value = "指令名称")
    private String commandTypeStr;

    @ExcelProperty(value = "指令类型")
    private String requestTypeStr;

    @ExcelProperty(value = "指令下发时间")
    private Date commandDispatchTime;

    @ExcelProperty(value = "指令完成时间")
    private Date commandCompleteTime;

    @ExcelProperty(value = "执行结果")
    private String commandResultStr;

    @ExcelProperty(value = "失败原因")
    private String failReason;

    @ExcelProperty(value = "操作来源")
    private String operateSourceStr;

    @ExcelProperty(value = "业务单据编号")
    private String businessNo;

    @ExcelProperty(value = "操作人")
    private String operatorName;

    @ExcelProperty(value = "操作人手机号")
    private String operatorMobile;

    @ExcelProperty(value = "操作人单位")
    private String deptName;

    @ExcelProperty(value = "操作人企业")
    private String companyName;
}