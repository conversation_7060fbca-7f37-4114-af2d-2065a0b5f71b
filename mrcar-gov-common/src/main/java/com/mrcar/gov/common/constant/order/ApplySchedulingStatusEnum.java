package com.mrcar.gov.common.constant.order;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum ApplySchedulingStatusEnum {

    // 调度状态
    PENDING(1, "待调度"),
    NOT_REQUIRED(2, "无需调度"),
    SCHEDULED(3, "已调度"),
    CANCELLED(4, "调度取消"),
    ;

    private final Integer code;
    private final String name;

    public static String getName(Integer code) {
        return Arrays.stream(values()).filter(e -> e.getCode().equals(code)).findFirst().map(ApplySchedulingStatusEnum::getName).orElse(null);
    }
}