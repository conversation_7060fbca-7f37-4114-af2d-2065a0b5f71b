package com.mrcar.gov.common.dto.business.request;

import com.mrcar.gov.common.dto.BaseDTO;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 取消办理
 *
 * <AUTHOR>
 * @date 2024/11/21 11:52
 */
@Data
public class GovDisposalCancelReqDTO extends BaseDTO {

    /**
     * 处置编码
     */
    @NotBlank(message = "处置编码不能为空")
    private String disposalNo;

    /**
     * 处置状态
     */
    private Integer disposalStatus;
}
