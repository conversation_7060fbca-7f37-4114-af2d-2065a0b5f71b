package com.mrcar.gov.common.dto.workflow.task;

import com.mrcar.gov.common.dto.workflow.enums.BpmProcessInstanceResultEnum;
import com.mrcar.gov.common.dto.workflow.enums.ModelEnum;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 待办列表返回参数
 */
@Data
public class BpmTaskAppPageRespDTO {

    /**
     * 流程实例编号
     */
    private String processInstanceId;

    /**
     * 流程名称
     */
    private String processInstanceName;

    /**
     * 任务编号
     */
    private String taskId;

    /**
     * 办理状态
     */
    private Byte result;

    /**
     * 办理状态名称
     */
    private String resultStr;

    /**
     * 业务类型
     */
    private Byte businessType;

    /**
     * 业务类型名称
     */
    private String businessTypeName;

    /**
     * 发起人
     */
    private String creterName;

    /**
     * 发起人的部门
     */
    private String deptName;

    /**
     * 发起时间
     */
    private Date createTime;

    /**
     * 单据编号
     */
    private String businessNo;

    /**
     * 取消按钮是否展示（true展示 false不展示）
     */
    private boolean buttonCancel;

    /**
     * 申请备注
     */
    private String applyRemark;

    /**
     * 流程定义编码
     */
    private String processDefinitionId;

    /**
     * 批量审批开关
     */
    private Boolean batchApproval;

    /**
     * 是否支持通过
     */
    private Boolean approve;

    /**
     * 是否支持不通过
     */
    private Boolean reject;

    /**
     * 是否支持转办
     */
    private Boolean transfer;

    /**
     * 是否支持回退
     */
    private Boolean back;

    /**
     * 可回退的历史任务节点（包含开始节点）
     */
    private List<BpmHistoricTaskInstanceRespDTO> historicTaskNodeList;

    /**
     * 所属主流程（父流程）-流程实例编号
     */
    private String parentProcessInstanceId;

    /**
     * 所属主流程（父流程）-流程名称
     */
    private String parentProcessName;

    /**
     * 子流程的状态字符串
     */
    private String associationProcessStatusStr;

    /**
     * 子流程状态
     */
    private Byte associationProcessStatus;

    /**
     * 是否展示发起下级流程
     */
    private boolean buttonApprove;

    /**
     * 模型ID
     */
    private String modelId;

    /**
     * 所属企业ID
     */
    private String companyId;

    /**
     * 所属企业名称
     */
    private String companyName;

    /**
     * 所属子流程-流程实例编号
     */
    private String subProcessInstanceId;

    /**
     * 所属子流程-流程名称
     */
    private String subProcessInstanceName;

    /**
     * 流程分类.1:业务流程 2:OA流程
     */
    private String category;

    public void setResult(Byte result) {
        this.result = result;
        this.resultStr = BpmProcessInstanceResultEnum.getEnum(result).getDesc();
    }

    public void setBusinessType(Byte businessType) {
        this.businessType = businessType;
        ModelEnum.BusinessTypeEnum enu = ModelEnum.BusinessTypeEnum.codeOf(businessType);
        if (Objects.nonNull(enu)) {
            this.businessTypeName = enu.getName();
            this.category = enu.getCategory();
        }
    }

    public String getBusinessNo() {
        if (StringUtils.isEmpty(businessNo)) {
            return this.processInstanceId;
        }
        return businessNo;
    }

    public String getAssociationProcessStatusStr() {
        if (associationProcessStatus == null || associationProcessStatus == 0) {
            return "";
        }
        return BpmProcessInstanceResultEnum.getEnum(associationProcessStatus).getDesc();
    }
}
