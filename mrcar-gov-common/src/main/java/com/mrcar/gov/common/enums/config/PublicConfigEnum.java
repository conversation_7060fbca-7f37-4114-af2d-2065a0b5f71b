package com.mrcar.gov.common.enums.config;

import com.mrcar.gov.common.constant.order.GovPublicCarDrivingTypeEnum;
import com.mrcar.gov.common.constant.order.GovPublicCarScheduleTypeEnum;
import lombok.Getter;

/**
 * <AUTHOR>
 */
public class PublicConfigEnum {

    @Getter
    public enum BusinessConfigEnum {

        // 编制内用车
        USE_CAR_APPLY_DRIVER_TYPE("use_car_apply_driver_type", "用车申请驾驶员类型", "编制内用车-用车申请驾驶员类型"),
        WHEN_USE_CAR_DISPATCH_WAY("when_use_car_dispatch_way", "用车时的调度方式", "编制内用车-用车时的调度方式"),
        // 社会化用车
        RENTAL_WAY("rental_way", "租赁方式", "社会化用车-租赁方式"),
        // 社会化用车-日租
        DAY_DRIVER_TYPE("day_driver_type", "驾驶员类型", "社会化用车-日租-驾驶员类型"),
        DAY_DISPATCH_WAY("day_dispatch_way", "调度方式", "社会化用车-日租-调度方式"),
        DAY_USE_CAR_MAX_DAYS("day_use_car_max_days", "最大用车天数", "社会化用车-最大用车天数"),
        //社会化用车-分时租赁

        HOUR_DRIVER_TYPE("hour_driver_type", "驾驶员类型", "社会化用车-分时租赁-驾驶员类型"),
        HOUR_DISPATCH_WAY("hour_dispatch_way", "调度方式", "社会化用车-分时租赁-调度方式"),
        HOUR_USE_CAR_MAX_DAYS("hour_use_car_max_days", "最大用车天数", "社会化用车-分时租赁-最大用车天数"),
        HOUR_RETURN_FENCE_CHECK("hour_return_fence_check", "还车围栏校验", "社会化用车-分时租赁-还车围栏校验"),
        HOUR_USE_CAR_DURATION_FILED("hour_use_car_duration_filed", "用车计费时长统计字段", "社会化用车-分时租赁-用车计费时长统计字段"),
        ;

        /**
         * 编码
         */
        private String code;
        /**
         * 名称
         */
        private String name;
        /**
         * 描述
         */
        private String description;

        BusinessConfigEnum(String code, String name, String description) {
            this.code = code;
            this.name = name;
            this.description = description;
        }

        public static BusinessConfigEnum getByCode(String code) {
            for (BusinessConfigEnum item : values()) {
                if (item.getCode().equals(code)) {
                    return item;
                }
            }
            return null;
        }

    }


    @Getter
    public enum BusinessConfigItemEnum {
        USE_CAR_APPLY_DRIVER_TYPE_ITEM("use_car_apply_driver_type_item", "用车申请驾驶员类型", GovPublicCarDrivingTypeEnum.class),
        WHEN_USE_CAR_DISPATCH_WAY_ITEM("when_use_car_dispatch_way_item", "用车时的调度方式", GovPublicCarScheduleTypeEnum.class),
        DRIVER_TYPE_ITEM("driver_type_item", "驾驶员类型", GovPublicCarDrivingTypeEnum.class),
        DISPATCH_WAY_ITEM("dispatch_way_item", "调度方式", GovPublicCarScheduleTypeEnum.class),
        RENTAL_WAY_ITEM("rental_way_item", "租赁方式", PublicConfigValueEnum.RentalWayEnum.class),
        RETURN_FENCE_CHECK_ITEM("return_fence_check_item", "还车围栏校验", PublicConfigValueEnum.ReturnFenceCheckEnum.class),
        USE_CAR_DURATION_FILED_ITEM("use_car_duration_filed_item", "用车计费时长统计字段", PublicConfigValueEnum.UseCarDurationFiledEnum.class),
        ;
        /**
         * 编码
         */
        private String code;
        /**
         * 名称
         */
        private String name;

        private Class clazz;

        BusinessConfigItemEnum(String code, String name, Class clazz) {
            this.code = code;
            this.name = name;
            this.clazz = clazz;
        }

        public static BusinessConfigItemEnum getByCode(String code) {
            for (BusinessConfigItemEnum item : values()) {
                if (item.getCode().equals(code)) {
                    return item;
                }
            }
            return null;
        }

    }

    public enum SelectTypeEnum {
        //单选/多选 适用于下拉框 0:单选  1:多选
        SINGLE(0, "单选"),
        MULTI(1, "多选");

        private Integer code;
        private String name;

        SelectTypeEnum(Integer code, String name) {
            this.code = code;
            this.name = name;
        }

        public Integer getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public static SelectTypeEnum getByCode(Integer code) {
            for (SelectTypeEnum item : values()) {
                if (item.getCode().equals(code)) {
                    return item;
                }
            }
            return null;
        }
    }


    public enum BusinessConfigItemTypeEnum {
        // 类型 1:单选框, 2:开关,3:单选择器时间,4:文本框,5:多选框,6:下拉框,7:多时间选择器'
        RADIO(1, "单选框"),
        SWITCH(2, "开关"),
        RADIO_DATE(3, "单选择器时间"),
        TEXTBOX(4, "文本框"),
        CHECKBOX(5, "多选框"),
        SELECT(6, "下拉框"),
        MANY_DATE(7, "多时间选择器"),
        TEXT_AREA(8, "文本框域"),
        ;

        private Integer code;
        private String name;

        BusinessConfigItemTypeEnum(Integer code, String name) {
            this.code = code;
            this.name = name;
        }

        public Integer getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public static BusinessConfigItemTypeEnum getByCode(Integer code) {
            for (BusinessConfigItemTypeEnum item : values()) {
                if (item.getCode().equals(code)) {
                    return item;
                }
            }
            return null;
        }

    }


    public enum ConfigItemDataType {
        //1:小数, 2:整数, 3:文本/字符串,4:数组,5:JSON对象
        DECIMAL(1, "小数"),
        INTEGER(2, "整数"),
        STRING(3, "字符串"),
        ARRAY(4, "数组"),
        JSON_OBJECT(5, "JSON对象");

        private Integer code;
        private String name;

        ConfigItemDataType(Integer code, String name) {
            this.code = code;
            this.name = name;
        }

        public Integer getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public static ConfigItemDataType getByCode(Integer code) {
            for (ConfigItemDataType item : values()) {
                if (item.getCode().equals(code)) {
                    return item;
                }
            }
            return null;
        }
    }


}
