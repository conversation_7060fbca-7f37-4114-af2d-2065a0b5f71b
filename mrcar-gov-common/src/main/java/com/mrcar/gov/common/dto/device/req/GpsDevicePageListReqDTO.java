package com.mrcar.gov.common.dto.device.req;

import com.mrcar.gov.common.dto.BaseDTO;
import com.mrcar.gov.common.dto.PageParamDTO;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class GpsDevicePageListReqDTO {
    private static final Integer PAGE_NUM = 1;
    private static final Integer PAGE_SIZE = 10;
    /**
     * 页码，从 1 开始
     */
    private Integer page = PAGE_NUM;
    /**
     * 每页条数
     */
    private Integer pageSize = PAGE_SIZE;
    /**
     * 设备所属公司
     */
    private Integer companyId;
    /**
     * 设备编号
     */
    private String deviceNo;
    /**
     * SIM卡号
     */
    private String simNo;
    /**
     * 型号集合
     */
    private List<Integer> modelIdList;
    /**
     * 设备类型
     */
    private Integer deviceType;
    /**
     * 车牌号
     */
    private String vehicleLicense;
    /**
     * 车架号
     */
    private String vehicleVin;
    /**
     * 首次绑定时间开始
     */
    private String startFirstBindTime;
    /**
     * 首次绑定时间结束
     */
    private String endFirstBindTime;
    /**
     * 最新绑定时间开始
     */
    private String startBindTime;
    /**
     * 最新绑定时间开始
     */
    private String endBindTime;
    /**
     * 设备绑定状态
     */
    private Integer bindStatus;

    /**
     * @ignore
     * 会话信息
     */
    private BaseDTO sesssionBaseDTO;

    /**
     * 设备来源类型 1:政府设备 2：供应商设备
     */
    @NotNull(message = "设备来源类型不能为空")
    private Integer deviceSource;

    /**
     * 供应商编码
     */
    private String supplierServiceCode;
    /**
     * 供应商登录时 用于区分供应商数据
     */
    private String permOrgNo;
}
