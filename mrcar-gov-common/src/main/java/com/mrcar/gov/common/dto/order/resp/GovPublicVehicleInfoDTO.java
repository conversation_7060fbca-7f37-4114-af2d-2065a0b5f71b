package com.mrcar.gov.common.dto.order.resp;

import com.mrcar.gov.common.dto.device.resp.BdTravelDTO;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


@ApiModel("详情-用车信息")
@Data
public class GovPublicVehicleInfoDTO {

    /**
     * 车牌号
     */
    private String vehicleLicense;

    /**
     * 驾驶员信息
     */
    private String driverInfo;

    /**
     * 驾驶员单位
     */
    private String driverDeptName;


    /**
     * 费用合计
     */
    private String totalFee;


    /**
     * 订单开始操作人
     */
    private String orderStartOperator;

    /**
     * 订单结束操作人
     */
    private String orderEndOperator;

    /**
     * 车杂费
     */
    private List<GovPublicVehicleFeeDTO> vehicleFeeList;

    /**
     * 车杂费单号
     */
    private String vehicleFeeCode;

    private String vehicleNo;


    /**
     * 租金(社会用车时有值)
     */
    private String rentFee;

    /**
     * 品牌车型
     */
    private String vehicleModelName;

    /**
     * 车辆类型
     */
    private String vehicleTypeStr;

    /**
     * 实际开始时间
     */
    private Date orderStartTime;

    /**
     * 实际结束时间
     */
    private Date orderEndTime;

    /**
     * 实际出发短地址
     */
    private String actualDepartureShortLocation;

    /**
     * 实际目的地短地址
     */
    private String actualDestinationShortLocation;

    /**
     * 用车时长(小时)
     */
    private String userTime;

    /**
     * 分时计费总时长
     */
    private String timeShareTime;

    /**
     * 分时计费总里程
     */
    private String timeShareMileage;

    /**
     * 里程数(km)
     */
    private String totalMileage;

    /**
     * 驾车路线规划
     */
    private BdTravelDTO bdTravelDTO;

}