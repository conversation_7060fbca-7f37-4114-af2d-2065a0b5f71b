package com.mrcar.gov.common.enums.carnet;
public enum CoordTypeEnum
{
    WORLD, MARS, BAIDU;

    private String name;


    public static boolean exist(String name)
    {
        return (find(name) != null);
    }

    public static CoordTypeEnum find(String name)
    {
        CoordTypeEnum[] arrayOfCoordTypeEnum = values(); int i = arrayOfCoordTypeEnum.length; for (int j = 0; j < i; ++j) { CoordTypeEnum info = arrayOfCoordTypeEnum[j];
        if (info.name.toLowerCase().equals(name))
            return info;
    }

        return null;
    }

    public String toString()
    {
        return this.name;
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }
}