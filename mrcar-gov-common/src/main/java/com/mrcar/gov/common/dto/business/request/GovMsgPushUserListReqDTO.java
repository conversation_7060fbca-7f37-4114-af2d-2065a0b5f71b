package com.mrcar.gov.common.dto.business.request;

import com.mrcar.gov.common.dto.PageParamDTO;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/12/28 08:41
 */
@Data
public class GovMsgPushUserListReqDTO extends PageParamDTO {
    /**
     * 接收人名字
     */
    private String userName;

    /**
     * 消息或模板编号
     */
    private String msgNo;

    /**
     * 用户查询类型。1：推送人员；2：已读人员；3：未读人员
     */
    @NotNull(message = "人员查询类型不能为空")
    private Integer userQryType;

    // 消息记录编号
    private String recordNo;
}
