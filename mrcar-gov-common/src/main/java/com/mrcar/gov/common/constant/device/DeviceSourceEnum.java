package com.mrcar.gov.common.constant.device;

import lombok.Getter;

/**
 * 设备来源
 * <AUTHOR>
 * @date 2025/1/2 20:10
 */
@Getter
public enum DeviceSourceEnum {

    //设备来源类型 1:政府设备 2：社会设备
    GOV_DEVICE(1,"政府设备"),
    SOCIAL_DEVICE(2,"供应商设备");

    private Integer type;
    private String desc;

    DeviceSourceEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }
}
