package com.mrcar.gov.common.util.pdf.model;

import com.mrcar.gov.common.util.pdf.layout.MetricType;
import com.mrcar.gov.common.util.pdf.layout.MetricValue;
import com.mrcar.gov.common.util.pdf.layout.Point;
import com.mrcar.gov.common.util.pdf.layout.RoundMetric;
import org.apache.pdfbox.pdmodel.PDPageContentStream;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

public abstract class AbstractBlockElement implements BlockElement {

    protected final RowElement _parent;
    protected final List<SingleElement> _children = new ArrayList<>();

    protected MetricValue _width;
    protected MetricValue _height;

    protected RoundMetric _margin;

    public AbstractBlockElement(RowElement parent,
                                MetricValue width,
                                MetricValue height,
                                RoundMetric margin) {
        this._parent = parent;
        this._width = width;
        this._height = height;
        this._margin = margin;
    }

    @Override
    public RowElement parent() {
        return this._parent;
    }

    @Override
    public Collection<SingleElement> children() {
        return new ArrayList<>(this._children);
    }

    @Override
    public void append(SingleElement child) {
        this._children.add(child);
    }

    @Override
    public MetricValue height() {
        return this._height;
    }

    @Override
    public MetricValue width() {
        return this._width;
    }

    @Override
    public RoundMetric margin() {
        return this._margin;
    }

    @Override
    public boolean refreshHeight() {
        if (this._height.type() == MetricType.PERCENTAGE) {
            if (this._parent.height().type() != MetricType.ABSOLUTE) {
                return false;
            }
            // 百分比
            this._height = MetricValue.create(MetricType.ABSOLUTE,
                    this._parent.height().value() * this._height.value());
            for (SingleElement child : children()) {
                boolean result = child.refreshHeight();
                if (!result) {
                    return false;
                }
            }
        } else if (this._height.type() == MetricType.ADAPTIVE) {
            for (SingleElement child : children()) {
                boolean result = child.refreshHeight();
                if (!result) {
                    return false;
                }
            }
            double height = _children.stream()
                    .mapToDouble(s -> s.height().value() + s.margin().top().value() + s.margin().bottom().value())
                    .sum();
            this._height = MetricValue.create(MetricType.ABSOLUTE, (float) height);
        } else {
            for (SingleElement child : children()) {
                boolean result = child.refreshHeight();
                if (!result) {
                    return false;
                }
            }
        }
        return true;
    }

    @Override
    public boolean refreshWidth() {
        if (this._width.type() == MetricType.PERCENTAGE) {
            PageElement page = this._parent.parent();
            float pageWidth = page.pageSize().getWidth()
                    - page.padding().left().value()
                    - page.padding().right().value();
            // 百分比
            this._width = MetricValue.create(MetricType.ABSOLUTE,
                    pageWidth * this._width.value());
            // 计算子元素宽度
            for (SingleElement child : _children) {
                boolean result = child.refreshWidth();
                if (!result) {
                    return false;
                }
            }
        } else if (this._width.type() == MetricType.ADAPTIVE) {
            for (SingleElement child : children()) {
                boolean result = child.refreshWidth();
                if (!result) {
                    return false;
                }
            }
            double width = _children
                    .stream()
                    .mapToDouble(s -> s.width().value() + s.margin().left().value() + s.margin().right().value())
                    .sum();
            this._width = MetricValue.create(MetricType.ABSOLUTE, (float) width);
        } else {
            for (SingleElement child : children()) {
                boolean result = child.refreshWidth();
                if (!result) {
                    return false;
                }
            }
        }
        return true;
    }

    @Override
    public void render(Point point,
                       PDPageContentStream stream) throws IOException {
        // TODO 一个block多个元素
        float x = point.x();
        float y = point.y();
        for (SingleElement child : _children) {
            y -= child.margin().top().value();
            child.render(Point.of(x, y), stream);
            y -= child.height().value();
            y -= child.margin().bottom().value();
        }
    }

    @Override
    public abstract String name();

}
