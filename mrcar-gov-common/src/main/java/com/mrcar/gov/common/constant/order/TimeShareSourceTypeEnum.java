package com.mrcar.gov.common.constant.order;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;


/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum TimeShareSourceTypeEnum {

    JOB(1, "定时任务Job"),
    FINISH_TRIP(2, "结束行程");

    ;
    private final Integer code;
    private final String name;

    public static String getName(Integer code) {
        return Arrays.stream(values()).filter(e -> e.getCode().equals(code)).findFirst().map(TimeShareSourceTypeEnum::getName).orElse(null);
    }
}
