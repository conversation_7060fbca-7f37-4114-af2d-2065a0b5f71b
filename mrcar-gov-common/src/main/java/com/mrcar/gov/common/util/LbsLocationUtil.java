package com.mrcar.gov.common.util;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;

import com.google.common.geometry.S2LatLng;
import com.mrcar.gov.common.constant.lbs.LbsConstants;
import com.mrcar.gov.common.dto.device.resp.BaiduRegeoAddressDTO;
import com.mrcar.gov.common.dto.device.resp.GaodeRegeoAddressDTO;
import com.mrcar.gov.common.dto.device.resp.LocationsDetailDTO;
import com.mrcar.gov.common.dto.device.resp.RegeoResDTO;

public class LbsLocationUtil {
	
	
	/**
     * 停车点判断
     * @param list
     */
    public static void compareLocation(List<LocationsDetailDTO> list){
        for(int i=0;i < list.size()-1;){
            LocationsDetailDTO start = list.get(i);
            LocationsDetailDTO end = list.get(++i);
            compareLocationBi(start,end);
        }
    }
    /**
     * 停车点判断
     * @param start
     * @param end
     */
    public static void compareLocationBi(LocationsDetailDTO start, LocationsDetailDTO end){
        BigDecimal startLat = start.getLatBaidu();
        BigDecimal startLng = start.getLngBaidu();
        Date startDate = start.getCreateDate();
        BigDecimal endLat = end.getLatBaidu();
        BigDecimal endLng = end.getLngBaidu();
        Date endDate = end.getCreateDate();
        if(startLat.compareTo(endLat) == 0 && startLng.compareTo(endLng) == 0){
            //如果坐标相同显然是停车点
            start.setStopPoint(true);
            end.setStopPoint(true);
        }else {
            //判断车速 <= 5km/h
            double distance = getDistance(startLat,startLng,endLat,endLng);
            long duration = LbsDateUtil.getDuration(startDate,endDate);
            if(isMove(distance,duration)){
                start.setStopPoint(false);
                end.setStopPoint(false);
            }else{
                start.setStopPoint(true);
                end.setStopPoint(true);
            }
        }
    }

    /**
     * 去除重复停车点
     * 保留最开始的停车点
     * @param list
     */
    public static void distinceStopPoint(List<LocationsDetailDTO> list){
        Iterator<LocationsDetailDTO> iterator = list.iterator();
        LocationsDetailDTO tmp = null;
        while (iterator.hasNext()){
            if(tmp == null){
                tmp = iterator.next();
            }else{
                LocationsDetailDTO next = iterator.next();
                if(tmp.getStopPoint() && next.getStopPoint()){
                    iterator.remove();
                }else{
                    tmp = next;
                }
            }
        }
    }

    /**
     * 1、停车时长小于3分钟的停车点更改为非停车点,保留停车点和行驶点
     * @param list
     * @param startDate
     */
    public static void calculateStopDuration(List<LocationsDetailDTO> list,Date startDate){
        Iterator<LocationsDetailDTO> iterator = list.iterator();
        //保存前一个点
        LocationsDetailDTO befre = null;
        while (iterator.hasNext()){
            if(befre == null){
                befre = iterator.next();
                if(befre.getStopPoint()){
                    Long duration = LbsDateUtil.getDuration(startDate,befre.getCreateDate());
                    befre.setStopDuration(duration.intValue());
                }
            }else{
                LocationsDetailDTO next = iterator.next();
                if(befre.getStopPoint()){
                    Long duration = LbsDateUtil.getDuration(befre.getCreateDate(),next.getCreateDate());
                    next.setStopDuration(duration.intValue() + defaultInt(befre.getStopDuration(),0));
                    //如果停车时长小于三分钟，则修改为非停车点；停车点纠错
                    //从静止变为行驶，行驶的点会保存停车时长
                    if(next.getStopDuration() <= LbsConstants.LBS_STOP_DURTAION){
                        befre.setStopPoint(false);
                    }
                }
                befre = next;
            }
        }
    }

    /**
     * @param list
     * @param count 过滤次数限制，避免大量递归影响性能
     * @return
     */
    public static List<LocationsDetailDTO> locationFilter(List<LocationsDetailDTO> list,int count){
        if (count > 0) {
            int beforeSize = list.size();
            list = removeErrLocation(list);
            int afterSize = list.size();
            if (afterSize < beforeSize) {
                --count;
                list = locationFilter(list, count);
            }
        }
        return list;
    }

    public static List<LocationsDetailDTO> locationFilter(List<LocationsDetailDTO> list){
        return locationFilter(list,3);
    }

    /**
     * 不能针对未过滤重复停车点的定位集合使用，无法过滤重复错误的定位
     * 删除漂移的行驶点
     * @param list
     */
    public static List<LocationsDetailDTO> removeErrLocation(List<LocationsDetailDTO> list){
        //删除漂移的行驶点
        Iterator<LocationsDetailDTO> iterator = list.iterator();
        LocationsDetailDTO before = null;
        LocationsDetailDTO error = null;
        while (iterator.hasNext()){
            LocationsDetailDTO tmp = iterator.next();
            if(before == null){
                try {
                    //判断起点是否是错误点
                    String l = tmp.getLongitude().toString().concat(",").concat(tmp.getLatitude().toString());
                    List<RegeoResDTO> regeoResDTOList = RegeoUtil.reverseGeocodingBatch(Arrays.asList(l));
                    if(regeoResDTOList != null && regeoResDTOList.size() > 0){
                        if(!isCHN(regeoResDTOList.get(0))){
                            tmp.setLocationName("error");
                            error = tmp;
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                before = tmp;
                continue;
            }
            //如果之前的坐标点为错误点，判断后续的坐标是否同一坐标范围，是则定义为error
            if(error != null){
                if(isSameCountry(error,tmp)){
                    tmp.setLocationName("error");
                    error = tmp;
                    before = tmp;
                    continue;
                }
            }
            if("remove".equals(before.getLocationName())){
                //修改最后一个定位点遗漏的情况
                if((tmp.getLatitude().compareTo(before.getLatitude()) == 0
                        && tmp.getLongitude().compareTo(before.getLongitude()) == 0)
                        || list.indexOf(tmp)+1 == list.size()){
                    tmp.setLocationName("remove");
                }
                //remove点无法判断前后哪个点错误，都删除后，继续两两比较
                before = tmp;
                continue;
            }
            if(isErrorLocation(before,tmp)){
                //error点为错误点，其它点无法判断前后哪个点错误，删除前后错误的点，此时的distance计算会有偏差
                if("error".equals(before.getLocationName())){
                    error = before;
                }else{
                    before.setLocationName("remove");
                }
                if("error".equals(tmp.getLocationName())){
                    error = tmp;
                }else{
                    tmp.setLocationName("remove");
                }
                before = tmp;
                continue;
            }
            before = tmp;
        }
        //删除定位信息中speed超大的点
        return list.stream().filter(l->!"remove".equals(l.getLocationName())
                && !"error".equals(l.getLocationName())
                && l.getSpeed().compareTo(LbsConstants.LBS_MAX_SUDDENLY_SPEED) < 0)
                .collect(Collectors.toList());
    }

    /**
     * 判断是否是中国
     * @param regeoResDTO
     * @return
     */
    private static boolean isCHN(RegeoResDTO regeoResDTO){
        String country = "";
        switch (regeoResDTO.getRegeoApiEnum()){
            case BAIDU:
                BaiduRegeoAddressDTO baiduRegeoAddressDTO = (BaiduRegeoAddressDTO) regeoResDTO.getAddressComponent();
                country = baiduRegeoAddressDTO.getCountry();
                if (StringUtils.isBlank(country) || !country.equals("中国")){
                    return false;
                }
                break;
            case GAODE:
                GaodeRegeoAddressDTO addressDTO = (GaodeRegeoAddressDTO) regeoResDTO.getAddressComponent();
                country = addressDTO.getCountry();
                if (StringUtils.isNotBlank(country) && country.equals("中国")
                        && StringUtils.isNotBlank(regeoResDTO.getFormatted_address())
                        && !"[]".equals(regeoResDTO.getFormatted_address())
                        && !"中华人民共和国".equals(addressDTO.getProvince())){
                    return true;
                }else{
                    return false;
                }
            default:
                break;
        }

        return true;
    }

    public static boolean isSameCountry(LocationsDetailDTO start,LocationsDetailDTO end){
        //判断两者的坐标系偏差大于1度
        BigDecimal lng = start.getLongitude().subtract(end.getLongitude());
        BigDecimal lat = start.getLatitude().subtract(end.getLatitude());

        if(lng.abs().compareTo(BigDecimal.ONE) <= 0
                && lat.abs().compareTo(BigDecimal.ONE) <= 0){
            return true;
        }else{
            return false;
        }
    }

    /**
     * 坐标偏差超过1度
     * <BR>平均速度超过150km/h
     * @param start
     * @param end
     * @return
     */
    public static boolean isErrorLocation(LocationsDetailDTO start,LocationsDetailDTO end){
        //判断两者的坐标系偏差大于1度
        BigDecimal lng;
        BigDecimal lat;
        try {
            //有可能读取到半拉的数据
             lng = start.getLongitude().subtract(end.getLongitude());
             lat = start.getLatitude().subtract(end.getLatitude());
        }catch (Exception e){
            return true;
        }

        if(lng.abs().compareTo(BigDecimal.ONE) >= 0
                || lat.abs().compareTo(BigDecimal.ONE) >= 0){
            //判断是否有定位在国外，定义为error定位
            String startL = start.getLongitude().toString().concat(",").concat(start.getLatitude().toString());
            String endL = end.getLongitude().toString().concat(",").concat(end.getLatitude().toString());
            try {
                List<RegeoResDTO> regeoResDTOList = RegeoUtil.reverseGeocodingBatch(Arrays.asList(startL,endL));
                if(regeoResDTOList != null && regeoResDTOList.size() > 1){
                    RegeoResDTO startR = regeoResDTOList.get(0);
                    RegeoResDTO endR = regeoResDTOList.get(1);
                    boolean isCHN0 = isCHN(startR);
                    boolean isCHN1 = isCHN(endR);
                    if(!isCHN0){
                        start.setLocationName("error");
                    }
                    if(!isCHN1){
                        end.setLocationName("error");
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            return true;
        }
        //判断两者之间的平均速度超过 200KM/H，则任务有问题
        double distance = getDistance(start.getLatitude(),start.getLongitude(),end.getLatitude(),end.getLongitude());
        long duration = LbsDateUtil.getDuration(start.getCreateDate(),end.getCreateDate());
        BigDecimal avgSpeed = calculateAvgSpeed(duration,distance);
        if(avgSpeed.compareTo(LbsConstants.LBS_MAX_AVG_SPEED) > 0){
            return true;
        }
        return false;
    }

    /**
     * 计算平均速度KM/H
     * @param duration 单位秒
     * @param distance 单位米
     * @return
     */
    public static BigDecimal calculateAvgSpeed(long duration,double distance){
        try {
            double hour = (double) duration / 3600D;
            double dis =  distance / 1000D;
            if(hour > 0){
                double avgS = dis / hour;
                return new BigDecimal(avgS).setScale(2,BigDecimal.ROUND_HALF_UP);
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        return BigDecimal.ZERO;
    }

    public static double getDistance(BigDecimal startLat,BigDecimal startLng,
                              BigDecimal endLat,BigDecimal endLng){
        S2LatLng start = S2LatLng.fromDegrees(startLat.doubleValue(),startLng.doubleValue());
        S2LatLng end = S2LatLng.fromDegrees(endLat.doubleValue(),endLng.doubleValue());
        double distance = start.getEarthDistance(end);
        return distance;
    }

    public static boolean isMove(double distance,long duration){
        //m/s 转换 km/h 的单位
        BigDecimal unit = new BigDecimal("3.6");
        //保留两位小数四舍五入
        if(duration < 1L){
            return false;
        }
        BigDecimal speed = new BigDecimal(distance).divide(new BigDecimal(duration),2,BigDecimal.ROUND_HALF_UP)
                .multiply(unit).setScale(2,BigDecimal.ROUND_HALF_UP);
        return speed.compareTo(new BigDecimal(LbsConstants.LBS_STOP_SPEED)) > 0;
    }
    public static int defaultInt(Integer source, int defaultTarget) {
        if (source == null) {
            return defaultTarget;
        } else {
            return source;
        }
    }
    

}
