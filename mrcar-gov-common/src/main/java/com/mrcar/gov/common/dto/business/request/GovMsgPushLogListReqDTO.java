package com.mrcar.gov.common.dto.business.request;

import com.google.gson.annotations.Expose;
import com.mrcar.gov.common.dto.PageParamDTO;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/12/28 17:32
 */
@Data
public class GovMsgPushLogListReqDTO extends PageParamDTO {

    // 消息日志编号
    private String logNo;
    // 消息记录编号
    private String recordNo;

    // 模板/公告编号
    private String msgNo;

    /**
     * 消息类型
     */
    private Integer msgType;

    /**
     * 推送方式
     */
    private Integer pushType;

    /**
     * 推送状态
     */
    private Integer pushStatus;

    /**
     * 推送开始时间
     */
    private Date pushStartTime;

    /**
     * 推送结束时间
     */
    private Date pushEndTime;
    /**
     * 触达状态
     */
    private Integer reachStatus;
    /**
     * 读取状态
     */
    private Integer readStatus;


    @Expose(serialize = false)
    private String userName;


    @NotNull(message = "人员查询类型不能为空")
    @Expose(serialize = false)
    private Integer userQryType;



}
