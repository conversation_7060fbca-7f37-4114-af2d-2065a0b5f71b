package com.mrcar.gov.common.util.pdf.model;

import com.mrcar.gov.common.util.pdf.layout.*;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.vandeseer.easytable.TableDrawer;
import org.vandeseer.easytable.settings.HorizontalAlignment;
import org.vandeseer.easytable.structure.Table;

import java.io.IOException;
import java.util.Arrays;

public class TableElement extends AbstractRowElement {

    private static final String NODE_NAME = "Table";

    private final float padding;
    private final float border;
    private final DisplayFont font;
    private final AlignmentType alignment;

    private final MetricValue[] columns;

    private Table table;

    protected TableElement(PageElement parent,
                           float padding,
                           float border,
                           DisplayFont font,
                           AlignmentType alignment,
                           MetricValue[] columns) {
        super(parent, MetricValue.adaptive(), RoundMetric.empty());
        this.padding = padding;
        this.border = border;
        this.font = font;
        this.alignment = alignment;
        this.columns = columns;
    }

    public static TableElement create(PageElement parent,
                                      float padding,
                                      float border,
                                      DisplayFont font,
                                      AlignmentType alignment,
                                      MetricValue[] columns) {
        return new TableElement(parent, padding, border, font, alignment, columns);
    }

    @Override
    public void append(BlockElement child) {
        if (!(child instanceof TableRowElement)) {
            throw new IllegalArgumentException("Table element only contains row element");
        }
        TableRowElement row = (TableRowElement) child;
        super.append(row);
    }

    public Table buildTable() {
        Table.TableBuilder builder =
                Table.builder()
                        .padding(padding)
                        .borderWidth(border)
                        .font(font.font())
                        .fontSize(font.size());
        Arrays.stream(this.columns).forEach(s -> builder.addColumnOfWidth(s.value()));
        switch (this.alignment) {
            case LEFT:
                builder.horizontalAlignment(HorizontalAlignment.LEFT);
                break;
            case RIGHT:
                builder.horizontalAlignment(HorizontalAlignment.RIGHT);
                break;
            case CENTER:
                builder.horizontalAlignment(HorizontalAlignment.CENTER);
                break;
            default:
                break;
        }
        for (BlockElement child : _children) {
            TableRowElement row = (TableRowElement) child;
            builder.addRow(row.buildRow());
        }
        return builder.build();
    }

    @Override
    public boolean refreshHeight() {
        if (this.table == null) {
            this.table = this.buildTable();
        }
        this._height = MetricValue.create(MetricType.ABSOLUTE, this.table.getHeight());
        return super.refreshHeight();
    }

    @Override
    public boolean refreshWidth() {
        for (int i = 0; i < columns.length; i++) {
            MetricValue metric = columns[i];
            if (metric.type() == MetricType.ADAPTIVE) {
                throw new IllegalArgumentException("Table Columns width not support adaptive");
            }
            if (metric.type() == MetricType.PERCENTAGE) {
                float pageWidth = _parent.pageSize().getWidth()
                        - _parent.padding().left().value()
                        - _parent.padding().right().value();
                MetricValue newMetric =
                        MetricValue.create(MetricType.ABSOLUTE,
                                metric.value() * pageWidth);
                columns[i] = newMetric;
            }
        }
        return true;
    }

    @Override
    public void render(Point point, PDPageContentStream stream) throws IOException {
        float x = point.x(); float y = point.y();
        TableDrawer drawer = TableDrawer.builder()
                .contentStream(stream)
                .startX(x)
                .startY(y)
                .table(table)
                .build();
        drawer.draw();
    }

    @Override
    public String name() {
        return NODE_NAME;
    }

}
