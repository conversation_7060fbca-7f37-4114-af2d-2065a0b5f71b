package com.mrcar.gov.common.security;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@NoArgsConstructor
public class LoginUser implements Serializable {

    private Long id;

    // 登陆平台
    private Integer plat;

    private String loginName;

    private Date loginTime;

    private String name;

    // 所属公司ID
    private Integer companyId;

    public LoginUser(Long id,
                     Integer plat,
                     String loginName,
                     String name,
                     Integer companyId) {
        this.id = id;
        this.plat = plat;
        this.loginName = loginName;
        this.name = name;
        this.loginTime = new Date();
        this.companyId = companyId;
    }

}
