package com.mrcar.gov.common.eventbus.events;

import com.mrcar.gov.common.eventbus.Event;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 公务用车出栏通知.
 */
@Data
public class GovPublicVehicleOutFenceEvent implements Event {

    /**
     * 报警编号
     */
    private String warnSn;

    /**
     * 车牌号
     */
    private String vehicleLicense;

    /**
     * 车辆车架号
     */
    private String vehicleVin;

    /**
     * 设备号
     */
    private String deviceNo;

    /**
     * Sim号
     */
    private String simNo;

    /**
     * 设备类型
     */
    private Integer deviceType;

    /**
     * 围栏快照id
     */
    private Integer fenceSnapId;

    /**
     * 出围栏时间
     */
    private Date outFenceTime;

    /**
     * 报警位置经度
     */
    private BigDecimal longitude;

    /**
     * 报警位置纬度
     */
    private BigDecimal latitude;

    /**
     * 报警位置地址
     */
    private String address;

    /**
     * 报警阈值时长, 单位:分钟
     */
    private Integer effectiveDuration;

}
