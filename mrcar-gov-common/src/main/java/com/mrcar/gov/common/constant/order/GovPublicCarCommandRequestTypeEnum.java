package com.mrcar.gov.common.constant.order;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum GovPublicCarCommandRequestTypeEnum {

    BLUE_TOOTH(1, "蓝牙"),
    NET(2, "网络");

    private final int code;
    private final String description;

    public static String getByCode(int code) {
        for (GovPublicCarCommandRequestTypeEnum type : values()) {
            if (type.code == code) {
                return type.getDescription();
            }
        }
        return "";
    }
}