package com.mrcar.gov.common.util;

import com.google.common.collect.Maps;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.DateTimeException;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 一些公用的日期方法
 * <AUTHOR>
 */
public class DateUtils {

    private static final Logger logger = LoggerFactory.getLogger(DateUtils.class);

    public static final String HOUR_TIME_FORMAT = "HH:mm";
    public static final String TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
    public static final String TIME_FORMAT_V2 = "yyyyMMddHHmmss";
    public static final String TIME_FORMAT_V3 = "yyyy-MM-dd HH:mm";
    public static final String TIMESTAMP_FORMAT = "yyyy-MM-dd HH:mm:ss.SSS";
    public static final String TIMESTAMP_CHINA = "yyyy年MM月dd日 HH:mm:ss";
    public static final String DATE_FORMAT = "yyyy-MM-dd";
    public static final String DATE_FORMAT_2 = "yyyyMMdd";
    public static final String MONTH_FORMAT = "yyyy_MM";
    public static final String TIMESTAMP_CHINA_APP = "yyyy年MM月dd日 HH:mm";
    public static final String MONTH_DAY_CHINA_APP = "MM月dd日 HH:mm";
    public static final String TIMESTAMP_CHINA_APP_ENGLISH = "yyyy/MM/dd HH:mm";
    public static final String MONTH_DAY_CHINA_APP_ENGLISH = "MM/dd HH:mm";
    public static final String DAY_MONTH_CHINA_APP = "HH:mm";
    public static final String DATE_FORMAT_CH = "yyyy年MM月dd日";
    public static final String DATE_FORMAT_CH_H = "yyyy年MM月dd日 HH时";
    public static final String DATE_FORMAT_BEGIN = "yyyy-MM-dd 00:00:00";
    public static final String DATE_FORMAT_BEGIN_YEAR = "yyyy-01-01 00:00:00";
    public static final String DATE_FORMAT_END = "yyyy-MM-dd 23:59:59";
    public static final String DATE_YYYY_MM = "yyyy-MM";
    public static final String DATE_YYYYMM = "yyyyMM";
    public static final String DATE_YYYY = "yyyy";
    public static final SimpleDateFormat TIME_SIMPLE_FORMAT = new SimpleDateFormat(TIME_FORMAT);
    public static final SimpleDateFormat DATE_SIMPLE_FORMAT = new SimpleDateFormat(DATE_FORMAT);
    public static final SimpleDateFormat DATE_SIMPLE_FORMAT_2 = new SimpleDateFormat(DATE_FORMAT_2);
    public static final SimpleDateFormat DATE_MONTH_FORMAT = new SimpleDateFormat(MONTH_FORMAT);
    public static final SimpleDateFormat HOUR_TIME_SIMPLE_FORMAT = new SimpleDateFormat(HOUR_TIME_FORMAT);
    public static final SimpleDateFormat DATE_SIMPLE_FORMAT_CH = new SimpleDateFormat(DATE_FORMAT_CH);
    public static final SimpleDateFormat DATE_SIMPLE_FORMAT_CH_H = new SimpleDateFormat(DATE_FORMAT_CH_H);
    public static final SimpleDateFormat DATE_YYYY_MM_FORMAT = new SimpleDateFormat(DATE_YYYY_MM);
    public static final SimpleDateFormat DATE_YYYYMM_FORMAT = new SimpleDateFormat(DATE_YYYYMM);
    public static final SimpleDateFormat DATE_YYYY_FORMAT = new SimpleDateFormat(DATE_YYYY);

    public static final SimpleDateFormat TIME_SIMPLE_FORMAT_V2 = new SimpleDateFormat(TIME_FORMAT_V2);

    public static int DAY_SECONDS = 86400;
	public static int HOUR_SECONDS = 3600;

    public static final Date DEFAULT_DATE_TIME = parseDate("1970-01-01 00:00:00");
    /**返回yyyy-MM-dd HH:mm:ss格式的字符串时间*/
    public static String createTimeString(){
        return TIME_SIMPLE_FORMAT.format(new Date());
    }

    /**返回yyyy-MM-dd格式的字符串时间*/
    public static String createDateString(){
        return DATE_SIMPLE_FORMAT.format(new Date());
    }
    /**返回yyyy-MM-dd格式的字符串时间*/
    public static String createMonthString(){
        return DATE_MONTH_FORMAT.format(new Date());
    }

    /**根据传入的参数返回yyyy-MM-dd格式的字符串时间*/
    public static String getDateString(Date date){
        return date==null?"":DATE_SIMPLE_FORMAT.format(date);
    }

    /**根据传入的参数返回yyyy-MM-dd HH:mm:ss格式的字符串时间*/
    public static String getTimeString(Date date){
        return date==null?"":TIME_SIMPLE_FORMAT.format(date);
    }

    /** 返回yyyy-MM-dd格式的字符串时间 */
    public static String date2string(Date date){
        return DATE_SIMPLE_FORMAT.format(date);
    }

    /** 返回yyyy-MM-dd HH:mm:ss格式的字符串时间 */
    public static String dateTime2string(Date date){
        return TIME_SIMPLE_FORMAT.format(date);
    }

    /**返回HH:mm格式的字符串时间*/
    public static String getTimeToString(Date date){
        return HOUR_TIME_SIMPLE_FORMAT.format(date);
    }
    /** 返回HH:mm:ss格式的字符串时间 */
    public static String dateHHmm2string(Date date){
        return date2String(date,HOUR_TIME_FORMAT);
    }

    public static String date2String(Date date,String timeFormat){
        SimpleDateFormat sdf = new SimpleDateFormat(timeFormat);
        return sdf.format(date);
    }

    public static Date yesterday() {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DAY_OF_MONTH, -1);
        return cal.getTime();
    }

    /**
     * 锁对象
     */
    private static Object lockObj = new Object();


    /**
     * 存放不同的日期模板格式的sdf的Map
     */
    private static Map<String, ThreadLocal<SimpleDateFormat>> sdfMap = Maps
            .newHashMapWithExpectedSize(2);


    
    /**
     * 返回一个ThreadLocal的sdf,每个线程只会new一次sdf
     *
     * @param pattern
     * @return SimpleDateFormat
     */
//    public static SimpleDateFormat getSdf(final String pattern) {
//        ThreadLocal<SimpleDateFormat> tl = sdfMap.get(pattern);
//
//        // 此处的双重判断和同步是为了防止sdfMap这个单例被多次put重复的sdf
//        if (tl == null) {
//            synchronized (lockObj) {
//                tl = sdfMap.get(pattern);
//                if (tl == null) {
//                    // 只有Map中还没有这个pattern的sdf才会生成新的sdf并放入map
//                    //System.out.println("put new sdf of pattern " + pattern + " to map");
//
//                    // 这里是关键,使用ThreadLocal<SimpleDateFormat>替代原来直接new SimpleDateFormat
//                    tl = new ThreadLocal<SimpleDateFormat>() {
//
//                        @Override
//                        protected SimpleDateFormat initialValue() {
//                            //System.out.println("thread: " + Thread.currentThread() + " init
//                            // pattern: " + pattern);
//                            return new SimpleDateFormat(pattern);
//                        }
//                    };
//                    // 把这个线程安全的对象放到对应key 为[pattern] 中去
//                    sdfMap.put(pattern, tl);
//                }
//            }
//        }
//        // 返回当前对象实例
//        return tl.get();
//    }
    
    //上面老的方法有问题sdfMap lockObj空指针，修改为下面
    public static SimpleDateFormat getSdf(final String pattern) {
        if (sdfMap == null) {
            synchronized (DateUtils.class) {
                if (sdfMap == null) {
                    sdfMap = new HashMap<>();
                }
            }
        }

        if (lockObj == null) {
            synchronized (DateUtils.class) {
                if (lockObj == null) {
                    lockObj = new Object();
                }
            }
        }

        ThreadLocal<SimpleDateFormat> tl = sdfMap.get(pattern);

        if (tl == null) {
            synchronized (lockObj) {
                tl = sdfMap.get(pattern);
                if (tl == null) {
                    tl = new ThreadLocal<SimpleDateFormat>() {
                        @Override
                        protected SimpleDateFormat initialValue() {
                            return new SimpleDateFormat(pattern);
                        }
                    };
                    sdfMap.put(pattern, tl);
                }
            }
        }
        return tl.get();
    }
    

    /**
     * 是用ThreadLocal<SimpleDateFormat>来获取SimpleDateFormat,这样每个线程只会有一个SimpleDateFormat
     *
     * @param date
     * @param pattern
     * @return String
     * <AUTHOR>
     */
    public static String format(Date date, String pattern) {
        return getSdf(pattern).format(date);
    }

    /**
     * @param dateStr
     * @param pattern
     * <AUTHOR>
     * @description parse
     * @date 2017-08-23 14:15:25
     */
    public static Date parse(String dateStr, String pattern) throws ParseException {
        return getSdf(pattern).parse(dateStr);
    }

    /**
     * 获取当天的00:00:00
     * @return
     */
    public static Date getTimeBegin(String time){
        Date beginOfDate = null;
        try {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(parse(time,"yyyy-MM-dd"));
            calendar.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH), calendar.get(Calendar.DAY_OF_MONTH),
                    0, 0, 0);
            beginOfDate = calendar.getTime();
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return beginOfDate;
    }

    /**
     * 获取当天的23:59:59
     * @return
     */
    public static Date getTimeEnd(String time){
        Date beginOfDate = null;
        try {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(parse(time,"yyyy-MM-dd"));
            calendar.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH), calendar.get(Calendar.DAY_OF_MONTH),
                    23, 59, 59);
            beginOfDate = calendar.getTime();
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return beginOfDate;
    }

    /** 当前日期 **/
    public static Date now() {
        Calendar cal = Calendar.getInstance();
        return cal.getTime();
    }

    /** 当前日期 **/
    public static Calendar calendar() {
        return Calendar.getInstance();
    }

    public static Date addDateMinutReturnDate(String date, int x)// 返回的是字符串型的时间，输入的
    {
        return addDateMinutB(getDate(date, TIME_FORMAT), x);
    }

    public static String addDateMinut(String date, int x)// 返回的是字符串型的时间，输入的
    {
        return getTimeString(addDateMinutB(getDate(date, TIME_FORMAT), x));
    }

    public static String addDateMinut(Date date, int x, String dateFormat)// 返回的是字符串型的时间，输入的
    {
        SimpleDateFormat format = new SimpleDateFormat(dateFormat);// 24小时制
        return format.format(addDateMinutB(date, x));

    }

    public static String addDateMinut(Date date, int x)// 返回的是字符串型的时间，输入的
    {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");// 24小时制
        return format.format(addDateMinutB(date, x));

    }

    public static String subtractDateMinut(Date date, int x)// 返回的是字符串型的时间，输入的
    {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");// 24小时制
        return format.format(subtractDateMinutB(date, x));

    }

    public static String subtractDateMinut(String dateStr, int x)// 返回的是字符串型的时间，输入的
    {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");// 24小时制
        return format.format(subtractDateMinutB(dateStr, x));

    }

    public static Date subtractDateMinutReturnDate(String dateStr, int x)// 返回的是字符串型的时间，输入的
    {
        return subtractDateMinutB(dateStr, x);
    }

    /**
     * 减少指定分钟数
     *
     * @return
     */
    public static Date subtractDateMinutB(String dateStr, int x)// 返回的是字符串型的时间，输入的
    // 是String day, int x
    {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");// 24小时制
        // 引号里面个格式也可以是 HH:mm:ss或者HH:mm等等，很随意的，不过在主函数调用时，要和输入的变
        // 量day格式一致
        Date date = null;
        try {
            date = format.parse(dateStr);
        } catch (Exception ex) {
            ex.printStackTrace();
        }

        if (date == null)
            return null;
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.set(Calendar.MINUTE, cal.get(Calendar.MINUTE) - x);
        date = cal.getTime();
        cal = null;
        return date;

    }

    /**
     * 减少指定分钟数
     *
     * @param date
     * @param x
     * @return
     */
    public static Date subtractDateMinutB(Date date, int x)// 返回的是字符串型的时间，输入的
    // 是String day, int x
    {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");// 24小时制
        // 引号里面个格式也可以是 HH:mm:ss或者HH:mm等等，很随意的，不过在主函数调用时，要和输入的变
        // 量day格式一致
        if (date == null)
            return null;
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.set(Calendar.MINUTE, cal.get(Calendar.MINUTE) - x);
        date = cal.getTime();
        cal = null;
        return date;

    }

    /**
     * 增加指定时间
     *
     * @param date
     * @param x
     * @return
     */
    public static Date addDateMinutB(Date date, int x)// 返回的是字符串型的时间，输入的
    // 是String day, int x
    {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");// 24小时制
        // 引号里面个格式也可以是 HH:mm:ss或者HH:mm等等，很随意的，不过在主函数调用时，要和输入的变
        // 量day格式一致
        if (date == null)
            return null;
        System.out.println("front:" + format.format(date)); // 显示输入的日期
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.MINUTE, x);// 24小时制
        date = cal.getTime();
        cal = null;
        return date;

    }

    public static Date addDateHour(Date date, int hour)// 返回的是字符串型的时间，输入的
    // 是String day, int x
    {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");// 24小时制
        // 引号里面个格式也可以是 HH:mm:ss或者HH:mm等等，很随意的，不过在主函数调用时，要和输入的变
        // 量day格式一致
        if (date == null)
            return null;
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.MINUTE, hour * 3600);// 24小时制
        date = cal.getTime();
        cal = null;
        return date;
    }


    public static long calculateRoundedMinutes(LocalDateTime startTime, LocalDateTime endTime) {
        // 计算时间差（以秒为单位）
        long totalSeconds = Duration.between(startTime, endTime).getSeconds();

        // 如果总秒数为负数，说明结束时间在开始时间之前
        if (totalSeconds < 0) {
            totalSeconds = -totalSeconds;
        }

        // 向上取整到最近的分钟
        // 如果总秒数不为0，则加59秒后整除60
        long roundedMinutes = (totalSeconds + 59) / 60;

        // 特殊情况处理：当totalSeconds为0时，表示时间差为0，按需求可能需要返回1
        if (roundedMinutes == 0 && totalSeconds > 0) {
            roundedMinutes = 1;
        }

        return roundedMinutes;
    }

    /**
     * 下一天
     *
     * @param date
     * @return
     */
    public static Date nextDay(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.DAY_OF_MONTH, 1);
        return cal.getTime();
    }

    public static Date addDay(Date date, int days) {
        Calendar cal = calendar();
        cal.setTime(date);
        cal.add(Calendar.DAY_OF_MONTH, days);
        return cal.getTime();
    }

    public static Date subDay(Date date, int days) {
        Calendar cal = calendar();
        cal.setTime(date);
        cal.add(Calendar.DAY_OF_MONTH, (0 - days));
        return cal.getTime();
    }

    /**
     *
     * addDate:
     *
     * @param date
     * @param field
     * @param amount
     * @return
     */
    public static Date addDate(Date date, int field, int amount) {
        Calendar cal = calendar();
        cal.setTime(date);
        cal.add(field, amount);
        return cal.getTime();
    }

    /**
     *
     * subDate:
     *
     * @param date
     * @param field
     * @param amount
     * @return
     */
    public static Date subDate(Date date, int field, int amount) {
        Calendar cal = calendar();
        cal.setTime(date);
        cal.add(field, (0 - amount));
        return cal.getTime();
    }

    /**
     * 获取一段时间段内的所有具体时间
     * <AUTHOR>
     *
     * @param dBegin
     * @param dEnd
     * @return
     */
    public static List<Date> findDates(Date dBegin, Date dEnd) {
        List<Date> dates = new ArrayList<Date>();
        dates.add(dBegin);
        Calendar calBegin = Calendar.getInstance();
        // 使用给定的 Date 设置此 Calendar 的时间
        calBegin.setTime(dBegin);
        Calendar calEnd = Calendar.getInstance();
        // 使用给定的 Date 设置此 Calendar 的时间
        calEnd.setTime(dEnd);
        // 测试此日期是否在指定日期之后
        while (dEnd.after(calBegin.getTime())) {
            // 根据日历的规则，为给定的日历字段添加或减去指定的时间量
            calBegin.add(Calendar.DAY_OF_MONTH, 1);
            dates.add(calBegin.getTime());
        }
        return dates;
    }


    /**
     * 获取一段时间段内的所有具体时间<String>
     * <AUTHOR>
     *
     * @param dBegin
     * @param dEnd
     * @return
     */
    public static List<String> findDatesAsString(Date dBegin, Date dEnd) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        List<String> dates = new ArrayList<String>();
        dates.add(sdf.format(dBegin));
        Calendar calBegin = Calendar.getInstance();
        // 使用给定的 Date 设置此 Calendar 的时间
        calBegin.setTime(dBegin);
        Calendar calEnd = Calendar.getInstance();
        // 使用给定的 Date 设置此 Calendar 的时间
        calEnd.setTime(dEnd);
        // 测试此日期是否在指定日期之后
        while (dEnd.after(calBegin.getTime())) {
            // 根据日历的规则，为给定的日历字段添加或减去指定的时间量
            calBegin.add(Calendar.DAY_OF_MONTH, 1);
            dates.add(sdf.format(calBegin.getTime()));
        }
        return dates;
    }

    public static Timestamp getCurrentTime() {
        return new Timestamp(new Date().getTime());
    }

    /**
     * 获取当前小时数（24小时制）
     * @param date
     * @return
     */
    public static int getCurrentHour(Date date)
    {
        Calendar rightNow = Calendar.getInstance();
        rightNow.setTime(date);
        return rightNow.get(Calendar.HOUR_OF_DAY);
    }

    /**
     * 获取当前日期和时间，格式为：yyyy-MM-dd HH:mm:ss
     *
     * @return
     */
    public static String getCurrentDateTime(String pattern) {
        Date now = new Date();
        DateFormat dateFormat = new SimpleDateFormat(pattern);
        return dateFormat.format(now);
    }

    /**
     * 判断当前时间
     * @param start
     * @param end
     * @return
     */
    public static boolean isExpried(String start, String end) {
        long s = 0, e = 0;
        long c = System.currentTimeMillis();
        if (null != start && !"".equals(start)) {
            s = getDate(start, "yyyy-MM-dd").getTime();
        }
        if (null != end && !"".equals(end)) {
            e = getDate(end, "yyyy-MM-dd").getTime();
        }

        if (s > 0 && e > 0) {
            // 严格时间范围内
            if (c >= s && c <= e) {
                return false;
            }
        } else if (s > 0 && e == 0) {
            // 只限制开始没有结束
            if (c >= s) {
                return false;
            }
        } else if (e > 0 && s == 0) {
            // 只限结束时间
            if (c <= e) {
                return false;
            }
        }
        return true;
    }

    public static Date getDate(String dateStr, String dateFormat) {
        DateFormat df=new SimpleDateFormat(dateFormat);
        Date d = null;
        try {
            d = df.parse(dateStr);
        } catch (ParseException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        return d;
    }

    /**
     * 获取当天剩余秒
     * @return
     */
    public static Long remainMilsToday()
    {
        Calendar c = Calendar.getInstance();
        c.set(Calendar.HOUR_OF_DAY, 23);
        c.set(Calendar.MINUTE, 59);
        c.set(Calendar.SECOND, 59);
        c.set(Calendar.MILLISECOND, 999);
        Date now = new Date();
        Long ms = (c.getTimeInMillis() - now.getTime()) / 1000;
        return ms;
    }

    /**
     * 日期相减(返回秒值)
     * @param date Date
     * @param date1 Date
     * @return int
     * <AUTHOR>
     */
    public static Long diffDateTime(Date date, Date date1) {

        Calendar c = Calendar.getInstance();
        c.setTime(date);

        long time1 =c.getTimeInMillis();

        c.setTime(date1);
        long time2 = c.getTimeInMillis();

        return (time1 - time2) / 1000;
    }

    public static Long diffDateTimeForMinute(Date date, Date date1) {

        Calendar c = Calendar.getInstance();
        c.setTime(date);

        long time1 =c.getTimeInMillis();

        c.setTime(date1);
        long time2 = c.getTimeInMillis();

        return (time1 - time2) / 1000/60;
    }

    /**
     * 获取 指定日期 后 指定毫秒后的 Date
     * @param date
     * @param millSecond
     * @return
     * <AUTHOR>
     */
    public static Date getDateAddMillSecond(Date date, int millSecond) {
        Calendar cal = Calendar.getInstance();
        if (null != date) {// 没有 就取当前时间
            cal.setTime(date);
        }
        cal.add(Calendar.MILLISECOND, millSecond);
        return cal.getTime();
    }

    /**
     *
     * @Title: getCurrentWeek
     * @Description: 获取当前周几
     * @param: @return
     * @return: int
     * @author: hufan
     * @throws
     */
    public static int getCurrentWeek(Calendar calendar) {

        if (calendar == null) {
            calendar = Calendar.getInstance();
        }
        // 一周第一天是否为星期天
        boolean isFirstSunday = (calendar.getFirstDayOfWeek() == Calendar.SUNDAY);
        // 获取周几
        int weekDay = calendar.get(Calendar.DAY_OF_WEEK);
        // 若一周第一天为星期天，则-1
        if (isFirstSunday) {
            weekDay = weekDay - 1;
            if (weekDay == 0) {
                weekDay = 7;
            }
        }

        return weekDay;
    }

    // add by lujiahao---------2017年5月9日22:03:38---------start
    /**
     * 日期增加若干小时
     * <AUTHOR>
     * @param date 日期
     * @param hour 增加的小时数
     * @return 增加后的日期
     */
    public static Date addDateHourNew(Date date,int hour){
        if (date == null)
            return null;
        Calendar c = Calendar.getInstance();
        c.setTime(date);   //设置当前日期
        c.add(Calendar.HOUR, hour); //日期分钟加1,Calendar.DATE(天),Calendar.HOUR(小时)
        date = c.getTime(); //结果
        return date;
    }
    /**
     * 日期增加若干分钟
     * <AUTHOR>
     * @param date
     * @param minute
     * @return
     */
    public static Date addDateMinute(Date date, int minute){// 返回的是字符串型的时间，输入的
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");// 24小时制
        // 引号里面个格式也可以是 HH:mm:ss或者HH:mm等等，很随意的，不过在主函数调用时，要和输入的变
        // 量day格式一致
        if (date == null)
            return null;
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.MINUTE, minute);// 24小时制
        date = cal.getTime();
        cal = null;
        return date;
    }
    /**
     * 日期增加小时(小数 eg:1.5)
     * <AUTHOR>
     * @param date
     * @param hourDouble
     * @return
     */
    public static Date addDateDoubleHour(Date date,Double hourDouble){
        Integer hour = hourDouble.intValue();
        Integer minute = (int) ((hourDouble - hour) * 60);
        Date dateAddHour = addDateHourNew(date, hour);
        Date dataAddMin = addDateMinute(dateAddHour, minute);
        return dataAddMin;
    }
    /**
     * 判断选择的日期是否是本周
     * @param time
     * @return
     */
    public static boolean isThisWeek(long time)  {
        Calendar calendar = Calendar.getInstance();
        int currentWeek = calendar.get(Calendar.WEEK_OF_YEAR);
        calendar.setTime(new Date(time));
        int paramWeek = calendar.get(Calendar.WEEK_OF_YEAR);
        if(paramWeek==currentWeek){
            return true;
        }
        return false;
    }
    /**
     * 判断选择的日期是否是今天
     * @param time
     * @return
     */
    public static boolean isToday(long time)  {
        return isThisTime(time,"yyyy-MM-dd");
    }
    /**
     * 判断选择的日期是否是本月
     * @param time
     * @return
     */
    public static boolean isThisMonth(long time)  {
        return isThisTime(time,"yyyy-MM");
    }
    private static boolean isThisTime(long time,String pattern) {
        Date date = new Date(time);
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        String param = sdf.format(date);//参数时间
        String now = sdf.format(new Date());//当前时间
        if(param.equals(now)){
            return true;
        }
        return false;
    }
    /**
     * 获取前月的第一天
     * @return
     */
    public static Date firstDayOfMonth(){
        Calendar c=Calendar.getInstance();//获取当前日期
        c.add(Calendar.MONTH, 0);
        c.set(Calendar.DAY_OF_MONTH,1);//设置为1号,当前日期既为本月第一天
        c.set(Calendar.HOUR_OF_DAY,0);
        c.set(Calendar.MINUTE,0);
        c.set(Calendar.SECOND,0);
        Date firstDay = c.getTime();
        return firstDay;
    }

    /**
     * 获取当月最后一刻
     *
     * @return
     */
    public static Date lastTimeOfCurrMonth() {
        LocalDate nowDate = LocalDate.now();
        LocalTime localTime = LocalTime.of(23, 59, 59);
        LocalDateTime dateTime = LocalDateTime.of(nowDate.with(TemporalAdjusters.lastDayOfMonth()), localTime);
        return Date.from(dateTime.atZone(ZoneId.systemDefault()).toInstant());
    }
    /**
     * 两个时间相差距离多少天多少小时多少分多少秒
     * <AUTHOR>
     * @param one
     * @param two
     * @return String 返回值为：xx天xx小时xx分xx秒
     */
    public static String getDistanceTime(Date one,Date two) {
        long day = 0;
        long hour = 0;
        long min = 0;
        long sec = 0;
        long time1 = one.getTime();
        long time2 = two.getTime();
        long diff ;
        if(time1<time2) {
            diff = time2 - time1;
        } else {
            diff = time1 - time2;
        }
        day = diff / (24 * 60 * 60 * 1000);
        hour = (diff / (60 * 60 * 1000) - day * 24);
        min = ((diff / (60 * 1000)) - day * 24 * 60 - hour * 60);
        sec = (diff/1000-day*24*60*60-hour*60*60-min*60);
        return day + "天" + hour + "小时" + min + "分" + sec + "秒";
    }
    /**
     * 两个时间相差距离多少天多少小时多少分多少秒
     * <AUTHOR>
     * @param str1 时间参数 1 格式：1990-01-01 12:00:00
     * @param str2 时间参数 2 格式：2009-01-01 12:00:00
     * @return String 返回值为：xx天xx小时xx分xx秒
     */
    public static String getDistanceTime(String str1, String str2) {
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date one = null;
        Date two = null;
        try {
            one = df.parse(str1);
            two = df.parse(str2);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return getDistanceTime(one, two);
    }
    /**
     * 两个时间相差距离多少小时多少分钟
     * <AUTHOR>
     * @param one
     * @param two
     * @return String 返回值为：xx小时xx分钟
     */
    public static String getDistanceTimeHHmm(Date one,Date two) {
        long hour = 0;
        long min = 0;
        long time1 = one.getTime();
        long time2 = two.getTime();
        long diff ;
        if(time1<time2) {
            diff = time2 - time1;
        } else {
            diff = time1 - time2;
        }
        hour = (diff / (60 * 60 * 1000));
        min = ((diff / (60 * 1000)) - hour * 60);
        String result = "";
        if (hour > 0) {
            result += hour + "小时";
            if (min > 0) {
                result += min + "分钟";
            }
        } else {
            result += min + "分钟";
        }
        return result;
    }
    // add by lujiahao---------2017年5月9日22:03:38---------end

    /**
     * 获取两个时间相差的小时
     * @param one
     * @param two
     * @return
     */
    public static long getDistanceTimeHours(Date one,Date two){
        long time1 = one.getTime();
        long time2 = two.getTime();
        long diff ;
        if(time1<time2) {
            diff = time2 - time1;
        } else {
            diff = time1 - time2;
        }
        long hour = (diff / (60 * 60 * 1000));
        return hour;
    }

    /**
     * 获取两个时间相差的天数
     * @param one
     * @param two
     * @return
     */
    public static long getDistanceTimeDays(Date one, Date two){
        LocalDate date1 = one.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate date2 = two.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        
        return ChronoUnit.DAYS.between(date1, date2);
    }
    
    /**
     * 日期加减
     * <AUTHOR>
     * @since 2017年5月19日 下午2:15:07
     * @param date 日期
     * @param num 增加   减少
     * @return
     */
    public static Date getDateTimeBefore(Date date, Integer num){
        if(null == date || null == num){
            return null;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DAY_OF_MONTH, num);
        return calendar.getTime();
    }

    public static Date getDateTimeBeforeHour(Date date, Integer num){
        if(null == date || null == num){
            return null;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.HOUR_OF_DAY, num);
        return calendar.getTime();
    }


    public static int compareDateString(String date1, String date2) {
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd hh:mm");
        try {
            Date dt1 = df.parse(date1);
            Date dt2 = df.parse(date2);
            compareDate(dt1,dt2);
        } catch (Exception exception) {
            exception.printStackTrace();
        }
        return 0;
    }

    public static Date comopareDate(Date date){
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        DateFormat df2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date d=null;
        try {
            String s =df2.format(date);
            d=df.parse(s);
        } catch (Exception exception) {
            exception.printStackTrace();
        }
        return d;
    }

    /**
     * 比较两个时间
     * date1 在 date2 后 ---> 1
     * date1 在 date2 前---> -1
     * date1 和 date2 相等---> 0
     */
    public static int compareDate(Date date1,Date date2){
        try {
            if (date1.getTime() > date2.getTime()) {
                return 1;
            } else if (date1.getTime() < date2.getTime()) {
                return -1;
            } else {
                return 0;
            }
        } catch (Exception exception) {
            exception.printStackTrace();
        }
        return 0;
    }

    /**
     *获取到凌晨剩余毫秒数
     * @return
     */
    public static long remainingMillisecondNumber(){
        Long milliSeconds = 0L;
        SimpleDateFormat dfs = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
        Date now = new Date();
        Long nowMillis = now.getTime();
        String today = DATE_SIMPLE_FORMAT.format(now);
        try {
            Date end = dfs.parse(today + " 23:59:59.999");
            Long endMillis = end.getTime();
            milliSeconds = endMillis-nowMillis;// 得到两者的毫秒数
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return milliSeconds;
    }

    /** 返回yyyyMMdd格式的字符串时间 */
    public static String date2yyyyMMdd(Date date){
        return DATE_SIMPLE_FORMAT_2.format(date);
    }

    /**
     * 获取指定'时分秒'的日期
     * @return
     */
    public static Date getAppointHMSDate(int hour,int minute,int second,int millSecond){
        Calendar c = Calendar.getInstance();// 获取当前日期
        c.set(Calendar.HOUR_OF_DAY, hour);
        c.set(Calendar.MINUTE, minute);
        c.set(Calendar.SECOND, second);
        c.set(Calendar.MILLISECOND, millSecond);
        return c.getTime();
    }

    /**
     * 获取指定'时分秒'的日期
     * @return
     */
    public static String getAppointHMSTimeStr(int hour,int minute,int second,int millSecond,String pattern){
        Calendar c = Calendar.getInstance();// 获取当前日期
        c.set(Calendar.HOUR_OF_DAY, hour);
        c.set(Calendar.MINUTE, minute);
        c.set(Calendar.SECOND, second);
        c.set(Calendar.MILLISECOND, millSecond);
        return DateUtils.date2String(c.getTime(), pattern);
    }
    /**
     * 将时间转换成app显示时间
     * 显示规则：
     * 当天0点0分和之后的：hh : mm
     * 昨天0点～23:59:59的：昨天 hh : mm
     * 7天前0点～前天23:59:59的：星期X hh : mm
     * 今年1月1日0点～7天前0点的：X月X日 hh : mm
     * 去年以前的：XXXX年 X月X日 hh : mm
     * @return
     */
    public static String formatShowAppDate(Date date){
        if(null==date) {
            return "";
        }
        //获取当前时间
        Calendar nowCalendar = Calendar.getInstance();// 获取当前日期
        nowCalendar.setTime(new Date());
        //当前时间年
        int nowYear = nowCalendar.get(Calendar.YEAR);
        //当前时间日
        int nowDay = nowCalendar.get(Calendar.DAY_OF_YEAR);
        //获取指定时间
        Calendar calendar=Calendar.getInstance();
        calendar.setTime(date);
        //指定时间年
        int year = calendar.get(Calendar.YEAR);
        //指定时间周几,从周日开始
        int week = calendar.get(Calendar.DAY_OF_WEEK);
        //指定时间日
        int day = calendar.get(Calendar.DAY_OF_YEAR);
        if(year<nowYear) {//去年以前的：XXXX年 X月X日 hh : mm
            return date2String(date, TIMESTAMP_CHINA_APP);
        }
        if(nowDay-7>=day) {//今年1月1日0点～7天前0点的：X月X日 hh : mm
            return date2String(date, MONTH_DAY_CHINA_APP);
        }
        if(nowDay-2>=day) {//7天前0点～前天23:59:59的：星期X hh : mm
            return showWeekChinaStr(week)+date2String(date, DAY_MONTH_CHINA_APP);
        }
        if(nowDay-1>=day) {//昨天0点～23:59:59的：昨天 hh : mm
            return "昨天 "+date2String(date, DAY_MONTH_CHINA_APP);
        }
        return date2String(date, DAY_MONTH_CHINA_APP);
    }

    /**
     * 将时间转换成app显示时间英文版
     * 显示规则：
     * 当天0点0分和之后的：hh : mm
     * 昨天0点～23:59:59的：Yesterday hh : mm
     * 7天前0点～前天23:59:59的：星期X hh : mm
     * 今年1月1日0点～7天前0点的：X月X日 hh : mm
     * 去年以前的：XXXX年 X月X日 hh : mm
     * @return
     */
    public static String formatShowAppDateEnglish(Date date){
        if(null==date) {
            return "";
        }
        //获取当前时间
        Calendar nowCalendar = Calendar.getInstance();// 获取当前日期
        nowCalendar.setTime(new Date());
        //当前时间年
        int nowYear = nowCalendar.get(Calendar.YEAR);
        //当前时间日
        int nowDay = nowCalendar.get(Calendar.DAY_OF_YEAR);
        //获取指定时间
        Calendar calendar=Calendar.getInstance();
        calendar.setTime(date);
        //指定时间年
        int year = calendar.get(Calendar.YEAR);
        //指定时间周几,从周日开始
        int week = calendar.get(Calendar.DAY_OF_WEEK);
        //指定时间日
        int day = calendar.get(Calendar.DAY_OF_YEAR);
        if(year<nowYear) {//去年以前的：XXXX年 X月X日 hh : mm
            return date2String(date, TIMESTAMP_CHINA_APP_ENGLISH);
        }
        if(nowDay-7>=day) {//今年1月1日0点～7天前0点的：X月X日 hh : mm
            return date2String(date, MONTH_DAY_CHINA_APP_ENGLISH);
        }
        if(nowDay-2>=day) {//7天前0点～前天23:59:59的：星期X hh : mm
            return showWeekChinaStrEnglish(week)+date2String(date, DAY_MONTH_CHINA_APP);
        }
        if(nowDay-1>=day) {//昨天0点～23:59:59的：昨天 hh : mm
            return "Yesterday "+date2String(date, DAY_MONTH_CHINA_APP);
        }
        return date2String(date, DAY_MONTH_CHINA_APP);
    }


    /*
     * 将时间戳转换为时间
     * 通用方法，根据传入的时间格式
     */
    public static String stampToDate(long time,String dateFormat){
        String res;
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(dateFormat);
        Date date = new Date(time);
        res = simpleDateFormat.format(date);
        return res;
    }

    /**
     *
     * <p>Title: 获取周几</p>
     * <p>Description: </p>
     * <AUTHOR>
     * @date 2018年9月26日
     * week Calendar
     */
    public static String showWeekChinaStr(int week) {
        String []arr = {"星期日  ","星期一  ","星期二 ","星期三 ","星期四  ","星期五  ","星期六  "};
        return arr[week-1];
    }
    /**
     *
     * <p>Title: 获取周几英文</p>
     * <p>Description: </p>
     * <AUTHOR>
     * @date 2018年9月26日
     * week Calendar
     */
    public static String showWeekChinaStrEnglish(int week) {
        String []arr = {"Sunday ","Monday ","Tuesday ","Wednesday ","Thursday ","Friday ","Saturday "};
        return arr[week-1];
    }


    /**
     * 根据传入的格式获取当前日期和时间
     *
     * @return
     */
    public static String getCurrentDate(String pattern) {
        Date now = new Date();
        DateFormat dateFormat = new SimpleDateFormat(pattern);
        return dateFormat.format(now.getTime());
    }

    /**
     * 两个时间相差距离多少天多少小时多少分多少秒
     *
     * @param str1
     *            时间参数 1 格式：1990-01-01 12:00:00
     * @param str2
     *            时间参数 2 格式：2009-01-01 12:00:00
     * @return String 返回值为：xx天xx小时xx分xx秒
     */
    @SuppressWarnings("unused")
    public static double getDistanceTimeDate(String str1, String str2) {
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date one;
        Date two;
        long day = 0;
        long hour = 0;
        long min = 0;
        long sec = 0;
        try {
            one = df.parse(str1);
            two = df.parse(str2);
            long time1 = one.getTime();
            long time2 = two.getTime();
            long diff;
            if (time1 < time2) {
                diff = time2 - time1;
            } else {
                diff = time1 - time2;
            }
            day = diff / (24 * 60 * 60 * 1000);
            hour = (diff / (60 * 60 * 1000) - day * 24);
            min = ((diff / (60 * 1000)) - day * 24 * 60 - hour * 60);
            sec = (diff / 1000 - day * 24 * 60 * 60 - hour * 60 * 60 - min * 60);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        logger.info("相差"+(double) day * 24 + (double) hour + (double) min/60);
        return (double) day * 24 + (double) hour + (double) min/60;
    }

    /**
     *
     * <p>Title: getDateTimeBeforeHour</p>
     * <p>Description:数据库time类型转换成当前时间 </p>
     * <AUTHOR>
     * @date 2018年9月23日
     * @param date
     * @return
     */
    public static Date getTimeChangeDate(Date date){
        if(null == date ){
            return null;
        }
        Calendar calendar = Calendar.getInstance();
        Calendar calendarNew = Calendar.getInstance();
        calendar.setTime(date);
        calendarNew.setTime(new Date());
        calendarNew.set(Calendar.HOUR_OF_DAY, calendar.get(Calendar.HOUR_OF_DAY));
        calendarNew.set(Calendar.MINUTE, calendar.get(Calendar.MINUTE));
        calendarNew.set(Calendar.SECOND, calendar.get(Calendar.SECOND));
        return calendarNew.getTime();
    }
    /**
     *
     * <p>Title: getDateTimeBeforeHour</p>
     * <p>Description:数据库time类型转换成当前时间  夜晚时间判断</p>
     * <AUTHOR>
     * @date 2018年9月23日
     * @param date
     * @return
     */
    public static Date getTimeChangeNightDate(Date date){
        if(null == date ){
            return null;
        }
        Calendar calendar = Calendar.getInstance();
        Calendar calendarNew = Calendar.getInstance();
        calendar.setTime(date);
        calendarNew.setTime(new Date());
        calendarNew.set(Calendar.HOUR, calendar.get(Calendar.HOUR));
        calendarNew.set(Calendar.MINUTE, calendar.get(Calendar.MINUTE));
        calendarNew.set(Calendar.SECOND, calendar.get(Calendar.SECOND));
        return calendarNew.getTime();
    }

    /*
     *返回当前零点
     */
    public static Date dayStarTime(){
        Date date = new Date();
        Calendar calendar=Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        return calendar.getTime();
    }

    /**
     * getDriverYear:(计算过去距离当前相差多少年). <br/>
     * <AUTHOR>
     * @param issue_date
     * @return
     * @throws Exception
     */
    public static int getDriverYear(String issue_date) throws Exception {

        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date  issue_dateDate = formatter.parse(issue_date);
        Calendar cal = Calendar.getInstance();

        if (cal.before(issue_dateDate)) {
            throw new IllegalArgumentException("参数错误，参数需要是一个过去的时间!");
        }
        int yearNow = cal.get(Calendar.YEAR);
        int monthNow = cal.get(Calendar.MONTH);
        int dayOfMonthNow = cal.get(Calendar.DAY_OF_MONTH);
        cal.setTime(issue_dateDate);

        int yearBirth = cal.get(Calendar.YEAR);
        int monthBirth = cal.get(Calendar.MONTH);
        int dayOfMonthBirth = cal.get(Calendar.DAY_OF_MONTH);

        int age = yearNow - yearBirth;

        if (monthNow <= monthBirth) {
            if (monthNow == monthBirth) {
                if (dayOfMonthNow < dayOfMonthBirth)
                    age--;
            } else {
                age--;
            }
        }
        return age;
    }


    /**
     * 查询从某时间到现在为止有多少个月
     * @param businessDate
     * @return
     */
    public static int getMonthToNow(Date businessDate) {
        if (businessDate == null) {
            return -1;
        }
        Calendar beforeCal = Calendar.getInstance();
        Calendar nowCal = Calendar.getInstance();
        beforeCal.setTime(businessDate);
        nowCal.setTime(new Date());
        int yearDiff = nowCal.get(Calendar.YEAR) - beforeCal.get(Calendar.YEAR);
        return nowCal.get(Calendar.MONTH) - beforeCal.get(Calendar.MONTH) + (yearDiff==0 ? 0 : 12 * yearDiff);
    }

    /**
     * 通过身份证号计算年龄
     * @param idNumber
     * @return
     */
    public static int getAgeFromIDcard(String idNumber) {
        int invalidAge = -1;
        String dateStr;
        if (idNumber.length() == 15) {
            dateStr = "19" + idNumber.substring(6, 12);
        } else if (idNumber.length() == 18) {
            dateStr = idNumber.substring(6, 14);
        } else {//默认是合法身份证号，但不排除有意外发生
            return invalidAge;
        }
        try {
            Date birthday = DATE_SIMPLE_FORMAT_2.parse(dateStr);
            return getAgeByDate(birthday);
        } catch (ParseException e) {
            return invalidAge;
        }
    }

    public static int getAgeByDate(Date birthday) {
        int invalidAge = -1;
        Calendar calendar = Calendar.getInstance();
        //calendar.before()有的点bug
        if (calendar.getTimeInMillis() - birthday.getTime() < 0L) {
            return invalidAge;
        }
        int yearNow = calendar.get(Calendar.YEAR);
        int monthNow = calendar.get(Calendar.MONTH);
        int dayOfMonthNow = calendar.get(Calendar.DAY_OF_MONTH);
        calendar.setTime(birthday);
        int yearBirthday = calendar.get(Calendar.YEAR);
        int monthBirthday = calendar.get(Calendar.MONTH);
        int dayOfMonthBirthday = calendar.get(Calendar.DAY_OF_MONTH);
        int age = yearNow - yearBirthday;
        if (monthNow <= monthBirthday && monthNow == monthBirthday && dayOfMonthNow < dayOfMonthBirthday || monthNow < monthBirthday) {
            age--;
        }
        return age;
    }

    public static Date getBeforeDaysDate(int num) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH ,-num);
        Date date = calendar.getTime();
        return date;
    }

    /*
     * 获取当天的日期转为固定格式
     */
    public static String getNowTimeString(String pattern){
        return date2String(new Date(),pattern);
    }

    public static Date getDateStart(Date date , String formtStr) {
        StringBuffer sb = new StringBuffer();
        sb.append(DATE_SIMPLE_FORMAT.format(date)).append(formtStr);
        Date reDate = null;
        try {
            reDate = TIME_SIMPLE_FORMAT.parse(sb.toString());
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return reDate;
    }

    public static Date format2Date(String strD , String formatStr) {
        if (StringUtils.isBlank(formatStr)) {
            return null;
        }
        SimpleDateFormat format = new SimpleDateFormat(formatStr);
        try {
            Date date = format.parse(strD);
            return date;
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }
    /**
     * 判断某一时间是否在一个区间内
     *
     * @param sourceTime
     *            时间区间,半闭合,如[10:00-20:00)
     * @param curTime
     *            需要判断的时间 如10:00
     * @return
     * @throws IllegalArgumentException
     */
    public static boolean isInTime(String sourceTime, String curTime) {
        if (sourceTime == null || !sourceTime.contains("-") || !sourceTime.contains(":")) {
            throw new IllegalArgumentException("Illegal Argument arg:" + sourceTime);
        }
        if (curTime == null || !curTime.contains(":")) {
            throw new IllegalArgumentException("Illegal Argument arg:" + curTime);
        }
        String[] args = sourceTime.split("-");
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
        try {
            long now = sdf.parse(curTime).getTime();
            long start = sdf.parse(args[0]).getTime();
            long end = sdf.parse(args[1]).getTime();
            if (args[1].equals("00:00")) {
                args[1] = "24:00";
            }
            if (end < start) {
                if (now >= end && now < start) {
                    return false;
                } else {
                    return true;
                }
            }
            else {
                if (now >= start && now < end) {
                    return true;
                } else {
                    return false;
                }
            }
        } catch (ParseException e) {
            e.printStackTrace();
            throw new IllegalArgumentException("Illegal Argument arg:" + sourceTime);
        }

    }

    /**检查字符串是否是合法日期格式**/
    public static boolean isValidDate(String str) {
        boolean convertSuccess=true;
        // 指定日期格式为四位年/两位月份/两位日期，注意yyyy/MM/dd区分大小写；
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        try {
            // 设置lenient为false. 否则SimpleDateFormat会比较宽松地验证日期，比如2007/02/29会被接受，并转换成2007/03/01
            format.setLenient(false);
            format.parse(str);
        } catch (ParseException e) {
            // e.printStackTrace();
            // 如果throw java.text.ParseException或者NullPointerException，就说明格式不对
            convertSuccess=false;
        }
        return convertSuccess;
    }

    /**
     * 比较两个日期时间字符串，比较date >= date2，返回比较结果
     *
     * @param dateTime1
     * @param dateTime2
     * @param parrent
     * @return
     */
    public static boolean compareDateForIsGreater(String dateTime1, String dateTime2,String parrent) {
        SimpleDateFormat sdf = getSdf(parrent);
        try {
            Date dt1 = sdf.parse(dateTime1);
            Date dt2 = sdf.parse(dateTime2);
            compareDateForIsGreater(dt1,dt2);
        } catch (ParseException e) {
            logger.error("传入日期错误");
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 比较两个日期时间字符串，比较date >= date2，返回比较结果
     *
     * @param dateTime1
     * @param dateTime2
     * @return
     */
    public static boolean compareDateForIsGreater(Date dateTime1, Date dateTime2){
        if(dateTime1 == null){
            return false;
        }
        if(dateTime2 == null){
            return true;
        }
        return dateTime1.compareTo(dateTime2)>=0;
    }

    public static Date parseDate(String dateStr){
        if (StringUtils.isBlank(dateStr)){
            return null;
        }
        Date parse = null;
        SimpleDateFormat sdf = null;
        if (dateStr.contains("/")){
            sdf = getSdf("yyyy/MM/dd HH:mm:ss");
        } else if (dateStr.contains("-")){
            sdf = getSdf(TIME_FORMAT);
        } else {
            sdf = getSdf(DATE_FORMAT);
        }
        try {
            sdf.setLenient(true);
            parse = sdf.parse(dateStr);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return parse;
    }

    public static LocalDateTime convertDate2LocalDateTime(Date date) {
        Instant instant = date.toInstant();
        ZoneId zoneId = ZoneId.of("GMT+8");
        return instant.atZone(zoneId).toLocalDateTime();
    }

    public static Date convertLocalDate2Date(LocalDate localDate) {
        ZonedDateTime zonedDateTime = localDate.atStartOfDay(ZoneId.systemDefault());
        return Date.from(zonedDateTime.toInstant());
    }


    /**
     * 获取日期所在月第一天
     * @return
     */
    public static String firstDayOfMonth(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.set(Calendar.DAY_OF_MONTH, 1);
        Date firstDay = c.getTime();
        return format(firstDay, DATE_FORMAT);
    }

    public static String date3String(Date date, String timeFormat) {
        return date != null ? date2String(date, timeFormat) : "";
    }
    /**
     * 根据时间 和时间格式 校验是否正确
     * @param length 校验的长度
     * @param sDate 校验的日期
     * @param format 校验的格式
     * @return
     */
    public static boolean isLegalDate(int length, String sDate,String format) {
        int legalLen = length;
        if ((sDate == null) || (sDate.length() != legalLen)) {
            return false;
        }
        DateFormat formatter = new SimpleDateFormat(format);
        try {
            Date date = formatter.parse(sDate);
            return sDate.equals(formatter.format(date));
        } catch (Exception e) {
            return false;
        }
    }



    public static DatePeriod getTimeInterval(DatePeriod startDatePeriod, DatePeriod endDatePeriod) {
        if (startDatePeriod.getStartDate().after(startDatePeriod.getEndDate())) {
            throw new DateTimeException("startDatePeriod的endDate不能小于startDate");
        }
        if (endDatePeriod.getStartDate().after(endDatePeriod.getEndDate())) {
            throw new DateTimeException("endDatePeriod的endDate不能小于startDate");
        }
        //判断是否有交集，没有交集直接返回空
        boolean before = startDatePeriod.getEndDate().before(endDatePeriod.getStartDate());
        boolean after = startDatePeriod.getStartDate().after(endDatePeriod.getEndDate());
        //过早或者过晚都没有交集
        if (before || after) {
            return null;
        }
        //从小到大进行排序，获取第二个和第三个元素生成的区间就是交集
        Date[] dateArray = {startDatePeriod.getStartDate(), startDatePeriod.getEndDate(),
                endDatePeriod.getStartDate(), endDatePeriod.getEndDate()};
        Arrays.sort(dateArray);
        DatePeriod datePeriod = new DatePeriod();
        datePeriod.setStartDate(dateArray[1]);
        datePeriod.setEndDate(dateArray[2]);
        return datePeriod;
    }


    @Data
   public static class DatePeriod{

        private Date startDate;

        private Date endDate;
    }

        public static String formatDate(Object obj) {
        if (obj instanceof Date) {
            Date date = (Date) obj;
            SimpleDateFormat dateFormat = null;

            // 判断日期是否包含时间部分
            if (date.getHours() == 0 && date.getMinutes() == 0 && date.getSeconds() == 0) {
                // 仅包含年月日
                dateFormat =getSdf(DATE_FORMAT);
            } else {
                // 包含年月日时分秒
                dateFormat = getSdf(TIME_FORMAT);
            }

            return dateFormat.format(date);
        } else {
            return String.valueOf(obj);
        }
    }
/**
     * 将指定日期加上给定小时数（可以是小数）
     *
     * @param date       输入的日期
     * @param hoursToAdd 需要加上的小时数（可以是小数）
     * @return 新的日期
     */
    public static Date addHoursToDate(Date date, BigDecimal hoursToAdd) {
        // 将输入日期转换为 Calendar 对象
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);

        // 计算加上的小时数，BigDecimal 转换为 long 和 double 进行运算
        BigDecimal fractionalHours = hoursToAdd.stripTrailingZeros(); // 去掉多余的0
        long hours = fractionalHours.longValue();
        double fractionalPart = fractionalHours.subtract(BigDecimal.valueOf(hours)).doubleValue();

        // 先加整数部分的小时数
        calendar.add(Calendar.HOUR_OF_DAY, (int) hours);

        // 再加小数部分的小时数
        if (fractionalPart > 0) {
            // 计算分钟数
            int minutesToAdd = (int) (fractionalPart * 60);
            calendar.add(Calendar.MINUTE, minutesToAdd);
        }

        return calendar.getTime();  // 返回新的 Date 对象
    }
    
    
    public static String getHourMinutes(Date end,Date start){
		if(Objects.isNull(end) || Objects.isNull(start)){
			return "";
		}
		LocalDateTime startDateTime =  start.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
		LocalDateTime endDateTime = end.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
		Duration duration = Duration.between(startDateTime, endDateTime);

		long hours = duration.toHours();
		long minutes = duration.toMinutes() % 60;
		return String.format("%02d", hours)+":"+String.format("%02d",minutes);
	}
    
  //获得某天的零点时刻
    public static long getDiffSeconds(Date source,Date dest){
        if(source==null||dest==null)
        {
            return -1;
        }
        return (dest.getTime()-source.getTime())/1000;
    }
    
    
    /**
	 * 日期相减(返回秒值)
	 * @param date Date
	 * @param date1 Date
	 * @return int
	 * <AUTHOR>
	 */
	public static Integer diffDateTimeInteger(Date date, Date date1) {
		return (int)(date.getTime()/1000)-(int)(date1.getTime()/1000);
	}  

    public static String dateStringToString(String date,String format) {
        if (StringUtils.isBlank(date)) {
            return "";
        }
        if (date.length() <= 11) {
            date += "T00:00:00";
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
        LocalDateTime dateTime = LocalDateTime.parse(date, formatter);
        return dateTime.format(formatter);
    }

    public static Date stringToDate(String date, String format) {
		if (StringUtils.isBlank(date)) {
			return null;
		}
		if (date.length() <= 11) {
			date += " 00:00:00";
		}
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
        LocalDateTime dateTime = LocalDateTime.parse(date, formatter);
        return Date.from(dateTime.atZone(java.time.ZoneId.systemDefault()).toInstant());
	}

    /**
	 * 计算两个日期相隔多少天
	 *
	 * @param day1  开始日期
	 * @param day2  结束日期
	 * @param scale 保留几位小数
	 * @return 相差天数
	 */
	public static BigDecimal daysBetween(Date day1, Date day2, int scale) {
		return timeBetween(day1, day2, scale, DAY_SECONDS);
	}

    /**
	 * 计算两个日期相隔多少小时
	 *
	 * @param day1  开始日期
	 * @param day2  结束日期
	 * @param scale 保留几位小数
	 * @return 相差天数
	 */
	public static BigDecimal hoursBetween(Date day1, Date day2, int scale) {
		return timeBetween(day1, day2, scale, HOUR_SECONDS);
	}

    	/**
	 * 计算两个日期相隔多少?
	 *
	 * @param day1  开始日期
	 * @param day2  结束日期
	 * @param scale 保留几位小数
	 * @return 相差天数
	 */
	public static BigDecimal timeBetween(Date day1, Date day2, int scale, int seconds) {
		if (day1 == null || day2 == null) {
			return null;
		}
		LocalDateTime date1 = day1.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
		LocalDateTime date2 = day2.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
		long betweenSeconds = ChronoUnit.SECONDS.between(date1, date2);
		return BigDecimal.valueOf(betweenSeconds).divide(BigDecimal.valueOf(seconds), scale, RoundingMode.HALF_DOWN);
	}

    public static long getDuration(Date start, Date end) {
        LocalDateTime localDate1 = convertDate2LocalDateTime(start);
        LocalDateTime localDate2 = convertDate2LocalDateTime(end);
        Duration duration = Duration.between(localDate1, localDate2);
        return duration.getSeconds();
    }

	
	//获得时间区间的月
    public static List<String> getDateList(Date beginCreateDate, Date endCreateDate,String node) throws Exception
    {
        List<String> dateList = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
        String begin = sdf.format(beginCreateDate);
        String end = sdf.format(endCreateDate);

        if(endCreateDate.before(sdf.parse(node)))
        {
            return new ArrayList<>();
        }
        if(beginCreateDate.before(sdf.parse(node)))
        {
            begin = node;
        }
        beginCreateDate = sdf.parse(begin);
        endCreateDate = sdf.parse(end);
        dateList.add(begin);
        while (org.apache.commons.lang3.time.DateUtils.addMonths(beginCreateDate,1).before(endCreateDate))
        {
            dateList.add(sdf.format(org.apache.commons.lang3.time.DateUtils.addMonths(beginCreateDate,1)));
            beginCreateDate = org.apache.commons.lang3.time.DateUtils.addMonths(beginCreateDate,1);
        }
        if(!dateList.contains(end))
        {
            dateList.add(end);
        }
        return dateList;
    }


    public static boolean compareTime(Date dateTime,Date start,Date end,String patter)  {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(patter);
            Date now = sdf.parse(sdf.format(dateTime));// 上报时间
            int toEnd = now.compareTo(end);// 与结束时间比较
            int toBegin = now.compareTo(start);// 与开始时间比较
            if (start.getTime()> end.getTime()){ //跨天
                if (toEnd<0) {
                    return true;
                } else {
                    if (toBegin>0) {
                        return true;
                    }
                }
            }
            if(toEnd<0&&toBegin>0){// 在两个时间中间
                return true;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    public static boolean compareDate(Date dateTime,Date start,Date end,String patter){
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(patter);
            Date beginDate = start;// 开始时间
            Date endDate = end;// 结束时间
            Date now = sdf.parse(sdf.format(dateTime));// 上报时间
            int toEnd = now.compareTo(endDate);// 与结束时间比较
            int toBegin = now.compareTo(beginDate);// 与开始时间比较
            if(toEnd<=0&&toBegin>=0){// 在两个时间中间
                return true;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    public static long getMinutesOfDuration(Date start, Date end) {
        LocalDateTime localDate1 = convertDate2LocalDateTime(start);
        LocalDateTime localDate2 = convertDate2LocalDateTime(end);
        Duration duration = Duration.between(localDate1, localDate2);
        return duration.toMinutes();
    }

    /**
     * 获取指定日期的年份
     */
    public static int getYear(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.YEAR);
    }

    /**
     * 获取一段时间内的所有年月日格式的日期，包含开始和结束日期本身
     */
    public static List<String> getDatesBetween(Date startDate, Date endDate) {
        List<String> dates = new ArrayList<>();
        SimpleDateFormat sdf = getSdf("yyyy-MM-dd");

        // 使用 Calendar 来遍历日期
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startDate);

        while (!calendar.getTime().after(endDate)) {
            dates.add(sdf.format(calendar.getTime()));
            calendar.add(Calendar.DAY_OF_MONTH, 1); // 增加一天
        }

        return dates;
    }

    /**
     * 格式化年月日字符串
     */
    public static Date formatDate(String dateStr, String pattern)  {
        try {
           return getSdf(pattern).parse(dateStr);
        }catch (Exception e){
            return null;
        }
    }

    public static String getCurrentTimeAsString() {
        LocalTime currentTime = LocalTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm:ss");
        return currentTime.format(formatter);
    }

    /**
     * 计算两个日期之间相差的天数
     * @param date1
     * @param date2
     * @return
     */
    public static long calculateDaysBetween(Date date1, Date date2) {
        // 使用 Calendar 清除时间部分
        Calendar cal1 = Calendar.getInstance();
        cal1.setTime(date1);
        cal1.set(Calendar.HOUR_OF_DAY, 0);
        cal1.set(Calendar.MINUTE, 0);
        cal1.set(Calendar.SECOND, 0);
        cal1.set(Calendar.MILLISECOND, 0);

        Calendar cal2 = Calendar.getInstance();
        cal2.setTime(date2);
        cal2.set(Calendar.HOUR_OF_DAY, 0);
        cal2.set(Calendar.MINUTE, 0);
        cal2.set(Calendar.SECOND, 0);
        cal2.set(Calendar.MILLISECOND, 0);

        // 计算毫秒差值
        long timeDiff = cal2.getTimeInMillis() - cal1.getTimeInMillis();

        // 转换为天数
        return (timeDiff / (24 * 60 * 60 * 1000))+1;
    }
    /**
     * 将 Date 的时间部分设置为 23:59:59
     *
     * @param date 输入的 Date 对象
     * @return 时间部分设置为 23:59:59 的 Date 对象
     */
    public static Date setEndOfDay(Date date) {
        if (date == null) {
            return null;
        }

        // 将 Date 转换为 LocalDateTime
        LocalDateTime localDateTime = date.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();

        // 将时间部分设置为 23:59:59
        LocalDateTime endOfDay = localDateTime.with(LocalTime.MAX);

        // 将 LocalDateTime 转换回 Date
        return Date.from(endOfDay.atZone(ZoneId.systemDefault()).toInstant());
    }
    /**
     * 将 Date 的时间部分设置为 00:00:00
     *
     * @param date 输入的 Date 对象
     * @return 时间部分设置为 00:00:00 的 Date 对象
     */
    public static Date setStartOfDay(Date date) {
        if (date == null) {
            return null;
        }

        // 将 Date 转换为 LocalDateTime
        LocalDateTime localDateTime = date.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();

        // 将时间部分设置为 23:59:59
        LocalDateTime endOfDay = localDateTime.with(LocalTime.MIN);

        // 将 LocalDateTime 转换回 Date
        return Date.from(endOfDay.atZone(ZoneId.systemDefault()).toInstant());
    }
    /**
     * LocalDate 转化字符串
     */
    public static String localDateToString(LocalDate localDate) {
        if (localDate == null) {
            return "";
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return localDate.format(formatter);
    }

    public static List<LocalDate> convertStringToLocalDateList(String input) {
        List<LocalDate> dateList = new ArrayList<>();

        // 定义日期格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        // 分割字符串，获取起始日期和结束日期
        String[] dates = input.split("~");
        LocalDate startDate = LocalDate.parse(dates[0], formatter);
        LocalDate endDate = LocalDate.parse(dates[1], formatter);

        // 生成日期范围
        for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
            dateList.add(date);
        }

        return dateList;
    }

	
}
