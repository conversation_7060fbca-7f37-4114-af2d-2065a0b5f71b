package com.mrcar.gov.common.dto.workflow.task;

import com.mrcar.gov.common.dto.workflow.BaseDTO;
import lombok.Data;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 流程任务的更新负责人的 Request VO
 */
@Data
public class BpmTaskUpdateAssigneeReqDTO extends BaseDTO {

    /**
     * 任务编号
     */
    @NotEmpty(message = "任务编号不能为空")
    private String id;

    /**
     * 新审批人的用户编号
     */
    @NotNull(message = "新审批人的用户编号不能为空")
    private Long assigneeUserId;
}
