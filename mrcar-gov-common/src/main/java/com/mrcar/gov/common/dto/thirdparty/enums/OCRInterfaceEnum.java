package com.mrcar.gov.common.dto.thirdparty.enums;


import com.mrcar.gov.common.dto.thirdparty.constants.OCRConstants;

public enum OCRInterfaceEnum {
    ALI_OCR(1, OCRConstants.ALI_OCR);

    private int code;
    private String name;

    OCRInterfaceEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }


    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
