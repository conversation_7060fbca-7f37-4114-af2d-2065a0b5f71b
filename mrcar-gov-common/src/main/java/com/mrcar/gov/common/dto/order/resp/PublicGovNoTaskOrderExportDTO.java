package com.mrcar.gov.common.dto.order.resp;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import izu.org.apache.poi.ss.usermodel.BorderStyle;
import izu.org.apache.poi.ss.usermodel.HorizontalAlignment;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@HeadStyle(
        fillForegroundColor = 22,
        fillBackgroundColor = 22
)
@ColumnWidth(30)
@ContentStyle(
        horizontalAlignment = HorizontalAlignment.CENTER,
        borderBottom = BorderStyle.THIN,
        borderLeft = BorderStyle.THIN,
        borderRight = BorderStyle.THIN,
        borderTop = BorderStyle.THIN,
        wrapped = true
)
@Data
public class PublicGovNoTaskOrderExportDTO {

    @ExcelProperty(value = "行程单号")
    private String orderNo;

    @ExcelProperty(value = "行程类型")
    private String orderTypeStr;

    @ExcelProperty(value = "核实状态")
    private String verifyStatusStr;

    @ExcelProperty(value = "行程状态")
    private String orderStatusStr;

    @ExcelProperty(value = "驾驶员类型")
    private String drivingTypeStr;

    @ExcelProperty(value = "驾驶员")
    private String driverInfo;

    @ExcelProperty(value = "主用车人")
    private String passengerUserName;

    @ExcelProperty(value = "主用车人部门")
    private String passengerStructName;

    @ExcelProperty(value = "主用车人单位")
    private String passengerDeptName;

    @ExcelProperty(value = "车辆类型")
    private String vehicleTypeStr;

    @ExcelProperty(value = "车牌号")
    private String vehicleLicense;

    @ExcelProperty(value = "实际出发城市")
    private String startCityName;

    @ExcelProperty(value = "实际出发地")
    private String actualDepartureShortLocation;

    @ExcelProperty(value = "实际目的城市")
    private String endCityName;

    @ExcelProperty(value = "实际目的地")
    private String actualDestinationShortLocation;

    @ExcelProperty(value = "实际开始时间")
    private Date orderStartTime;

    @ExcelProperty(value = "实际结束时间")
    private Date orderEndTime;

    @ExcelProperty(value = "用车时长(小时)")
    private String userTime;

    @ExcelProperty(value = "里程数(km)")
    private BigDecimal totalMileage;

    @ExcelProperty(value = "费用合计(元)")
    private String totalFee;

    @ExcelProperty(value = "用车事由")
    private String carUseReason;

    @ExcelProperty(value = "用车备注")
    private String orderUserMemo;

    @ExcelProperty(value = "是否跨单位用车")
    private String crossTypeStr;
}