package com.mrcar.gov.common.constant.order;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2024/8/18 13:47
 */
@AllArgsConstructor
@Getter
public enum ApprovalWithdrawalTypeEnum {
    UNKNOWN(0, "未知"),
    SYSTEM(1, "系统撤回"),
    PERSON(2, "人为撤回");

    private final Integer code;
    private final String name;

    public static String getName(Integer code) {
        return Arrays.stream(values()).filter(e -> e.getCode().equals(code)).findFirst().map(ApprovalWithdrawalTypeEnum::getName).orElse(null);
    }

}
