package com.mrcar.gov.common.util.pdf.layout;

public interface MetricValue {

    MetricType type();

    float value();

    static MetricValue create(MetricType type, float value) {
        return new DefaultMetricValue(type, value);
    }

    static MetricValue full() {
        return new DefaultMetricValue(MetricType.PERCENTAGE, 1);
    }

    static MetricValue adaptive() {
        return new DefaultMetricValue(MetricType.ADAPTIVE, -1);
    }

}
