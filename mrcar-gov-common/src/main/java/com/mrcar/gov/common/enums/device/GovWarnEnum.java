package com.mrcar.gov.common.enums.device;

import java.util.Arrays;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/11/19 20:50
 */
public class GovWarnEnum {


    public enum WarnDealTypeEnum {
        VALID_NEED_TO_FOLLOW(  1, "有效报警需要跟进"),
        CONFIRM_FALSE_ALARM(  2, "经核实为误报"),
        DEVICE_EXCEPTION(  3, "设备异常"),
        OTHER(  4, "其他"),
        DEVICE_LOCATION_FLOAT(  5, "设备定位飘点"),
        ORDER_INPUT(  6, "订单补录"),
        GOV_ORDER_NO_TASK_VERIFIED(  7, "用车已核实"),

        ;

        private final Integer code;

        private final String name;

        WarnDealTypeEnum(Integer code, String name) {
            this.code = code;
            this.name = name;
        }

        public Integer getCode() {
            return code;
        }

        public String getName() {
            return name;
        }


        public static String getNameByCode(Integer code) {
            for (WarnDealTypeEnum e : WarnDealTypeEnum.values()) {
                if (e.getCode().equals(code)) {
                    return e.getName();
                }
            }
            return "";
        }
    }

    public enum WarnStatusEnum {
        UNHANDLED(  1, "未处理"),
        HANDLED(  2, "已处理");

        private final Integer code;

        private final String name;

        WarnStatusEnum(Integer code, String name) {
            this.code = code;
            this.name = name;
        }

        public Integer getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public static String getNameByCode(Integer code) {
            for (WarnStatusEnum e : WarnStatusEnum.values()) {
                if (e.getCode().equals(code)) {
                    return e.getName();
                }
            }
            return "";
        }
    }

    public enum FenceTypeEnum {
        /**
         * 圆形
         */
        ROUND(1, "圆形"),
        /**
         * 多边形
         */
        POLYGON(2, "多边形"),
        /**
         * 行政区划（省市）
         */
        REGION(3, "行政区划"),

        province(4, "省份"),

        city(5, "城市"),

        county(6, "区县");

        private final Integer code;

        private final String name;

        FenceTypeEnum(Integer code, String name) {
            this.code = code;
            this.name = name;
        }

        public Integer getCode() {
            return code;
        }


        public String getName() {
            return name;
        }


        public static String getNameByCode(Integer code) {
            for (FenceTypeEnum e : FenceTypeEnum.values()) {
                if (e.getCode().equals(code)) {
                    return e.getName();
                }
            }
            return "";
        }
    }

    public enum FenceWarnBussStatusEnum {
        DEFAULT(  0, "默认-无业务语义"),
        NO_WORKING(  1, "无任务"),
        IN_WORKING(  2, "任务中");

        private final Integer code;
        private final String name;

        FenceWarnBussStatusEnum(Integer code, String name) {
            this.code = code;
            this.name = name;
        }

        public Integer getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public static String getNameByCode(Integer code) {
            for (FenceWarnBussStatusEnum e : FenceWarnBussStatusEnum.values()) {
                if (e.getCode().equals(code)) {
                    return e.getName();
                }
            }
            return "";
        }
    }

    public enum WarnType {
        /**
         * 出栏报警
         */
        WARN_OUT(1, "出栏报警"),
        /**
         * 入栏报警
         */
        WARN_IN(2, "入栏报警"),

        /**
         * 超速报警
         */
        WARN_OVER_SPEED(3, "围栏超速"),
        /**
         * 掉线报警
         */
        DEVICE_OFF_WARN(4, "掉线报警"),
        /**
         * 无线设备拆除报警
         */
        WIFI_DEVICE_REMOVE_WARN(5, "无线设备拆除报警"),
        /**
         * 有线设备被拔报警
         */
        WIRED_DEVICE_REMOVE_WARN(6, "有线设备被拔报警"),
        /**
         * 急加速
         */
        SPEED_QUICK_UP_WARN(7, "急加速"),
        /**
         * 急刹车
         */
        SPEED_QUICK_DOWN_WARN(8, "急刹车"),
        /**
         * 急转弯
         */
        SUDDEN_TURN_WARN(9, "急转弯"),
        /**
         * 超速
         */
        OVERSPEED_WARN(10, "超速"),

        /**
         * 出栏记录
         * 单纯记录出栏，不用处理该报警信息
         */
        WARN_OUT_LOG(11, "出栏记录"),

        /**
         * 以下为视频监控设备新增的报警类型，已有的报警类型还沿用之前的编码定义
         **/

        TIRED(12, "疲劳驾驶"),
        PHONE(13, "打电话"),
        SMOKE(14, "抽烟"),
        ATTENTION(15, "分神驾驶"),
        DRIVER_EXCEPTION(16, "未检测到驾驶员"),
        DRIVER_CHANGE(17, "驾驶员变更事件"),
        BLOCKING_CAMERA(18, "遮挡镜头"),
        INFARED_BLOCKING(19, "红外阻断"),
        FRONT(20, "前向碰撞"),
        DEVIATE(21, "车道偏离"),
        CLOSE(22, "车距过近"),
        WALKER(23, "行人碰撞"),
        LANE_CHANGE(24, "频繁变道"),
        BLOCK(26, "障碍物"),
        ROAD_SIGNS(27, "道路标志识别事件"),
        SNAP_PHOTO(28, "主动抓拍事件"),
        RAPID_DECELERATION(31, "急减速"),
        NO_SAFETY_BELT(32, "未系安全带"),
        OFFICIAL_VEHICLES_WARN_OUT(33, "公务用车报警"),
        UNKNOWN(99, "未知");

        private final Integer value;

        private final String valueStr;


        public Integer value() {
            return value;
        }


        WarnType(Integer value, String valueStr) {
            this.value = value;
            this.valueStr = valueStr;
        }

        public Integer getValue() {
            return value;
        }

        public String getValueStr() {
            return valueStr;
        }


        public static String getValueStrByValue(Integer value) {
            Optional<WarnType> first = Arrays.stream(values()).filter(e -> e.value().equals(value)).findFirst();
            if (first.isPresent()) {
                return first.get().getValueStr();
            } else {
                return "";
            }
        }

        public static WarnType getWarnTypeEnum(Integer value) {
            Optional<WarnType> first = Arrays.stream(values()).filter(e -> e.value().equals(value)).findFirst();
            return first.orElse(UNKNOWN);
        }
    }

    public enum WarnDealBusEnum {
        OFF("off","离线"),
        REMOVE("remove","拆机"),
        FENCE("fence","围栏")
        ;

        private final String code;

        private final String name;

        WarnDealBusEnum(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public static String getNameByCode(String code){
            for(WarnDealBusEnum e: WarnDealBusEnum.values()){
                if(e.getCode().equals(code)){
                    return e.getName();
                }
            }
            return "";
        }

        public static WarnDealBusEnum getEnumByCode(String code){
            for(WarnDealBusEnum e: WarnDealBusEnum.values()){
                if(e.getCode().equals(code)){
                    return e;
                }
            }
            return null;
        }
    }

    public enum DateTypeEnum {
        WORKDAY( 1,"工作日"),
        HOLIDAY( 2,"节假日"),
        CUSTOM(3,"自定义日期");


        private final Integer code;
        private final String msg;


        DateTypeEnum(Integer code, String msg) {
            this.code =code;
            this.msg = msg;
        }

        public static String getMsg(Integer code){
            for(DateTypeEnum e: DateTypeEnum.values()){
                if(e.getCode().equals(code)){
                    return e.getMsg();
                }
            }
            return null;
        }


        public static DateTypeEnum getEnumByCode(Integer code){
            for(DateTypeEnum e: DateTypeEnum.values()){
                if(e.getCode().equals(code)){
                    return e;
                }
            }
            return null;
        }

        public Integer getCode() {
            return code;
        }

        public String getMsg() {
            return msg;
        }
    }

}
