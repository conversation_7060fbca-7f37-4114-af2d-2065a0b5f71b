package com.mrcar.gov.common.dto.workflow.group;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import java.util.Date;

/**
 * 管理后台 - 用户组 Response VO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BpmUserGroupRespDTO extends BpmUserGroupBaseDTO {

    /**
     * 编号
     * 示例值: 1024
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date createTime;

}
