package com.mrcar.gov.common.constant.bi;

import com.mrcar.gov.common.constant.asset.VehicleFeeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;


/**
 * <AUTHOR>
 * @date 2025/2/18 20:36
 */
@AllArgsConstructor
@Getter
public enum BiVehicleFeeTypeEnum {
    //    1:加油费 2:保险费 3：维修保养费 4:过路过桥费 5：洗车费 6：其他费用
    oilFee(1, "加油费", 1),
    insuranceFee(2, "保险费", 2),
    maintainFee(3, "维修保养费", 3),
    tollFee(4, "过路过桥费", 4),
    carWashFee(5, "洗车费", 5),
    otherFee(6, "其他费用", 100),
    annualInspectionFee(7, "年检费", 7),
    ;

    private Integer code;

    private String desc;

    private Integer sort;


    public static Integer getSortByCode(Integer code) {
        for (BiVehicleFeeTypeEnum value : BiVehicleFeeTypeEnum.values()) {
            if (Objects.equals(value.getCode(), code)){
                return value.getSort();
            }
        }
        return 0;
    }

    public static String getDescByCode(int code) {
        for (BiVehicleFeeTypeEnum type : BiVehicleFeeTypeEnum.values()) {
            if (type.getCode() == code) {
                return type.getDesc();
            }
        }
        return "";
    }
    /**
     * 年检费
     */
//    private BigDecimal annualInspectionFee;

}