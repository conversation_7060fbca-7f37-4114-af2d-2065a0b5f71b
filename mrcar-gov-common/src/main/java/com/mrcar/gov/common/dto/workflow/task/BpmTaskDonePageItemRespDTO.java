package com.mrcar.gov.common.dto.workflow.task;

import cn.hutool.core.text.CharSequenceUtil;
import com.mrcar.gov.common.dto.workflow.enums.BpmProcessInstanceResultEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import java.util.Date;

/**
 * 流程任务的 Done 已完成的分页项 Response VO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BpmTaskDonePageItemRespDTO extends BpmTaskTodoPageItemRespDTO {

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 持续时间
     */
    private Long durationInMillis;

    /**
     * 任务结果
     * 参见 bpm_process_instance_result
     */
    private Byte result;

    /**
     * 任务结果名称
     */
    private String resultStr;

    /**
     * 审批建议
     */
    private String reason;

    @Override
    public void setResult(Byte result) {
        this.result = result;
        this.resultStr = BpmProcessInstanceResultEnum.getEnum(result).getDesc();
    }

    public String getReason() {
        if(CharSequenceUtil.startWith(reason, "不通过任务，原因：")){
            return "";
        }
        return reason;
    }
}
