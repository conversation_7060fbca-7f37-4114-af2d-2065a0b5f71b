package com.mrcar.gov.common.enums.device;

import lombok.Getter;
import lombok.Setter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @description: 开关锁类型 1 开锁 2 关锁
 * @date 2025/6/23 18:11
 */
public enum GovPublicCarOperateTypeEnum {
    OPEN_LOCK(1,"开锁"),
    CLOSE_LOCK(2,"关锁");


    @Getter
    @Setter
    private Integer code;
    @Getter
    @Setter
    private String  msg;

    GovPublicCarOperateTypeEnum(Integer code, String msg){
        this.code = code;
        this.msg = msg;
    }

    public static GovPublicCarOperateTypeEnum getEnumByCode(Integer code) {
        return Arrays.stream(GovPublicCarOperateTypeEnum.values())
                .filter(publicCarOperateTypeEnum -> publicCarOperateTypeEnum.code.equals(code))
                .findFirst()
                .orElse(null);
    }

    public static String getEnumMsgByCode(Integer code) {
        return Arrays.stream(GovPublicCarOperateTypeEnum.values())
                .filter(publicCarOperateTypeEnum -> publicCarOperateTypeEnum.code.equals(code))
                .map(GovPublicCarOperateTypeEnum::getMsg)
                .findFirst()
                .orElse("");
    }
}
