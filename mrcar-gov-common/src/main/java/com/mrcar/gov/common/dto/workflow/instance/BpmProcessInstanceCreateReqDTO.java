package com.mrcar.gov.common.dto.workflow.instance;

import com.mrcar.gov.common.dto.workflow.BaseDTO;
import lombok.Data;
import javax.validation.constraints.NotEmpty;

/**
 * 管理后台 - 流程实例的创建 Request VO
 */
@Data
public class BpmProcessInstanceCreateReqDTO extends BaseDTO {

    /**
     * 流程定义的编号
     * 示例值: 1024
     */
    @NotEmpty(message = "流程定义编号不能为空")
    private String processDefinitionId;

    /**
     * 变量实例
     */
    private String variables;

    /**
     * 父级流程实例id（发起下级流程时保存传入）
     * 示例值: 9f9f770d-69c0-11ef-8ea4-96b1e6fae18b
     */
    private String parentProcessInstanceId;

}
