
package com.mrcar.gov.common.dto.catnet;

import com.mrcar.gov.common.enums.carnet.CoordTypeEnum;

public class Point
{
    private double lat;
    private double lng;
    private double latitude;
    private double longitude;
    private CoordTypeEnum coordType;

    public Point(double lng, double lat, CoordTypeEnum coordType)
    {
        this.lat = lat;
        this.lng = lng;
        this.latitude =lat;
        this.longitude =lng;
        this.coordType = coordType;
    }

    public Point(String lngAndLat, CoordTypeEnum coordType)
    {
        this.coordType = coordType;
        if ((lngAndLat != null) && (lngAndLat.contains(","))) {
            String[] lnglat = lngAndLat.split(",");
            if (lnglat.length == 2) {
                this.lng = Double.valueOf(lnglat[0]).doubleValue();
                this.lat = Double.valueOf(lnglat[1]).doubleValue();
            }
        }
    }

    public String toLngLat()
    {
        return this.lng + "," + this.lat;
    }

    public String toLatLng()
    {
        return this.lat + "," + this.lng;
    }

    public double getLat() {
        return this.lat;
    }

    public void setLat(Double lat) {
        this.lat = lat.doubleValue();
    }

    public double getLng() {
        return this.lng;
    }

    public void setLng(Double lng) {
        this.lng = lng.doubleValue();
    }

    public CoordTypeEnum getCoordType() {
        return this.coordType;
    }

    public void setCoordType(CoordTypeEnum coordType) {
        this.coordType = coordType;
    }

    public String toString()
    {
        return "Point{lat=" + this.lat + ", lng=" + this.lng + ", coordType=" + this.coordType + '}';
    }

    public double getLatitude() {
        return latitude;
    }

    public void setLatitude(double latitude) {
        this.latitude = latitude;
    }

    public double getLongitude() {
        return longitude;
    }

    public void setLongitude(double longitude) {
        this.longitude = longitude;
    }
}