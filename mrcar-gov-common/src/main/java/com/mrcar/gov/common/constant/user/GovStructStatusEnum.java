package com.mrcar.gov.common.constant.user;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.data.mongodb.core.aggregation.ArrayOperators;

import java.util.Objects;

/**
 * 部门状态枚举类
 * <AUTHOR>
 * @date 2024/11/7 15:01
 */
@AllArgsConstructor
@Getter
public enum GovStructStatusEnum{

    // 启用
    NORMAL(1, "启用"),

    // 停用
    STOP(0, "停用");

    private final Integer code;

    private final String desc;

    // 根据 code 获取枚举实例
    public static GovStructStatusEnum getByCode(int code) {
        for (GovStructStatusEnum status : values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        return null;
    }

    // 根据 code 获取文本描述
    public static String getDesByCode(Integer code) {
        for (GovStructStatusEnum status : values()) {
            if (Objects.equals(status.getCode(), code)) {
                return status.getDesc();
            }
        }
       return "";
    }
}
