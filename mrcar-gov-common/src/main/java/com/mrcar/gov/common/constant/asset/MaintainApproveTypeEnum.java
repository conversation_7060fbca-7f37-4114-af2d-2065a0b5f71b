package com.mrcar.gov.common.constant.asset;

/**
 * 维保报价审批类型
 * <AUTHOR>
 * @date 2025/1/21 17:28
 */
public enum MaintainApproveTypeEnum {
    //审批类型 0：报价审批 1：增项审批 3:维保审批
    QUOTE_APPROVE(0, "报价审批"),
    ADD_APPROVE(1, "增项审批"),
    MAINTAIN_APPROVE(3, "维保审批");

    private Integer code;
    private String name;

    MaintainApproveTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
