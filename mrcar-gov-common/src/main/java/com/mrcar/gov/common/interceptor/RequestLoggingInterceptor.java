package com.mrcar.gov.common.interceptor;

import com.alibaba.fastjson.JSON;
import com.mrcar.gov.common.security.LoginUser;
import com.mrcar.gov.common.security.SecurityUtil;
import com.mrcar.gov.common.util.IpUtil;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.util.ContentCachingRequestWrapper;

import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class RequestLoggingInterceptor implements HandlerInterceptor {

    private static final Logger logger = LoggerFactory.getLogger(RequestLoggingInterceptor.class);

    private static final String REQUEST_LOGGING_PREFIX = "[Request Logging][%s]";

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // path
        String path = request.getRequestURI();
        // method
        HttpMethod method = HttpMethod.resolve(request.getMethod());
        // session
        LoginUser user = SecurityUtil.currLoginUser();
        String identifier = user == null ? IpUtil.getIpAddress(request) : user.getLoginName();

        if (method == HttpMethod.GET) {
            StringBuilder sb = new StringBuilder(String.format(REQUEST_LOGGING_PREFIX, identifier));
            sb.append("[").append(path).append("?").append(parseRequestParams(request)).append("]");
            logger.info(sb.toString());
        } else if (method == HttpMethod.POST) {
            String contentType = request.getContentType();
            StringBuilder sb = new StringBuilder(String.format(REQUEST_LOGGING_PREFIX, identifier));
            sb.append("[").append(path).append("]");
            if (StringUtils.isNotBlank(contentType)) {
                MediaType mediaType = MediaType.parseMediaType(contentType);
                if (Objects.equals(mediaType, MediaType.APPLICATION_FORM_URLENCODED)) {
                    // application/x-www-form-urlencoded
                    sb.append("[").append(parseRequestParams(request)).append("]");
                } else if (Objects.equals(mediaType, MediaType.APPLICATION_JSON)) {
                    // application/json
                    if (request instanceof ContentCachingRequestWrapper) {
                        ContentCachingRequestWrapper wrapper = (ContentCachingRequestWrapper) request;
                        ByteArrayOutputStream outStream = new ByteArrayOutputStream();
                        IOUtils.copy(wrapper.getInputStream(), outStream);
                        sb.append("[").append(new String(outStream.toByteArray(), StandardCharsets.UTF_8)).append("]");
                    }
                }
            }
            logger.info(sb.toString());
        }
        return true;
    }

    private String parseRequestParams(HttpServletRequest request) {
        Map<String, String[]> parameterMap = request.getParameterMap();
        List<String> parameters = new ArrayList<>(parameterMap.size());
        for (Map.Entry<String, String[]> entry : parameterMap.entrySet()) {
            String key = entry.getKey();
            String[] values = entry.getValue();
            for (String value : values) {
                parameters.add(key + "=" + value);
            }
        }
        return String.join("&", parameters);
    }

}
