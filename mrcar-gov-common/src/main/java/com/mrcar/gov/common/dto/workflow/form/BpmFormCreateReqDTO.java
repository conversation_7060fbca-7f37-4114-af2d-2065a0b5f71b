package com.mrcar.gov.common.dto.workflow.form;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 动态表单创建 Request VO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BpmFormCreateReqDTO extends BpmFormBaseDTO {

    /**
     * 表单的配置，JSON 字符串
     */
    @NotNull(message = "表单的配置不能为空")
    private String conf;

    /**
     * 表单项的数组，JSON 字符串的数组
     */
    @NotNull(message = "表单项的数组不能为空")
    private List<String> fields;

}
