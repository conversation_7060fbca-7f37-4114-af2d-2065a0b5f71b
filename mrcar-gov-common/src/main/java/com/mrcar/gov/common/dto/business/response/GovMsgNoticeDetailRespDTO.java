package com.mrcar.gov.common.dto.business.response;

import com.mrcar.gov.common.constant.business.GovMsgModuleEnum;
import com.mrcar.gov.common.constant.business.GovMsgNoticeStatusEnum;
import com.mrcar.gov.common.dto.business.request.GovMsgFileDTO;
import com.mrcar.gov.common.dto.user.resp.GovStructSimpleDTO;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/27 18:48
 */
@Data
public class GovMsgNoticeDetailRespDTO {



    /**
     * id
     */
    private Integer id;

    /**
     * 企业id
     */
    private Integer companyId;

    /**
     * 通知分类 1公文公告 2系统通知
     */
    private Integer msgModule;
    /**
     * 通知分类描述
     */
    private String msgModuleDesc;

    /**
     * 公告通知编号
     */
    private String noticeNo;

    /**
     * 公告通知标题
     */
    private String noticeName;

    /**
     * 发布人编码
     */
    private String publisherCode;

    /**
     * 发布单位编码
     */
    private String publishDeptCode;
    /**
     * 发布单位名称
     */
    private String publishDeptName;

    /**
     * 发布时间
     */
    private Date publishTime;

    /**
     * 发布状态0未发布；1已发布
     */
    private Integer publishStatus;

    /**
     * 内容
     */
    private String noticeContent;

    /**
     * 状态 0:删除 1:正常
     */
    private Integer noticeStatus;
    /**
     * 状态 描述
     */
    private String noticeStatusDesc;

    /**
     * @ignore
     * 推送部门列表
     */
    private List<String> pushDeptCodeList;

    /**
     * 推送部门模式 1:全部 2指定部门
     */
    private Integer pushDeptModel;
    /**
     * 推送部门模式描述
     */
    private Integer pushDeptModelDesc;

    /**
     * 部门树信息
     */
    private List<GovStructSimpleDTO> structList;


    /**
     * 附件地址列表
     */
    private List<GovMsgFileDTO> attachmentUrlList;

    public void setMsgModule(Integer msgModule) {
        this.msgModule = msgModule;
        this.msgModuleDesc = GovMsgModuleEnum.getDescByCode(msgModule);
    }

    public void setNoticeStatus(Integer noticeStatus){
        this.noticeStatus = noticeStatus;
        this.noticeStatusDesc = GovMsgNoticeStatusEnum.getDescByCode(noticeStatus);
    }


}
