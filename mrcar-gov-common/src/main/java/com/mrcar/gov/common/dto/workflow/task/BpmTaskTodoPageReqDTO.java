package com.mrcar.gov.common.dto.workflow.task;

import com.mrcar.gov.common.dto.workflow.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 管理后台 - 流程任务的 TODO 待办的分页 Request VO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BpmTaskTodoPageReqDTO extends PageParam {

    /**
     * 流程任务名
     * 示例值: 用车
     */
    private String name;

    /**
     * 开始的创建时间
     */
    private String beginCreateTime;

    /**
     * 结束的创建时间
     */
    private String endCreateTime;

    /**
     * 任务ID
     */
    private String id;

    /**
     * 办理状态
     * 隐藏
     */
    private Byte result;

    /**
     * 业务类型
     */
    private Byte businessType;

    /**
     * 流程分类
     */
    private String category;

    /**
     * 发起人
     */
    private String startUserNickname;

    /**
     * 发起人部门Id
     */
    private Integer startDeptId;

}
