package com.mrcar.gov.common.dto.workflow.task;

import com.mrcar.gov.common.dto.workflow.BaseDTO;
import lombok.Data;
import javax.validation.constraints.NotBlank;

/**
 * 通过流程任务的 Request VO
 */
@Data
public class BpmTaskApproveReqDTO extends BaseDTO {

    /**
     * 任务编号
     */
    @NotBlank(message = "任务编号不能为空")
    private String id;

    /**
     * 审批意见
     * 产品去掉了，通过原因非必填
     */
    private String reason;

    /**
     * 批量审批时，选择了权限按钮，则传 true
     */
    private Boolean batchApprovalAllSelected;
}
