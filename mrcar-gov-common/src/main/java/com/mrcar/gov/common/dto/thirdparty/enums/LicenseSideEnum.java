package com.mrcar.gov.common.dto.thirdparty.enums;

public enum LicenseSideEnum {

    FACE(1, "face"),
    BACK(2, "back");

    private int code;
    private String side;

    LicenseSideEnum(int code, String side) {
        this.code = code;
        this.side = side;
    }

    public static LicenseSideEnum getEnumByCode(int code) {
        for (LicenseSideEnum sideEnum : values()) {
            if (sideEnum.code == code) {
                return sideEnum;
            }
        }
        throw new IllegalArgumentException("code is invalid");
    }

    public static LicenseSideEnum getEnumByName(String name) {
        for (LicenseSideEnum sideEnum : values()) {
            if (sideEnum.side.equals(name)) {
                return sideEnum;
            }
        }
        throw new IllegalArgumentException("name is invalid");
    }

    public String getSide() {
        return side;
    }
}
