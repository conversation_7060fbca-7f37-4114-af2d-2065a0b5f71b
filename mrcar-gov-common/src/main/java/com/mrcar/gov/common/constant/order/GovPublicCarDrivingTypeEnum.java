package com.mrcar.gov.common.constant.order;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum GovPublicCarDrivingTypeEnum {

    // 订单类型：0-自家 1-驾驶员
    SELF_DRIVE(1, "自驾"),
    // 社会租赁 为 供应商驾驶员
    DRIVER_DRIVE(2, "驾驶员"),
    // 只用于社会租赁
    UNIT_DRIVE(3, "单位驾驶员"),
    ;

    private final Integer code;
    private final String name;

    public static String getName(Integer code) {
        return Arrays.stream(values()).filter(e -> e.getCode().equals(code)).findFirst().map(GovPublicCarDrivingTypeEnum::getName).orElse(null);
    }
}