package com.mrcar.gov.common.util;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;

public class LocalDateTimeUtils {

    public static LocalDateTime DEFAULT_DATE = LocalDateTime.of(1970, 1, 1, 0, 0, 0);

    public static Long DAY_SECONDS = 86400L;

    public static Long DAY_MILLISECONDS = 86400000L;
    public static Date localDateToDate(LocalDate localDate) {
        // LocalDate 转换为 Date
        return Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
    }

    public static Date localDateTimeToDate(LocalDateTime localDateTime) {
        // LocalDate 转换为 Date
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    public static LocalDateTime dateToLocalDateTime(Date date) {
        return Instant.ofEpochMilli(date.getTime()).atZone(ZoneId.systemDefault()).toLocalDateTime();
    }

    public static LocalDate dateToLocalDate(Date date) {
        return Instant.ofEpochMilli(date.getTime()).atZone(ZoneId.systemDefault()).toLocalDate();
    }

    public static boolean isDefaultDate(Date date) {
        return localDateTimeToDate(DEFAULT_DATE).getTime() == date.getTime();
    }

    /**
     * 两个日期 相差天数
     * @param date1
     * @param date2
     * @return
     */
    public static int betweenDay(Date date1, Date date2) {
        return (int)(Math.abs(date1.getTime() - date2.getTime())/DAY_MILLISECONDS);
    }


    public static Date minusDay(Date date, int day) {
        return localDateTimeToDate(dateToLocalDateTime(date).minusDays(day));
    }


}
