package com.mrcar.gov.common.constant.asset;

import lombok.Getter;

import java.util.Objects;
import java.util.stream.Stream;

/**
 * Author:  wangM
 * Date:  2025/1/11 13:14
 */
@Getter
public enum GovWorkBaseStatusEnum {
    DEFAULT(0, "未知"),

    ENABLED(1, "启用"),

    DISABLED(4, "禁用");

    private final Integer code;

    private final String desc;


    GovWorkBaseStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    //根据code获取desc
    public static String getDescByCode(Integer code) {
        for (GovWorkBaseStatusEnum value : GovWorkBaseStatusEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getDesc();
            }
        }
        return null;
    }

    public static GovWorkBaseStatusEnum getName(String name) {
        return (GovWorkBaseStatusEnum) Stream.of(values()).filter((e) -> {
            return Objects.equals(e.getDesc(), name);
        }).findFirst().orElse(DEFAULT);
    }
}
