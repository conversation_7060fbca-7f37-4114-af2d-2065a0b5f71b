package com.mrcar.gov.common.dto.workflow.task;

import com.mrcar.gov.common.dto.workflow.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import javax.validation.constraints.NotNull;

/**
 * 管理后台 - 流程任务的 PC 端分页 Request VO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BpmTaskPcPageReqDTO extends PageParam {

    /**
     * 办理类型（1我发起的流程 2我处理的流程）
     * 示例值: 1
     * 必填
     */
    @NotNull(message = "办理类型不能为空")
    private Byte doType;

}
