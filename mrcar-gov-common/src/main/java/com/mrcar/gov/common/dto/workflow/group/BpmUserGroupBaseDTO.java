package com.mrcar.gov.common.dto.workflow.group;

import com.mrcar.gov.common.dto.workflow.BaseDTO;
import lombok.Data;
import javax.validation.constraints.NotNull;

/**
 * 用户组 Base VO，提供给添加、修改、详细的子 VO 使用
 */
@Data
public class BpmUserGroupBaseDTO extends BaseDTO {

    /**
     * 组名
     * 示例值: 用车
     */
    @NotNull(message = "组名不能为空")
    private String name;

    /**
     * 描述
     * 示例值: hxc
     */
    @NotNull(message = "描述不能为空")
    private String description;

    /**
     * 成员编号数组
     * 示例值: 1,2,3
     */
    @NotNull(message = "成员编号不能为空")
    private String memberUserIds;

    /**
     * 成员名称数组
     * 示例值: 张三,李四,王五
     */
    @NotNull(message = "成员名称不能为空")
    private String memberUserNames;

    /**
     * 状态
     * 示例值: 1
     */
    @NotNull(message = "状态不能为空")
    private Byte status;

}
