package com.mrcar.gov.common.dto.asset;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.mrcar.gov.common.constant.asset.OilConsumptionEnum;
import com.mrcar.gov.common.dto.workflow.enums.BpmProcessInstanceResultEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR> dongxiya 2023/4/14 15:03
 */
@Data
public class OilConsumptionBaseDTO {
    private Integer id;
    @ApiModelProperty(value = "企业code")
    private String companyCode;
    @ApiModelProperty(value = "企业名称")
    private String companyName;
    /**
     * 车牌号
     */
    @ApiModelProperty(value = "车牌号")
    private String vehicleLicense;
    /**
     * 车架号
     */
    @ApiModelProperty(value = "车架号")
    private String vehicleVin;
    @ApiModelProperty(value = "车辆所在城市代码")
    private String belongCityCode;
    @ApiModelProperty(value = "所属城市名称")
    private String belongCityName;
    @ApiModelProperty(value = "车辆所属部门id")
    private Integer structId;
    @ApiModelProperty(value = "车辆所属部门id")
    private String structName;
    @ApiModelProperty(value = "状态1有效")
    private Integer status;
    @ApiModelProperty(value = "审批流ID（综合/Mr.Car）")
    private String approvalId;
    /**
     * 审批状态(1 待审批,2 审批通过,3 审批驳回,4 已撤回)
     */
    @ApiModelProperty(value = "审批状态(1 待审批,2 审批通过,3 审批驳回,4 已撤回)")
    private Integer approvalStatus;
    /**
     * 审批状态(1 待审批,2 审批通过,3 审批驳回,4 已撤回)
     */
    @ApiModelProperty(value = "审批状态(1 待审批,2 审批通过,3 审批驳回,4 已撤回)")
    private String approvalStatusValue;
    @ApiModelProperty(value = "撤回原因")
    private String cancelReason;
    @ApiModelProperty(value = "备注")
    private String remark;
    @ApiModelProperty(value = "创建人id")
    private Integer createId;
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    @ApiModelProperty(value = "创建人姓名")
    private String createName;
    @ApiModelProperty(value = "更新人id")
    private Integer updateId;
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
    @ApiModelProperty(value = "更新人姓名")
    private String updateName;

    public void setApprovalStatus(Integer approvalStatus) {
        this.approvalStatus = approvalStatus;
        if (approvalStatus != null) {
            this.approvalStatusValue =
                    OilConsumptionEnum.ApprovalStatusEnum.getValueByCode(Byte.valueOf(approvalStatus.toString()));
        }
    }
}
