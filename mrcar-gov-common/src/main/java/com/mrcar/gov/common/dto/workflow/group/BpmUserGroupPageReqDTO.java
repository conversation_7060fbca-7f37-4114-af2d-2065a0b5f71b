package com.mrcar.gov.common.dto.workflow.group;

import com.mrcar.gov.common.dto.workflow.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 管理后台 - 用户组分页 Request VO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BpmUserGroupPageReqDTO extends PageParam {

    /**
     * 组名
     * 示例值: 用车
     */
    private String name;

    /**
     * 状态
     * 示例值: 1
     */
    private Integer status;

    /**
     * 开始创建时间
     */
    private String beginCreateTime;

    /**
     * 结束创建时间
     */
    private String endCreateTime;

}
