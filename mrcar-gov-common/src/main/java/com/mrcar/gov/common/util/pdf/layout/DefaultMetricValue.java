package com.mrcar.gov.common.util.pdf.layout;

class DefaultMetricValue implements MetricValue {

    private final MetricType _type;
    private final float _value;

    DefaultMetricValue(MetricType type, float value) {
        _type = type;
        _value = value;
    }

    @Override
    public MetricType type() {
        return this._type;
    }

    @Override
    public float value() {
        return this._value;
    }

    @Override
    public String toString() {
        return "DefaultMetricValue{" +
                "_type=" + _type +
                ", _value=" + _value +
                '}';
    }
}
