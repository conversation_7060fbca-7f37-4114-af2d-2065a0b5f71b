package com.mrcar.gov.common.dto.business.request;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.mrcar.gov.common.dto.BaseDTO;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/12/31 15:15
 */
@Data
public class GovCourseReqDTO extends BaseDTO {

    /**
     * 主键id
     */
    private Integer id;

    /**
     * 教程类型 1：图文教程 2：视频教程
     */
    @NotNull(message = "教程类型不能为空")
    private Integer courseType;


    /**
     * 教程标题
     */
    @NotBlank(message = "教程标题不能为空")
    private String courseTitle;

    /**
     * 教程简介
     */
    @NotBlank(message = "教程简介不能为空")
    private String courseIntroduction;

    /**
     * 教程文件名称
     */
    @NotBlank(message = "教程文件名称不能为空")
    private String fileName;

    /**
     * 教程文件url
     */
    @NotBlank(message = "教程文件url不能为空")
    private String courseUrl;

    /**
     * 创建人
     */
    private String createName;

    /**
     * 修改人
     */
    private String updateName;


    /**
     * 是否删除 1：正常 0：删除
     */
    private Integer valid;
}
