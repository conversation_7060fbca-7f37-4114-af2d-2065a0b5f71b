package com.mrcar.gov.common.dto.device.resp;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class TravelBackpointDataExportRespDTO {

    /**
     * 返点时间
     */
    @ExcelProperty(value = {"上报时间"}, index = 0)
    @ColumnWidth(25)
    private Date createDate;

    /**
     * 经纬度
     */
    @ExcelProperty(value = {"经纬度"}, index = 1)
    @ColumnWidth(25)
    private String point;

    /**
     * 起点地址名称
     */
    @ExcelProperty(value = {"上报位置"}, index = 2)
    @ColumnWidth(50)
    private String locationName;

    /**
     * 停车点
     */
    @ExcelProperty(value = {"停车点"}, index = 3)
    @ColumnWidth(25)
    private String stopPointDesc;

    /**
     * 停车时长
     */
    @ExcelProperty(value = {"停车时长"}, index = 4)
    @ColumnWidth(25)
    private String stopDurationDetail;

    /**
     * 方向角描述
     */
    @ExcelProperty(value = {"方向"}, index = 5)
    @ColumnWidth(25)
    private String directionMsg;

    /**
     * 方向角描述
     */
    @ExcelProperty(value = {"时速(km/h)"}, index = 6)
    @ColumnWidth(25)
    private String speed;

}
