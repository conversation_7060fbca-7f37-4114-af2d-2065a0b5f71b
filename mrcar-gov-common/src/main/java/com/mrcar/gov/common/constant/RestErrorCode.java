package com.mrcar.gov.common.constant;

import com.izu.framework.resp.InfoCode;
import com.izu.framework.web.rest.response.ResultMessage;

import java.text.MessageFormat;

/**
 * 返回错误码定义.
 *
 * <AUTHOR>
 * @date 2024-11-07
 */
public interface RestErrorCode {


    /**
     * 扩展InfoCode, 支持渲染字符串功能
     */
    static InfoCode render(InfoCode code, Object... args) {
        return InfoCode.build(code.getStatus(), MessageFormat.format(code.getMsg(), args));
    }

    // ------------ 登录逻辑 -------------

    InfoCode LOGIN_ACCOUNT_LOCKED = InfoCode.build(1001, "您输入密码错误次数超过5次，请{0}分钟后再试！");

    InfoCode LOGIN_PHONE_NOT_EXIST = InfoCode.build(1002, "不存在有效的手机号码，请联系管理员添加！");

    InfoCode LOGIN_PASSWORD_ERROR = InfoCode.build(1003, "密码错误！");


    InfoCode LOGIN_VERIFY_CODE_ERROR = InfoCode.build(1004, "短信验证码不正确");

    InfoCode LOGIN_VERIFY_CODE_SEND_TO_MANY = InfoCode.build(1005, "短信验证码发送次数过多，请明天再试！");

    InfoCode LOGIN_UNABLE_OBTAIN_VERIFY_CODE_AGAIN = InfoCode.build(1006, "请60秒之后再次获取！");

    InfoCode LOGIN_VERIFY_CODE_INVALID = InfoCode.build(1007, "短信验证码已经失效");

    InfoCode LOGIN_CAPTCHA_INVALID = InfoCode.build(1008, "图形验证码已失效");

    InfoCode LOGIN_MINIPROGRAM_ERROR = InfoCode.build(1009, "小程序登录失败, 请稍后重试");

    InfoCode LOGIN_PHONE_UNAVAILABLE = InfoCode.build(1010, "手机号已停用");

    InfoCode PASSWORD_LENGTH_ERROR = InfoCode.build(1011, "密码长度不符合要求，请输入8-12位字符！");

    InfoCode PASSWORD_FORMAT_ERROR = InfoCode.build(1011, "密码格式不符合要求，请包含大小写字母、数字、特殊字符（允许输入 + - * _ % # @）！");
    // 用户所在供应商已经停用，不允许登录
    InfoCode USER_SUPPLIER_LOCKED = InfoCode.build(1012, "用户所属机构已经停用，不允许登录");
    /** 设备相关 1500-2000 start **/
    InfoCode GPS_MANUFACT_NOT_EXIST = InfoCode.build(1500,"设备厂商不存在");
    InfoCode GPS_MODEL_NOT_EXIST =  InfoCode.build(1501,"设备型号不存在");
    InfoCode GPS_MODEL_MANUFACT_NOT_MATCH =  InfoCode.build(1502,"设备型号与厂商不对应");
    InfoCode GPS_DEVICE_NO_ERROR  =  InfoCode.build(1503,"设备号不合法");
    InfoCode GPS_SIM_NO_ERROR  =  InfoCode.build(1504,"SIM卡号不合法");
    InfoCode GPS_DEVICE_NO_EXIST  =  InfoCode.build(1505,"设备号已存在");
    InfoCode GPS_SIM_NO_EXIST  =  InfoCode.build(1507,"SIM卡号已存在");
    InfoCode GPS_VEHICLE_RELATION_DEVICE_TYPE_EXIST  =  InfoCode.build(1507,"已存在同类型的绑定记录");

    InfoCode GPS_DEVICE_NOT_FIND  =  InfoCode.build(1508,"未找到设备记录");
    InfoCode GPS_VEHICLE_RELATION_NOT_FIND  =  InfoCode.build(1509,"未找到设备记录");
    InfoCode GPS_DEVICE_IS_BIND = InfoCode.build(1510,"设备处于绑定状态，请先解绑");
    InfoCode GPS_VEHICLE_NOT_FIND = InfoCode.build(1511,"未找到车辆");
    InfoCode GPS_DEVICE_IMPORT_FILE_ERROR = InfoCode.build(1512,"导入文件格式错误");
    InfoCode GPS_VEHICLE_INFO_NOT_EXIST = InfoCode.build(1513,"车辆不存在");
    InfoCode GPS_VEHICLE_PERMISS_NOT_MATCH = InfoCode.build(1514,"无该车辆数据权限");
    /** 设备相关 1500-2000 end **/


    /** 用户相关 2000-2500* start*/
    InfoCode GOV_USER_NOT_EXIST = InfoCode.build(2001, "用户不存在");


    InfoCode DATA_PERM_ERROR = InfoCode.build(2002, "数据权限错误");
    // 手机号已被他人使用
    InfoCode GOV_USER_PHONE_USED = InfoCode.build(2003, "手机号已被他人使用");
    InfoCode GOV_USER_EMAIL_USED = InfoCode.build(2004, "邮箱已被他人使用");
    //  至少要有一个企业管理员
    InfoCode GOV_USER_COMPANY_ADMIN_LESS_THAN_ONE = InfoCode.build(2005, "至少要有一个企业管理员");

    // 身份证已被其他司机使用。目前只有司机才需要录入身份证
    InfoCode GOV_USER_ID_CARD_USED = InfoCode.build(2006, "身份证已被使用");


    // 手机号与机构用户重复
    InfoCode PHONE_REPEAT_WITH_ORG_USER = InfoCode.build(2009, "手机号与机构用户重复");
    // 操作用户信息获取所失败，请稍后重试
    InfoCode OPERATE_USER_INFO_OBTAIN_LOCK_FAIL = InfoCode.build(2010, "操作用户信息获取锁失败，请稍后重试");
    // 当前登录人员无员工导入权限
    InfoCode NO_EMPLOYEE_IMPORT_PERMISSION = InfoCode.build(2011, "当前登录人员无员工导入权限");
    // 当前登录人员无员工导入权限，禁止模板下载
    InfoCode NO_EMPLOYEE_IMPORT_PERMISSION_DOWNLOAD_TEMPLATE = InfoCode.build(2012, "当前登录人员无员工导入权限，禁止下载模板");
    // 该管理员已有其它客户下的生效账号
    InfoCode ADMIN_HAS_OTHER_CUSTOMER_EFFECTIVE_ACCOUNT = InfoCode.build(2013, "该管理员已有其它客户下的生效账号，请核实后处理");
    // 客户企业不存在
    InfoCode CUSTOMER_NOT_EXIST = InfoCode.build(2014, "客户企业不存在");
    //账号已到期
    InfoCode ACCOUNT_EXPIRED = InfoCode.build(2015, "账号已到期");
    // 手机号与非机构用户重复
    InfoCode PHONE_REPEAT_WITH_NON_ORG_USER = InfoCode.build(2016, "手机号与非当前机构用户重复");

    // 不允许修改用户机构
    InfoCode NOT_ALLOW_CHANGE_ORG = InfoCode.build(2017, "不允许修改用户所在机构");

    // 机构负责人不允许修改所在机构
    InfoCode NOT_ALLOW_CHANGE_ORG_PRINCIPAL = InfoCode.build(2018, "机构负责人不允许修改所在机构");




    InfoCode FILE_UPLOAD_ERROR = InfoCode.build(2403, "文件上传失败，请稍后重试！");

    //文件格式错误
    InfoCode FILE_FORMAT_ERROR = InfoCode.build(2404, "文件格式错误");

    //管理范围重复
    InfoCode MANAGE_RANGE_REPEAT = InfoCode.build(2405, "管理范围重复");

    // excel转换错误
    InfoCode EXCEL_CONVERT_ERROR = InfoCode.build(2406, "excel转换错误");

    InfoCode EXCEL_DOWNLOAD_ERROR = InfoCode.build(2407, "excel下载失败");
    // excel中无数据
    InfoCode EXCEL_DATA_EMPTY = InfoCode.build(2408, "excel中无数据");
    InfoCode EXCEL_NIT_EXIST = InfoCode.build(2409, "文件不存在");
    /** 用户相关 2000-2500* end*/

    /** 车辆相关 2500-3000 start**/
    // 车辆已被占用
    InfoCode VEHICLE_ALREADY_OCCUPIED = InfoCode.build(2500, "车辆已被占用");
    // 车辆上存在未结束订单
    InfoCode VEHICLE_ORDER_NOT_END = InfoCode.build(2501, "车辆上存在未结束订单");
    // 车辆不在围栏内
    InfoCode VEHICLE_NOT_IN_FENCE = InfoCode.build(2502, "车辆不在围栏内");


    /** 车辆相关 2500-3000 end**/

    /** OCR相关 3000-4000 start **/
    InfoCode STRATEGY_ERROR = InfoCode.build(3000, "策略获取失败");

    InfoCode INVALID_DOCUMENT_TYPE = InfoCode.build(3001, "证件类型无效");

    InfoCode OCR_RECOGNITION_FAILED = InfoCode.build(3002, "ocr识别失败");

    InfoCode OCR_PICTURE_CONVERSION_ANOMALY= InfoCode.build(3003, "ocr 图片转换异常");

    InfoCode POLICY_IDENTIFICATION_FAILURE = InfoCode.build(3004, "保单识别失败");
    /** OCR相关 3000-4000 end **/

    /** 订单相关 4000-5000 start **/
    // 无对应操作
    InfoCode ORDER_OPERATION_NOT_EXIST = InfoCode.build(4000, "不存在对应的订单操作");



    InfoCode GOV_PUBLIC_CAR_ORDER_NOT_EXIST = InfoCode.build(4001, "订单不存在");
    InfoCode ORDER_STATUS_ERROR_NOT_SUPPORT_OPT = InfoCode.build(4002, "订单状态错误，不支持此操作");
    InfoCode ORDER_STATUS_HAS_CHANGE = InfoCode.build(4003, "订单状态已改变，请刷新后重试");
    InfoCode QUERY_ORDER_NOT_EXISTS = InfoCode.build(4004, "查询订单不存在");

    // 订单无对应审批单
    InfoCode ORDER_NO_APPROVAL_FORM = InfoCode.build(4005, "订单无对应审批单");
    // 当前订单不支持核实操作
    InfoCode ORDER_NOT_SUPPORT_VERIFY = InfoCode.build(4006, "当前订单不支持核实操作");



    /** 订单相关 4000-5000 end **/

    /** 保单相关 5000-6000 start **/
    InfoCode WARRANTY_NOT_EXIST = InfoCode.build(5000, "保单不存在");
    InfoCode ONLY_WARRANTY = InfoCode.build(5001, "同一车辆，保司，保单号不得重复");
    InfoCode WARRANTY_TIME = InfoCode.build(5002, "保险时段不得有交集");

    /** 保单相关 5000-6000 end **/

    /** 加油相关 6000-7000 end **/
    InfoCode REFUEL_RECORD_NOT_EXIST_ERROR = InfoCode.build(60001, "加油记录不存在");
    InfoCode REFUEL_RECORD_EXIST_ERROR = InfoCode.build(60002, "已存在加油时间相同的记录");
    InfoCode REFUEL_OPERATION_FAILED_ERROR = InfoCode.build(60003, "操作失败，请稍后重试");
    InfoCode REFUEL_DASH_BOARD_MILEAGE_ERROR = InfoCode.build(60003, "输入的仪表盘里程不能小于等于已存在的里程数");

    /**
     * 车架号验证未通过提示
     */
    InfoCode CHECK_FRAME_NUMBER_FAIL_MSG = InfoCode.build(60005, "验真未通过，建议再次确认");



    /** 消息通知相关 7000-8000 start **/

    // 公告已发布，请不要重复发布
    InfoCode GOV_MSG_NOTICE_PUBLISHED = InfoCode.build(7000, "公告已发布，请不要重复发布");


    /** 消息通知相关 7000-8000 end **/



}
