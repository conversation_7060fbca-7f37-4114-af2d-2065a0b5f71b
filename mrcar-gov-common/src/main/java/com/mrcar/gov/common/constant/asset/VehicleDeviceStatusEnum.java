package com.mrcar.gov.common.constant.asset;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/11/21 15:50
 * 车辆设备状态
 */
@AllArgsConstructor
@Getter
public enum VehicleDeviceStatusEnum {
    // 在线
    ONLINE(1, "在线"),
    // 离线
    OFFLINE(2, "离线"),
    // 未绑定
    UNBIND(3, "未绑定"),
    ;

    private Integer code;

    private String desc;

    public static String getDeviceStatusDesc(Integer code) {
        for (VehicleDeviceStatusEnum statusEnum : VehicleDeviceStatusEnum.values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum.getDesc();
            }
        }
        return "";
    }




}
