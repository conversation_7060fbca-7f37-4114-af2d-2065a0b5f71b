package com.mrcar.gov.common.constant.asset;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 燃油标号
 * <AUTHOR>
 * @date 2024/11/14 17:52
 */
@Getter
public enum FuelNoEnum {
    //92 93 95 92/93
    ZERO_NINE("92", "92"),
    ONE_ZERO("93", "93"),
    ONE_ONE("95", "95");
    private String code;
    private String desc;
    FuelNoEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    public static List<String> getDescList() {
        return Arrays.stream(FuelNoEnum.values()).map(FuelNoEnum::getDesc).collect(Collectors.toList());
    }

    public static String getCodeByDesc(String desc) {
        for (FuelNoEnum value : FuelNoEnum.values()) {
            if (Objects.equals(value.getDesc(), desc)) {
                return value.getCode();
            }
        }
        return null;
    }
}
