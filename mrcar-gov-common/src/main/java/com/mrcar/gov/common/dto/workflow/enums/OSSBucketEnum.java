package com.mrcar.gov.common.dto.workflow.enums;

/**
 * <AUTHOR>
 * @date 2019-05-05 16:56
 */
public enum OSSBucketEnum {

    IMG(1),
    FILE(2),
    OBJECT(3),
    IMG_PRIVATE(4),
    FILE_PRIVATE(5),
    OBJECT_PRIVATE(6),
    TMP_IMG(7),
    REFERER_FILE_PRIVATE(8),
    VIDEO(9),
    ;

    private int code;


    OSSBucketEnum(int code) {
        this.code = code;
    }


    public static OSSBucketEnum getEnumByCode(int code){
        for (OSSBucketEnum ossBucketEnum : values()){
            if (ossBucketEnum.code == code) {
                return ossBucketEnum;
            }
        }
        return FILE;
    }

    public int getCode() {
        return code;
    }
}
