package com.mrcar.gov.common.util.pdf.api;

import com.mrcar.gov.common.util.pdf.layout.MetricType;

public class MetricBuilder {

    float value;
    MetricType type;

    private MetricBuilder() { }

    private MetricBuilder(float value, MetricType type) {
        this.value = value;
        this.type = type;
    }

    public static MetricBuilder fixed(float value) {
        return new MetricBuilder(value, MetricType.ABSOLUTE);
    }

    public static MetricBuilder percentage(float value) {
        return new MetricBuilder(value, MetricType.PERCENTAGE);
    }

    public static MetricBuilder adaptive() {
        return new MetricBuilder(-1, MetricType.ADAPTIVE);
    }

    public static MetricBuilder full() {
        return new MetricBuilder(1.0f, MetricType.PERCENTAGE);
    }

}
