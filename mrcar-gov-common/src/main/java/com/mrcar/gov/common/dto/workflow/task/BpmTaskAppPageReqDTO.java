package com.mrcar.gov.common.dto.workflow.task;

import com.mrcar.gov.common.dto.workflow.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * BpmTaskAppPage 请求 DTO
 * 包含了分页查询任务的相关参数
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BpmTaskAppPageReqDTO extends PageParam {

    /**
     * 办理状态
     */
    private Byte result;

    /**
     * 办理状态，多值
     */
    private List<Byte> results;

    /**
     * 业务类型
     */
    private Byte businessType;

    /**
     * 业务类型，多值
     */
    private List<Byte> businessTypes;

    /**
     * 流程分类
     */
    private String category;

    /**
     * 发起人或流程名称
     */
    private String keyWord;

    /**
     * 办理类型（1待办理 2已办理 3我发起的 4 抄送给我的）
     */
    @NotNull(message = "办理类型不能为空")
    private Byte doType;

    /**
     * 是否批量审批
     */
    private Boolean batchApproval;
}
