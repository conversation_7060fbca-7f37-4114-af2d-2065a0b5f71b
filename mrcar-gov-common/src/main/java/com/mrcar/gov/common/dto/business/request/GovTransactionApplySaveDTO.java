package com.mrcar.gov.common.dto.business.request;
import com.mrcar.gov.common.dto.BaseDTO;
import com.mrcar.gov.common.dto.FileDTO;
import lombok.Data;
import javax.validation.constraints.NotBlank;
import java.util.Date;
import java.util.List;

/**
 * 车辆 配备，更新处置调拨 申请 保存 DTO
 * <AUTHOR>
 */
@Data
public class GovTransactionApplySaveDTO extends BaseDTO {
    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    private Integer id;

    /**
     * 业务申请编码
     */
    private String applyNo;

    /**
     * 申请类型 1 更新 2 配备 3 处理 4.调拨
     */
    @NotBlank(message = "申请类型不能为空")
    private Integer applyType;

    /**
     * 申请状态
     */
    private Integer applyStatus;

    /**
     * 申请时间
     */
    private Date applyDate;

    /**
     * 申请说明
     */
    private String applyRemark;

    /**
     * 申请单位编码
     */
    @NotBlank(message = "申请单位编码不能为空")
    private String applyStructCode;

    /**
     * 申请单位名称
     */
    private String applyStructName;

    /**
     * 车辆所属单位编码
     */
    @NotBlank(message = "申请单位编码不能为空")
    private String vehicleBelongDeptCode;

    /**
     * 车辆所属单位名称
     */
    private String vehicleBelongDeptName;

    /**
     * 审批id
     */
    private String approveId;

    /**
     * 所属公司id
     */
    private Integer companyId;

    /**
     * 创建人编码
     */
    private String createCode;

    /**
     * 创建人
     */
    private String createName;

    /**
     * 修改人
     */
    private String updateCode;

    /**
     * 修改人
     */
    private String updateName;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;


    /**
     * 车辆信息列表
     */
    private List<GovApplyVehicleReqDTO> govApplyVehicleDTOList;


    /**
     * 申请文件
     */
    private List<FileDTO>  applyFileList;

    /**
     * 批复文件
     */
    private List<FileDTO>  replyFileList;

    /**
     * 批复说明
     */
    private String replyRemark;

    @Data
    public static class GovApplyVehicleReqDTO {
        /**
         * 车辆拟购信息
         **/
        private GovVehiclePlanReqDTO govVehiclePlan;

        /**
         * 批复信息
         **/
        private GovApproveReplyReqDTO govApproveReply;

    }


}