package com.mrcar.gov.common.dto.business.request;

import com.mrcar.gov.common.dto.PageParamDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;

import java.util.Date;

/**
 * 我的消息列表请求参数
 *
 * <AUTHOR> on  2024/12/30 21:23
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MyMsgListReqDTO extends PageParamDTO {

    /**
     * 消息接收人编码
     * 前端无需传入，后端自动获取
     */
    private String receiverCode;


    /**
     * 消息模块 GovMsgModuleEnum
     * <pre>
     * 1 - 公文公告 (GOVERNMENT_NOTICE, 关联 GovMsgTypeEnum.NOTICE)
     * 2 - 系统消息 (SYSTEM_MESSAGE, 关联 GovMsgTypeEnum.NOTICE)
     * 3 - 用车通知 (VEHICLE_NOTICE, 关联 GovMsgTypeEnum.TEMPLATE_MSG)
     * 4 - 审批通知 (APPROVAL_NOTICE, 关联 GovMsgTypeEnum.TEMPLATE_MSG)
     * 5 - 监控报警 (MONITOR_ALARM, 关联 GovMsgTypeEnum.TEMPLATE_MSG)
     * 6 - 车务消息 (VEHICLE_SERVICE, 关联 GovMsgTypeEnum.TEMPLATE_MSG)
     * 7 - 维保通知 (MAINTENANCE_NOTICE, 关联 GovMsgTypeEnum.TEMPLATE_MSG)
     * </pre>
     */
    private Integer msgModule;


    /**
     * 读取状态 0未读 1已读
     */
    private Integer readStatus;


    /**
     * 消息发送时间-开始区间
     */
    private Date pushStartTimeBegin;

    /**
     * 消息发送时间-结束区间
     */
    private Date pushStartTimeEnd;


    /**
     * 排序字段 1-推送时间
     */
    private Integer orderByField;


    /**
     * 排序规则 1-正序 2-倒序
     */
    private Integer orderByRule;


    @Getter
    public enum OrderByField {
        /**
         * 推送时间
         */
        PUSH_TIME(1, "推送时间","createTime");

        private final Integer code;
        private final String desc;
        //排序字段名字
        private final String fieldName;

        OrderByField(Integer code, String desc, String fieldName) {
            this.code = code;
            this.desc = desc;
            this.fieldName = fieldName;
        }

        //获取排序字段
        public static String getFieldName(Integer code) {
            for (OrderByField value : OrderByField.values()) {
                if (value.code.equals(code)) {
                    return value.fieldName;
                }
            }
            return null;
        }
    }


    @Getter
    public enum OrderByRule {
        /**
         * 正序
         */
        ASC(1, "正序"),
        /**
         * 倒序
         */
        DESC(2, "倒序");

        private final Integer code;

        private final String desc;

        OrderByRule(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }

}
