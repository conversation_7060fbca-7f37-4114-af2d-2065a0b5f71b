package com.mrcar.gov.common.constant.asset;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/11/14 18:45
 */
@Getter
public enum GearboxTypeEnum {
    //变速箱类型
    MANUAL(1, "手动"),
    AUTOMATIC(2, "自动"),
    ;
    private Integer code;
    private String desc;

    GearboxTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    public static String getNameByCode(Integer code) {
        for (GearboxTypeEnum gearboxTypeEnum : GearboxTypeEnum.values()) {
            if (gearboxTypeEnum.getCode().equals(code)) {
                return gearboxTypeEnum.getDesc();
            }
        }
        return "";
    }


    public static List<String> getDescList() {
        return Arrays.stream(GearboxTypeEnum.values()).map(GearboxTypeEnum::getDesc).collect(Collectors.toList());
    }

    // 根据desc获取code
    public static Integer getCodeByDesc(String desc) {
        for (GearboxTypeEnum value : GearboxTypeEnum.values()) {
            if (Objects.equals(value.getDesc(), desc)) {
                return value.getCode();
            }
        }
        return null;
    }
}
