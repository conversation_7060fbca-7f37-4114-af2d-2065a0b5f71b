package com.mrcar.gov.common.constant;

/**
 * Redis常量
 */
public interface RedisKeyConstants {

    // ---------- 登录相关逻辑 ---------------

    String LOGIN_LOCKED_ACCOUNTS_KEY = "mrcar:gov:login:locked_accounts:%s";

    String LOGIN_FAILURE_COUNT_KEY = "mrcar:gov:login:error_count:%s";

    String LOGIN_USER_INFO_KEY = "mrcar:gov:login:user_info:%s";

    String LOGIN_VERIFY_CODE_TIMES_KEY = "mrcar:gov:login:verify_code_times:%s";

    String LOGIN_VERIFY_CODE_KEY = "mrcar:gov:login:verify_code:%s";

    String LOGIN_MODIFY_PASSWORD_VERIFY_CODE_KEY = "mrcar:gov:login:modify_password_verify_code:%s";

    String STRUCT_UPDATE_RESULT_KEY = "mrcar:gov:struct:update:result:%s";


    String GOV_USER_ADD_KEY = "gov:user:add:%s";
    String GOV_USER_MODIFY_KEY = "gov:user:modify:%s";

    String GOV_VEHICLE_KEY = "gov:vehicle:key:%s";

    String VEHICLE_QUICK_GET_KEY = "vehicle:quick:get:%s";

    String GOV_VEHICLE_FEE_KEY = "gov:vehicle:fee:%s";




}
