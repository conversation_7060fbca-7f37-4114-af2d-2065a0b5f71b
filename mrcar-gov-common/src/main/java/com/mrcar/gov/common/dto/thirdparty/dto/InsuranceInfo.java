package com.mrcar.gov.common.dto.thirdparty.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

/**
 * 保单DTO
 */
@Data
public class InsuranceInfo {


    /**
     * 被保人信息
     */
    @JSONField(name = "insuredList")
    private List<InsuredInfo> insuredList;
    /**
     * 保单类型 交强险/商业险
     */
    @JSONField(name = "vehiclePolicyType")
    private String vehiclePolicyType;
    /**
     * 保单名称
     */
    @JSONField(name = "policyName")
    private String policyName;
    /**
     * 保险公司名称
     */
    @JSONField(name = "companyName")
    private String companyName;
    /**
     * 交强险折扣
     */
    @JSONField(name = "tciDiscount")
    private String tciDiscount;
    /**
     * 商业险折扣
     */
    @JSONField(name = "policyEndDate")
    private String policyEndDate;
    /**
     * 保单号或者合同号
     */
    @JSONField(name = "policyNo")
    private String policyNo;
    /**
     * 保险起期
     */
    @JSONField(name = "policyStartDate")
    private String policyStartDate;
    /**
     * 保险期间
     */
    @JSONField(name = "coverYears")
    private String coverYears;
    /**
     * 总保费
     */
    @JSONField(name = "premium")
    private String premium;
    /**
     * 车船税
     */
    @JSONField(name = "taxPremium")
    private String taxPremium;
    /**
     * 车辆信息
     */
    @JSONField(name = "car")
    private CarInfo car;
    /**
     * 车主信息
     */
    @JSONField(name = "carOwner")
    private CarOwnerInfo carOwner;
    /**
     * 投保人信息
     */
    @JSONField(name = "insurer")
    private InsurerInfo insurer;
    /**
     * 商业险折扣
     */
    @JSONField(name = "vciDiscount")
    private String vciDiscount;
    /**
     * 投保确认时间
     */
    @JSONField(name = "policyConfirmDate")
    private String policyConfirmDate;
    /**
     * 机构名称
     */
    @JSONField(name = "department")
    private String department;
    /**
     * 签单日期
     */
    @JSONField(name = "signingDate")
    private String signingDate;
    /**
     * 险种对象
     */
    @JSONField(name = "productList")
    private List<ProductInfo> productList;
    /**
     * 业务类型
     */
    @JSONField(name = "policyType")
    private String policyType;
    /**
     * 投保单号
     */
    @JSONField(name = "insureNo")
    private String insureNo;
    /**
     * 车船税往年应缴
     */
    @JSONField(name = "arrearFee")
    private String arrearFee;
    /**
     * 车船税滞纳金
     */
    @JSONField(name = "lateFee")
    private String lateFee;
    /**
     * 车船税当年应缴
     */
    @JSONField(name = "payableFee")
    private String payableFee;
    /**
     * 其他险种集合
     */
    @JSONField(name = "otherPolicies")
    private List<InsuranceInfo> otherPolicies;

    @Data
    public static class CarInfo {
        /**
         * 车型名称
         */
        @JSONField(name = "modelName")
        private String modelName;
        /**
         * 发动机号
         */
        @JSONField(name = "engineNo")
        private String engineNo;
        /**
         * 车辆种类
         */
        @JSONField(name = "carKind")
        private String carKind;
        /**
         * 排量
         */
        @JSONField(name = "exhaustCapacity")
        private String exhaustCapacity;
        /**
         * 车辆使用性质
         */
        @JSONField(name = "carUseProperty")
        private String carUseProperty;
        /**
         * 核定载重
         */
        @JSONField(name = "loads")
        private String loads;
        /**
         * 功率
         */
        @JSONField(name = "power")
        private String power;
        /**
         * 过户日期
         */
        @JSONField(name = "transferDate")
        private String transferDate;
        /**
         * 车架号
         */
        @JSONField(name = "vinNo")
        private String vinNo;
        /**
         * 座位数
         */
        @JSONField(name = "seatCount")
        private String seatCount;
        /**
         * 车牌
         */
        @JSONField(name = "carLicense")
        private String carLicense;
        /**
         * 初登日期
         */
        @JSONField(name = "registerDate")
        private String registerDate;
        /**
         * 能源种类
         */
        @JSONField(name = "energyType")
        private String energyType;
    }

    @Data
    public static class CarOwnerInfo {
        /**
         * 车主
         */
        @JSONField(name = "name")
        private String name;
    }

    @Data
    public static class InsurerInfo {
        /**
         * 姓名
         */
        @JSONField(name = "name")
        private String name;
        /**
         * 年龄
         */
        @JSONField(name = "age")
        private String age;
        /**
         * 性别
         */
        @JSONField(name = "gender")
        private String gender;
        /**
         * 出生日期
         */
        @JSONField(name = "birth")
        private String birth;
        /**
         * 证件类型
         */
        @JSONField(name = "certiType")
        private String certiType;
        /**
         * 证件号码
         */
        @JSONField(name = "certiCode")
        private String certiCode;
        /**
         * 电话号码
         */
        @JSONField(name = "mobilePhone")
        private String mobilePhone;
        /**
         * 座机号码
         */
        @JSONField(name = "landlinePhone")
        private String landlinePhone;
        /**
         * 联系地址
         */
        @JSONField(name = "contactAddress")
        private String contactAddress;
        /**
         * 邮箱地址
         */
        @JSONField(name = "email")
        private String email;
    }

    @Data
    public static class InsuredInfo {
        /**
         * 性别
         */
        @JSONField(name = "gender")
        private String gender;
        /**
         * 电话号码
         */
        @JSONField(name = "mobilePhone")
        private String mobilePhone;
        /**
         * 姓名
         */
        @JSONField(name = "name")
        private String name;
        /**
         * 出生日期
         */
        @JSONField(name = "birth")
        private String birth;
        /**
         * 联系地址
         */
        @JSONField(name = "contactAddress")
        private String contactAddress;
        /**
         * 证件类型
         */
        @JSONField(name = "certiType")
        private String certiType;
        /**
         * 座机号码
         */
        @JSONField(name = "landlinePhone")
        private String landlinePhone;
        /**
         * 邮箱地址
         */
        @JSONField(name = "email")
        private String email;
        /**
         * 年龄
         */
        @JSONField(name = "age")
        private String age;
        /**
         * 客户号
         */
        @JSONField(name = "customerNo")
        private String customerNo;
        /**
         * 证件号码
         */
        @JSONField(name = "certiCode")
        private String certiCode;
    }

    @Data
    public static class ProductInfo {
        /**
         * 基本保额
         */
        @JSONField(name = "basicAmnt")
        private String basicAmnt;
        /**
         * 是否投保不计免赔
         */
        @JSONField(name = "isDeductible")
        private String isDeductible;
        /**
         * 乘客险投保份数
         */
        @JSONField(name = "quantity")
        private String quantity;
        /**
         * 乘客险单位保额
         */
        @JSONField(name = "singleAmount")
        private String singleAmount;
        /**
         * 险种名称
         */
        @JSONField(name = "name")
        private String name;
        /**
         * 玻璃类型
         */
        @JSONField(name = "glassType")
        private String glassType;
        /**
         * 保险费小计；包含不计免赔
         */
        @JSONField(name = "riskPremSum")
        private String riskPremSum;
        /**
         * 保费
         */
        @JSONField(name = "prem")
        private String prem;
        /**
         * 不计免赔率
         */
        @JSONField(name = "deductibleRate")
        private String deductibleRate;
        /**
         * 不计免赔额
         */
        @JSONField(name = "deductibleAmount")
        private String deductibleAmount;
    }


}
