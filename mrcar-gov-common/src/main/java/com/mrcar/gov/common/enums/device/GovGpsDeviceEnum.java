package com.mrcar.gov.common.enums.device;

import java.util.Arrays;

import lombok.Getter;
import lombok.Setter;

public class GovGpsDeviceEnum {

    public static enum DeviceTypeEnum{
        WIRED(1,"有线"),
        WIFI(2,"无线"),
        TBOX(4,"车机"),
        VIDEO(6,"视频");

        @Getter
        @Setter
        private Integer code;
        @Getter
        @Setter
        private String  msg;

        DeviceTypeEnum(Integer code, String msg){
            this.code = code;
            this.msg = msg;
        }

        public static DeviceTypeEnum getEnumByCode(Integer code) {
            return Arrays.stream(DeviceTypeEnum.values())
                    .filter(deviceTypeEnum -> deviceTypeEnum.code.equals(code))
                    .findFirst()
                    .orElse(null);
        }

        public static String getEnumMsgByCode(Integer code) {
            return Arrays.stream(DeviceTypeEnum.values())
                    .filter(deviceTypeEnum -> deviceTypeEnum.code.equals(code))
                    .map(DeviceTypeEnum::getMsg)
                    .findFirst()
                    .orElse("");
        }
        
        public static String getMsg(Integer code){
            if(code==null)
            {
                return null;
            }
            for(DeviceTypeEnum e: DeviceTypeEnum.values()){
                if(code == e.getCode()){
                    return e.getMsg();
                }
            }
            return null;
        }
    }

    public static enum DeviceImportErrorEnum{
        DEVICE_NO_REQUIRED("设备号必传"),
        SIM_NO_REQUIRED("SIM卡号必传"),
        MANUFACT_NAME_REQUIRED("设备厂商必传"),
        MODEL_NAME_REQUIRED("设备型号必传"),

        MANUFACT_NAME_ERROR("设备厂商信息有误"),
        MODEL_NAME_ERROR("设备型号信息有误"),
        VEHICLE_NOT_EXIST("车辆信息不存在"),
        SUPPLIER_ERROR("供应商信息有误"),
        ;

        @Getter
        @Setter
        private String  msg;

        DeviceImportErrorEnum(String msg){
            this.msg = msg;
        }

    }

    public static enum DeviceDeleteStatusEnum{
        NORMAL(0,"正常"),
        DELETED(1,"已删除");

        @Getter
        @Setter
        private Integer code;
        @Getter
        @Setter
        private String  msg;

        DeviceDeleteStatusEnum(Integer code, String msg){
            this.code = code;
            this.msg = msg;
        }

        public static DeviceDeleteStatusEnum getEnumByCode(Integer code) {
            return Arrays.stream(DeviceDeleteStatusEnum.values())
                    .filter(deviceDeleteStatusEnum -> deviceDeleteStatusEnum.code.equals(code))
                    .findFirst()
                    .orElse(null);
        }

        public static String getEnumMsgByCode(Integer code) {
            return Arrays.stream(DeviceDeleteStatusEnum.values())
                    .filter(deviceDeleteStatusEnum -> deviceDeleteStatusEnum.code.equals(code))
                    .map(DeviceDeleteStatusEnum::getMsg)
                    .findFirst()
                    .orElse("");
        }
    }


    public static enum DeviceBindStatusEnum{
        BIND(1,"已绑定"),
        UN_BIND(2,"未绑定");

        @Getter
        @Setter
        private Integer code;
        @Getter
        @Setter
        private String  msg;

        DeviceBindStatusEnum(Integer code, String msg){
            this.code = code;
            this.msg = msg;
        }

        public static DeviceBindStatusEnum getEnumByCode(Integer code) {
            return Arrays.stream(DeviceBindStatusEnum.values())
                    .filter(deviceBindStatusEnum -> deviceBindStatusEnum.code.equals(code))
                    .findFirst()
                    .orElse(null);
        }

        public static String getEnumMsgByCode(Integer code) {
            return Arrays.stream(DeviceBindStatusEnum.values())
                    .filter(deviceBindStatusEnum -> deviceBindStatusEnum.code.equals(code))
                    .map(DeviceBindStatusEnum::getMsg)
                    .findFirst()
                    .orElse("");
        }
    }


    public static enum DeviceBindRealtionStatusEnum{
        BIND(1,"已绑定"),
        UN_BIND(2,"已解绑");

        @Getter
        @Setter
        private Integer code;
        @Getter
        @Setter
        private String  msg;

        DeviceBindRealtionStatusEnum(Integer code, String msg){
            this.code = code;
            this.msg = msg;
        }

        public static DeviceBindRealtionStatusEnum getEnumByCode(Integer code) {
            return Arrays.stream(DeviceBindRealtionStatusEnum.values())
                    .filter(deviceBindRealtionStatusEnum -> deviceBindRealtionStatusEnum.code.equals(code))
                    .findFirst()
                    .orElse(null);
        }

        public static String getEnumMsgByCode(Integer code) {
            return Arrays.stream(DeviceBindRealtionStatusEnum.values())
                    .filter(deviceBindRealtionStatusEnum -> deviceBindRealtionStatusEnum.code.equals(code))
                    .map(DeviceBindRealtionStatusEnum::getMsg)
                    .findFirst()
                    .orElse("");
        }
    }
}
