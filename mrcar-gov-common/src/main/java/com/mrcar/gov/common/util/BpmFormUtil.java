package com.mrcar.gov.common.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.mrcar.gov.common.dto.workflow.form.BpmFormExportDTO;
import com.mrcar.gov.common.dto.workflow.form.element.FormElementDTO;
import com.mrcar.gov.common.util.pdf.PdfCreator;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.function.Function;


/**
 * <AUTHOR>
 * @date 2024/9/7 12:37
 */
public class BpmFormUtil {

    @AllArgsConstructor
    @Getter
    public enum FormElementTagEnum {
        EL_INPUT("el-input", "input", "el-input"),
        EL_INPUT_TEXTAREA("el-input", "textarea", "el-input-textarea"),
        EL_INPUT_PASSWORD("el-input", "password", "el-input-password"),
        EL_INPUT_NUMBER("el-input-number", "number", "el-input-number"),
        EL_SELECT("el-select", "select", "el-select"),// 下拉选择
        EL_CASCADER("el-cascader", "cascader", "el-cascader"),// 级联选择
        EL_RADIO_GROUP("el-radio-group", "radio", "el-radio-group"),// 单选框
        EL_CHECKBOX_GROUP("el-checkbox-group", "checkbox", "el-checkbox-group"),// 复选框
        EL_SWITCH("el-switch", "switch", "el-switch"),// 开关
        EL_SLIDER("el-slider", "slider", "el-slider"),// 开关
        EL_TIME_PICKER("el-time-picker", "time", "el-time-picker"),
        EL_TIME_RANGE("el-time-picker", "time-range", "el-time-range"),
        EL_DATE_PICKER("el-date-picker", "date", "el-date-picker"),
        EL_DATE_RANGE("el-date-picker", "date-range", "el-date-range"),
        EL_RATE("el-rate", "rate", "el-rate"),
        EL_UPLOAD("el-upload", "upload", "el-upload"),
        ;

        private String tag;

        private String tagIcon;

        private String appTagName;

        public static String gtAppTagName(String tag, String tagIcon){
            for(FormElementTagEnum enu : FormElementTagEnum.values()){
                if(Objects.equals(tag, enu.getTag()) && Objects.equals(tagIcon, enu.getTagIcon())){
                    return enu.getAppTagName();
                }
            }
            return tag;
        }
        public static FormElementTagEnum gtEnum(String tag, String tagIcon){
            for(FormElementTagEnum enu : FormElementTagEnum.values()){
                if(Objects.equals(tag, enu.getTag()) && Objects.equals(tagIcon, enu.getTagIcon())){
                    return enu;
                }
            }
            return null;
        }
        public static FormElementTagEnum gtEnumByAppTag(String tag){
            for(FormElementTagEnum enu : FormElementTagEnum.values()){
                if(Objects.equals(tag, enu.getAppTagName())){
                    return enu;
                }
            }
            return null;
        }
    }

    @Data
    private static class ElementHandleDTO{

        private JSONObject elementJson;

        private JSONObject formValue;

        private FormElementTagEnum elementTagEnum;
    }
    private static List<String> excludeConfigAttr =
            Lists.newArrayList("labelWidth", "showLabel", "changeTag", "layout", "span", "document", "formId", "renderKey");
    // "maxlength",
    private static List<String> excludeAttr =
            Lists.newArrayList("style", "prefix-icon", "suffix-icon", "how-word-limit", "show-word-limit");

    private static final String CONFIG_TAG_ATTR = "tag";
    private static final String CONFIG_TAG_LABEL_ATTR = "label";
    private static final String CONFIG_TAG_ICON_ATTR = "tagIcon";
    private static final String CONFIG_ATTR = "__config__";
    private static final String V_MODEL_ATTR = "__vModel__";
    private static final String SLOT_ATTR = "__slot__";
    private static final String SLOT_OPTIONS_ATTR = "options";
    private static final String SLOT_OPTIONS_VALUE_ATTR = "value";
    private static final String SLOT_OPTIONS_LABEL_ATTR = "label";
    private static final String SLOT_OPTIONS_CHILDREN_ATTR = "children";
    private static final String SEPARATOR_ATTR = "separator";
    private static final String RANGE_SEPARATOR = "至";

    private static final String DEFAULT_SEPARATOR = "|";

    private static final String APP_ELE_SWITCH_SLOT_ATTR = "{\"options\":[{\"label\":\"是\",\"value\":true },{\"label\":\"否\",\"value\":false}]}";


    private static Map<FormElementTagEnum, Function<ElementHandleDTO, FormElementDTO>> elementHandleMap = Maps.newHashMap();
    public static List<JSONObject> appFormFieldsHandle(String formFields) {
        if(strIsEmpty(formFields)){
            return Lists.newArrayList();
        }
        List<JSONObject> formFieldsList = JSONObject.parseArray(formFields, JSONObject.class);
        if(collectionIsEmpty(formFieldsList)){
            return Lists.newArrayList();
        }
        Iterator<JSONObject> iterator = formFieldsList.iterator();
        while (iterator.hasNext()){
            JSONObject formField = iterator.next();
            JSONObject configJson = formField.getJSONObject(CONFIG_ATTR);
            String tag = configJson.getString(CONFIG_TAG_ATTR);
            String tagIcon = configJson.getString(CONFIG_TAG_ICON_ATTR);
            FormElementTagEnum elementTagEnum = FormElementTagEnum.gtEnum(tag, tagIcon);
            if(Objects.isNull(elementTagEnum)){
                iterator.remove();
                continue;
            }
            configJson.put(CONFIG_TAG_ATTR, elementTagEnum.getAppTagName());
            excludeAttr.forEach(key -> {
                formField.remove(key);
            });
            excludeConfigAttr.forEach(key -> {
                configJson.remove(key);
            });
            if(Objects.equals(elementTagEnum, FormElementTagEnum.EL_SWITCH)){
                formField.put(SLOT_ATTR, JSONObject.parseObject(APP_ELE_SWITCH_SLOT_ATTR));
            }
        }
        return formFieldsList;
    }

    public static FormElementDTO commonElementHandle(ElementHandleDTO handleDto){
        JSONObject formField = handleDto.getElementJson();
        JSONObject formValue = handleDto.getFormValue();
        JSONObject configJson = formField.getJSONObject(CONFIG_ATTR);
        String label = configJson.getString(CONFIG_TAG_LABEL_ATTR);
        String vModel = formField.getString(V_MODEL_ATTR);
        String elementValue = formValue.getString(vModel);
        if(strIsEmpty(elementValue)){
            elementValue = "";
        }
        FormElementDTO formElementDTO = new FormElementDTO();
        formElementDTO.setName(label);
        formElementDTO.setElementTag(handleDto.getElementTagEnum().getAppTagName());
        formElementDTO.setValue(elementValue);
        if(!strIsEmpty(elementValue) && Objects.equals(handleDto.getElementTagEnum(), FormElementTagEnum.EL_INPUT_PASSWORD)){
            formElementDTO.setValue("******");
        }
        return formElementDTO;
    }

    public static FormElementDTO selectElementHandle(ElementHandleDTO handleDto){
        FormElementDTO formElementDTO = new FormElementDTO();
        formElementDTO.setElementTag(handleDto.getElementTagEnum().getAppTagName());
        formElementDTO.setValue("");

        JSONObject formField = handleDto.getElementJson();
        JSONObject formValue = handleDto.getFormValue();
        JSONObject configJson = formField.getJSONObject(CONFIG_ATTR);
        String label = configJson.getString(CONFIG_TAG_LABEL_ATTR);
        formElementDTO.setName(label);
        String vModel = formField.getString(V_MODEL_ATTR);

        String elementValue = formValue.getString(vModel);
        if(strIsEmpty(elementValue)){
            return formElementDTO;
        }
        JSONArray options = formField.getJSONObject(SLOT_ATTR).getJSONArray(SLOT_OPTIONS_ATTR);
        if(collectionIsEmpty(options)){
            return formElementDTO;
        }
        for(int i = options.size() - 1; i >= 0; i--){
            JSONObject obj = (JSONObject) options.get(i);
            if(Objects.equals(obj.getString(SLOT_OPTIONS_VALUE_ATTR), elementValue)){
                formElementDTO.setValue(obj.getString(SLOT_OPTIONS_LABEL_ATTR));
                break;
            }
        }
        return formElementDTO;
    }

    public static FormElementDTO cascaderElementHandle(ElementHandleDTO handleDto){
        FormElementDTO formElementDTO = new FormElementDTO();
        formElementDTO.setElementTag(handleDto.getElementTagEnum().getAppTagName());
        formElementDTO.setValue("");

        JSONObject formField = handleDto.getElementJson();
        JSONObject formValue = handleDto.getFormValue();
        JSONObject configJson = formField.getJSONObject(CONFIG_ATTR);
        String label = configJson.getString(CONFIG_TAG_LABEL_ATTR);
        formElementDTO.setName(label);
        String vModel = formField.getString(V_MODEL_ATTR);

        JSONArray elementValueArr = formValue.getJSONArray(vModel);
        if(collectionIsEmpty(elementValueArr)){
            return formElementDTO;
        }
        JSONArray options = formField.getJSONArray(SLOT_OPTIONS_ATTR);
        if(collectionIsEmpty(options)){
            return formElementDTO;
        }
        String separator = formField.getString(SEPARATOR_ATTR);
        StringBuilder valueBuild = new StringBuilder();
        while(collectionIsNotEmpty(options) && collectionIsNotEmpty(elementValueArr)){
            String elementValue = elementValueArr.getString(0);
            elementValueArr.remove(0);
            boolean find = false;
            for(int i = options.size() - 1; i >= 0; i--){
                JSONObject obj = (JSONObject) options.get(i);
                if(Objects.equals(obj.getString(SLOT_OPTIONS_VALUE_ATTR), elementValue)){
                    valueBuild.append(obj.getString(SLOT_OPTIONS_LABEL_ATTR));
                    options = obj.getJSONArray(SLOT_OPTIONS_CHILDREN_ATTR);
                    find = true;
                    if(collectionIsNotEmpty(options)){
                        valueBuild.append(separator);
                    }
                    break;
                }
            }
            if(!find){
                options = null;
            }
        }
        formElementDTO.setValue(valueBuild.toString());
        return formElementDTO;
    }

    public static FormElementDTO switchElementHandle(ElementHandleDTO handleDto){
        FormElementDTO formElementDTO = new FormElementDTO();
        formElementDTO.setElementTag(handleDto.getElementTagEnum().getAppTagName());
        formElementDTO.setValue("");

        JSONObject formField = handleDto.getElementJson();
        JSONObject formValue = handleDto.getFormValue();
        JSONObject configJson = formField.getJSONObject(CONFIG_ATTR);
        String label = configJson.getString(CONFIG_TAG_LABEL_ATTR);
        formElementDTO.setName(label);
        String vModel = formField.getString(V_MODEL_ATTR);

        Boolean elementValue = formValue.getBooleanValue(vModel);
        if(Objects.isNull(elementValue)){
            return formElementDTO;
        }
        formElementDTO.setValue(elementValue ? "是" : "否");
        return formElementDTO;
    }


    public static FormElementDTO checkElementHandle(ElementHandleDTO handleDto){
        FormElementDTO formElementDTO = new FormElementDTO();
        formElementDTO.setElementTag(handleDto.getElementTagEnum().getAppTagName());
        formElementDTO.setValue("");

        JSONObject formField = handleDto.getElementJson();
        JSONObject formValue = handleDto.getFormValue();
        JSONObject configJson = formField.getJSONObject(CONFIG_ATTR);
        String label = configJson.getString(CONFIG_TAG_LABEL_ATTR);
        formElementDTO.setName(label);
        String vModel = formField.getString(V_MODEL_ATTR);

        JSONArray elementValueArr = formValue.getJSONArray(vModel);
        if(collectionIsEmpty(elementValueArr)){
            return formElementDTO;
        }
        JSONArray options = formField.getJSONObject(SLOT_ATTR).getJSONArray(SLOT_OPTIONS_ATTR);
        if(collectionIsEmpty(options)){
            return formElementDTO;
        }
        Map<String, String> labelValueMap = new HashMap<>(options.size());
        for(int i = options.size() - 1; i >= 0; i--){
            JSONObject obj = (JSONObject) options.get(i);
            labelValueMap.put(obj.getString(SLOT_OPTIONS_VALUE_ATTR), obj.getString(SLOT_OPTIONS_LABEL_ATTR));
        }
        StringBuilder valueBuild = new StringBuilder();
        for(int i = 0; i < elementValueArr.size(); i++){
            String value = String.valueOf(elementValueArr.get(i));
            String labelStr = labelValueMap.get(value);
            if(!strIsEmpty(labelStr)){
                valueBuild.append(labelStr);
            }
            if(i != elementValueArr.size() - 1){
                valueBuild.append(DEFAULT_SEPARATOR);
            }

        }
        formElementDTO.setValue(valueBuild.toString());
        return formElementDTO;
    }


    public static FormElementDTO dateTimeElementHandle(ElementHandleDTO handleDto){
        FormElementDTO formElementDTO = new FormElementDTO();
        formElementDTO.setElementTag(handleDto.getElementTagEnum().getAppTagName());
        formElementDTO.setValue("");

        JSONObject formField = handleDto.getElementJson();
        JSONObject formValue = handleDto.getFormValue();
        JSONObject configJson = formField.getJSONObject(CONFIG_ATTR);
        String label = configJson.getString(CONFIG_TAG_LABEL_ATTR);
        formElementDTO.setName(label);
        String vModel = formField.getString(V_MODEL_ATTR);

        JSONArray elementValueArr = formValue.getJSONArray(vModel);
        if(collectionIsEmpty(elementValueArr)){
            return formElementDTO;
        }
        StringBuilder valueBuild = new StringBuilder();
        for(int i = 0; i < elementValueArr.size(); i++){
            String value = (String) elementValueArr.get(i);
            valueBuild.append(value);
            if(i != elementValueArr.size() - 1){
                valueBuild.append(RANGE_SEPARATOR);
            }
        }
        formElementDTO.setValue(valueBuild.toString());
        return formElementDTO;
    }

    public static FormElementDTO uploadElementHandle(ElementHandleDTO handleDto){
        FormElementDTO formElementDTO = new FormElementDTO();
        formElementDTO.setElementTag(handleDto.getElementTagEnum().getAppTagName());
        formElementDTO.setValue("");

        JSONObject formField = handleDto.getElementJson();
        JSONObject formValue = handleDto.getFormValue();
        JSONObject configJson = formField.getJSONObject(CONFIG_ATTR);
        String label = configJson.getString(CONFIG_TAG_LABEL_ATTR);
        formElementDTO.setName(label);
        String vModel = formField.getString(V_MODEL_ATTR);

        JSONArray elementValueArr = formValue.getJSONArray(vModel);
        if(collectionIsEmpty(elementValueArr)){
            return formElementDTO;
        }
        StringBuilder valueBuild = new StringBuilder();
        for(int i = 0; i < elementValueArr.size(); i++){
            JSONObject jsonObj = (JSONObject) elementValueArr.get(i);
            String value = jsonObj.getString("name");
            if(value == null || value.length() < 1){
                continue;
            }
            valueBuild.append(value);
            if(i != elementValueArr.size() - 1){
                valueBuild.append("\r\n");
            }
        }
        formElementDTO.setValue(valueBuild.toString());
        return formElementDTO;
    }

    static {
        elementHandleMap.put(FormElementTagEnum. EL_INPUT, BpmFormUtil::commonElementHandle);
        elementHandleMap.put(FormElementTagEnum. EL_INPUT_TEXTAREA, BpmFormUtil::commonElementHandle);
        elementHandleMap.put(FormElementTagEnum. EL_INPUT_PASSWORD, BpmFormUtil::commonElementHandle);
        elementHandleMap.put(FormElementTagEnum. EL_INPUT_NUMBER, BpmFormUtil::commonElementHandle);
        elementHandleMap.put(FormElementTagEnum. EL_SELECT, BpmFormUtil::selectElementHandle);
        elementHandleMap.put(FormElementTagEnum. EL_CASCADER, BpmFormUtil::cascaderElementHandle);
        elementHandleMap.put(FormElementTagEnum. EL_RADIO_GROUP, BpmFormUtil::selectElementHandle);
        elementHandleMap.put(FormElementTagEnum. EL_CHECKBOX_GROUP, BpmFormUtil::checkElementHandle);
        elementHandleMap.put(FormElementTagEnum. EL_SWITCH, BpmFormUtil::switchElementHandle);
        elementHandleMap.put(FormElementTagEnum. EL_SLIDER, BpmFormUtil::commonElementHandle);
        elementHandleMap.put(FormElementTagEnum. EL_TIME_PICKER, BpmFormUtil::commonElementHandle);
        elementHandleMap.put(FormElementTagEnum. EL_TIME_RANGE, BpmFormUtil::dateTimeElementHandle);
        elementHandleMap.put(FormElementTagEnum. EL_DATE_PICKER, BpmFormUtil::commonElementHandle);
        elementHandleMap.put(FormElementTagEnum. EL_DATE_RANGE, BpmFormUtil::dateTimeElementHandle);
        elementHandleMap.put(FormElementTagEnum. EL_RATE, BpmFormUtil::commonElementHandle);
        elementHandleMap.put(FormElementTagEnum. EL_UPLOAD, BpmFormUtil::uploadElementHandle);
    }

    /**
     * 提取form 表单的值
     * @param formFields
     * @param formValue
     * @return
     */
    public static List<FormElementDTO> extractFormValue(List<JSONObject> formFields, JSONObject formValue){
        List<FormElementDTO> formElementList = new ArrayList<>(formFields.size());
        formFields.forEach(formField -> {
            JSONObject configJson = formField.getJSONObject(CONFIG_ATTR);
            String tag = configJson.getString(CONFIG_TAG_ATTR);
            FormElementTagEnum formElementTagEnum = FormElementTagEnum.gtEnumByAppTag(tag);
            if(Objects.isNull(formElementTagEnum)){
                return;
            }
            Function<ElementHandleDTO, FormElementDTO> function = elementHandleMap.get(formElementTagEnum);
            if(Objects.isNull(function)){
                return;
            }
            ElementHandleDTO elementHandleDTO = new ElementHandleDTO();
            elementHandleDTO.setFormValue(formValue);
            elementHandleDTO.setElementJson(formField);
            elementHandleDTO.setElementTagEnum(formElementTagEnum);
            FormElementDTO formElementDTO = function.apply(elementHandleDTO);
            if(strIsEmpty(formElementDTO.getValue()) || "null".equals(formElementDTO.getValue())){
                formElementDTO.setValue("");
            }

            formElementList.add(formElementDTO);
        });
        return formElementList;

    }

    private static Boolean collectionIsEmpty(Collection collection) {
        return collection == null || collection.isEmpty();
    }

    private static Boolean collectionIsNotEmpty(Collection collection) {
        return !collectionIsEmpty(collection);
    }

    private static Boolean strIsEmpty(String str) {
        return str == null || str.trim().isEmpty();
    }

    public static void main(String[] args) {
        String form = "[{\"__config__\":{\"label\":\"单行文本\",\"labelWidth\":null,\"showLabel\":true,\"changeTag\":true,\"tag\":\"el-input\",\"tagIcon\":\"input\",\"required\":true,\"layout\":\"colFormItem\",\"span\":24,\"document\":\"https://element.eleme.cn/#/zh-CN/component/input\",\"regList\":[],\"formId\":106,\"renderKey\":\"1061726036617602\"},\"__slot__\":{\"prepend\":\"\",\"append\":\"\"},\"placeholder\":\"请输入单行文本\",\"style\":{\"width\":\"100%\"},\"clearable\":true,\"prefix-icon\":\"\",\"suffix-icon\":\"\",\"maxlength\":null,\"show-word-limit\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"field106\"}, {\"__config__\":{\"label\":\"多行文本\",\"labelWidth\":null,\"showLabel\":true,\"tag\":\"el-input\",\"tagIcon\":\"textarea\",\"required\":true,\"layout\":\"colFormItem\",\"span\":24,\"regList\":[],\"changeTag\":true,\"document\":\"https://element.eleme.cn/#/zh-CN/component/input\",\"formId\":107,\"renderKey\":\"1071726036622334\"},\"type\":\"textarea\",\"placeholder\":\"请输入多行文本\",\"autosize\":{\"minRows\":4,\"maxRows\":4},\"style\":{\"width\":\"100%\"},\"maxlength\":null,\"show-word-limit\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"field107\"}, {\"__config__\":{\"label\":\"密码\",\"showLabel\":true,\"labelWidth\":null,\"changeTag\":true,\"tag\":\"el-input\",\"tagIcon\":\"password\",\"layout\":\"colFormItem\",\"span\":24,\"required\":true,\"regList\":[],\"document\":\"https://element.eleme.cn/#/zh-CN/component/input\",\"formId\":108,\"renderKey\":\"1081726036624851\"},\"__slot__\":{\"prepend\":\"\",\"append\":\"\"},\"placeholder\":\"请输入密码\",\"show-password\":true,\"style\":{\"width\":\"100%\"},\"clearable\":true,\"prefix-icon\":\"\",\"suffix-icon\":\"\",\"maxlength\":null,\"show-word-limit\":false,\"readonly\":false,\"disabled\":false,\"__vModel__\":\"field108\"}, {\"__config__\":{\"label\":\"计数器\",\"showLabel\":true,\"changeTag\":true,\"labelWidth\":null,\"tag\":\"el-input-number\",\"tagIcon\":\"number\",\"span\":24,\"layout\":\"colFormItem\",\"required\":true,\"regList\":[],\"document\":\"https://element.eleme.cn/#/zh-CN/component/input-number\",\"formId\":109,\"renderKey\":\"1091726036626601\"},\"placeholder\":\"计数器\",\"step\":1,\"step-strictly\":false,\"controls-position\":\"\",\"disabled\":false,\"__vModel__\":\"field109\"}, {\"__config__\":{\"label\":\"下拉选择\",\"showLabel\":true,\"labelWidth\":null,\"tag\":\"el-select\",\"tagIcon\":\"select\",\"layout\":\"colFormItem\",\"span\":24,\"required\":true,\"regList\":[],\"changeTag\":true,\"document\":\"https://element.eleme.cn/#/zh-CN/component/select\",\"formId\":110,\"renderKey\":\"1101726036633052\"},\"__slot__\":{\"options\":[{\"label\":\"选项一\",\"value\":1},{\"label\":\"选项二\",\"value\":2}]},\"placeholder\":\"请选择下拉选择\",\"style\":{\"width\":\"100%\"},\"clearable\":true,\"disabled\":false,\"filterable\":false,\"multiple\":false,\"__vModel__\":\"field110\"}, {\"__config__\":{\"label\":\"级联选择\",\"url\":\"https://www.fastmock.site/mock/f8d7a54fb1e60561e2f720d5a810009d/fg/cascaderList\",\"method\":\"get\",\"dataPath\":\"list\",\"dataConsumer\":\"options\",\"showLabel\":true,\"labelWidth\":null,\"tag\":\"el-cascader\",\"tagIcon\":\"cascader\",\"layout\":\"colFormItem\",\"defaultValue\":[],\"dataType\":\"static\",\"span\":24,\"required\":true,\"regList\":[],\"changeTag\":true,\"document\":\"https://element.eleme.cn/#/zh-CN/component/cascader\",\"formId\":111,\"renderKey\":\"1111726036634649\"},\"options\":[{\"id\":1,\"value\":1,\"label\":\"选项1\",\"children\":[{\"id\":2,\"value\":2,\"label\":\"选项1-1\"}]},{\"label\":\"父1\",\"value\":\"父项1\",\"id\":100,\"children\":[{\"label\":\"子11\",\"value\":\"z11\",\"id\":102},{\"label\":\"子22\",\"value\":\"z22\",\"id\":103}]},{\"label\":\"父2\",\"value\":\"f2\",\"id\":101}],\"placeholder\":\"请选择级联选择\",\"style\":{\"width\":\"100%\"},\"props\":{\"props\":{\"multiple\":false,\"label\":\"label\",\"value\":\"value\",\"children\":\"children\"}},\"show-all-levels\":true,\"disabled\":false,\"clearable\":true,\"filterable\":false,\"separator\":\"/\",\"__vModel__\":\"field111\"}, {\"__config__\":{\"label\":\"单选框组\",\"labelWidth\":null,\"showLabel\":true,\"tag\":\"el-radio-group\",\"tagIcon\":\"radio\",\"changeTag\":true,\"layout\":\"colFormItem\",\"span\":24,\"optionType\":\"default\",\"regList\":[],\"required\":true,\"border\":false,\"document\":\"https://element.eleme.cn/#/zh-CN/component/radio\",\"formId\":112,\"renderKey\":\"1121726036992659\"},\"__slot__\":{\"options\":[{\"label\":\"选项一\",\"value\":1},{\"label\":\"选项二\",\"value\":2}]},\"style\":{},\"size\":\"medium\",\"disabled\":false,\"__vModel__\":\"field112\"}, {\"__config__\":{\"label\":\"多选框组\",\"tag\":\"el-checkbox-group\",\"tagIcon\":\"checkbox\",\"defaultValue\":[],\"span\":24,\"showLabel\":true,\"labelWidth\":null,\"layout\":\"colFormItem\",\"optionType\":\"default\",\"required\":true,\"regList\":[],\"changeTag\":true,\"border\":false,\"document\":\"https://element.eleme.cn/#/zh-CN/component/checkbox\",\"formId\":113,\"renderKey\":\"1131726036996155\"},\"__slot__\":{\"options\":[{\"label\":\"选项一\",\"value\":1},{\"label\":\"选项二\",\"value\":2}]},\"style\":{},\"size\":\"medium\",\"disabled\":false,\"__vModel__\":\"field113\"}, {\"__config__\":{\"label\":\"开关\",\"tag\":\"el-switch\",\"tagIcon\":\"switch\",\"defaultValue\":false,\"span\":24,\"showLabel\":true,\"labelWidth\":null,\"layout\":\"colFormItem\",\"required\":true,\"regList\":[],\"changeTag\":true,\"document\":\"https://element.eleme.cn/#/zh-CN/component/switch\",\"formId\":114,\"renderKey\":\"1141726037005922\"},\"style\":{},\"disabled\":false,\"active-text\":\"\",\"inactive-text\":\"\",\"active-color\":null,\"inactive-color\":null,\"active-value\":true,\"inactive-value\":false,\"__vModel__\":\"field114\"}, {\"__config__\":{\"label\":\"时间选择\",\"tag\":\"el-time-picker\",\"tagIcon\":\"time\",\"defaultValue\":null,\"span\":24,\"showLabel\":true,\"layout\":\"colFormItem\",\"labelWidth\":null,\"required\":true,\"regList\":[],\"changeTag\":true,\"document\":\"https://element.eleme.cn/#/zh-CN/component/time-picker\",\"formId\":116,\"renderKey\":\"1161726037027823\"},\"placeholder\":\"请选择时间选择\",\"style\":{\"width\":\"100%\"},\"disabled\":false,\"clearable\":true,\"picker-options\":{\"selectableRange\":\"00:00:00-23:59:59\"},\"format\":\"HH:mm:ss\",\"value-format\":\"HH:mm:ss\",\"__vModel__\":\"field116\"}, {\"__config__\":{\"label\":\"滑块\",\"tag\":\"el-slider\",\"tagIcon\":\"slider\",\"defaultValue\":0,\"span\":24,\"showLabel\":true,\"layout\":\"colFormItem\",\"labelWidth\":null,\"required\":true,\"regList\":[],\"changeTag\":true,\"document\":\"https://element.eleme.cn/#/zh-CN/component/slider\",\"formId\":115,\"renderKey\":\"1151726037009605\"},\"disabled\":false,\"min\":0,\"max\":100,\"step\":1,\"show-stops\":false,\"range\":false,\"__vModel__\":\"field115\"}, {\"__config__\":{\"label\":\"时间选择\",\"tag\":\"el-time-picker\",\"tagIcon\":\"time\",\"defaultValue\":null,\"span\":24,\"showLabel\":true,\"layout\":\"colFormItem\",\"labelWidth\":null,\"required\":true,\"regList\":[],\"changeTag\":true,\"document\":\"https://element.eleme.cn/#/zh-CN/component/time-picker\",\"formId\":117,\"renderKey\":\"1171726037031373\"},\"placeholder\":\"请选择时间选择\",\"style\":{\"width\":\"100%\"},\"disabled\":false,\"clearable\":true,\"picker-options\":{\"selectableRange\":\"00:00:00-23:59:59\"},\"format\":\"HH:mm:ss\",\"value-format\":\"HH:mm:ss\",\"__vModel__\":\"field117\"}, {\"__config__\":{\"label\":\"时间范围\",\"tag\":\"el-time-picker\",\"tagIcon\":\"time-range\",\"span\":24,\"showLabel\":true,\"labelWidth\":null,\"layout\":\"colFormItem\",\"defaultValue\":null,\"required\":true,\"regList\":[],\"changeTag\":true,\"document\":\"https://element.eleme.cn/#/zh-CN/component/time-picker\",\"formId\":118,\"renderKey\":\"1181726037038723\"},\"style\":{\"width\":\"100%\"},\"disabled\":false,\"clearable\":true,\"is-range\":true,\"range-separator\":\"至\",\"start-placeholder\":\"开始时间\",\"end-placeholder\":\"结束时间\",\"format\":\"HH:mm:ss\",\"value-format\":\"HH:mm:ss\",\"__vModel__\":\"field118\"}, {\"__config__\":{\"label\":\"日期选择\",\"tag\":\"el-date-picker\",\"tagIcon\":\"date\",\"defaultValue\":null,\"showLabel\":true,\"labelWidth\":null,\"span\":24,\"layout\":\"colFormItem\",\"required\":true,\"regList\":[],\"changeTag\":true,\"document\":\"https://element.eleme.cn/#/zh-CN/component/date-picker\",\"formId\":119,\"renderKey\":\"1191726037046374\"},\"placeholder\":\"请选择日期选择\",\"type\":\"date\",\"style\":{\"width\":\"100%\"},\"disabled\":false,\"clearable\":true,\"format\":\"yyyy-MM-dd\",\"value-format\":\"yyyy-MM-dd\",\"readonly\":false,\"__vModel__\":\"field119\"}, {\"__config__\":{\"label\":\"日期范围\",\"tag\":\"el-date-picker\",\"tagIcon\":\"date-range\",\"defaultValue\":null,\"span\":24,\"showLabel\":true,\"labelWidth\":null,\"required\":true,\"layout\":\"colFormItem\",\"regList\":[],\"changeTag\":true,\"document\":\"https://element.eleme.cn/#/zh-CN/component/date-picker\",\"formId\":120,\"renderKey\":\"1201726037084875\"},\"style\":{\"width\":\"100%\"},\"type\":\"daterange\",\"range-separator\":\"至\",\"start-placeholder\":\"开始日期\",\"end-placeholder\":\"结束日期\",\"disabled\":false,\"clearable\":true,\"format\":\"yyyy-MM-dd\",\"value-format\":\"yyyy-MM-dd\",\"readonly\":false,\"__vModel__\":\"field120\"}, {\"__config__\":{\"label\":\"评分\",\"tag\":\"el-rate\",\"tagIcon\":\"rate\",\"defaultValue\":0,\"span\":24,\"showLabel\":true,\"labelWidth\":null,\"layout\":\"colFormItem\",\"required\":true,\"regList\":[],\"changeTag\":true,\"document\":\"https://element.eleme.cn/#/zh-CN/component/rate\",\"formId\":121,\"renderKey\":\"1211726037088807\"},\"style\":{},\"max\":5,\"allow-half\":false,\"show-text\":false,\"show-score\":false,\"disabled\":false,\"__vModel__\":\"field121\"}, {\"__config__\":{\"label\":\"上传\",\"tag\":\"el-upload\",\"tagIcon\":\"upload\",\"layout\":\"colFormItem\",\"defaultValue\":null,\"showLabel\":true,\"labelWidth\":null,\"required\":true,\"span\":24,\"showTip\":false,\"buttonText\":\"点击上传\",\"regList\":[],\"changeTag\":true,\"fileSize\":2,\"sizeUnit\":\"MB\",\"document\":\"https://element.eleme.cn/#/zh-CN/component/upload\",\"formId\":122,\"renderKey\":\"1221726037094023\"},\"__slot__\":{\"list-type\":true},\"action\":\"/api/oss/batchUpload\",\"data\":{\"type\":1,\"business\":\"workFlow\",\"randomname\":true},\"disabled\":false,\"accept\":\"\",\"name\":\"file\",\"auto-upload\":true,\"list-type\":\"text\",\"multiple\":false,\"__vModel__\":\"field122\"}]";

//        List<JSONObject> arr = JSONObject.parseArray(form, JSONObject.class);

        List<JSONObject> formFields = appFormFieldsHandle(form);
        System.out.println("appFormFieldsHandle:" + formFields);
//        System.out.println(appFormFieldsHandle(form).toString());
        String formValueStr = "{\"field106\":\"单行文本测试\",\"field107\":\"多行文本测试\",\"field108\":\"1234324235343\",\"field109\":8,\"field110\":1,\"field111\":[\"父项1\",\"z22\"],\"field112\":1,\"field113\":[2,1],\"field114\":true,\"field116\":\"14:51:52\",\"field115\":44,\"field117\":\"14:51:59\",\"field118\":[\"14:52:07\",\"15:52:07\"],\"field119\":\"2024-09-11\",\"field120\":[\"2024-09-11\",\"2024-09-14\"],\"field121\":4,\"field122\":[{\"name\":\"哪吒AYA.png\",\"percentage\":100,\"status\":\"success\",\"uid\":1726037545261,\"url\":\"https://sqzl-img.oss-cn-beijing.aliyuncs.com/pc/test/workFlow/59f3fd57350f47eb.png\",\"size\":83554},{\"name\":\"上汽大通MIFA9.png\",\"percentage\":100,\"status\":\"success\",\"uid\":1726037550686,\"url\":\"https://sqzl-img.oss-cn-beijing.aliyuncs.com/pc/test/workFlow/6297e77c03f34204.png\",\"size\":67552}]}";
        JSONObject formValue = JSONObject.parseObject(formValueStr);
//        List<FormElementDTO> formElementList = extractFormValue(formFields, formValue);
//        System.out.println("formElementList:" + JSON.toJSONString(formElementList));


        List<BpmFormExportDTO> bpmFormExportDTOList = Lists.newArrayList();
            BpmFormExportDTO bpmFormExportDTO = new BpmFormExportDTO();
            bpmFormExportDTO.setCreatorName("测试人员");
        String processName = "快兔兔小心翼翼我以为我在♨️我再找找占座占座";
            StringBuilder processNameBuilder = new StringBuilder();
            for (int i = 0; i < processName.length(); ){
                if(i + 12 >= processName.length()){
                    processNameBuilder.append(processName.substring(i, processName.length() ));
                }else {
                    processNameBuilder.append(processName.substring(i, i + 12)).append("\r\n");
                }
                i += 12;

            }
            bpmFormExportDTO.setProcessName(processNameBuilder.toString());
            bpmFormExportDTO.setCreateDate("2024年9月12 11:12:13");
            List<FormElementDTO> formEleList = BpmFormUtil.extractFormValue(formFields, formValue);
            List<BpmFormExportDTO.RowEntry> rowEntries = new ArrayList<>(formEleList.size());
            for (FormElementDTO formEle : formEleList) {
                BpmFormExportDTO.RowEntry rowEntry = new BpmFormExportDTO.RowEntry();
                rowEntry.setKey(formEle.getName());
                rowEntry.setValue(formEle.getValue());
                rowEntries.add(rowEntry);
            }
            BpmFormExportDTO.RowEntry rowEntry = new BpmFormExportDTO.RowEntry();
            rowEntry.setKey("审批流程");
            StringBuilder valueBuild = new StringBuilder();
            valueBuild.append("张三").append(" ").append("通过").append("\r\n");
            valueBuild.append("李四").append(" ").append("通过").append("\r\n");
            rowEntry.setValue(valueBuild.toString());
            rowEntries.add(rowEntry);
            bpmFormExportDTO.setRows(rowEntries);
            bpmFormExportDTOList.add(bpmFormExportDTO);
            bpmFormExportDTOList.add(bpmFormExportDTO);
            bpmFormExportDTOList.add(bpmFormExportDTO);

        try {
            long start = System.currentTimeMillis();
            PdfCreator pdfCreator = BpmFormExportUtil.builderPdfCreator(bpmFormExportDTOList, "测试人员", "2024年9月12 11:12:13");
            File output = new File("/Users/<USER>/Downloads/20240912测试.pdf");
            pdfCreator.export(output);
            // 生成图片 TODO
//            PdfUtil.pdf2Image(output, "/Users/<USER>/Downloads/testImage/", 128);
            long end = System.currentTimeMillis();
            System.out.println("pdf2Image cost:" + ((end - start)/ 1000));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }


    }



}
