package com.mrcar.gov.common.constant.business;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/12/30 13:39
 */
@AllArgsConstructor
@Getter
public enum GovMsgTypeEnum {

    // 模板消息
    TEMPLATE_MSG(1, "模版消息"),
    // 公告
    NOTICE(2, "公告");

    private final Integer code;
    private final String desc;

    //校验消息类型枚举是否有效
    public static boolean isValid(Integer code) {
        for (GovMsgTypeEnum msgTypeEnum : GovMsgTypeEnum.values()) {
            if (msgTypeEnum.getCode().equals(code)) {
                return true;
            }
        }
        return false;
    }

    public static String getDescByCode(Integer msgType) {
        for (GovMsgTypeEnum msgTypeEnum : GovMsgTypeEnum.values()) {
            if (Objects.equals(msgType, msgTypeEnum.getCode())) {
                return msgTypeEnum.getDesc();
            }
        }
        return "";
    }
}
