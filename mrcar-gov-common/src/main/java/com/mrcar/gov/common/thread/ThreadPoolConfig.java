package com.mrcar.gov.common.thread;


import com.google.common.util.concurrent.ThreadFactoryBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 统一线程池配置
 * <p>
 * 将不同业务线需要使用的线程池统一维护
 *
 * <AUTHOR> on  2024/12/30 18:20
 */
public class ThreadPoolConfig {

    private static final Logger logger = LoggerFactory.getLogger(ThreadPoolConfig.class);


    //消息中心业务线-线程池
    private static final ThreadPoolExecutor MSG_CENTER_THREAD_POOL = newMsgCenterThreadPool();


    static {
        // 注册 shutdown hook
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            logger.info("开始关闭线程池");
            shutdownMsgCenterThreadPool();
        }));
    }

    public static ThreadPoolExecutor getMsgCenterThreadPool() {
        return MSG_CENTER_THREAD_POOL;
    }

    /**
     * 创建一个消息中心需要的线程池。
     * <p>
     * 特点：用于处理短时间高并发的任务，能够根据任务量的变化动态调整线程数量，并在高负载情况下保证所有任务都有机会被执行。
     * 线程池大小根据任务量动态调整，核心线程数默认为5，最大线程数默认为30，空闲线程超过60秒会被回收。
     * 线程数达到核心线程数后，直接创建新的线程执行任务。
     * 达到最大线程数之后，将由调用方线程直接执行任务。
     *
     * @return 返回线程池
     */
    public static ThreadPoolExecutor newMsgCenterThreadPool() {
        final int CORE_POOL_SIZE = 5; // 核心线程数
        final int MAX_POOL_SIZE = 30; // 最大线程数
        final long KEEP_ALIVE_TIME = 60L;
        final TimeUnit TIME_UNIT = TimeUnit.SECONDS;

        ThreadFactory threadFactory = new ThreadFactoryBuilder()
                .setNameFormat("msg-center-pool-%d")
                .build();

        return new ThreadPoolExecutor(
                CORE_POOL_SIZE,
                MAX_POOL_SIZE,
                KEEP_ALIVE_TIME,
                TIME_UNIT,
                new SynchronousQueue<>(), // 使用同步队列确保任务直接交给空闲线程或创建新线程
                threadFactory,
                new ThreadPoolExecutor.CallerRunsPolicy() // 当达到最大线程数时，由调用线程执行任务
        );
    }


    /**
     * 关闭消息中心线程池。
     */
    public static void shutdownMsgCenterThreadPool() {
        if (!MSG_CENTER_THREAD_POOL.isShutdown()) {
            MSG_CENTER_THREAD_POOL.shutdown();
            try {
                // 等待最多 1 秒钟让所有任务完成
                if (!MSG_CENTER_THREAD_POOL.awaitTermination(1, TimeUnit.SECONDS)) {
                    logger.info("消息中心线程池正常关闭");
                    // 如果有必要，可以在这里调用 shutdownNow 来立即停止所有任务
                    MSG_CENTER_THREAD_POOL.shutdownNow();
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                // 强制关闭线程池
                MSG_CENTER_THREAD_POOL.shutdownNow();
                logger.error("强制关闭消息中心的线程池");
            }
        }
    }


}
