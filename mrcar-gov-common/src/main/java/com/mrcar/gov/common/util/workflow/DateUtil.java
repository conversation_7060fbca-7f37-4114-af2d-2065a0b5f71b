package com.mrcar.gov.common.util.workflow;

import cn.hutool.core.date.TemporalAccessorUtil;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;
import java.util.Map;

/**
 * 一些公用的日期方法
 *
 * <AUTHOR>
 */
@Slf4j
public class DateUtil {

    private DateUtil() {
        throw new IllegalStateException("Utility class");
    }

    public static final String TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
    public static final String DATE_FORMAT = "yyyy-MM-dd";
    public static final String TIMESTAMP_CHINA_APP = "yyyy年MM月dd日 HH:mm";
    public static final String TIMESTAMP_CHINA_YMD_HMS_APP = "yyyy年MM月dd日 HH:mm:ss";
    public static final String MONTH_DAY_CHINA_APP = "MM月dd日 HH:mm";
    public static final String TIMESTAMP_CHINA_APP_ENGLISH = "yyyy/MM/dd HH:mm";
    public static final String MONTH_DAY_CHINA_APP_ENGLISH = "MM/dd HH:mm";
    public static final String DAY_MONTH_CHINA_APP = "HH:mm";
    public static final DateTimeFormatter TIME_SIMPLE_FORMAT = createFormatter(TIME_FORMAT);

    /**
     * 返回yyyy-MM-dd HH:mm:ss格式的字符串时间
     */
    public static String createTimeString() {
        return TemporalAccessorUtil.format(new Date().toInstant(), TIME_SIMPLE_FORMAT);
    }

    public static String date2String(Date date, String timeFormat) {
        SimpleDateFormat sdf = new SimpleDateFormat(timeFormat);
        return sdf.format(date);
    }

    /**
     * 锁对象
     */
    private static final Object lockObj = new Object();


    /**
     * 存放不同的日期模板格式的sdf的Map
     */
    private static Map<String, ThreadLocal<SimpleDateFormat>> sdfMap = Maps
            .newHashMapWithExpectedSize(2);


    /**
     * 返回一个ThreadLocal的sdf,每个线程只会new一次sdf
     *
     * @param pattern
     * @return SimpleDateFormat
     */
    public static SimpleDateFormat getSdf(final String pattern) {
        ThreadLocal<SimpleDateFormat> tl = sdfMap.get(pattern);

        // 此处的双重判断和同步是为了防止sdfMap这个单例被多次put重复的sdf
        if (tl == null) {
            synchronized (lockObj) {
                tl = sdfMap.get(pattern);
                if (tl == null) {
                    // 这里是关键,使用ThreadLocal<SimpleDateFormat>替代原来直接new SimpleDateFormat
                    tl = ThreadLocal.withInitial(() -> new SimpleDateFormat(pattern));
                    // 把这个线程安全的对象放到对应key 为[pattern] 中去
                    sdfMap.put(pattern, tl);
                }
            }
        }
        // 返回当前对象实例
        return tl.get();
    }

    /**
     * 是用ThreadLocal<SimpleDateFormat>来获取SimpleDateFormat,这样每个线程只会有一个SimpleDateFormat
     *
     * @param date
     * @param pattern
     * @return String
     * <AUTHOR>
     */
    public static String format(Date date, String pattern) {
        return getSdf(pattern).format(date);
    }


    public static Date getEndOfDate(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        date = calendar.getTime(); // 这就是当
        return date;
    }
    public static Date getStartOfDate(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        date = calendar.getTime(); // 这就是当
        return date;
    }

    /**
     * @param dateStr
     * @param pattern
     * <AUTHOR>
     * @description parse
     * @date 2017-08-23 14:15:25
     */
    public static Date parse(String dateStr, String pattern) throws ParseException {
        return getSdf(pattern).parse(dateStr);
    }

    /**
     * 两个时间相差距离多少天多少小时多少分多少秒
     *
     * @param one
     * @param two
     * @return String 返回值为：xx天xx小时xx分xx秒
     * <AUTHOR>
     */
    public static String getDistanceTime(Date one, Date two) {
        long day = 0;
        long hour = 0;
        long min = 0;
        long sec = 0;
        long time1 = one.getTime();
        long time2 = two.getTime();
        long diff;
        if (time1 < time2) {
            diff = time2 - time1;
        } else {
            diff = time1 - time2;
        }
        day = diff / (24 * 60 * 60 * 1000);
        hour = (diff / (60 * 60 * 1000) - day * 24);
        min = ((diff / (60 * 1000)) - day * 24 * 60 - hour * 60);
        sec = (diff / 1000 - day * 24 * 60 * 60 - hour * 60 * 60 - min * 60);
        return day + "天" + hour + "小时" + min + "分" + sec + "秒";
    }

    /**
     * 将时间转换成app显示时间
     * 显示规则：
     * 当天0点0分和之后的：hh : mm
     * 昨天0点～23:59:59的：昨天 hh : mm
     * 7天前0点～前天23:59:59的：星期X hh : mm
     * 今年1月1日0点～7天前0点的：X月X日 hh : mm
     * 去年以前的：XXXX年 X月X日 hh : mm
     *
     * @return
     */
    public static String formatShowAppDate(Date date) {
        if (null == date) {
            return "";
        }
        //获取当前时间
        Calendar nowCalendar = Calendar.getInstance();// 获取当前日期
        nowCalendar.setTime(new Date());
        //当前时间年
        int nowYear = nowCalendar.get(Calendar.YEAR);
        //当前时间日
        int nowDay = nowCalendar.get(Calendar.DAY_OF_YEAR);
        //获取指定时间
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        //指定时间年
        int year = calendar.get(Calendar.YEAR);
        //指定时间周几,从周日开始
        int week = calendar.get(Calendar.DAY_OF_WEEK);
        //指定时间日
        int day = calendar.get(Calendar.DAY_OF_YEAR);
        if (year < nowYear) {//去年以前的：XXXX年 X月X日 hh : mm
            return date2String(date, TIMESTAMP_CHINA_APP);
        }
        if (nowDay - 7 >= day) {//今年1月1日0点～7天前0点的：X月X日 hh : mm
            return date2String(date, MONTH_DAY_CHINA_APP);
        }
        if (nowDay - 2 >= day) {//7天前0点～前天23:59:59的：星期X hh : mm
            return showWeekChinaStr(week) + date2String(date, DAY_MONTH_CHINA_APP);
        }
        if (nowDay - 1 >= day) {//昨天0点～23:59:59的：昨天 hh : mm
            return "昨天 " + date2String(date, DAY_MONTH_CHINA_APP);
        }
        return date2String(date, DAY_MONTH_CHINA_APP);
    }

    /**
     * 将时间转换成app显示时间英文版
     * 显示规则：
     * 当天0点0分和之后的：hh : mm
     * 昨天0点～23:59:59的：Yesterday hh : mm
     * 7天前0点～前天23:59:59的：星期X hh : mm
     * 今年1月1日0点～7天前0点的：X月X日 hh : mm
     * 去年以前的：XXXX年 X月X日 hh : mm
     *
     * @return
     */
    public static String formatShowAppDateEnglish(Date date) {
        if (null == date) {
            return "";
        }
        //获取当前时间
        Calendar nowCalendar = Calendar.getInstance();// 获取当前日期
        nowCalendar.setTime(new Date());
        //当前时间年
        int nowYear = nowCalendar.get(Calendar.YEAR);
        //当前时间日
        int nowDay = nowCalendar.get(Calendar.DAY_OF_YEAR);
        //获取指定时间
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        //指定时间年
        int year = calendar.get(Calendar.YEAR);
        //指定时间周几,从周日开始
        int week = calendar.get(Calendar.DAY_OF_WEEK);
        //指定时间日
        int day = calendar.get(Calendar.DAY_OF_YEAR);
        if (year < nowYear) {//去年以前的：XXXX年 X月X日 hh : mm
            return date2String(date, TIMESTAMP_CHINA_APP_ENGLISH);
        }
        if (nowDay - 7 >= day) {//今年1月1日0点～7天前0点的：X月X日 hh : mm
            return date2String(date, MONTH_DAY_CHINA_APP_ENGLISH);
        }
        if (nowDay - 2 >= day) {//7天前0点～前天23:59:59的：星期X hh : mm
            return showWeekChinaStrEnglish(week) + date2String(date, DAY_MONTH_CHINA_APP);
        }
        if (nowDay - 1 >= day) {//昨天0点～23:59:59的：昨天 hh : mm
            return "Yesterday " + date2String(date, DAY_MONTH_CHINA_APP);
        }
        return date2String(date, DAY_MONTH_CHINA_APP);
    }

    /**
     * <p>Title: 获取周几</p>
     * <p>Description: </p>
     *
     * <AUTHOR>
     * @date 2018年9月26日
     * week Calendar
     */
    public static String showWeekChinaStr(int week) {
        String[] arr = {"星期日  ", "星期一  ", "星期二 ", "星期三 ", "星期四  ", "星期五  ", "星期六  "};
        return arr[week - 1];
    }

    /**
     * <p>Title: 获取周几英文</p>
     * <p>Description: </p>
     *
     * <AUTHOR>
     * @date 2018年9月26日
     * week Calendar
     */
    public static String showWeekChinaStrEnglish(int week) {
        String[] arr = {"Sunday ", "Monday ", "Tuesday ", "Wednesday ", "Thursday ", "Friday ", "Saturday "};
        return arr[week - 1];
    }

    /**
     * 两个时间相差距离多少天多少小时多少分多少秒
     *
     * @param str1 时间参数 1 格式：1990-01-01 12:00:00
     * @param str2 时间参数 2 格式：2009-01-01 12:00:00
     * @return String 返回值为：xx天xx小时xx分xx秒
     */
    @SuppressWarnings("unused")
    public static double getDistanceTimeDate(String str1, String str2) {
        DateFormat df = new SimpleDateFormat(TIME_FORMAT);
        Date one;
        Date two;
        long day = 0;
        long hour = 0;
        long min = 0;
        try {
            one = df.parse(str1);
            two = df.parse(str2);
            long time1 = one.getTime();
            long time2 = two.getTime();
            long diff;
            if (time1 < time2) {
                diff = time2 - time1;
            } else {
                diff = time1 - time2;
            }
            day = diff / (24 * 60 * 60 * 1000);
            hour = (diff / (60 * 60 * 1000) - day * 24);
            min = ((diff / (60 * 1000)) - day * 24 * 60 - hour * 60);
        } catch (ParseException e) {
            log.error(e.getMessage());
        }
        return (double) day * 24 + hour + (double) min / 60;
    }

    public static Date parseDate(String dateStr) {
        if (StringUtils.isBlank(dateStr)) {
            return null;
        }
        Date parse = null;
        SimpleDateFormat sdf = null;
        if (dateStr.contains("/")) {
            sdf = getSdf("yyyy/MM/dd HH:mm:ss");
        } else if (dateStr.contains("-")) {
            sdf = getSdf(TIME_FORMAT);
        } else {
            sdf = getSdf(DATE_FORMAT);
        }
        try {
            sdf.setLenient(true);
            parse = sdf.parse(dateStr);
        } catch (ParseException e) {
            log.error(e.getMessage());
        }
        return parse;
    }

    public static DateTimeFormatter createFormatter(String pattern) {
        return DateTimeFormatter.ofPattern(pattern, Locale.getDefault()).withZone(ZoneId.systemDefault());
    }

    /**
     * 获取当日零点
     * @return
     */
    public static Date getZeroTime() {
        // 获取当前日期
        LocalDate today = LocalDate.now();

        // 将当前日期转换为零点的 LocalDateTime
        LocalDateTime startOfDay = today.atStartOfDay();

        // 将 LocalDateTime 转换为 Date
        return Date.from(startOfDay.atZone(ZoneId.systemDefault()).toInstant());
    }
    /**
     * 获取当日零点
     * @return
     */
    public static Date getEndTime() {
        // 获取今天的日期
        LocalDate today = LocalDate.now();

        // 将今天的日期设置为今天23:59:59
        LocalDateTime endOfDay = today.atTime(23, 59, 59);

        // 转换为当前时区的Instant
        Instant instant = endOfDay.atZone(ZoneId.systemDefault()).toInstant();

        // 将Instant转换为Date对象
        return Date.from(instant);
    }
}
