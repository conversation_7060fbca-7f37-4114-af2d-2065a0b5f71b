package com.mrcar.gov.common.constant.iot;

public enum QueryDirectionEnum {
    FORWARD(1, "前向"),
    BACKWARD(-1, "后向"),
    BIDIRECTIONAL(0, "双向");

    private final int value;
    private final String description;

    private QueryDirectionEnum(int value, String description) {
        this.value = value;
        this.description = description;
    }

    public static QueryDirectionEnum of(int value) {
        for(QueryDirectionEnum entry : values()) {
            if (entry.value == value) {
                return entry;
            }
        }

        return null;
    }

    public int getValue() {
        return this.value;
    }

    public String getDescription() {
        return this.description;
    }
}