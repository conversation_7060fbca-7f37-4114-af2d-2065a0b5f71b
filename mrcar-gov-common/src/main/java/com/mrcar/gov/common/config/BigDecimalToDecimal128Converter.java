package com.mrcar.gov.common.config;

/**
 * <AUTHOR>
 * @date 2025/2/13 16:50
 */

import org.bson.types.Decimal128;
import org.springframework.data.convert.ReadingConverter;
import org.springframework.data.convert.WritingConverter;
import org.springframework.core.convert.converter.Converter;

import java.math.BigDecimal;

/**
 * 转化处理类
 **/
@WritingConverter
public class BigDecimalToDecimal128Converter implements Converter<BigDecimal, Decimal128> {
    @Override
    public Decimal128 convert(BigDecimal source) {
        return new Decimal128(source);
    }
}
