package com.mrcar.gov.common.dto.device.resp;

import java.math.BigDecimal;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class DeviceListDTO {
	/**
	 * SIM号
	 */
    private String simNo;
    /**
     * 设备号
     */
    private String deviceNo;
    /**
     * 车架号
     */
    private String vehicleVin;
    /**
     * 车牌号
     */
    private String vehicleLicense;
    /**
     * 设备类型
     */
    private Integer deviceType;
    /**
     * 设备类型名称
     */
    private String deviceTypeName;
    /**
     * 设备厂商code
     */
    private String manfactCode;
    /**
     * 设备厂商name
     */
    private String manfactName;
    /**
     * 设备型号
     */
    private String modelCode;
    /**
     * 设备型号name
     */
    private String modelName;
    /**
     * 绑定状态
     */
    private Integer bindStatus;
    /**
     * 绑定状态名称
     */
    private String bindStatusName;
    /**
     * 绑定时间
     */
    private String bindTime;
    /**
     * 解除绑定时间
     */
    private String unBindTime;

    /**
     * 车辆归属
     */
    private Byte bindVehicleType;
    /**
     * 车辆归属
     * 2自有，3员工，-1未绑定
     */
    private String bindVehicleTypeValue;

    /**
     * 纬度
     */
    private BigDecimal latBaidu;
    /**
     * 经度
     */
    private BigDecimal lngBaidu;
    /**
     * 上报时间
     */
    private String time;
    /**
     * 转向角
     */
    private Integer direction;
    /**
     * 转向角
     */
    private String directionMsg;
    
    
    public String getDirectionMsg(){
        if (direction == null) {
            return "未知";
        } else if (direction >= 0 && direction < 360) {
            if (direction == 0) {
                return "正北";
            } else if (direction > 0 && direction < 90) {
                return "东北";
            } else if (direction == 90) {
                return "正东";
            } else if (direction > 90 && direction < 180) {
                return "东南";
            } else if (direction == 180) {
                return "正南";
            } else if (direction > 180 && direction < 270) {
                return "西南";
            } else if (direction == 270) {
                return "正西";
            } else {
                return direction > 270 && direction < 360 ? "西北" : "未知";
            }
        } else {
            return "未知";
        }
    }

}
