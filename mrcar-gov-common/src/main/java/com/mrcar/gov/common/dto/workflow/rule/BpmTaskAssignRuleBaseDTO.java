package com.mrcar.gov.common.dto.workflow.rule;

import com.mrcar.gov.common.dto.workflow.BaseDTO;
import lombok.Data;
import javax.validation.constraints.NotNull;

/**
 * 流程任务分配规则 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响文档生成
 */
@Data
public class BpmTaskAssignRuleBaseDTO extends BaseDTO {

    /**
     * 规则类型
     * 必填
     * 示例值: bpm_task_assign_rule_type
     */
    @NotNull(message = "规则类型不能为空")
    private Integer type;

    /**
     * 规则值数组
     * 必填
     * 示例值: 1,2,3
     */
    @NotNull(message = "规则值数组不能为空")
    private String options;

    /**
     * 规则值数组名称
     * 必填
     * 示例值: 1,2,3
     */
    @NotNull(message = "规则值数组名称不能为空")
    private String optionsName;

}
