package com.mrcar.gov.common.constant.asset;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum MaintenanceFileEnum {

    // 维保文件 1-送修人手写签字，2-维修前照片（仪表盘）3-维修前照片（车牌照）4-报价依据照片 5-报价依据视频 6-出厂接车人手写签字 7-出厂里程表照片 8-发票 9-维修明细
    HANDWRITTEN_SIGNATURE_BY_DELIVERY_PERSON(1, "送修人手写签字"),
    PRE_REPAIR_PHOTO_DASHBOARD(2, "维修前照片（仪表盘）"),
    PRE_REPAIR_PHOTO_LICENSE_PLATE(3, "维修前照片（车牌照）"),
    QUOTATION_BASIS_PHOTO(4, "报价依据照片"),
    QUOTATION_BASIS_VIDEO(5, "报价依据视频"),
    HANDWRITTEN_SIGNATURE_BY_PICKUP_PERSON(6, "出厂接车人手写签字"),
    POST_REPAIR_ODOMETER_PHOTO(7, "出厂里程表照片"),
    INVOICE(8, "发票"),
    REPAIR_DETAILS(9, "维修明细"),
    ;

    private final Integer code;
    private final String name;

    public static String getName(Integer code) {
        return Arrays.stream(values()).filter(e -> e.getCode().equals(code)).findFirst().map(MaintenanceFileEnum::getName).orElse(null);
    }
    public static MaintenanceFileEnum getEnum(Integer code) {
        return Arrays.stream(values()).filter(e -> e.getCode().equals(code)).findFirst().orElse(null);
    }
}