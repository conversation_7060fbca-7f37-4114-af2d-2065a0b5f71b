package com.mrcar.gov.common.dto.order.resp;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.util.Date;

/**
 * @Author: mengshuang
 * @Date: 2025/4/10 18:08
 * @Param:
 * @Return:
 * @Description:
 **/
@Data
public class TimeShareConfigPageListExportRespDTO {


    /**
     * (社会租赁)供应商name
     */
    @ExcelProperty({"供应商"})
    @ColumnWidth(20)
    private String supplierName;


    /**
     * 定价类型
     */
    @ExcelProperty({"定价类型"})
    @ColumnWidth(20)
    private String priceTypeName;

    /**
     * 车辆类型名称
     */
    @ExcelProperty({"车辆类型"})
    @ColumnWidth(20)
    private String vehicleTypeName;


    /**
     * 车辆品牌
     */
    @ExcelProperty({"车辆品牌"})
    @ColumnWidth(20)
    private String vehicleBrandName;


    /**
     * 车系
     */
    @ExcelProperty({"车系"})
    @ColumnWidth(20)
    private String vehicleSeriesName;


    /**
     * 带驾里程费
     */
    @ExcelProperty({"带驾里程费（元/公里）"})
    @ColumnWidth(40)
    private String otherDrivingMilesFee;

    /**
     * 带驾时长费
     */
    @ExcelProperty({"带驾时长费（元/分钟）"})
    @ColumnWidth(40)
    private String otherDrivingTimeFee;

    /**
     * 自驾里程费
     */
    @ExcelProperty({"自驾里程费（元/公里）"})
    @ColumnWidth(40)
    private String selfDrivingMilesFee;


    /**
     * 自驾时长费
     */
    @ExcelProperty({"自驾时长费（元/分钟）"})
    @ColumnWidth(40)
    private String selfDrivingTimeFee;


    /**
     * 创建人
     */
    @ExcelProperty({"创建人"})
    @ColumnWidth(40)
    private String createName;

    /**
     * 创建时间
     */
    @ExcelProperty({"创建时间"})
    @ColumnWidth(40)
    private Date createTime;


    /**
     * 修改人
     */
    @ExcelProperty({"修改人"})
    @ColumnWidth(40)
    private String updateName;

    /**
     * 更新时间
     */
    @ExcelProperty({"修改时间"})
    @ColumnWidth(40)
    private Date updateTime;


}
