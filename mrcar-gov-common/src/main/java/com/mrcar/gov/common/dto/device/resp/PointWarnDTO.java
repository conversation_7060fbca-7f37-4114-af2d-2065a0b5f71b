package com.mrcar.gov.common.dto.device.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 点位报警信息
 * @date 2023/11/1 10:29
 */
@Data
public class PointWarnDTO {

    /**
     * 报警类型
     */
    private String warnTypeDesc;

    /**
     * 报警图标url
     */
    private String warnIcon;

    /**
     * 报警图片url
     */
    private List<String> warnPicUrl;

    /**
     * 报警视频url
     */
    private List<VideoWarnFileDTO>  warnVideoUrl;

    /**
     * 报警时间
     */
    private Date warnTime;

    /**
     * 报警点位名称
     */
    private String warnPlace;
}
