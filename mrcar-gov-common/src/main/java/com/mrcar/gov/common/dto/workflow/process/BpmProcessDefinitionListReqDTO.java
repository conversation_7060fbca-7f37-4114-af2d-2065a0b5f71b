package com.mrcar.gov.common.dto.workflow.process;

import com.mrcar.gov.common.dto.workflow.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import javax.validation.constraints.NotNull;

/**
 * 管理后台 - 流程定义列表请求数据传输对象
 * 提供了流程定义的查询参数，包括中断状态、流程分类和流程定义ID等信息。
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class BpmProcessDefinitionListReqDTO extends PageParam {

    /**
     * 中断状态
     * 示例值: 1
     * 参见 SuspensionState 枚举
     */
    @NotNull
    private Integer suspensionState;

    /**
     * 流程分类（默认只查询OA类型）
     * 隐藏字段
     */
    private String category;

    /**
     * 当前流程定义ID
     */
    private String processDefinitionId;

}
