package com.mrcar.gov.common.config;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.codec.binary.Hex;
import org.codehaus.jackson.map.DeserializationConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.lang.Nullable;
import org.springframework.util.StreamUtils;
import org.springframework.web.client.RestTemplate;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.util.Objects;
import java.util.TimeZone;

@Configuration
public class RestConfig {

    /**
     * 实现response的包装
     */
    public static class BufferingClientHttpResponseWrapper implements ClientHttpResponse {

        private final ClientHttpResponse response;

        @Nullable
        private byte[] body;

        public BufferingClientHttpResponseWrapper(ClientHttpResponse response) {
            this.response = response;
        }

        @Override
        public HttpStatus getStatusCode() throws IOException {
            return this.response.getStatusCode();
        }

        @Override
        public int getRawStatusCode() throws IOException {
            return this.response.getRawStatusCode();
        }

        @Override
        public String getStatusText() throws IOException {
            return this.response.getStatusText();
        }

        @Override
        public HttpHeaders getHeaders() {
            return this.response.getHeaders();
        }

        @Override
        public InputStream getBody() throws IOException {
            if (this.body == null) {
                this.body = StreamUtils.copyToByteArray(this.response.getBody());
            }
            return new ByteArrayInputStream(this.body);
        }

        @Override
        public void close() {
            this.response.close();
        }

        public byte[] getRepeatableBody() throws IOException {
            if (this.body == null) {
                this.body = StreamUtils.copyToByteArray(this.response.getBody());
            }
            return this.body;
        }
    }

    /**
     * 默认restful拦截器
     */
    public static class DefaultClientHttpRequestInterceptor
            implements ClientHttpRequestInterceptor {

        private static final Logger logger = LoggerFactory.getLogger(DefaultClientHttpRequestInterceptor.class);

        @Override
        public ClientHttpResponse intercept(HttpRequest request,
                                            byte[] body,
                                            ClientHttpRequestExecution execution) throws IOException {
            try {
                ClientHttpResponse response = execution.execute(request, body);
                // content-type
                MediaType contentType = response.getHeaders().getContentType();
                if (contentType != null) {
                    // 如果是text或者json格式, 打印日志
                    String type = contentType.getType();
                    String subtype = contentType.getSubtype();
                    Charset charset = contentType.getCharset();
                    if (Objects.equals("text", type)
                            || Objects.equals("json", subtype)) {
                        // wrapper
                        BufferingClientHttpResponseWrapper wrapper = new BufferingClientHttpResponseWrapper(response);
                        if (Objects.isNull(charset)) {
                            charset = StandardCharsets.UTF_8;
                        }
                        logger.info("Restful Call, url : {}, request : {}, response : {}",
                                request.getURI(),
                                new String(body, StandardCharsets.UTF_8),
                                new String(wrapper.getRepeatableBody(), charset));
                        return wrapper;
                    }
                }
                return response;
            } catch (IOException e) {
                logger.error("Occur error during Restful Call, url : {}", request.getURI(), e);
                throw e;
            }
        }

    }

    @Bean
    public RestTemplate restTemplate() {
        // custom converter
        DateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        ObjectMapper mapper = new ObjectMapper();
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        mapper.setDateFormat(format);
        mapper.setTimeZone(TimeZone.getDefault());
        MappingJackson2HttpMessageConverter converter =
                new MappingJackson2HttpMessageConverter(mapper);
        return new RestTemplateBuilder()
                .messageConverters(converter)
                .interceptors(new DefaultClientHttpRequestInterceptor())
                .setConnectTimeout(Duration.ofSeconds(15))
                .setReadTimeout(Duration.ofSeconds(15))
                .build();
    }

}
