package com.mrcar.gov.common.filter;

import org.apache.commons.io.IOUtils;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.lang.Nullable;
import org.springframework.web.util.WebUtils;

import javax.servlet.ReadListener;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import java.io.*;

public class ContentCachingRequestWrapper extends HttpServletRequestWrapper {

    private ByteArrayOutputStream cache;

    /**
     * Constructs a request object wrapping the given request.
     *
     * @param request the {@link HttpServletRequest} to be wrapped.
     * @throws IllegalArgumentException if the request is null
     */
    public ContentCachingRequestWrapper(HttpServletRequest request) {
        super(request);
    }

    @Override
    public ServletInputStream getInputStream() throws IOException {
        if (cache == null) {
            cache = new ByteArrayOutputStream();
            IOUtils.copy(getRequest().getInputStream(), cache);
        }
        return new ContentCachingInputStream(
                new ByteArrayInputStream(cache.toByteArray()), getRequest().getInputStream());
    }

    @Override
    public BufferedReader getReader() throws IOException {
        return new BufferedReader(new InputStreamReader(getInputStream(), getCharacterEncoding()));
    }

    private static class ContentCachingInputStream extends ServletInputStream {

        private final ByteArrayInputStream inStream;
        private final ServletInputStream source;

        private ContentCachingInputStream(ByteArrayInputStream inStream,
                                          ServletInputStream source) {
            this.inStream = inStream;
            this.source = source;
        }

        @Override
        public boolean isFinished() {
            return source.isFinished();
        }

        @Override
        public boolean isReady() {
            return source.isReady();
        }

        @Override
        public void setReadListener(ReadListener readListener) {
            source.setReadListener(readListener);
        }

        @Override
        public int read() throws IOException {
            return inStream.read();
        }
    }

}
