package com.mrcar.gov.common.util.pdf.api;

import java.util.ArrayList;
import java.util.List;

public class PdfSpanBuilder implements RowLikeElementBuilder {

    List<PdfDivBuilder> elements = new ArrayList<>();
    MetricBuilder height = MetricBuilder.adaptive();
    // 外边距
    float marginTopBottom = 0;
    float marginLeftRight = 0;

    public static PdfSpanBuilder builder() {
        return new PdfSpanBuilder();
    }

    public PdfSpanBuilder height(MetricBuilder metric) {
        this.height = metric;
        return this;
    }

    public PdfSpanBuilder margin(float topBottom, float leftRight) {
        this.marginTopBottom = topBottom;
        this.marginLeftRight = leftRight;
        return this;
    }

    public PdfSpanBuilder appendText(PdfTextBuilder text) {
        this.elements.add(PdfDivBuilder.create(text));
        return this;
    }

    public PdfSpanBuilder appendText(PdfTextBuilder text, MetricBuilder width) {
        this.elements.add(PdfDivBuilder.create(text, width));
        return this;
    }

    @Override
    public ElementType builderType() {
        return ElementType.SPAN;
    }

}
