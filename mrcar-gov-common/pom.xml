<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.izu</groupId>
        <artifactId>izu-parent</artifactId>
        <version>1.2.0-SNAPSHOT</version>
        <relativePath/>
    </parent>

    <groupId>com.mrcar</groupId>
    <artifactId>mrcar-gov-common</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>jar</packaging>

    <name>mrcar-gov-common</name>
    <url>http://maven.apache.org</url>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>2.8.6</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-extension</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webmvc</artifactId>
        </dependency>
        <dependency>
            <groupId>com.izu.framework</groupId>
            <artifactId>web-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.izu.cache</groupId>
            <artifactId>cache-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
            <scope>provided</scope>
        </dependency>
        <!-- 文件导出组件 -->
        <dependency>
            <groupId>com.izu.excel</groupId>
            <artifactId>izu-excel</artifactId>
            <version>2.0.0-SNAPSHOT</version>
            <!-- 排除mrcar相关依赖 -->
            <exclusions>
                <exclusion>
                    <artifactId>asset-core-common</artifactId>
                    <groupId>com.izu</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>carnet-core-common</artifactId>
                    <groupId>com.izu</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mrcar-order-common</artifactId>
                    <groupId>com.izu</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mrcar-asset-common</artifactId>
                    <groupId>com.izu</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>order-core-common</artifactId>
                    <groupId>com.izu</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>erpLease-core-common</artifactId>
                    <groupId>com.izu</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>third-common</artifactId>
                    <groupId>com.izu</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mrcar-user-common</artifactId>
                    <groupId>com.izu</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>crm-core-common</artifactId>
                    <groupId>com.izu</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>bi-common</artifactId>
                    <groupId>com.izu</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>carAsset-core-common</artifactId>
                    <groupId>com.izu</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--拼音-->
        <dependency>
            <groupId>com.belerweb</groupId>
            <artifactId>pinyin4j</artifactId>
            <version>2.5.0</version>
        </dependency>
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-annotations</artifactId>
            <version>1.5.20</version>
        </dependency>

        <!-- mongo -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-mongodb</artifactId>
        </dependency>

        <!-- jackson -->
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jsr310</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>pdfbox</artifactId>
            <version>2.0.26</version>
        </dependency>
        <dependency>
            <groupId>com.github.vandeseer</groupId>
            <artifactId>easytable</artifactId>
            <version>0.8.5</version>
        </dependency>
        <dependency>
            <groupId>com.mrcar</groupId>
            <artifactId>mrcar-gov-model</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.izu.framework</groupId>
            <artifactId>izu-mail</artifactId>
        </dependency>
        <dependency>
            <groupId>com.izu.framework</groupId>
            <artifactId>izu-locker</artifactId>
        </dependency>
        <dependency>
            <groupId>io.sgr</groupId>
            <artifactId>s2-geometry-library-java</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.9</version>
        </dependency>

        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
        </dependency>

        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
            <version>1.14.3</version>
        </dependency>

    </dependencies>
</project>
